<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.1.0 http://maven.apache.org/xsd/settings-1.1.0.xsd">

    <servers>
        <server>
            <id>nexus-snapshots</id>
            <username>dev</username>
            <password>dev</password>
            <configuration>
                <httpConfiguration>
                    <all>
                        <connectionTimeout>200000</connectionTimeout>
                        <readTimeout>200000</readTimeout>
                    </all>
                </httpConfiguration>
            </configuration>
        </server>
        <server>
            <id>nexus-releases</id>
            <username>dev</username>
            <password>dev</password>
            <configuration>
                <httpConfiguration>
                    <all>
                        <connectionTimeout>200000</connectionTimeout>
                        <readTimeout>200000</readTimeout>
                    </all>
                </httpConfiguration>
            </configuration>
        </server>
    </servers>

    <profiles>
        <profile>
            <id>cmc-repo</id>
            <repositories>
                <repository>
                    <id>cmc-releases</id>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <url>https://nexus.coinmarketcap.supply/repository/maven-public/</url>
                </repository>

            </repositories>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

</settings>