<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cmc-asset-service</artifactId>
        <groupId>com.cmc</groupId>
        <version>1.0.165-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>asset-test-report</artifactId>

    <properties>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/../${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>asset-business</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>asset-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>asset-job</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>asset-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>aggregate-report</id>
                        <phase>test</phase>
                        <goals><goal>report-aggregate</goal></goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>com/cmc/asset/model/**</exclude>
                        <exclude>com/cmc/asset/dao/**</exclude>
                        <exclude>com/cmc/asset/service/config/**</exclude>
                        <exclude>com/cmc/asset/job/config/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
