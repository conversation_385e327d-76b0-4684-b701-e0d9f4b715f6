package com.cmc.asset.model.contract.exchangelisting;

import com.cmc.data.common.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/5 16:32
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCoinListingQueryResponse extends BaseResponse {

    @ApiModelProperty(value = "subscribed ids")
    private List<String> subscribedIds;
}
