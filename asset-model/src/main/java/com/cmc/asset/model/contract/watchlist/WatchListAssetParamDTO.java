package com.cmc.asset.model.contract.watchlist;

import com.cmc.data.common.BaseRequest;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName WatchListResultMultiDTO.java
 * <AUTHOR>
 * @Date 2022/11/7 09:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchListAssetParamDTO extends BaseRequest {


    private List<String> dexTokenIds;

}
