package com.cmc.asset.model.contract.portfolio.aggregation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/04/17
 * @description
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioSummaryResponseDTO {
    @ApiModelProperty(value = "type: manual wallet")
    private String portfolioType;

    @ApiModelProperty(value = "type not manual,manualSummary is null")
    PortfolioSummaryDTO<ManualSummaryResponseDTO> manualSummary;

    @ApiModelProperty(value = "type not wallet,manualSummary is null")
    PortfolioSummaryDTO<WalletSummaryResponseDTO> walletSummary;

    @ApiModelProperty(value = "type not wallet,manualSummary is null")
    PortfolioSummaryDTO<ThirdPartySummaryResponseDTO> thirdPartySummary;
}
