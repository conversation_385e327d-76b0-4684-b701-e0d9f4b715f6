package com.cmc.asset.model.contract.watchlist;

import com.cmc.asset.model.enums.FollowTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName WatchListCacheDTO.java
 * <AUTHOR>
 * @Date 2022/11/7 15:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchListCacheDTO {

    private String userId;

    private String watchListId;

    private String name;

    private String description;

    private Long followerSize;

    private Integer cryptoSize;

    private Integer dexPairSize;

    private Integer dexTokenSize;

    private Integer assetSize;

    private Boolean shared;

    private Boolean main;

    private WatchListResultDTO.WatchListType type;

    private List<WatchListResultDTO.CryptoCurrencyDTO> cryptoCurrencies;

    private String createdTime;

    private String updatedTime;

    private FollowTypeEnum followType;

}
