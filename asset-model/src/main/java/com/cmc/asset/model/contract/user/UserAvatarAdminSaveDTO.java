package com.cmc.asset.model.contract.user;

import com.cmc.data.common.BaseRequest;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/8/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserAvatarAdminSaveDTO extends BaseRequest {

    private String id;
    @ApiModelProperty(required = true)
    @NotNull
    private String name;
    @ApiModelProperty(required = true)
    @NotNull
    private Integer isReward;
    @ApiModelProperty(required = true)
    @NotNull
    private Boolean isActive;
    @ApiModelProperty(required = true)
    private Integer sort;
}
