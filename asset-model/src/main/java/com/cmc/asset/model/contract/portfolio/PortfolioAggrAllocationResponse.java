package com.cmc.asset.model.contract.portfolio;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PortfolioAssetAllocationResponse
 * <AUTHOR> ricky.x
 * @date : 2023/1/27 下午3:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PortfolioAggrAllocationResponse {

    @ApiModelProperty(value = "token allocation list")
    List<PortfolioTokenAllocationDTO> tokenAllocationList;

    @ApiModelProperty(value = "chain allocation list,only wallet portfolio return")
    List<PortfolioChainAllocationDTO> chainAllocationList;

}
