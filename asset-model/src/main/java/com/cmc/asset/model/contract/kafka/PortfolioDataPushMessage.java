package com.cmc.asset.model.contract.kafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/4 09:40
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioDataPushMessage {

    /**
     * portfolio type
     * @see com.cmc.asset.model.enums.PortfolioTypeEnum
     */
    private String pt;

    /**
     * portfolio data type
     * @see com.cmc.asset.model.enums.PortfolioDataEnum
     */
    private String dt;

    /**
     * portfolio type
     * @see com.cmc.asset.model.enums.PortfolioWalletDataFlagEnum
     */
    private Integer status;

    private Long updated;

}
