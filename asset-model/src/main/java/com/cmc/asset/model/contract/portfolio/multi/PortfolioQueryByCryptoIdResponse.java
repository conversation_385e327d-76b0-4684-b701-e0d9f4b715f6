package com.cmc.asset.model.contract.portfolio.multi;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PortfolioQueryByCryptoIdResponse
 * <AUTHOR> ricky.x
 * @date : 2023/2/28 下午5:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioQueryByCryptoIdResponse {
    private String portfolioSourceId;
    private String portfolioName;
    private String portfolioType;
    private Boolean isMain;
    private String bgColor;
    private BigDecimal totalAmount;
    private BigDecimal totalHoldings;
    private BigDecimal cryptoUnitPrice;
    private BigDecimal fiatUnitPrice;
    @JsonIgnore
    private Integer sortIndex;
}
