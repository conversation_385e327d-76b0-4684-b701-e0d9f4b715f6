package com.cmc.asset.model.enums;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024-08-02
 */
public enum PortfolioWalletFetchStatusEnum {
    NO_FETCH(0, "not fetch data"),
    FETCH(1, "fetch data"),
    FETCH_ERROR(2, "fetch error");

    private final int status;
    private final String desc;

    PortfolioWalletFetchStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
