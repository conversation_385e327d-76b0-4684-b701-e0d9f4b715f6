package com.cmc.asset.model.enums;

import java.util.stream.Stream;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/8/30
 */
@Getter
public enum UserAvatarTypeEnum {
    /**
     * System avatar
     */
    SYSTEM(0),
    /**
     * reward avatar
     */
    REWARD(1);

    private int value;

    UserAvatarTypeEnum(int value) {
        this.value = value;
    }

    public static UserAvatarTypeEnum getByValue(int value) {
        return Stream.of(UserAvatarTypeEnum.values())
            .filter(userAvatarTypeEnum -> userAvatarTypeEnum.getValue() == value).findFirst().orElse(null);
    }
}
