package com.cmc.asset.model.contract.portfolio.aggregation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * the response body of query third party
 * <AUTHOR> lunke
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ThirdPartySummaryResponseDTO {

    @ApiModelProperty(value = "crypto id")
    private Integer cryptocurrencyId;

    @ApiModelProperty(value = "crypto name")
    private String name;

    @ApiModelProperty(value = "crypto symbol")
    private String symbol;

    @ApiModelProperty(value = "crypto slug")
    private String slug;

    @ApiModelProperty(value = "holding amount")
    private BigDecimal balance;

    @ApiModelProperty(value = "value,unit usd")
    private BigDecimal value;

    @ApiModelProperty(value = "price,unit usd")
    private BigDecimal currentPrice;

    @ApiModelProperty(value = "price,unit usd")
    private BigDecimal prePrice;
    @ApiModelProperty(value = "holding percent")
    private BigDecimal cryptoHoldings;
    @ApiModelProperty(value = "price,update time")
    private String lastUpdated;

    @ApiModelProperty(value = "change price 24h,unit usd")
    private BigDecimal preFiatChangePercent;
    @ApiModelProperty(value = "change price 24h,unit usd")
    private BigDecimal preCryptoChangePercent;
    @ApiModelProperty(value = "change price 24h,unit usd")
    private BigDecimal preChangeValue;

    @ApiModelProperty(value = "holding percent")
    private BigDecimal holdingsPercent;

    @ApiModelProperty(value = "change price 24h,unit crypto")
    private BigDecimal cryptoPriceChangePercent24h;

    @ApiModelProperty(value = "change price 24h,unit fiat")
    private BigDecimal fiatPriceChangePercent24h;
}
