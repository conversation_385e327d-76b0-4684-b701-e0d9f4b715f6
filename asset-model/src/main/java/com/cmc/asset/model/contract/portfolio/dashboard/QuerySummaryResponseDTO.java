package com.cmc.asset.model.contract.portfolio.dashboard;

import com.cmc.asset.model.common.CommonPageVO;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QuerySummaryResponse
 *
 * @author: lunke.z
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuerySummaryResponseDTO extends CommonPageVO<DashboardSummaryDTO> {

    private BigDecimal cryptoUnitPrice;

    private BigDecimal fiatUnitPrice;

    private List<DashboardSummaryDTO> list;

}
