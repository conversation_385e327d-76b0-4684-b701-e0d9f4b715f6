package com.cmc.asset.model.contract.portfolio.aggregation;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/8 06:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnionPortfolio {

    private Integer type;

    private Integer cryptocurrencyId;
    private String logoUrl;
    private String name;
    private String symbol;
    private String slug;
    private Boolean isActive;
    private BigDecimal currentPrice;
    private BigDecimal priceChangePercent1h;
    private BigDecimal priceChangePercent24h;
    private BigDecimal priceChangePercent7d;
    private String lastUpdated;

    private String contractAddress;
    private Integer platformId;
    private String platformName;

    private String portfolioSourceId;
    private String portfolioName;
    private String bgColor;
    private Integer sortIndex;
    private Boolean ownWallet;
    private Boolean isMain;
    private String portfolioType;
    private String portfolioAvatar;

    private Integer chainId;
    private Integer chainLogoId;
    private String chainName;
    private String chainShortName;

    private BigDecimal balance;
    private BigDecimal value;

    private Date createTime;

}
