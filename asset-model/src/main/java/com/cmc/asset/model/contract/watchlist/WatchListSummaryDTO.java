package com.cmc.asset.model.contract.watchlist;

import com.cmc.asset.model.serializer.CustomLongSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WatchListSummaryDTO {
    private String watchListId;
    private String name;
    @JsonSerialize(using = CustomLongSerializer.class)
    private Long followerSize;
    private String followType;
    private Integer cryptoSize;
    private Integer assetSize;
    private Integer totalAssetSize;
    private Boolean shared;
    private Boolean main;
    private String description;
    private String type;
    private List<Integer> topCrypto;
    private List<Long> topDexBaseCryptoIds;
    private List<String> topDexTokenLogos;

    @JsonIgnore
    private LocalDateTime createdTime;
}