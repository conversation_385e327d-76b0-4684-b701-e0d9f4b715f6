package com.cmc.asset.model.enums;

import com.cmc.framework.common.enums.BaseEnum;

/**
 * <AUTHOR>
 */
public enum WebsocketBusinessTopicEnum implements BaseEnum {

    /**
     * business topic portfolio
     */
    PORTFOLIO("portfolio","asset websocket business topic portfolio"),


    ;

    private String code;
    private String desc;

    WebsocketBusinessTopicEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
