package com.cmc.asset.model.contract.useraudit;

import com.cmc.data.common.BaseRequest;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/12/2 14:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImplioWebHookRequestDTO extends BaseRequest {
    private Long packedAt;
    private String domain;
    private Ad ad;
    private Result result;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        private String title;
        private String body;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ad {
        private String id;
        private String batchId;
        private String taskId;
        private Content content;
        private User user;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class User {
        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Feedback {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Word {
        private String word;
        private String regex;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WordHighlighting {
        private String variableName;
        private List<Word> words;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Notice {
        private String text;
        private String severity;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MatchingFilter {
        private String id;
        private String name;
        private String vote;
        private List<WordHighlighting> wordHighlighting;
        private Notice notice;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String outcome;
        private List<Reason> reasons;
        private List<Feedback> feedback;
        private List<MatchingFilter> matchingFilters;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Reason {
        private String id;
        private String name;
    }
}
