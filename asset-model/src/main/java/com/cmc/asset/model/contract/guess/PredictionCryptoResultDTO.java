package com.cmc.asset.model.contract.guess;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/11/18 11:08
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PredictionCryptoResultDTO {

    private String predictionId;

//    private Integer cryptoId;
    /**
//     * User's receiving address type
//     */
//    private Integer payAddrType;

    /**
     * User's receiving binanceId
     */
    private String binanceId;

    /**
     *  2020-11-18 5:00~6:00
     * first user prediction begin time.
     */
    private String firstFromTime;

    private String firstToTime;

    /**
     * 2020-11-18 5:00~6:00
     * second user prediction begin time.
     */
    private String secondFromTime;

    private String secondToTime;
    /**
     * 2020-11-18 5:00~6:00
     * third user prediction begin time.
     */
    private String thirdFromTime;

    private String thirdToTime;
}
