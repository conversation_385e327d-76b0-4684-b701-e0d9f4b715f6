package com.cmc.asset.model.contract.user;

import com.cmc.data.common.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "User Setting Config Query")
public class UserSettingConfigQueryDTO extends BaseRequest {

    @ApiModelProperty(value = "client platform, web/ios/android")
    private String platform;
    @ApiModelProperty(value = "client device id")
    private String deviceId;
}
