package com.cmc.asset.model.contract.priceprediction;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/7/30 11:20:48
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PricePredictionAccuracyYpoint {

    private BigDecimal averageAccuracy;

    private BigDecimal estimateScore;

    private Long estimateCount;

}
