package com.cmc.asset.model.contract.portfolio;

import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoAggregationVo;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoDetailCacheV3VO;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyQuotesLatestCacheV3VO;
import com.cmc.asset.model.enums.ListingStatus;
import com.cmc.data.common.utils.ExtUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioCryptoInfoDTO {
    private Integer cryptoId;
    @JsonProperty(value = "price_usd")
    private String price;
    private String name;
    private String symbol;
    @JsonProperty(value = "total_supply")
    private String supply;
    @JsonProperty(value = "percentage_change_price_usd_1h")
    private String oneHourChangePercent;
    @JsonProperty(value = "percentage_change_price_usd_24h")
    private String yesterdayChangePercent;

    @JsonProperty(value = "percentage_change_price_usd_7d")
    private String sevenDaysChangePercent;
    @JsonProperty(value = "percentage_change_price_usd_30d")
    private String thirtyDaysChangePercent;
    private String slug;
    private String status;
    @JsonProperty(value = "last_updated")
    private String lastUpdated;

    public BigDecimal transferPrice() {
        return StringUtils.isNotBlank(this.price) ? new BigDecimal(this.price).setScale(30,
                RoundingMode.HALF_EVEN).stripTrailingZeros() : BigDecimal.ZERO;
    }

    public BigDecimal transferSupply() {
        return StringUtils.isNotBlank(this.supply) ? new BigDecimal(this.supply) : BigDecimal.ZERO;
    }

    public BigDecimal transferChangePerCent() {
        return StringUtils.isNotBlank(yesterdayChangePercent) ?
            new BigDecimal(yesterdayChangePercent).stripTrailingZeros() : BigDecimal.ZERO;
    }
    public BigDecimal transferOneHourChangePerCent() {
        return StringUtils.isNotBlank(oneHourChangePercent) ?
            new BigDecimal(oneHourChangePercent).stripTrailingZeros() : BigDecimal.ZERO;
    }
    public BigDecimal transferSevenDaysChangePerCent() {
        return StringUtils.isNotBlank(sevenDaysChangePercent) ?
            new BigDecimal(sevenDaysChangePercent).stripTrailingZeros() : BigDecimal.ZERO;
    }
    public BigDecimal transferThirtyDaysChangePerCent() {
        return StringUtils.isNotBlank(thirtyDaysChangePercent) ?
            new BigDecimal(thirtyDaysChangePercent).stripTrailingZeros() : BigDecimal.ZERO;
    }

    public static List<PortfolioCryptoInfoDTO> fromCryptoCurrencyInfoAggregationVo(
        CryptoCurrencyInfoAggregationVo aggregationVo) {
        if (aggregationVo == null
            || CollectionUtils.isEmpty(aggregationVo.getDetailCacheV3VOs())
            || CollectionUtils.isEmpty(aggregationVo.getQuotesLatestCacheV3VOS())) {
            return List.of();
        }
        Map<Integer, CryptoCurrencyQuotesLatestCacheV3VO> quotesLatestV3Map = aggregationVo.getQuotesLatestCacheV3VOS()
            .stream()
            .collect(
                Collectors.toMap(CryptoCurrencyQuotesLatestCacheV3VO::getId, Function.identity(), (k1, k2) -> k1)
            );
        List<PortfolioCryptoInfoDTO> portfolioCryptoInfoDTOS =
            Lists.newArrayListWithCapacity(aggregationVo.getDetailCacheV3VOs().size());
        for (CryptoCurrencyInfoDetailCacheV3VO detailCacheV3VO : aggregationVo.getDetailCacheV3VOs()) {
            CryptoCurrencyQuotesLatestCacheV3VO quotesLatest = quotesLatestV3Map.get(detailCacheV3VO.getId());
            PortfolioCryptoInfoDTO portfolioCryptoInfoDTO =
                PortfolioCryptoInfoDTO.builder()
                    .cryptoId(detailCacheV3VO.getId())
                    .name(detailCacheV3VO.getName())
                    .symbol(detailCacheV3VO.getSymbol())
                    .slug(detailCacheV3VO.getSlug())
                    .status(parseStatus(detailCacheV3VO.getIsListed(), detailCacheV3VO.getIsActive()))
                    .supply(
                        quotesLatest == null || quotesLatest.getTotalSupply() == null ? null :
                            quotesLatest.getTotalSupply().toString()
                    )
                    .lastUpdated(quotesLatest == null ? null : quotesLatest.getLastUpdated())
                    .price(
                        quotesLatest == null || quotesLatest.getPriceUsd() == null ? null :
                            quotesLatest.getPriceUsd().toString()
                    )
                    .yesterdayChangePercent(
                        quotesLatest == null || quotesLatest.getPercentageChangePriceUsd24h() == null ? null :
                            quotesLatest.getPercentageChangePriceUsd24h().toString()
                    ).oneHourChangePercent(
                        quotesLatest == null || quotesLatest.getPercentageChangePriceUsd1h() == null ? null :
                            quotesLatest.getPercentageChangePriceUsd1h().toString()
                    ).sevenDaysChangePercent(
                        quotesLatest == null || quotesLatest.getPercentageChangePriceUsd7d() == null ? null :
                            quotesLatest.getPercentageChangePriceUsd7d().toString()
                    ).thirtyDaysChangePercent(
                        quotesLatest == null || quotesLatest.getPercentageChangePriceUsd30d() == null ? null :
                            quotesLatest.getPercentageChangePriceUsd30d().toString()
                    ).build();
            portfolioCryptoInfoDTOS.add(portfolioCryptoInfoDTO);
        }
        return portfolioCryptoInfoDTOS;
    }

    public static List<PortfolioCryptoInfoDTO> fromCryptoCurrencyQuotesLatestCacheV3VO(List<CryptoCurrencyQuotesLatestCacheV3VO> latestCacheV3VOS) {
        if (CollectionUtils.isEmpty(latestCacheV3VOS)) {
            return List.of();
        }
        return latestCacheV3VOS.stream()
                .filter(vo -> vo.getPriceUsd() != null)
                .map(vo -> PortfolioCryptoInfoDTO.builder()
                        .cryptoId(vo.getId())
                        .lastUpdated(vo.getLastUpdated())
                        .price(vo.getPriceUsd() == null ? null : vo.getPriceUsd().toString())
                        .yesterdayChangePercent(vo.getPercentageChangePriceUsd24h() == null ? null : vo.getPercentageChangePriceUsd24h()
                                .toString())
                        .oneHourChangePercent(vo.getPercentageChangePriceUsd1h() == null ? null : vo.getPercentageChangePriceUsd1h()
                                .toString())
                        .sevenDaysChangePercent(vo.getPercentageChangePriceUsd7d() == null ? null : vo.getPercentageChangePriceUsd7d()
                                .toString())
                        .thirtyDaysChangePercent(vo.getPercentageChangePriceUsd30d() == null ? null : vo.getPercentageChangePriceUsd30d()
                                .toString())
                        .supply(vo.getTotalSupply() == null ? null : vo.getTotalSupply().toString())
                        .build())
                .collect(Collectors.toList());
    }

    public static String parseStatus(Integer is_listed, Integer is_actived) {
        if (ExtUtils.getDefaultValue(is_listed, 0) > 0) {
            return ListingStatus.UNTRACKED.getCode();
        } else if (ExtUtils.getDefaultValue(is_actived, 0) > 0) {
            return ListingStatus.ACTIVE.getCode();
        } else {
            return ListingStatus.INACTIVE.getCode();
        }
    }

    public boolean isActive() {
        return ListingStatus.ACTIVE.getCode().equals(this.status);
    }

}
