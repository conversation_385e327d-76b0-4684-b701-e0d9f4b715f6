package com.cmc.asset.model.contract.kafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * websocat record data
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WebsocketMetaDataVO<T> {

    /**
     * ws metaData
     */
    private T d;
    /**
     * ws dataTimestamp
     */
    private Long t;
    /**
     * ws channel
     */
    private String c;

}
