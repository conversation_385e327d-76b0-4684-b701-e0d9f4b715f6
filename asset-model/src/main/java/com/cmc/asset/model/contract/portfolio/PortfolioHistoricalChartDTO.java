package com.cmc.asset.model.contract.portfolio;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2020/11/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortfolioHistoricalChartDTO {
    private Integer cryptoId;
    private BigDecimal unitPrice;
    private List<PortfolioTotalDTO> totalList;
}
