package com.cmc.asset.model.contract.portfolio.thirdparty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * the response body of query wallet
 *
 * <AUTHOR> ricky.x
 * @date : 2023/1/27 下午3:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryThirdPartyDTO {

    private List<ThirdPartySummaryDTO> summaryDTOList;

    private Long lastUpdateValueTime;

    private Long lastUpdateTime;
}
