package com.cmc.asset.model.contract.guess;

import com.cmc.data.common.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/11/18 11:07
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PredictionCryptoParamDTO extends BaseRequest {

    private String predictionId;

    /**
     * User's receiving address
     */
    private String binanceId;

    /**
     *  2020-11-18 5:00~6:00
     * first user prediction begin time.
     */
    private String firstFromTime;

    private String firstToTime;

    /**
     * 2020-11-18 5:00~6:00
     * second user prediction begin time.
     */
    private String secondFromTime;

    private String secondToTime;
    /**
     * 2020-11-18 5:00~6:00
     * third user prediction begin time.
     */
    private String thirdFromTime;

    private String thirdToTime;
}
