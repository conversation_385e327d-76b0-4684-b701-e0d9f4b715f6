package com.cmc.asset.model.contract.crypto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data required by {@link com.cmc.worker.processor.job.handler.ndler}
 *
 * <AUTHOR>
 * @date 2021/4/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractInfoVO {
    private Integer platformId;
    private Integer platformCryptoId;
    private String contractAddress;
    private String platformName;
    private Integer platformChainId;
}