package com.cmc.asset.model.contract.watchlist;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:43
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchDexTokenDTO {
    private Integer platformId;
    private String address;
    private String symbol;
    private Integer age;
    private BigDecimal mc;
    private BigDecimal liq;
    private Integer holders;
    private Long txs1h;
    private Long vol1h;
    private BigDecimal priceUsd;
    private BigDecimal priceChange5m;
    private BigDecimal priceChange1h;
    private BigDecimal priceChange4h;
    private BigDecimal priceChange24h;
    private String riskLevel;
}