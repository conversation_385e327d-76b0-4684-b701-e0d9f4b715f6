package com.cmc.asset.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
public enum UserAlertsTypeEnum {

    /**
     * Pair alert
     */
    PAIR("pair");

    private final String code;

    UserAlertsTypeEnum(String code) {
        this.code = code;
    }

    public static UserAlertsTypeEnum findByCode(String code) {

        if (StringUtils.isEmpty(code)) {
            return null;
        }
        code = code.trim().toLowerCase();
        for (UserAlertsTypeEnum b : UserAlertsTypeEnum.values()) {

            if (StringUtils.equalsIgnoreCase(b.name(), code)) {
                return b;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }
}
