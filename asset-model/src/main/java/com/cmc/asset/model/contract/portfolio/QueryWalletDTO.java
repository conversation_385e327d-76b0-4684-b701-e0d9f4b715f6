package com.cmc.asset.model.contract.portfolio;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * the response body of query wallet
 * <AUTHOR> ricky.x
 * @date : 2023/1/27 下午3:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryWalletDTO {

    @ApiModelProperty(value = "false means the data is old,need request again")
    private Boolean jobFlag;

    private Integer dataFlag;

    private List<WalletSummaryDTO> walletSummaryList;

    private List<Integer> chains;

    private String walletAddress;

    private Long lastUpdateTime;
}
