package com.cmc.asset.model.contract.priceprediction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/4/20
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePricePredictionParamDTO extends BasePricePredictionParamDTO implements Serializable {

    private String id;
    private BigDecimal predictedPrice;
    private String targetUserId;
    private String operator;

}
