package com.cmc.asset.model.common;

import java.math.BigDecimal;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @date 2023/06/12
 * @description
 **/
public interface Constant {

    String UTC_TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    /**
     * key to cache crypto 5m data,v3:cr:history:<convert_id>:<time>
     */
    String CR_HISTORY_DATA = "v3:cr:history:%s:%s";

    String DEX_TOKEN_DATA_V2 = "v3:dex:token:uni:cache:%s";
    String UNDERLINE = "_";

    /**
     * ddb v2 table pk filed format
     */
    String CRYPTO_OHLCV_DDB_PK = "%s_%s";

    int ONE_DAY_MINUTES = 24 * 60;
    int ONE_DAY_HOURS = 24;
    long FIVE_MINUTE_BY_SECONDS = 300L;
    long ONE_DAY_SECONDS = 24 * 60 * 60;

    BiFunction<Integer, String,String> EXCHANGE_MARKET_PAIR_RANK = (id, p1) -> "v2:ex:market-pairs-latest:" + id + ":5m:sort-" + p1;

    Integer EXCHANGE_ID_BINANCE = 270;
    Integer EXCHANGE_ID_OKX = 294;

    Integer USD_CURRENCY_ID = 2781;
    Integer BTC_CURRENCY_ID = 1;
    Integer BITS_CURRENCY_ID = 9023;
    Integer SATS_CURRENCY_ID = 9022;
    BigDecimal BTC_BITS_RATIO = new BigDecimal("1000000");
    BigDecimal BTC_SATS_RATIO = new BigDecimal("100000000");

    Integer CHAIN_ID_CARDANO = 29;

    String CTX_KEY_MULTI_ENTITY = "multiEntity";
    String CTX_KEY_MULTI_CRYPTO_UNIT = "multiCryptoUnit";
    String CTX_KEY_REQUEST_CRYPTO_UNITS = "cryptoUnits";

    String DEFAULT_PORTFOLIO_SOURCE = "default";
    int DEFAULT_SCALE = 18;
    Integer CHAIN_ID_SOLANA = 16;
    Integer CHAIN_ID_BTC = 144;

    Integer CHAIN_SOLANA_DECIMALS = 9;

    String BEARER_TOKEN_PREFIX = "Bearer ";
    String WALLET_ADDRESS_XPUB = "xpub";

    String DUPLICATE_PORTFOLIO_SUFFIX = " copy";
    String DEFAULT_PORTFOLIO_PREFIX = "Portfolio ";
}
