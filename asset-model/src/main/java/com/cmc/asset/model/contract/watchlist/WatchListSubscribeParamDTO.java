package com.cmc.asset.model.contract.watchlist;

import com.cmc.data.common.BaseRequest;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:41
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchListSubscribeParamDTO extends BaseRequest {

    /**
     * subscribe | unsubscribe
     */
    private String subscribeType;

    /**
     * exchange | crypto| marketpair | dexToken
     */
    private String resourceType;

    private String resourceId;

    private String watchListId;

    private Boolean shared;

    /**
     * Replace all resource IDs.
     */
    private Boolean needReplaceAllResourceIds;
}
