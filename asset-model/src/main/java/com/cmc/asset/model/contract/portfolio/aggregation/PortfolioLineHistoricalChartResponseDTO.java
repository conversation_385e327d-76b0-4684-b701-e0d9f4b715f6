package com.cmc.asset.model.contract.portfolio.aggregation;

import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryLineChartResponseDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/04/13
 * @description
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioLineHistoricalChartResponseDTO {
    List<PortfolioHistoricalChartDTO> portfolioChart;
    List<QueryLineChartResponseDTO> dashboardChart;
}
