package com.cmc.asset.model.contract.portfolio.aggregation;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/04/11
 * @description
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PortfolioStatisticsResponseDTO {
    private BigDecimal totalPlValue;
    private BigDecimal totalPlpercentValue;

    private String portfolioSourceId;
    private BigDecimal cryptoUnitPrice;
    private BigDecimal fiatUnitPrice;
    private BigDecimal preChangePercent;
    private BigDecimal preChangeValue;

    private BigDecimal preTotalValue;
    @JsonInclude
    private BigDecimal currentTotalHoldings;
    @JsonInclude
    private BigDecimal preTotalHoldings;
    // 没有
    private String lastUpdated;
    private Boolean hasBuySpent;
    private Boolean needReauthorize;

    private BigDecimal plValue;
    private BigDecimal plPercentValue;
    private BigDecimal totalBuySpent;
    private BigDecimal realizedProfit;
    private BigDecimal unrealizedProfit;

}
