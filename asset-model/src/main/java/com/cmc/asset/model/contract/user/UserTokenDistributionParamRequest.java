package com.cmc.asset.model.contract.user;

import com.cmc.data.common.BaseRequest;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/03/30
 */
@EqualsAndHashCode
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "Query the user token distribution")
public class UserTokenDistributionParamRequest extends BaseRequest implements Serializable {

    @NotNull
    @ApiModelProperty("The event")
    private String event;

    @NotNull
    @ApiModelProperty("The user's email")
    private String email;

    @NotNull
    @ApiModelProperty("The user's binance Id")
    private String binanceId;
}
