package com.cmc.asset.model.contract.portfolio.dashboard;

import com.cmc.data.common.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * QueryStatisticsRequestDTO
 *
 * <AUTHOR>
 */
@Data
public class QueryLineChartRequestDTO extends BaseRequest {

    @NotNull
    private List<Integer> cryptoIds;
    /**
     * 0 代表 all
     */
    @NotNull
    private Integer days;

    @ApiModelProperty("crypto verification status: 0: all crypto, 1: verified, 2: unverified")
    @Nullable
    private Integer cryptoVerificationStatus;

}
