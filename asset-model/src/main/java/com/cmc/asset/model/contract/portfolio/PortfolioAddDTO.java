package com.cmc.asset.model.contract.portfolio;

import com.cmc.data.common.BaseRequest;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2020/11/12
 */
@Data
public class PortfolioAddDTO extends BaseRequest {
    @Length(max = 30)
    private String userId;
    @NotNull
    private Integer cryptocurrencyId;
    private Integer cryptoUnit;
    private Integer fiatUnit;
    @NotNull
    private BigDecimal amount;
    private BigDecimal price;
    private BigDecimal fee;
    private String transactionType;
    private Date transactionTime;
    @Length(max = 30)
    private String portfolioSourceId;
    private String note;
    private String secretKey;
}
