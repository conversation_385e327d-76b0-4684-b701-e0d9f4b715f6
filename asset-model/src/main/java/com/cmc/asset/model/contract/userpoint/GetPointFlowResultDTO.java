package com.cmc.asset.model.contract.userpoint;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/9/2 下午7:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GetPointFlowResultDTO {

    private String userId;

    private Integer type;

    private Integer point;

    private Integer unit;

    private Date rewardDate;

    private Date createDate;
}
