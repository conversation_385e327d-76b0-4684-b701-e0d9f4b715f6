package com.cmc.asset.model.contract.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.cmc.asset.model.contract.news.QueryTopCoinsResultDTO.CoinDTO;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/7 16:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserRelevantCoinsResponseDTO implements Serializable {
    private List<CoinDTO> watchListCoins;
    private List<CoinDTO> portfolioCoins;
}
