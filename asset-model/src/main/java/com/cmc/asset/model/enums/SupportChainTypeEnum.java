package com.cmc.asset.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;

import java.util.Set;
import java.util.function.Supplier;

/**
 * AddressChainTypeEnum
 * <AUTHOR> ricky.x
 * @date : 2023/4/24 下午3:57
 */
@Getter
@AllArgsConstructor
public enum SupportChainTypeEnum {

    /**
     * eth等7条链 ETH:1  BSC:14 FTM:24 POLYGON:25 AVAXC:28 OPTIMISM:42 ARBITRUM:51
     */
    ETH_ETC("0",() -> getChainIds("0")),

    /**
     * btc BTC:144
     */
    BTC("1",() -> getChainIds("1")),

    /**
     * Cardano ADA:29
     */
    CARDANO("2",() -> getChainIds("2")),

    /**
     * Cardano_stake ADA:29
     */
    CARDANO_STAKE("3",() -> getChainIds("2")),

    TRON("4",() -> getChainIds("4")),

    SUI("5",() -> getChainIds("5")),

    APTOS("6",() -> getChainIds("6")),
    ;
    @Getter
    private final Supplier<Set<Integer>> chainIds;

    @Getter
    private final String code;

    @NotNull
    private static Set<Integer> getChainIds(String code) {
        if(code.equals(ETH_ETC.getCode())){
            return Set.of(1, 14, 20, 24, 25, 26, 28, 31, 42, 51, 66, 73, 82, 117, 137, 140, 183, 185, 198, 199, 200, 204, 205, 216);
        }
        if(code.equals(BTC.getCode())){
            return Set.of(144);
        }
        if(code.equals(TRON.getCode())){
            return Set.of(47);
        }
        if(code.equals(SUI.getCode())){
            return Set.of(176);
        }
        if(code.equals(APTOS.getCode())){
            return Set.of(141);
        }
        return Set.of(29);
    }


    SupportChainTypeEnum(String code, Supplier<Set<Integer>> chainIds) {
        this.code = code;
        this.chainIds = chainIds;
    }

    public static SupportChainTypeEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SupportChainTypeEnum chainTypeEnum : SupportChainTypeEnum.values()) {
            if (chainTypeEnum.code.equals(code)) {
                return chainTypeEnum;
            }
        }
        return null;
    }

}
