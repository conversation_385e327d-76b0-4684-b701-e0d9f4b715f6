package com.cmc.asset.model.contract.crypto;

import lombok.Data;

import java.util.List;

/**
 * VO for cache of v3:cr:info:detail
 * value Flux (worker:processor[CryptoCurrencyDatapointRedisHandler]
 *      -> redis[v1:cr:info(deprecated), v3:cr:info:detail(new)]
 *      -> baseDataService
 *      -> assetService
 * )
 * <AUTHOR>
 * @date 2021/11/30
 */
@Data
public class CryptoCurrencyInfoDetailCacheV3VO {
    private Integer id;
    private String name;
    private String symbol;
    private String category;
    private String slug;
    private Integer isActive;
    private Integer isHidden;
    private Integer isListed;
    private Integer rank;
    private List<String> contractAddresses;
    private List<ContractInfoVO> contractInfo;
}
