package com.cmc.asset.model.contract.userpoint;

import com.cmc.data.common.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryPointSummaryRequestDTO extends BaseRequest {
    @ApiModelProperty(value = "userId", example = "60cabed7a6b91830b6b6a9a8", required = true)
    private String userId;

    @ApiModelProperty(value = "secretKey for internal service invoke", example = "5296b3b6-c683-44a1-9439-8ed27bc486b0")
    private String secretKey;
}
