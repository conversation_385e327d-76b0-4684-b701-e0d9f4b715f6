package com.cmc.asset.model.contract.portfolio.transaction;

import com.cmc.asset.model.common.PageDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/26 17:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionCryptosQueryRequest extends PageDTO {

    private String portfolioSourceId;

}
