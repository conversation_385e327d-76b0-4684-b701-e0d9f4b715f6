package com.cmc.asset.model.contract.userpoint;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description UserSettingDTO
 * @date 2021/8/24 下午4:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "notification setting response")
public class SettingDTO {

    /**
     * topic id
     */
    @ApiModelProperty(value = "topic id")
    private Integer topicId;

    /**
     * topic name
     */
    @ApiModelProperty(value = "topic name")
    private String name;

    /**
     * topic category
     */
    @ApiModelProperty(value = "topic category")
    private String category;

    /**
     * topic description
     */
    @ApiModelProperty(value = "topic description")
    private String description;

    /**
     * is active
     */
    @ApiModelProperty(value = "if topic is active")
    private Boolean isActive;

    /**
     * is active
     */
    @ApiModelProperty(value = "user operate time")
    private Long operateTime;

}
