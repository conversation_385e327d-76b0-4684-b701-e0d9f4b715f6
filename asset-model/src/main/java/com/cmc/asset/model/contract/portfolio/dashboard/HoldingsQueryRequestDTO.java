package com.cmc.asset.model.contract.portfolio.dashboard;

import com.cmc.asset.model.common.PageDTO;
import javax.annotation.Nullable;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * HoldingsQueryRequestDTO
 *
 * @author: lunke.z
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HoldingsQueryRequestDTO extends PageDTO {

    private Integer convertFiatId;

    private Integer convertCryptoId;

    private String sortBy;

    private String sortType;

    private Integer assetLimit;

    /**
     * @see com.cmc.asset.model.enums.PortfolioCryptoVerificationStatus
     */
    @Nullable
    @ApiModelProperty("crypto verification status: 0: all crypto, 1: verified, 2: unverified")
    private Integer cryptoVerificationStatus;
}
