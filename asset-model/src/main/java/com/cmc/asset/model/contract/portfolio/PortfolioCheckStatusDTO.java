package com.cmc.asset.model.contract.portfolio;

import com.cmc.data.common.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioCheckStatusDTO extends BaseRequest {
    @NotNull
    private String portfolioSourceId;
}
