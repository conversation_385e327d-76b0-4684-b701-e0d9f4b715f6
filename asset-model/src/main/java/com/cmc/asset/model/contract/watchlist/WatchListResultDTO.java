package com.cmc.asset.model.contract.watchlist;

import com.cmc.asset.model.contract.crypto.CryptoAuditResultDTO;
import com.cmc.asset.model.contract.crypto.CryptoPlatformDTO;
import com.cmc.asset.model.contract.crypto.CryptoPriceChangeDTO;
import com.cmc.asset.model.enums.FollowTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:43
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchListResultDTO {

    /**
     * Watch Lists created by the user
     */
    private List<WatchListDTO> watchLists;

    private WatchListType watchListType;

    /**
     * watchlist can be added again，
     * if watchList size greater than 100（configuration），not allowed to add
     */
    private Boolean addFlag;

    public WatchListResultDTO(List<WatchListDTO> watchLists, WatchListType watchListType) {
        this.watchLists = watchLists;
        this.watchListType = watchListType;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WatchListDTO {

        @JsonIgnore
        private String userId;

        private String creator;

        private Boolean verifiedByCmc;

        private String watchListId;

        private Integer order;

        private String name;

        private String description;

        private Long followerSize;

        private Integer cryptoSize;

        private Integer dexPairSize;

        private Integer dexTokenSize;

        private Integer assetSize;

        private Boolean shared;

        private Boolean main;

        private WatchListType type;

        private List<CryptoCurrencyDTO> cryptoCurrencies;

        private List<ExchangeDTO> exchanges;

        private List<MarketPairDTO> marketPairs;

        private List<DexTokenDTO> dexTokens;

        private List<Long> dexPairIds;

        private List<String> dexTokenUniIds;

        private String createdTime;

        private String updatedTime;

        private FollowTypeEnum followType;

        private String sharedImageUrl;

        private String contentType;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CryptoCurrencyDTO {

        private Integer id;

        private String name;

        private String symbol;

        private String slug;

        private String status;

        /**
         * the cmc's rank
         */
        private Integer rank;

        private Integer wrappedStakedMcRank;

        private Object tags;

        private Integer marketPairCount;

        private BigDecimal circulatingSupply;

        private BigDecimal selfReportedCirculatingSupply;

        private BigDecimal totalSupply;

        private BigDecimal maxSupply;

        /**
         * date time
         */
        private String lastUpdated;

        /**
         * date time
         */
        private String dateAdded;

        private List<CryptoPriceChangeDTO> quotes;

        /**
         * Metadata about the parent cryptocurrency platform this cryptocurrency belongs to if it is a token, otherwise null.
         */
        private CryptoPlatformDTO platform;

        /**
         * The category for this cryptocurrency.
         */
        private String category;

        private BigDecimal ath;

        private BigDecimal atl;

        private BigDecimal high24h;

        private BigDecimal low24h;

        private Integer isActive;

        private Boolean isAudited;

        private List<CryptoAuditResultDTO> auditInfoList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ExchangeDTO {

        private Integer id;

        private String name;

        private String slug;

        private String status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MarketPairDTO {

        private Integer id;

       // private String marketPair;

        private Boolean status;
    }

    public enum WatchListType {
        NONE,
        ORDINARY,
        FOLLOWED;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DexTokenDTO {
        @JsonProperty("n")
        private String name;

        @JsonProperty("sym")
        private String symbol;

        @JsonProperty("addr")
        private String address;

        @JsonProperty("pdex")
        private String platformDexerName;

        @JsonProperty("pcid")
        private Integer platformCryptoId;

        @JsonProperty("plt")
        private String platform;

        @JsonProperty("pid")
        private Integer platformId;

        @JsonProperty("dec")
        private Integer decimals;

        @JsonProperty("crt")
        private String creator;

        @JsonProperty("own")
        private String owner;

        @JsonProperty("rnc")
        private String renounced;

        @JsonProperty("web")
        private String website;

        @JsonProperty("tw")
        private String twitter;

        @JsonProperty("tg")
        private String telegram;

        @JsonProperty("lg")
        private String logo;

        @JsonProperty("pubAt")
        private Long publishAt;

        @JsonProperty("lchAt")
        private Long launchedAt;

        @JsonProperty("fdv")
        private String fdv;

        @JsonProperty("mcap")
        private String marketcap;

        @JsonProperty("ts")
        private String totalSupply;

        @JsonProperty("bs")
        private String burnSupply;

        @JsonProperty("cs")
        private String circulatingSupply;

        @JsonProperty("liqUsd")
        private String liquidityUsd;

        @JsonProperty("liq")
        private String liquidity;

        @JsonProperty("hld")
        private Long holders;

        @JsonProperty("p")
        private String priceUsd;

        @JsonProperty("bcr")
        private Double bondingCurveRatio;

        @JsonProperty("sts")
        private List<DexTokenStatsDTO> stats;

        @JsonProperty("tsrc")
        private String poolSource;

        @JsonProperty("rl")
        private String riskLevel;


        @JsonProperty("pt")
        private Long priceTime;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DexTokenStatsDTO {
        @JsonProperty("tp")
        private String type;

        @JsonProperty("vu")
        private String volume;

        @JsonProperty("txs")
        private Long txs;

        @JsonProperty("nb")
        private Long numBuy;
        @JsonProperty("ns")
        private Long numSell;
        @JsonProperty("bvu")
        private String buyVolume;
        @JsonProperty("svu")
        private String sellVolume;
        @JsonProperty("but")
        private Long buyers;
        @JsonProperty("sut")
        private Long sellers;

        @JsonProperty("pc")
        private Float priceChangeRate;

        @JsonProperty("ut")
        private Long uniqueTraders;
    }
}