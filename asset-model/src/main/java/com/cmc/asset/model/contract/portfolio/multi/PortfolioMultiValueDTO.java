package com.cmc.asset.model.contract.portfolio.multi;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PortfolioMultiValueDTO
 * <AUTHOR> ricky.x
 * @date : 2023/2/20 下午1:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioMultiValueDTO {
    /**
     * 当天的portfolio multi总金额
     */
    private BigDecimal totalValue;

    /**
     * 昨天的portfolio multi总金额
     */
    private BigDecimal yesterdayTotalValue;
}
