package com.cmc.asset.model.contract.watchlist;

import com.cmc.data.common.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:42
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryWatchListParamDTO  extends BaseRequest {

    private String watchListId;

    /**
     * the cryptocurrenies of page
     */
    private Integer start;

    /**
     * the cryptocurrenies of page
     */
    private Integer limit;

    /**
     *  ORDINARY,FOLLOWED; default ORDINARY
     */
    private String watchListType;

    /**
     * 1 crypto 2 exchange, default 1.
     */
    private Integer aux;

    private String convertIds;

    private String cryptoAux;

    private Boolean isMain;

    private Boolean containDexToken;

    /**
     * resource type
     */
    private String resourceType;

    /**
     * resource id, 当resourceType为dexToken时,resourceId展示为 16:6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN 如下规则
     */
    private String resourceId;

}
