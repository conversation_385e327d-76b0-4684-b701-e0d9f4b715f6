package com.cmc.asset.model.contract.dexer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DataHubTokenDetailDTO {
    @JsonProperty("n")
    private String name;

    @JsonProperty("sym")
    private String symbol;

    @JsonProperty("addr")
    private String address;

    @JsonProperty("plt")
    private String platform;

    @JsonProperty("pid")
    private Integer platformId;

    @JsonProperty("dec")
    private Integer decimals;

    @JsonProperty("crt")
    private String creator;

    @JsonProperty("own")
    private String owner;

    @JsonProperty("rnc")
    private String renounced;

    @JsonProperty("web")
    private String website;

    @JsonProperty("tw")
    private String twitter;

    @JsonProperty("tg")
    private String telegram;

    @JsonProperty("lg")
    private String logo;

    @JsonProperty("pubAt")
    private Long publishAt;

    @JsonProperty("lchAt")
    private Long launchedAt;

    @JsonProperty("fdv")
    private String fdv;

    @JsonProperty("mcap")
    private String marketcap;

    @JsonProperty("ts")
    private String totalSupply;

    @JsonProperty("bs")
    private String burnSupply;

    @JsonProperty("cs")
    private String circulatingSupply;

    @JsonProperty("liqUsd")
    private String liquidityUsd;

    @JsonProperty("liq")
    private String liquidity;

    @JsonProperty("hld")
    private Long holders;

    @JsonProperty("p")
    private String priceUsd;

    @JsonProperty("bcr")
    private Double bondingCurveRatio;

    @JsonProperty("tsrc")
    private String poolSource;

    @JsonProperty("rl")
    private Integer riskLevel;
}