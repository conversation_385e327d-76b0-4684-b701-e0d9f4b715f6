package com.cmc.asset.model.enums;

/**
 * <AUTHOR>
 * @date 2021/12/01 15:55
 * @description copy from data-api ListingStatus
 */
public enum ListingStatus {

    UNTRACKED("untracked", "untracked"),
    ACTIVE("active", "active"),
    INACTIVE("inactive", "inactive");

    private final String code;

    private final String desc;

    ListingStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
