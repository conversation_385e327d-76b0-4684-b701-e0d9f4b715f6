package com.cmc.asset.model.contract.task;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024-08-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalletSyncJobParamDTO {

    private List<String> walletAddressList;

    private Integer syncInterval;
}
