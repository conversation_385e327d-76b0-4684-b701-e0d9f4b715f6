package com.cmc.asset.model.contract.watchlist;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/24 11:37
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchAssetInfoRespDTO {

    private List<WatchListResultDTO.DexTokenDTO> dexTokenInfos;

}
