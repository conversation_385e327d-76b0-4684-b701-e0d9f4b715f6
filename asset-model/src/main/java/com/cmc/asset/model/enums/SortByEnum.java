package com.cmc.asset.model.enums;

import com.cmc.framework.utils.StringUtils;
import lombok.Getter;

/**
 * 排序枚举
 * <AUTHOR> ricky.x
 * @date : 2023/8/2 15:34
 */
@Getter
public enum SortByEnum {
    /**
     * holdings排序
     */
    HOLDINGS("holdings"),

    /**
     * price排序
     */
    PRICE("price");

    private final String code;

    public String getCode() {
        return code;
    }


    SortByEnum(String code) {
        this.code = code;
    }

    public static SortByEnum getHoldingSortByCode(String code) {
        if(StringUtils.isEmpty(code)){
            return null;
        }
        SortByEnum sortByEnum = null;
        for (SortByEnum dayEnum : SortByEnum.values()) {
            if (dayEnum.getCode().equals(code.toLowerCase())) {
                sortByEnum = dayEnum;
            }
        }
        if(sortByEnum == SortByEnum.HOLDINGS || sortByEnum == SortByEnum.PRICE){
            return sortByEnum;

        }
        return null;
    }
}
