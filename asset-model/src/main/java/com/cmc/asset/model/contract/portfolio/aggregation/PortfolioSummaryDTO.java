package com.cmc.asset.model.contract.portfolio.aggregation;

import com.cmc.asset.model.contract.portfolio.PortfolioSupportChainDTO;
import com.cmc.asset.model.enums.PortfolioWalletDataFlagEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/04/18
 * @description
 **/
@Data
public class PortfolioSummaryDTO<T> {
    private String portfolioSourceId;
    private String portfolioName;
    private String bgColor;
    private Integer sortIndex;
    private Boolean isMain;
    private Boolean ownWallet;
    private String portfolioType;
    private String portfolioAvatar;

    private BigDecimal cryptoUnitPrice;
    private BigDecimal fiatUnitPrice;
    private Boolean jobFlag;

    /**
     * 数据同步标志
     * @see PortfolioWalletDataFlagEnum
     */
    private Integer dataFlag;
    private Boolean isCalculating;
    private Map<Integer,String> supportChains;
    /**
     * app使用map比较难映射,因此从map<Integer,String>改为List<PortfolioSupportChainDTO>
     */
    private List<PortfolioSupportChainDTO> supportChainList;
    private String walletAddress;
    private String lastUpdateTime;
    private List<T> list;
}
