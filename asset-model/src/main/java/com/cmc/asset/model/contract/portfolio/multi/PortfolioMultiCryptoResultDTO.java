package com.cmc.asset.model.contract.portfolio.multi;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/6/16 10:35:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioMultiCryptoResultDTO {
    private String portfolioSourceId;
    private String portfolioName;
    private String portfolioType;
    private String bgColor;
    private BigDecimal totalAmount;
    private BigDecimal totalHoldings;
    private BigDecimal cryptoUnitPrice;
    private BigDecimal fiatUnitPrice;
    @JsonIgnore
    private Integer sortIndex;
}
