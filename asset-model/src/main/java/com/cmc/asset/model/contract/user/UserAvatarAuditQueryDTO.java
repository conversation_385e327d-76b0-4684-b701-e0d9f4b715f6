package com.cmc.asset.model.contract.user;

import com.cmc.data.common.BaseRequest;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName UserAvatarAuditQueryDTO.java
 * <AUTHOR>
 * @Date 2022/8/9 15:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserAvatarAuditQueryDTO extends BaseRequest {

    @NotNull
    private Long uid;
}
