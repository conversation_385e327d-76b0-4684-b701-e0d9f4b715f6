package com.cmc.asset.model.contract.portfolio.transaction;

import com.cmc.asset.model.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.util.List;

/**
 * PortfolioTransactionRequest
 * <AUTHOR> lunke.z
 * @date : 2023/10/26
 */
@Data
@NoArgsConstructor
public class PortfolioTransactionRequest extends PageDTO {

    @ApiModelProperty(value = "portfolioSourceId, null: all portfolio")
    private String portfolioSourceId;

    @ApiModelProperty(value = "type: buy|sell|transactionIn|transactionOut, null: all types")
    private List<String> transactionTypes;

    @ApiModelProperty(value = "crypto ids, null: all crypto")
    private List<Integer> cryptoIds;

    @ApiModelProperty(value = "type: transactionIn transactionOut")
    private String type;

    @ApiModelProperty(value = "crypto id")
    private Integer cryptoId;

    @ApiModelProperty(value = "chain id")
    private Integer chainId;

    @ApiModelProperty("crypto verification status: 0: all crypto, 1: verified, 2: unverified")
    @Nullable
    private Integer cryptoVerificationStatus;

    @ApiModelProperty(value = "cryptoUnit")
    private Integer cryptoUnit;

    @ApiModelProperty(value = "fiatUnit")
    private Integer fiatUnit;

}
