package com.cmc.asset.model.contract.watchlist;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询watchList基础信息
 * <AUTHOR> lunke.z
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryWatchListBasicV2Response {

    /**
     * Watch cryptoCurrencies
     */
    @ApiModelProperty(value = "watchLists.")
    private List<WatchListDTO> watchLists;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WatchListDTO {
        @ApiModelProperty("watchListId.")
        private String watchListId;
        @ApiModelProperty("name.")
        private String name;
        @ApiModelProperty("main. the default watchList")
        private Boolean main;
        @ApiModelProperty("contentType")
        private String contentType;
        @ApiModelProperty("cryptoCurrencies. ")
        private List<CryptoCurrencyDTO> cryptoCurrencies;
        private List<DexPairDTO> dexPairs;
        private List<DexTokenUniqueDTO> dexTokens;
        @ApiModelProperty("is param cryptoId in the cryptoCurrencies list. ")
        private Boolean isCryptoIn;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CryptoCurrencyDTO {

        private Integer id;

        private String name;

        private String symbol;

        private String slug;

        /**
         * the cmc's rank
         */
        @JsonIgnore
        private Integer rank;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DexPairDTO {

        private Long poolId;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DexTokenUniqueDTO {

        private String dexTokenUniqueId;

    }
}
