package com.cmc.asset.model.contract.portfolio.transaction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * the response body of query wallet transaction
 *
 * <AUTHOR> ricky.x
 * @date : 2023/1/27 下午3:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionDTO {

    private String id;

    @ApiModelProperty(value = "transactionType: transactionIn transactionOut")
    private String transactionType;

    @ApiModelProperty(value = "transactionTime: mills timestamp, Long type")
    private Long transactionTime;

    @ApiModelProperty(value = "cryptoType: 0: untracked crypto,1: listed crypto,2:dex token")
    private Integer cryptoType;

    @ApiModelProperty(value = "transaction amount")
    private BigDecimal amount;

    @ApiModelProperty(value = "price")
    private BigDecimal price;

    @ApiModelProperty(value = "crypto id")
    private Integer cryptoId;

    @ApiModelProperty(value = "crypto symbol")
    private String cryptoSymbol;

    @ApiModelProperty(value = "crypto slug")
    private String cryptoSlug;

    @ApiModelProperty(value = "crypto name")
    private String cryptoName;

    @ApiModelProperty(value = "crypto logoUrl")
    private String cryptoLogoUrl;

    @ApiModelProperty(value = "dex pair id")
    @Deprecated
    private Long pairId;

    @ApiModelProperty(value = "dex pair base contract address")
    private String contractAddress;

    @ApiModelProperty(value = "platformId")
    private Integer platformId;

    @ApiModelProperty(value = "input unit")
    private Integer inputUnit;

    @ApiModelProperty(value = "input unit symbol")
    private String inputUnitSymbol;

    @ApiModelProperty(value = "input fee")
    private BigDecimal inputFee;

    @ApiModelProperty(value = "input price")
    private BigDecimal inputPrice;

    @ApiModelProperty(value = "fee amount")
    private BigDecimal fee;

    @ApiModelProperty(value = "fee crypto symbol")
    private String feeCryptoSymbol;

    @ApiModelProperty(value = "notes")
    private String note;

    @ApiModelProperty(value = "chain id")
    @JsonIgnore
    private Integer chainId;

    @ApiModelProperty(value = "chain name")
    private String chainName;

    @ApiModelProperty(value = "from address")
    private List<String> fromAddressList;

    @ApiModelProperty(value = "to address")
    private List<String> toAddressList;

    @ApiModelProperty(value = "transaction hash")
    private String transactionHash;

    @ApiModelProperty(value = "block height")
    private String blockHeight;

    @ApiModelProperty(value = "hashUrl")
    private String hashUrl;

    @ApiModelProperty(value = "status")
    private String status;

    private String portfolioSourceId;

    private String portfolioSourceName;

    private String portfolioType;

    private String portfolioAvatar;

    private String portfolioBgColor;

}
