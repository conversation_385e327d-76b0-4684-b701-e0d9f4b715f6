package com.cmc.asset.model.contract.portfolio.aggregation;

import com.cmc.data.common.BaseRequest;
import java.util.List;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PortfolioLineChartQueryDTO
 *
 * <AUTHOR> evan.h
 * @date : 2023/4/5 下午9:34
 */
@Data
@NoArgsConstructor
public class PortfolioLineChartQueryDTO extends BaseRequest {
    @NotNull
    private List<Integer> cryptoIds;
    /**
     * 0 代表 all
     */
    @NotNull
    private Integer days;

    private String portfolioSourceId;

    /**
     * wallet 类型会传该值
     */
    private Integer chainId;
    @ApiModelProperty(value = "allocation type: dashboard portfolio")
    @NotNull
    private String moduleType;

    /**
     * @see com.cmc.asset.model.enums.PortfolioCryptoVerificationStatus
     */
    @Nullable
    @ApiModelProperty("crypto verification status: 0: all crypto, 1: verified, 2: unverified")
    private Integer cryptoVerificationStatus;

}
