package com.cmc.asset.model.contract.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/8/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserAvatarResultDTO {
    private String avatarId;
    private Boolean selected;
    private Boolean nft;
    private Boolean newest;

    private AvatarDTO avatar;

    /**
     * default avatar color tag   0-grey 1-colorful
     */
    private Integer defaultColor;
}
