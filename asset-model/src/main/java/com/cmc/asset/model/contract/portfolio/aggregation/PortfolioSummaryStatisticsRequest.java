package com.cmc.asset.model.contract.portfolio.aggregation;

import com.cmc.data.common.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Nullable;

/**
 * PortfolioQuerySummaryRequest
 * <AUTHOR> ricky.x
 * @date : 2023/2/11 下午2:25
 */
@Data
public class PortfolioSummaryStatisticsRequest extends BaseRequest {
    private Integer convertFiatId;

    private Integer convertCryptoId;
    private String portfolioSourceId;
    /**
     * 根据chainId来统计不同的summary,此字段暂时只有app用
     */
    private Integer chainId;
    @ApiModelProperty(value = "1h,24h,7d,30d")
    private  String changePeriod;

    @ApiModelProperty("crypto verification status: 0: all crypto, 1: verified, 2: unverified")
    @Nullable
    private Integer cryptoVerificationStatus;
}
