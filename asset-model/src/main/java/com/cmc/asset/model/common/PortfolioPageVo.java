package com.cmc.asset.model.common;

import com.cmc.asset.model.contract.portfolio.PortfolioSupportChainDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/12
 */
@Data
public class PortfolioPageVo<T> extends CommonPageVO {
    private String portfolioSourceId;
    private String portfolioName;
    private String bgColor;
    private Integer sortIndex;
    private Boolean isMain;
    private Boolean ownWallet;
    private String portfolioType;
    private String portfolioAvatar;

    private BigDecimal cryptoUnitPrice;
    private BigDecimal fiatUnitPrice;
    private Boolean jobFlag;
    private Integer dataFlag;
    private Map<Integer,String> supportChains;
    /**
     * app使用map比较难映射,因此从map<Integer,String>改为List<PortfolioSupportChainDTO>
     */
    private List<PortfolioSupportChainDTO> supportChainList;
    private String walletAddress;
    private Long lastUpdateTime;
}
