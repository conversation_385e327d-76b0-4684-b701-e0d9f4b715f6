package com.cmc.asset.model.enums;

import org.apache.commons.lang3.StringUtils;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/10/19 10:15
 * @description
 */
@Getter
public enum ResourceTypeEnum {
    EXCHANGE("exchanges", null, "My First Exchange Watchlist", "My First Exchange Watchlist"),
    CRYPTO("cryptos", null, "My First Coin Watchlist", "My First Watchlist"),
    MARKETPAIR("marketPairs", null, "My First Market Pair Watchlist", "My First Market Pair Watchlist"),

    /**
     * dex pool subscribe
     */
    DEX_PAIR("dexPairs", "dexPair", "My First Pair Watchlist","My First Watchlist"),

    /**
     * dex token subscribe
     */
    DEX_TOKEN("dexTokens", "dexToken", "My First Pair Watchlist","My First Watchlist");

    private final String fieldName;


    private final String typeName;

    private final String watchlistName;

    private final String watchlistNameV2;

    ResourceTypeEnum(String fieldName, String typeName, String watchlistName, String watchlistNameV2){
        this.fieldName = fieldName;
        this.typeName = typeName;
        this.watchlistName = watchlistName;
        this.watchlistNameV2 = watchlistNameV2;
    }

    public static ResourceTypeEnum findByCode(String code) {

        if (StringUtils.isEmpty(code)) {
            return null;
        }
        code = code.trim().toLowerCase();
        for (ResourceTypeEnum b : ResourceTypeEnum.values()) {

            if (StringUtils.equalsIgnoreCase(b.name(), code)) {
                return b;
            }
        }
        return null;
    }

    public static ResourceTypeEnum findByType(String typeName) {

        if (StringUtils.isEmpty(typeName)) {
            return CRYPTO;
        }
        for (ResourceTypeEnum b : ResourceTypeEnum.values()) {

            if (StringUtils.equalsIgnoreCase(b.getTypeName(), typeName)) {
                return b;
            }
        }
        return null;
    }
}
