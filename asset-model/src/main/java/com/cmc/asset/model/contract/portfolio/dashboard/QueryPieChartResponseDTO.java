package com.cmc.asset.model.contract.portfolio.dashboard;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QueryStatisticsResponseDTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryPieChartResponseDTO {

    private List<PieChartDTO> token;
    private List<PieChartDTO> chain;
    private List<PieChartDTO> portfolio;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PieChartDTO {
        private String icon;
        private String bgColor;
        private String name;
        private BigDecimal percent;
        private BigDecimal value;
    }

}
