package com.cmc.asset.model.contract.portfolio;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * portfolio support chain info
 * <AUTHOR> ricky.x
 * @date : 2023/1/27 下午3:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PortfolioSupportChainDTO {

    @ApiModelProperty(value = "chain id")
    private Integer chainId;

    @ApiModelProperty(value = "chain logo id")
    private Integer chainLogoId;

    @ApiModelProperty(value = "chain full name")
    private String chainName;

    @ApiModelProperty(value = "chain short name")
    private String chainShortName;

    @ApiModelProperty(value = "chain support transaction")
    private Boolean supportTransaction;

}
