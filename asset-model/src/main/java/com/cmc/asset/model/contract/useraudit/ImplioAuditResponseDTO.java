package com.cmc.asset.model.contract.useraudit;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/11/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImplioAuditResponseDTO {
    private String batchId;
    private List<ImplioResponseAcceptedDTO> accepted;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ImplioResponseAcceptedDTO {
        private String id;
        private String taskId;
    }
}
