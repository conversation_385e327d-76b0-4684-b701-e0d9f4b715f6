package com.cmc.asset.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:44
 * @description
 */
public enum FollowTypeEnum {
    NONE, FOLLOW, UNFOLLOW, ORDINARY;

    public static FollowTypeEnum findByCode(String code) {

        if (StringUtils.isEmpty(code)) {
            return null;
        }
        code = code.trim().toLowerCase();
        for (FollowTypeEnum b : FollowTypeEnum.values()) {

            if (StringUtils.equalsIgnoreCase(b.name(), code)) {
                return b;
            }
        }
        return null;
    }
}
