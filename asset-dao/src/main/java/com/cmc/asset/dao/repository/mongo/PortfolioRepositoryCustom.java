package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioEntity;
import com.cmc.asset.model.contract.portfolio.transaction.PortfolioTransactionRequest;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2023/11/2 09:56
 */
public interface PortfolioRepositoryCustom {

    Flux<PortfolioEntity> findTransaction(PortfolioTransactionRequest requestDto);

    Mono<Long> countTransaction(PortfolioTransactionRequest requestDto);

}
