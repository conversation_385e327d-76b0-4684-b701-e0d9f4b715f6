package com.cmc.asset.dao.entity.portfolio;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PortfolioThirdPartyAssetHistoryAggregatedEntity {

    private Date syncTime;
    private Integer cryptoId;
    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal totalBalance;
    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal totalValue;

}
