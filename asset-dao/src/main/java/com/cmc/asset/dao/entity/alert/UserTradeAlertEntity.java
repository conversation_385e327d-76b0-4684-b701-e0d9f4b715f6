package com.cmc.asset.dao.entity.alert;

import java.util.Date;
import java.util.Objects;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.domain.Persistable;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_trade_alert")
public class UserTradeAlertEntity implements Persistable<ObjectId> {

    @Id
    private ObjectId id;

    /**
     * userid
     */
    private String userId;

    /**
     * uid
     */
    private Long uid;

    /**
     * business id
     */
    private String bizId;

    /**
     * alert type: pair for pair alerts
     */
    private String type;

    /**
     * alert threshold
     */
    private int threshold;

    /**
     * extra info
     */
    private String extraInfo;

    private Date createdTime;

    private Date updatedTime;

    @Override
    public boolean isNew() {
        return Objects.isNull(id);
    }
}
