package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/6/16 11:07:42
 */
@Repository
public interface PortfolioMultiRepository extends ReactiveMongoRepository<PortfolioMultiEntity, ObjectId>,
    ReactiveCrudRepository<PortfolioMultiEntity, ObjectId>, PortfolioMultiRepositoryCustom {

    Flux<PortfolioMultiEntity> findAllByUserId(ObjectId userId);

    Mono<PortfolioMultiEntity> findFirstByUserIdOrderByTimeUpdatedDesc(ObjectId userId);

    Mono<PortfolioMultiEntity> findFirstByThirdPartyId(ObjectId thirdPartyId);

    Flux<PortfolioMultiEntity> findByUserIdAndPortfolioTypeIn(ObjectId userId, List<String> portfolioTypes);

    Mono<PortfolioMultiEntity> findFirstByUserIdAndPortfolioSourceId(ObjectId userId, String sourceId);

    Flux<PortfolioMultiEntity> findByUserIdAndPortfolioSourceIdIn(ObjectId userId, Set<String> sourceId);

    Mono<PortfolioMultiEntity> findFirstByIdAndUserId(ObjectId id, ObjectId userId);

    Mono<PortfolioMultiEntity> findFirstByUserIdAndThirdPartyId(ObjectId userId, ObjectId thirdPartyId);

    Mono<PortfolioMultiEntity> findFirstByUserIdAndState(ObjectId userId, String state);

    Mono<PortfolioMultiEntity> findFirstByUserIdAndIsMain(ObjectId userId, boolean isMain);

    Mono<Long> countByUserId(ObjectId userId);

}
