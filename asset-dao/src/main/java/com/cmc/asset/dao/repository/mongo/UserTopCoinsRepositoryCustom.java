package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.UserTopCoinsEntity;
import java.util.List;
import reactor.core.publisher.Mono;

public interface UserTopCoinsRepositoryCustom {

    /**
     * update or insert entity
     *
     * @param userId - userId
     * @param portfolioCoins - portfolioCoins
     * @param watchlistCoins - watchlistCoins
     * @return entity
     */
    Mono<UserTopCoinsEntity> upsert(String userId, List<Integer> portfolioCoins, List<Integer> watchlistCoins, Integer coinsLimit);
}
