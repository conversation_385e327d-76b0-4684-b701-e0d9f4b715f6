package com.cmc.asset.dao.entity.portfolio;

import com.cmc.asset.dao.BaseMongoEntity;
import lombok.*;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.domain.Persistable;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Entity for daily third-party asset history records
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "portfolio_third_party_asset_history_1d")
public class PortfolioThirdPartyAssetHistory1dEntity extends BaseMongoEntity<ObjectId> implements Persistable<ObjectId> {

    @Id
    private ObjectId id;

    private ObjectId thirdPartyId;

    private String openId;

    private Integer source;

    private Integer cryptoId;

    private String cryptoSymbol;

    private String cryptoName;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal balance;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal value;

    private Date syncTime;
}
