package com.cmc.asset.dao.entity.avatar;

import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/8/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "system_avatars")
public class SystemAvatarsEntity {
    @Id
    private ObjectId id;
    private String name;
    private Integer type;
    private Boolean active;
    private Integer sort;
    private Date createTime;
    private Date updateTime;
}
