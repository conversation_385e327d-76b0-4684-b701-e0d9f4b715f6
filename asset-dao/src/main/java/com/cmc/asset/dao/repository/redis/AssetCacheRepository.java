package com.cmc.asset.dao.repository.redis;

import com.cmc.framework.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * Cache specific repository
 *
 * <AUTHOR>
 * @date 2020/12/9
 */
@Repository
public class AssetCacheRepository{


    @Autowired
    @Qualifier("assetRedisTemplate")
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;
    /**
     * Cache data in string
     */
    public Mono<Boolean> cacheInString(String key, String value, Duration duration) {
       return assetRedisTemplate.opsForValue().set(key,value,duration);
    }

    public Mono<Boolean> setIfAbsent(String key, String value, Duration duration) {
       return assetRedisTemplate.opsForValue().setIfAbsent(key,value,duration);
    }

    /**
     * Delete the cache key.
     */
    public Mono<Long> delete(String key) {
        return assetRedisTemplate.delete(key);
    }

    /**
     * getInString
     */
    public Mono<String> get(String key){
        return assetRedisTemplate.opsForValue().get(key);
    }

    /**
     * set for long
     */
    public Mono<Long> addOpsSet(String key,String field){
        return assetRedisTemplate.opsForSet().add(key,field);
    }

    public Mono<Boolean> expire(String key, Duration timeout) {
        return assetRedisTemplate.expire(key, timeout);
    }

    /**
     * get all
     */
    public Mono<List<String>> getOpsSet(String key){
        return assetRedisTemplate.opsForSet().members(key).collectList().defaultIfEmpty(new ArrayList<>());
    }

    public Mono<String> wrapperCache(String cacheKey, Supplier<Mono<String>> dataSupplier, Duration duration) {
        return assetRedisTemplate.opsForValue()
                .get(cacheKey)
                .filter(StringUtils::isNotBlank)
                .switchIfEmpty(Mono.defer(dataSupplier)
                        .subscribeOn(Schedulers.boundedElastic())
                        .doOnNext(data -> assetRedisTemplate.opsForValue()
                                .set(cacheKey, data, duration)
                                .subscribe()));
    }
}
