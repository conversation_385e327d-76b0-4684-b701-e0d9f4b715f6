package com.cmc.asset.dao.repository.redis;

import com.cmc.asset.dao.entity.PricePredictionUserRankEntity;
import com.cmc.asset.dao.repository.redis.template.DataCacheRedisTemplate;
import com.cmc.asset.model.common.RedisConstants;
import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/8/3 14:09:49
 */
@Repository
public class PricePredictionUserRankRepository extends BaseRedisRepository<DataCacheRedisTemplate> {

    public Mono<PricePredictionUserRankEntity> getUserRank(String userId) {
        return reactiveRedisTemplate.opsForHash().get(RedisConstants.KEY_PRICE_PREDICTION_LEADERBOARD_TOP_RANK, userId)
            .map(v -> JSON.parseObject((String) v, PricePredictionUserRankEntity.class));
    }

}
