package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.PortfolioWalletEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;

/**
 * @Description
 * <AUTHOR> Z
 * @CreateTime 2024-08-02
 */
@Repository
public interface PortfolioWalletRepository extends ReactiveMongoRepository<PortfolioWalletEntity, ObjectId>,
    ReactiveCrudRepository<PortfolioWalletEntity, ObjectId>, PortfolioWalletRepositoryCustom {
}
