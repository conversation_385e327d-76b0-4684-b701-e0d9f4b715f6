package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyAssetHistoryAggregatedEntity;
import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface PortfolioThirdPartyAssetRepositoryCustom {

    Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity> aggregation(ObjectId thirdPartyId, Integer cryptoId);

    Mono<DeleteResult> deleteAllByThirdPartyId(ObjectId thirdPartyId);
}
