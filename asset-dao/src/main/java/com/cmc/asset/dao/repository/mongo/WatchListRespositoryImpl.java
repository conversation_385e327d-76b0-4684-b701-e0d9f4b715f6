package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.UpdateUtils;
import com.cmc.asset.dao.entity.WatchListCacheEntity;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.model.enums.ErrorCodeEnums;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.data.common.utils.ExtUtils;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.ReactiveMongoOperations;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 2020/10/19 20:11
 * @description
 */
@Slf4j
@Repository
public class WatchListRespositoryImpl {

    public static final String MY_FIRST_WATCHLIST = "My First Coin Watchlist";


    public static final String MY_FIRST_WATCHLIST_COMBINE = "My First Watchlist";

    @Value("${cmc.asset.popular-watchlist-size:1000}")
    private Long popularWatchListSize;

    @Value("${cmc.asset.popular-watchlist.ingore-type:true}")
    private Boolean saveIgnoreType;

    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Autowired
    private WatchListRepository watchListRepository;

    public WatchListRepository getWatchListRepository() {
        return watchListRepository;
    }

    /**
     * @param watchListEntity
     * @param isAdded
     * @return
     */
    @Nullable
    public Mono<WatchListEntity> followResources(WatchListEntity watchListEntity, boolean isAdded, boolean isAllReplace ,boolean isNewDocument) {

        Criteria criteria = Criteria.where("userId").is(watchListEntity.getUserId()).and("id").is(watchListEntity.getId());
        Update update = new Update();
        if (CollectionUtils.isNotEmpty(watchListEntity.getCryptos())) {
            if (isAllReplace) {
                update = update.set("cryptos", watchListEntity.getCryptos().toArray());
            } else {
                update = isAdded ? update.addToSet("cryptos").each(watchListEntity.getCryptos().toArray()) :
                        new Update().pullAll("cryptos", watchListEntity.getCryptos().toArray());
            }
        }
        if (CollectionUtils.isNotEmpty(watchListEntity.getExchanges())) {
            if (isAllReplace) {
                update = update.set("exchanges", watchListEntity.getExchanges().toArray());
            } else {
                update = isAdded ? update.addToSet("exchanges").each(watchListEntity.getExchanges().toArray()) :
                        update.pullAll("exchanges", watchListEntity.getExchanges().toArray());
            }
        }
        if (CollectionUtils.isNotEmpty(watchListEntity.getMarketPairs())) {
            if (isAllReplace) {
                update = update.set("marketPairs", watchListEntity.getMarketPairs().toArray());
            } else {
                update = isAdded ? update.addToSet("marketPairs").each(watchListEntity.getMarketPairs().toArray()) :
                        update.pullAll("marketPairs", watchListEntity.getMarketPairs().toArray());
            }
        }
        if (watchListEntity.getShared() != null) {
            update = update.set("shared", watchListEntity.getShared());
        }
        update = update.setOnInsert("name", MY_FIRST_WATCHLIST_COMBINE);
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return reactiveMongoTemplate.findAndModify(Query.query(criteria),
                update,
                FindAndModifyOptions.options().upsert(true).returnNew(isNewDocument),
                WatchListEntity.class);
    }

    public Mono<WatchListEntity> upsertWatchlistWithType(WatchListEntity watchListEntity, ResourceTypeEnum type) {
        Criteria criteria = Criteria.where("userId").is(watchListEntity.getUserId());
        if (watchListEntity.getId() != null) {
            criteria = criteria.and("id").is(watchListEntity.getId());
            return reactiveMongoTemplate.findOne(Query.query(criteria), WatchListEntity.class);
        }else {
            criteria = criteria.and("actived").is(true)
                .and("type").is(type.getTypeName())
                .and("main").is(true);
            Update update = new Update();
            update = update.setOnInsert("name", type.getWatchlistName());
            update = update.setOnInsert("main", true);
            UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
            return reactiveMongoTemplate
                .findAndModify(Query.query(criteria), update, FindAndModifyOptions.options().upsert(true).returnNew(true),
                    WatchListEntity.class);
        }
    }

    public Mono<WatchListEntity> followResourcesByType(WatchListEntity watchListEntity,
        ResourceTypeEnum resourceTypeEnum, Object[] resourceArray, boolean isAdded, boolean isAllReplace) {
        Criteria criteria = followResourceCriteriaWithTypeV2(watchListEntity);
        Update update = new Update();
        String filedName = ResourceTypeEnum.DEX_PAIR.getFieldName();
        if (isAllReplace) {
            update = update.set(filedName, resourceArray);
        } else {
            update = isAdded ? update.addToSet(filedName).each(resourceArray) :
                new Update().pullAll(filedName, resourceArray);
        }
        if (watchListEntity.getShared() != null) {
            update = update.set("shared", watchListEntity.getShared());
        }
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return reactiveMongoTemplate.findAndModify(Query.query(criteria), update,
            FindAndModifyOptions.options().upsert(true).returnNew(true), WatchListEntity.class);
    }


    public Mono<WatchListEntity> addDexPairAndToken(WatchListEntity watchListEntity, Object[] dexPair, Object[] dexToken, boolean isAdded,
                                                    boolean isAllReplace) {
        Criteria criteria = followResourceCriteriaWithTypeV2(watchListEntity);
        Update update = new Update();
        String dexPairName = ResourceTypeEnum.DEX_PAIR.getFieldName();
        String dexTokenName = ResourceTypeEnum.DEX_TOKEN.getFieldName();
        if (isAllReplace) {
            update = update.set(dexPairName, dexPair);
            update = update.set(dexTokenName, dexToken);
        } else {
            update = isAdded ? update.addToSet(dexPairName).each(dexPair) :
                    new Update().pullAll(dexPairName, dexPair);
            if(dexToken.length != 0){
                update = isAdded ? update.push(dexTokenName).each(dexToken) :
                        update.pullAll(dexTokenName, dexToken);
            }
        }
        if (watchListEntity.getShared() != null) {
            update = update.set("shared", watchListEntity.getShared());
        }
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return reactiveMongoTemplate.findAndModify(Query.query(criteria), update,
                FindAndModifyOptions.options().upsert(true).returnNew(true), WatchListEntity.class);
    }
    public Mono<WatchListEntity> addDexSource(WatchListEntity watchListEntity,
                                                       ResourceTypeEnum resourceTypeEnum, Object[] resourceArray, boolean isAdded, boolean isAllReplace) {
        Criteria criteria = followResourceCriteriaWithTypeV2(watchListEntity);
        Update update = new Update();
        String filedName = resourceTypeEnum.getFieldName();
        if (isAllReplace) {
            update = update.set(filedName, resourceArray);
        } else {
            update = isAdded ? update.push(filedName).each(resourceArray) :
                    new Update().pullAll(filedName, resourceArray);
        }
        if (watchListEntity.getShared() != null) {
            update = update.set("shared", watchListEntity.getShared());
        }
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return reactiveMongoTemplate.findAndModify(Query.query(criteria), update,
                FindAndModifyOptions.options().upsert(true).returnNew(true), WatchListEntity.class);
    }
    public Mono<Boolean> existsMainWatchlist(ResourceTypeEnum resourceTypeEnum, String userId) {
        Criteria criteria =
            Criteria.where("userId").is(userId)
                .and("type").is(Optional.ofNullable(resourceTypeEnum).map(ResourceTypeEnum::getTypeName)
                    .orElse(null))
                .and("main").is(true)
                .and("actived").is(true);
        return reactiveMongoTemplate.exists(Query.query(criteria), WatchListEntity.class);
    }

    /**
     * Initialize MainWatchList at login
     *
     * @param watchListEntity the main watchlist
     * @return
     */
    public Mono<WatchListEntity> doInitMainWatchlist(WatchListEntity watchListEntity) {
        Criteria criteria = followResourceCriteria(watchListEntity);
        return reactiveMongoTemplate.findOne(Query.query(criteria), WatchListEntity.class)
                .map(existingEntity -> updateModify(existingEntity, watchListEntity))
                .defaultIfEmpty(updateForFindAndModify(watchListEntity))
                .flatMap(update -> reactiveMongoTemplate.findAndModify(Query.query(criteria),
                        update,
                        FindAndModifyOptions.options().upsert(true).returnNew(true),
                        WatchListEntity.class));
    }

    private Update updateForFindAndModify(WatchListEntity watchListEntity) {
        Update update = new Update();
        update = update.setOnInsert("name", MY_FIRST_WATCHLIST)
                .setOnInsert("shared", false);
        if (CollectionUtils.isNotEmpty(watchListEntity.getCryptos())) {
            update.addToSet("cryptos").each(watchListEntity.getCryptos());
        }
        if (CollectionUtils.isNotEmpty(watchListEntity.getDexPairs())) {
            update.addToSet("dexPairs").each(watchListEntity.getDexPairs());
        }
        if (CollectionUtils.isNotEmpty(watchListEntity.getDexTokens())) {
            update.push("dexTokens").each(watchListEntity.getDexTokens());
        }
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return update;
    }


    private Update updateModify(WatchListEntity existingEntity,WatchListEntity watchListEntity) {
        Update update = new Update();
        // 添加 dexToken（如果有的话）
        if (CollectionUtils.isNotEmpty(watchListEntity.getCryptos())) {
            Set<Integer> existCryptos = Optional.ofNullable(existingEntity.getCryptos()).orElse(new HashSet<>());
            existCryptos.addAll(watchListEntity.getCryptos());
            update.set("cryptos", existCryptos);
        }
        // 添加 dexPair（如果有的话）
        if (CollectionUtils.isNotEmpty(watchListEntity.getDexPairs())) {
            List<Long> existDexPairs = Optional.ofNullable(existingEntity.getDexPairs()).orElse(new ArrayList<>());
            existDexPairs.addAll(watchListEntity.getDexPairs());
            update.set("dexPairs", existDexPairs);
        }
        // 添加 dexToken（如果有的话）
        if (CollectionUtils.isNotEmpty(watchListEntity.getDexTokens())) {
            List<String> existDexToken = Optional.ofNullable(existingEntity.getDexTokens()).orElse(new ArrayList<>());
            existDexToken.addAll(watchListEntity.getDexTokens());
            List<String> uniqueDexTokens = existDexToken.stream()
                    .distinct()
                    .collect(Collectors.toList());
            update.set("dexTokens", uniqueDexTokens);
        }
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return update;
    }

    /**
     * @param watchListEntity
     * @return
     */
    public Mono<WatchListEntity> findWatchList(WatchListEntity watchListEntity) {
        return this.reactiveMongoTemplate.findOne(Query.query(followResourceCriteria(watchListEntity)), WatchListEntity.class);
    }




    /**
     * 合并crypto与dex后的返回默认main的逻辑
     */
    public Mono<Tuple2<AtomicReference<Boolean>,WatchListEntity>> findAndCombineMainWatchList(WatchListEntity watchListEntity) {
        AtomicReference<Boolean> initWatch = new AtomicReference<>(false);
        //如果指定id则返回id对应的watchList
        if (watchListEntity.getId() != null) {
            Criteria criteria =
                Criteria.where("userId").is(watchListEntity.getUserId()).and("id").is(watchListEntity.getId());
            return this.reactiveMongoTemplate.findOne(Query.query(criteria), WatchListEntity.class)
                .flatMap(e -> Mono.just(Tuples.of(initWatch, e)))
                .switchIfEmpty(Mono.error(new BusinessException(ErrorCodeEnums.WATCHLIST_INVALID_PARAMETER)));
        }
        //如果没有指定id则查询mainCoin与mainDex
        return Mono.zip(findMainCoinWatchListByUserId(watchListEntity.getUserId()),
            findMainDexWatchListNoIdByUserId(watchListEntity.getUserId()))
            .flatMap(tuple -> {
                //如果只存在mainCoin则返回mainCoin
                if (tuple.getT1().isPresent() && tuple.getT2().isEmpty()) {
                    return Mono.just(tuple.getT1().get());
                }
                //如果只存在dexCoin则返回dexCoin
                if (tuple.getT1().isEmpty() && tuple.getT2().isPresent()) {
                    return Mono.just(tuple.getT2().get());
                }
                //如果两者都存在,在mainCoin中无数据,且mainDex有数据的情况下返回mainDex,否则返回mainPair,
                //此处需要将剩下的main置为false
                if (tuple.getT1().isPresent() && tuple.getT2().isPresent()) {
                    if (CollectionUtils.isEmpty(tuple.getT1().get().getCryptos()) && CollectionUtils.isNotEmpty(
                        tuple.getT2().get().getDexPairs())) {
                        return Mono.just(tuple.getT2().get()).publishOn(Schedulers.boundedElastic())
                            .doOnNext(m -> resetWatchListMainFalse(tuple.getT1().get().getId()).subscribe());
                    }
                    return Mono.just(tuple.getT1().get()).publishOn(Schedulers.boundedElastic())
                        .doOnNext(m -> resetWatchListMainFalse(tuple.getT2().get().getId()).subscribe());
                }
                //如果两者都为空则新创建一个main类型的数据
                initWatch.set(true);
                return initCryptoMainWatchList(watchListEntity);
            })
            .flatMap(e -> Mono.just(Tuples.of(initWatch, e)));
    }

    public Mono<WatchListEntity> initCryptoMainWatchList(WatchListEntity watchListEntity) {
        Criteria criteria = Criteria.where("userId").is(watchListEntity.getUserId()).and("type").exists(false)
            .and("actived").is(true).and("main").is(true);
        Update update = new Update();
        update = update.setOnInsert("name", MY_FIRST_WATCHLIST_COMBINE);
        update = update.setOnInsert("main", true);
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return reactiveMongoTemplate
                .findAndModify(Query.query(criteria), update, FindAndModifyOptions.options().upsert(true).returnNew(true),
                    WatchListEntity.class);
    }

    public Mono<Boolean> resetWatchListMainFalse(ObjectId watchListId) {
        Update update = new Update();
        update = update.set("main", false);
        update.set("updatedTime", ExtUtils.nowUTC());
        return this.reactiveMongoTemplate.upsert(
            Query.query(Criteria.where("id").is(watchListId))
            , update
            , WatchListEntity.class).flatMap(e -> Mono.just(e.getModifiedCount() >=0));
    }

    private Mono<Optional<WatchListEntity>> findMainCoinWatchListByUserId(String userId){
        Criteria criteria =
            Criteria.where("userId").is(userId).and("type").exists(false).and("main").is(true).and("actived").is(true);
        return this.reactiveMongoTemplate.findOne(Query.query(criteria), WatchListEntity.class)
            .flatMap(e -> Mono.just(Optional.of(e))).defaultIfEmpty(Optional.empty());
    }

    private Mono<Optional<WatchListEntity>> findMainDexWatchListNoIdByUserId(String userId){
        Criteria criteria = Criteria.where("userId").is(userId).and("actived").is(true).and("type")
            .is(ResourceTypeEnum.DEX_PAIR.getTypeName()).and("main").is(true);
        return this.reactiveMongoTemplate.findOne(Query.query(criteria), WatchListEntity.class)
            .flatMap(e -> Mono.just(Optional.of(e))).defaultIfEmpty(Optional.empty());
    }


    public Mono<WatchListEntity> findWatchListByType(WatchListEntity watchListEntity, String type) {
        return this.reactiveMongoTemplate.find(Query.query(followResourceCriteria(watchListEntity)),
            WatchListEntity.class).filter(entity -> Objects.equals(type,entity.getType())).take(1L).next();
    }

    public Mono<Long> countWatchList(String userId) {
        Criteria criteria = Criteria.where("userId").is(userId);
        criteria.and("actived").is(true);
        return this.reactiveMongoTemplate.count(Query.query(criteria), WatchListEntity.class);
    }


    /**
     * followResourceCriteria
     *
     * @param watchListEntity
     * @return
     */
    private Criteria followResourceCriteria(WatchListEntity watchListEntity) {
        Criteria criteria = Criteria.where("userId").is(watchListEntity.getUserId()).and("type").exists(false);
        criteria = watchListEntity.getId() == null ? criteria.and("main").is(true).and("actived").is(true) : criteria.and("id")
                .is(watchListEntity.getId());
        return criteria;
    }

    private Criteria followResourceCriteriaWithType(WatchListEntity watchListEntity,
        ResourceTypeEnum resourceTypeEnum) {
        Criteria criteria =
            Criteria.where("userId").is(watchListEntity.getUserId()).and("type").is(resourceTypeEnum.getTypeName());
        criteria = watchListEntity.getId() == null ? criteria.and("main").is(true).and("actived").is(true) :
            criteria.and("id").is(watchListEntity.getId());
        return criteria;
    }

    private Criteria followResourceCriteriaWithTypeV2(WatchListEntity watchListEntity) {
        return Criteria.where("userId").is(watchListEntity.getUserId()).and("id").is(watchListEntity.getId());
    }


    /**
     * incrByFollows
     *
     * @param watchListId
     * @return
     */
    public Mono<WatchListEntity> incrByFollows(String watchListId, int value) {

        Criteria criteria = Criteria.where("id").is(watchListId).and("actived").is(true);
        Update update = new Update();
        update.inc("follows", value);
        if (value < 0) {
            criteria = criteria.and("follows").gt(0);
        }
        //update.set("updatedTime", ExtUtils.nowUTC());
        return reactiveMongoTemplate.findAndModify(Query.query(criteria),
                update,
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                WatchListEntity.class);
    }

    @Nullable
    public Mono<WatchListEntity> save(WatchListEntity watchListEntity) {

        if (ExtUtils.getDefaultValue(watchListEntity.getMain()) && watchListEntity.getId() != null) {
            return saveWatchListForDefault(watchListEntity);
        }
        return createOrModifyWatchList(reactiveMongoTemplate, watchListEntity);
    }

    @Nullable
    public Mono<String> delete(ObjectId id, String userId) {

        Update update = new Update().set("actived", false)
                .set("follows", 0)
                .set("updatedTime", ExtUtils.nowUTC());
        return reactiveMongoTemplate.updateFirst(Query.query(Criteria.where("id").is(id)
                .and("userId").is(userId).and("main").is(false)), update, WatchListEntity.class)
                .filter(f -> f.getModifiedCount() > 0)
                .switchIfEmpty(Mono.error(new BusinessException(id + " delete failed")))
                .map(r -> id.toHexString());
    }

    /**
     * get the all shared watchlist ,but does not include the user himself
     *
     * @param start
     * @param limit
     * @return
     */
    public Mono<Tuple2<List<WatchListCacheEntity>, Long>> querySharedWatchList(int start, int limit) {

        Mono<List<WatchListCacheEntity>> listMono = getSharedWatchList(start, limit);
        Mono<Long> countMono = getSharedWatchListSize();
        return Mono.zip(listMono, countMono.map(c -> Math.min(popularWatchListSize, c)));
    }

    /**
     * @param start
     * @param limit
     * @return
     */
    public Mono<List<WatchListCacheEntity>> getSharedWatchList(int start, int limit) {

        Criteria criteria = getSharedWatchListCriteria();
        Mono<List<WatchListCacheEntity>> listMono = reactiveMongoTemplate.find(Query
                        .query(criteria).with(Sort.by(Sort.Order.desc("follows"))).skip(start).limit(limit)
                , WatchListEntity.class)
                .map(m -> {
                    var watchList = WatchListCacheEntity.builder()
                            .id(m.getId().toHexString())
                            .name(m.getName())
                            .description(m.getDescription())
                            .main(m.getMain())
                            .shared(m.getShared())
                            .userId(m.getUserId())
                            .order(m.getOrder())
                            .cryptos(m.getCryptos())
                            .follows(m.getFollows())
                            .cryptoSize(CollectionUtils.size(m.getCryptos()))
                            .build();
                    watchList.setCreatedTime(m.getCreatedTime());
                    watchList.setUpdatedTime(m.getUpdatedTime());
                    watchList.setActived(m.isActived());
                    return watchList;
                })
                .collectList();
        return listMono;
    }

    /**
     * @return
     */
    public Mono<Long> getSharedWatchListSize() {

        Criteria criteria = getSharedWatchListCriteria();
        return reactiveMongoTemplate.count(Query.query(criteria), WatchListEntity.class);
    }

    public Mono<List<WatchListEntity>> findByResourceId(ResourceTypeEnum resourceTypeEnum, List<Long> resourceIds,
        String userId) {
        Criteria criteria = Criteria.where("userId").is(userId)
            .and("main").is(true);
        if (resourceTypeEnum.getTypeName() != null) {
            criteria.and("type").is(resourceTypeEnum.getTypeName());
        }else {
            criteria.and("type").exists(false);
        }
        criteria.and(resourceTypeEnum.getFieldName()).in(resourceIds);
        return reactiveMongoTemplate.find(Query.query(criteria), WatchListEntity.class).collectList();
    }

    /**
     * @return
     */
    private Criteria getSharedWatchListCriteria() {

        return Criteria.where("shared").is(true).and("actived").is(true);
    }

    /**
     * save watchlist
     *
     * @param watchListEntity
     * @return
     */
    private Mono<WatchListEntity> saveWatchListForDefault(WatchListEntity watchListEntity) {

        return reactiveMongoTemplate.inTransaction().execute(action ->
                createOrModifyWatchList(action, watchListEntity)
                        .flatMap(wl -> {
                            Update update = new Update().set("main", false);
                            UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
                            Criteria criteria =  Criteria.where("id").ne(wl.getId())
                                    .and("userId").is(wl.getUserId())
                                    .and("main").is(true);
                            if (!Boolean.TRUE.equals(saveIgnoreType)) {
                                criteria.and("type").is(watchListEntity.getType());
                            }
                            return action.updateMulti(Query.query(criteria)
                                    , update
                                    // , FindAndModifyOptions.options().upsert(false)
                                    , WatchListEntity.class).thenReturn(wl);
                        })
        ).single();
    }

    /**
     * create or modify
     *
     * @param watchListEntity
     * @return
     */
    private Mono<WatchListEntity> createOrModifyWatchList(ReactiveMongoOperations operations, WatchListEntity watchListEntity) {

        Mono<WatchListEntity> entityMono;
        if (watchListEntity.getId() != null) {
            Update update = new Update();
            if (watchListEntity.getShared() != null) {
                update = update.set("shared", watchListEntity.getShared());
            }
            if (StringUtils.isNotBlank(watchListEntity.getName())) {
                update = update.set("name", watchListEntity.getName());
            }
            if (null != watchListEntity.getDescription()) {
                update = update.set("description", watchListEntity.getDescription());
            }
            if (ExtUtils.getDefaultValue(watchListEntity.getMain())) {
                update = update.set("main", watchListEntity.getMain());
            }
            UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
            entityMono = operations.findAndModify(
                    Query.query(Criteria.where("id").is(watchListEntity.getId())
                            .and("userId").is(watchListEntity.getUserId()))
                    , update
                    , FindAndModifyOptions.options().returnNew(true).upsert(false)
                    , WatchListEntity.class);
        } else {
            watchListEntity.setUpdatedTime(ExtUtils.nowUTC());
            watchListEntity.setCreatedTime(watchListEntity.getUpdatedTime());
            watchListEntity.setMain(ExtUtils.getDefaultValue(watchListEntity.getMain()));
            watchListEntity.setShared(ExtUtils.getDefaultValue(watchListEntity.getShared()));
            watchListEntity.setDescription(watchListEntity.getDescription());
            watchListEntity.setName(StringUtils.defaultString(watchListEntity.getName(),
                Optional.ofNullable(ResourceTypeEnum.findByType(watchListEntity.getType()))
                    .map(ResourceTypeEnum::getWatchlistName).orElse(MY_FIRST_WATCHLIST)));
            watchListEntity.setOrder(0);
            watchListEntity.setCryptos(watchListEntity.getCryptos());
            watchListEntity.setExchanges(watchListEntity.getExchanges());
            watchListEntity.setMarketPairs(watchListEntity.getMarketPairs());
            entityMono = operations.insert(watchListEntity);
        }
        return entityMono;
    }
}
