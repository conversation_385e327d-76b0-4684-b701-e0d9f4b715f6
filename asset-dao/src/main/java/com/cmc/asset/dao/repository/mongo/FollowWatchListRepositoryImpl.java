package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.UpdateUtils;
import com.cmc.asset.dao.entity.FollowWatchListEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2020/12/25 10:36
 * @description
 */
@Repository
public class FollowWatchListRepositoryImpl {

    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;

    /**
     * @param watchListEntity
     * @param isAdded
     * @return
     */
    @Nullable
    public Mono<FollowWatchListEntity> findAndModify(FollowWatchListEntity watchListEntity, String followWatchListId, int limitFollowSize, boolean isAdded) {

        Criteria criteria = Criteria.where("userId").is(watchListEntity.getUserId()).and("actived").is(true);
        Update update = new Update();
        if (StringUtils.isNotBlank(followWatchListId)) {
            if (isAdded) {
                update = update.addToSet("watchListIds", followWatchListId);
                // add up to 500
                criteria = criteria.and("watchListIds").nin(followWatchListId).and("cryptos." + limitFollowSize).exists(false);
            } else {
                update = update.pull("watchListIds", followWatchListId);
                criteria = criteria.and("watchListIds").in(followWatchListId);
            }
        }
        update = update.setOnInsert("userId", watchListEntity.getUserId());
        UpdateUtils.applyCreateTimeAndUpdateTime(update, watchListEntity);
        return reactiveMongoTemplate
            .findAndModify(Query.query(criteria), update, FindAndModifyOptions.options().upsert(true).returnNew(true),
                FollowWatchListEntity.class);
    }

    @Nullable
    public Mono<FollowWatchListEntity> findByUserId(String userId) {

        Criteria criteria = Criteria.where("userId").is(userId).and("actived").is(true);
        return reactiveMongoTemplate.findOne(Query.query(criteria), FollowWatchListEntity.class);
    }

    @Nullable
    public Mono<Boolean> existsByUserIdAndWatchListId(String userId, String watchListId) {

        Criteria criteria = Criteria.where("userId").is(userId).and("watchListIds").in(watchListId).and("actived").is(true);
        return reactiveMongoTemplate.exists(Query.query(criteria), FollowWatchListEntity.class);
    }
}
