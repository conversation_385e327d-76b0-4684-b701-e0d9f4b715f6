package com.cmc.asset.dao.entity;

import com.cmc.asset.dao.BaseMongoEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/17 16:17
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_prediction_crypto")
public class UserPredictionCryptoEntity extends BaseMongoEntity<ObjectId> {

    @Id
    private ObjectId id;
    /**
     * the id of user
     */
    @Indexed(unique = true)
    private String userId;

    /**
     * the crypto currencry of id
     */
    private Integer cryptoId;

    /**
     * User's receiving address
     */
    @Indexed(unique = true)
    private String binanceId;

    /**
     * first user prediction begin time.
     */
    private Date firstFromTime;

    /**
     * first user prediction end time
     */
    private Date firstToTime;

    private Date firstGuessTime;
    /**
     * second user prediction begin time.
     */
    private Date secondFromTime;

    /**
     * second user prediction end time
     */
    private Date secondToTime;

    private Date secondGuessTime;
    /**
     * third user prediction begin time.
     */
    private Date thirdFromTime;

    /**
     * third user prediction end time
     */
    private Date thirdToTime;

    private Date thirdGuessTime;
}
