package com.cmc.asset.dao.entity;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/3/31 下午3:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_top_coins")
public class UserTopCoinsEntity {

    @Id
    private ObjectId id;

    private String userId;

    private List<Integer> portfolioCoins;

    private List<Integer> watchlistCoins;

    private Integer coinsLimit;

}
