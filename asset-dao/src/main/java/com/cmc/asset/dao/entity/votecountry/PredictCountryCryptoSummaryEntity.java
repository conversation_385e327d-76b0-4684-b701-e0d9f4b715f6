package com.cmc.asset.dao.entity.votecountry;

import java.util.Date;
import java.util.Set;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Yin
 * @Description
 * @date 2021/6/24 下午4:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "predict_country_crypto_summary")
public class PredictCountryCryptoSummaryEntity {

    @Id
    private ObjectId id;

    private String countryCode;

    private long voteAmount;

    private String activityId;

    private Date createDate;
}
