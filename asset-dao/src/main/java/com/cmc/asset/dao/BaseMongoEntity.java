package com.cmc.asset.dao;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.Version;
import org.springframework.data.domain.Persistable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/9/15 19:44
 * @description
 */
@Data
public abstract class BaseMongoEntity<T> implements Persistable<T> {

    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdTime;

    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedTime;

    @Version
    private Long version;

    private boolean actived = true;

    @Override
    @JsonIgnore
    public boolean isNew() {
        return false;
    }
}
