package com.cmc.asset.dao.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DexTokenDetailCacheVo
 * <AUTHOR> ricky.x
 * @date: 2025/6/6 15:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DexTokenDetailCacheVo {
    private String uniKey;
    @JsonProperty("n")
    private String name;

    @JsonProperty("sym")
    private String symbol;

    @JsonProperty("addr")
    private String address;


    @JsonProperty("pdex")
    private String platformDexerName;

    @JsonProperty("pcid")
    private Integer platformCryptoId;

    @JsonProperty("plt")
    private String platform;

    @JsonProperty("pid")
    private Integer platformId;

    @JsonProperty("dec")
    private Integer decimals;

    @JsonProperty("crt")
    private String creator;

    @JsonProperty("own")
    private String owner;

    @JsonProperty("rnc")
    private String renounced;

    @JsonProperty("web")
    private String website;

    @JsonProperty("tw")
    private String twitter;

    @JsonProperty("tg")
    private String telegram;

    @JsonProperty("lg")
    private String logo;

    @JsonProperty("pubAt")
    private Long publishAt;

    @JsonProperty("lchAt")
    private Long launchedAt;

    @JsonProperty("fdv")
    private String fdv;

    @JsonProperty("mcap")
    private String marketcap;

    @JsonProperty("ts")
    private String totalSupply;

    @JsonProperty("bs")
    private String burnSupply;

    @JsonProperty("cs")
    private String circulatingSupply;

    @JsonProperty("liqUsd")
    private String liquidityUsd;

    @JsonProperty("liq")
    private String liquidity;

    @JsonProperty("hld")
    private Long holders;

    @JsonProperty("p")
    private String priceUsd;

    @JsonProperty("bcr")
    private Double bondingCurveRatio;

    @JsonProperty("sts")
    private List<DexTokenStatsCacheDTO> stats;

    @JsonProperty("tsrc")
    private String poolSource;

    @JsonProperty("rl")
    private String riskLevel;


    @JsonProperty("pt")
    private Long priceTime;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DexTokenStatsCacheDTO {
        @JsonProperty("tp")
        private String type;

        @JsonProperty("vu")
        private String volume;

        @JsonProperty("txs")
        private Long txs;

        @JsonProperty("nb")
        private Long numBuy;
        @JsonProperty("ns")
        private Long numSell;
        @JsonProperty("bvu")
        private String buyVolume;
        @JsonProperty("svu")
        private String sellVolume;
        @JsonProperty("but")
        private Long buyers;
        @JsonProperty("sut")
        private Long sellers;

        @JsonProperty("pc")
        private Float priceChangeRate;

        @JsonProperty("ut")
        private Long uniqueTraders;
    }
}
