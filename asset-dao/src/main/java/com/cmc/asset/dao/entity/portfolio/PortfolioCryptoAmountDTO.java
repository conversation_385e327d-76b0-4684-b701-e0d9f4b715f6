package com.cmc.asset.dao.entity.portfolio;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020-12-4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioCryptoAmountDTO {
    private Integer cryptoId;
    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal amount;
}
