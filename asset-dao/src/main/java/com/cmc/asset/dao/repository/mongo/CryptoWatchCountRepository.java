package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.CryptoWatchCountEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;


/**
 * <AUTHOR>
 */
public interface CryptoWatchCountRepository extends ReactiveMongoRepository<CryptoWatchCountEntity, ObjectId>,
    ReactiveCrudRepository<CryptoWatchCountEntity, ObjectId>, CryptoWatchCountRepositoryCustom {
}
