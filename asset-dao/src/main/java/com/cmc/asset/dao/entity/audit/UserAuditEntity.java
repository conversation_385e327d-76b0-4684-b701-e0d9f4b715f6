package com.cmc.asset.dao.entity.audit;

import com.cmc.asset.dao.BaseMongoEntity;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_audit")
public class UserAuditEntity extends BaseMongoEntity<ObjectId> {
    @Id
    private ObjectId id;

    private String userId;

    private Long userUid;

    /**
     * @see com.cmc.asset.model.enums.UserAuditTypeEnum
     */
    private Integer type;

    /**
     * @see com.cmc.asset.model.enums.UserAuditStatusEnum
     */
    private Integer status;

    /**
     * the audited subject
     */
    private String subject;

    /**
     * the task id of third-party
     */
    private String taskId;

    /**
     * the pre audited subject
     */
    private String preSubject;
}
