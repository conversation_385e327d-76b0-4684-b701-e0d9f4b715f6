package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.referral.UserReferralDetailEntity;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/9/27
 */
@Repository
public interface UserReferralDetailRepository extends ReactiveMongoRepository<UserReferralDetailEntity, ObjectId>,
        ReactiveCrudRepository<UserReferralDetailEntity, ObjectId>,  UserReferralDetailRepositoryCustom{

    Mono<UserReferralDetailEntity> findByReferredUserId(String referredUserId);

    Mono<Long> countByReferralUserId(String referredUserId);

    Flux<UserReferralDetailEntity> findByReferralUserId(String referralUserId);

}
