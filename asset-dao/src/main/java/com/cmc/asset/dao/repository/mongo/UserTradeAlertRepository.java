package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.alert.UserTradeAlertEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/12/29 下午2:50
 * @description
 */
@Repository
public interface UserTradeAlertRepository extends ReactiveMongoRepository<UserTradeAlertEntity, ObjectId>,
    ReactiveCrudRepository<UserTradeAlertEntity, ObjectId>, UserTradeAlertRepositoryCustom {
}
