package com.cmc.asset.dao.dynamo.entity;

import com.cmc.data.common.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.NavigableMap;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 * @date 2022/3/23
 * Entity for DynamoDB
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@DynamoDbBean
public class HoldingHistoryEntity {

    private String holdingAll;

    private NavigableMap<Date, NavigableMap<Date, BigDecimal>> mapByDay;

    private Instant timeUpdated;

    private Instant timeCreated;

    private String userPortfolioSource;

    private Integer year;

    @DynamoDbIgnore
    public NavigableMap<Date, NavigableMap<Date, BigDecimal>> getMapByDay() {
        if (mapByDay == null && (StringUtils.isNotEmpty(holdingAll))) {
            mapByDay = JacksonUtils.getInstance()
                .deserialize(holdingAll, new TypeReference<NavigableMap<Date, NavigableMap<Date, BigDecimal>>>() {
                });
        }
        if (mapByDay == null) {
            mapByDay = Maps.newTreeMap();
        }
        return mapByDay;
    }

    @DynamoDbPartitionKey
    public String getUserPortfolioSource() {
      return  userPortfolioSource;
    }

    @DynamoDbSortKey
    public Integer getYear() {
        return year;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HoldingHistoryEntity entity = (HoldingHistoryEntity)o;
        return userPortfolioSource.equals(entity.userPortfolioSource) && year.equals(entity.year);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userPortfolioSource, year);
    }
}
