package com.cmc.asset.dao.entity.portfolio;

import lombok.*;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "portfolio_calculating_status_v2")
public class PortfolioCalculatingStatusEntity {
    @Id
    private ObjectId id;

    private String userId;

    private String portfolioSourceId;

    private String status;

    private Date transactionTime;

    private Date updateTime;

    private String note;

    private String scheduleFlag;

    private Date timeCreated;
}
