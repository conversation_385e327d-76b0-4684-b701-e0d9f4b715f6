package com.cmc.asset.dao.entity.portfolio;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BlockChainPO
 * <AUTHOR> ricky.x
 * @date : 2023/1/28 下午3:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlDetailPO {

    /**
     * chain id
     */
    private Integer cryptoId;

    /**
     * chain id
     */
    private BigDecimal totalBuy;

    /**
     * chain id
     */
    private BigDecimal totalSell;

    /**
     * chain id
     */
    private BigDecimal amount;

}
