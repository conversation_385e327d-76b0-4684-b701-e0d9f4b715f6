package com.cmc.asset.dao.entity.portfolio;

import java.math.BigDecimal;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CryptoDetailPO
 * <AUTHOR> ricky.x
 * @date : 2023/6/12 14:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CryptoDetailPO {

    /**
     * crypto id
     */
    private Integer cryptoId;
    /**
     * amount
     */
    private BigDecimal amount;
    /**
     * buy total no fee
     */
    private BigDecimal buyTotal;

    /**
     * buy amount
     */
    private BigDecimal buyAmount;

    /**
     * buy avg price : buyTotal / buyAmount
     */
    private BigDecimal buyAvgPrice;

    /**
     * total buy
     */
    private BigDecimal totalBuy;

    /**
     * total sell
     */
    private BigDecimal totalSell;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal realizedProfit;
    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal unrealizedProfit;

    /**
     * result in crypto unit
     * @see com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity#cryptoUnit
     * ========================================
     */
    private BigDecimal buyTotalInUnit;
    private BigDecimal buyAvgPriceInUnit;
    private BigDecimal totalBuyInUnit;
    private BigDecimal totalSellInUnit;
    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal realizedProfitInUnit;
    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal unrealizedProfitInUnit;
    /**
     * ========================================
     */

}
