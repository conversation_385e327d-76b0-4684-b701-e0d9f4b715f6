package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioPlChangeRecordPO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;

/**
 * portfolio pl change
 * <AUTHOR> ricky.x
 * @date : 2023/4/13 下午5:18
 */
@Repository
public interface PortfolioPlChangeRepository
    extends ReactiveMongoRepository<PortfolioPlChangeRecordPO, ObjectId>, ReactiveCrudRepository<PortfolioPlChangeRecordPO, ObjectId> {
}
