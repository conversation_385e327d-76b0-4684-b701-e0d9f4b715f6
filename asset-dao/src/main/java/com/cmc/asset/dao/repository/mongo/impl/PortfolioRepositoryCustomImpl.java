package com.cmc.asset.dao.repository.mongo.impl;

import com.cmc.asset.dao.entity.portfolio.PortfolioEntity;
import com.cmc.asset.dao.repository.mongo.PortfolioRepositoryCustom;
import com.cmc.asset.model.contract.portfolio.transaction.PortfolioTransactionRequest;
import com.cmc.framework.utils.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2023/11/2 09:56
 */
@Repository
public class PortfolioRepositoryCustomImpl implements PortfolioRepositoryCustom {

    private final ReactiveMongoTemplate reactiveMongoTemplate;

    public PortfolioRepositoryCustomImpl(ReactiveMongoTemplate reactiveMongoTemplate) {
        this.reactiveMongoTemplate = reactiveMongoTemplate;
    }

    @Override
    public Flux<PortfolioEntity> findTransaction(PortfolioTransactionRequest requestDto) {
        String userId = requestDto.getHeader().getUserId();
        Criteria criteria = Criteria.where("userId")
                .is(new ObjectId(userId))
                .and("portfolioSourceId")
                .is(requestDto.getPortfolioSourceId());

        if (CollectionUtils.isNotEmpty(requestDto.getTransactionTypes())) {
            criteria.and("transactionType").in(requestDto.getTransactionTypes());
        }
        if (CollectionUtils.isNotEmpty(requestDto.getCryptoIds())) {
            if (requestDto.getCryptoIds().size() == 1) {
                criteria.and("cryptocurrencyId").is(requestDto.getCryptoIds().get(0));
            } else {
                criteria.and("cryptocurrencyId").in(requestDto.getCryptoIds());
            }
        }
        Query query = new Query();
        query.addCriteria(criteria);
        Integer currentPage = requestDto.getCurrentPage();
        Integer pageSize = requestDto.getPageSize();
        Sort sort = Sort.by(
            Sort.Order.desc("transactionTime"),
            Sort.Order.desc("timeCreated")
        );
        if (currentPage != null && pageSize != null) {
            PageRequest pageRequest = PageRequest.of(currentPage - 1
                    , pageSize, sort);
            query.with(pageRequest);
        }
        query.with(sort);
        return reactiveMongoTemplate.find(query, PortfolioEntity.class);
    }

    @Override
    public Mono<Long> countTransaction(PortfolioTransactionRequest requestDto) {
        String userId = requestDto.getHeader().getUserId();
        Criteria criteria = Criteria.where("userId")
                .is(new ObjectId(userId))
                .and("portfolioSourceId")
                .is(requestDto.getPortfolioSourceId());

        if (CollectionUtils.isNotEmpty(requestDto.getTransactionTypes())) {
            criteria.and("transactionType").in(requestDto.getTransactionTypes());
        }
        if (CollectionUtils.isNotEmpty(requestDto.getCryptoIds())) {
            if (requestDto.getCryptoIds().size() == 1) {
                criteria.and("cryptocurrencyId").is(requestDto.getCryptoIds().get(0));
            } else {
                criteria.and("cryptocurrencyId").in(requestDto.getCryptoIds());
            }
        }
        Query query = new Query();
        query.addCriteria(criteria);
        return reactiveMongoTemplate.count(query, PortfolioEntity.class);

    }

}
