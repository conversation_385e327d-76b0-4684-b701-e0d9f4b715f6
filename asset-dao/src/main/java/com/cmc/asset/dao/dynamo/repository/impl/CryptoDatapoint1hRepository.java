package com.cmc.asset.dao.dynamo.repository.impl;

import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint1hEntity;
import com.cmc.asset.dao.entity.dynamondb.PkResolver;
import com.cmc.asset.model.common.Constant;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.UnicastProcessor;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchGetItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.ReadBatch;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

@Component
@ConditionalOnBean(value = {DynamoDbEnhancedAsyncClient.class, DynamoDbAsyncClient.class,
    DynamoDbAsyncTable.class}, name = "cryptoDatapoint1hTable")
@Slf4j
public class CryptoDatapoint1hRepository {

    @Autowired
    private DynamoDbAsyncClient dynamoDbAsyncClient;

    @Autowired
    private DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient;

    @Autowired
    private DynamoDbAsyncTable<CryptoDatapoint1hEntity> cryptoDataPoint1HDynamoDbAsyncTable;

    @Value("${com.cmc.asset.crypto.1hr.batch-get-size:98}")
    private Integer batchGetSize;

    public Mono<List<CryptoDatapoint1hEntity>> getCryptoDatapointByTimeRange(Integer cryptoId, Long startTime,
        Long endTime) {
        UnicastProcessor<Map<String, AttributeValue>> messageProcessor = UnicastProcessor.create();
        AtomicLong pageSize = new AtomicLong(0);
        Mono<List<CryptoDatapoint1hEntity>> mono = messageProcessor.flatMap(
            exclusiveStartKey -> getCryptoDatapointByPage(cryptoId, startTime, endTime, exclusiveStartKey).doOnError(
                error -> messageProcessor.sink().error(error))
                .doOnNext(page -> {
                    if (page == null || page.lastEvaluatedKey() == null) {
                        messageProcessor.sink().complete();
                    } else {
                        messageProcessor.sink().next(page.lastEvaluatedKey());
                    }
                }))
            .collectList()
            .map(pageList -> {
                List<CryptoDatapoint1hEntity> cryptoDatapoint1hEntityList = new ArrayList<>();
                pageList.forEach(page -> {
                    if (CollectionUtils.isNotEmpty(page.items())) {
                        cryptoDatapoint1hEntityList.addAll(page.items());
                    }
                });
                cryptoDatapoint1hEntityList.sort(Comparator.comparing(CryptoDatapoint1hEntity::getScore));
                return cryptoDatapoint1hEntityList;
            })
            .doOnError(throwable -> {
                log.error("CryptoDatapoint1hRepository getCryptoDatapointByTimeRange error", throwable);
                messageProcessor.sink().error(throwable);
            });
        messageProcessor.sink().next(Maps.newHashMap());
        return mono;
    }


    private Mono<Page<CryptoDatapoint1hEntity>> getCryptoDatapointByPage(Integer cryptoId, Long startTime, Long endTime,
        Map<String, AttributeValue> exclusiveStartKey) {
        Key startKey = Key.builder()
            .partitionValue(cryptoId)
            .sortValue(startTime)
            .build();
        Key endKey = Key.builder()
            .partitionValue(cryptoId)
            .sortValue(endTime)
            .build();
        QueryEnhancedRequest request;
        if (MapUtils.isNotEmpty(exclusiveStartKey)) {
            request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBetween(startKey, endKey))
                .exclusiveStartKey(exclusiveStartKey)
                .limit(100)
                .build();
        } else {
            request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBetween(startKey, endKey))
                .limit(100)
                .build();
        }
        return Mono.from(cryptoDataPoint1HDynamoDbAsyncTable.query(request));
    }

    public Mono<List<CryptoDatapoint1hEntity>> getOhlcvHistoricalhData(int id, List<Long> timeCodeList) {
        if (CollectionUtils.isEmpty(timeCodeList)) {
            return Mono.just(Lists.newArrayList());
        }
        return Flux.fromIterable(timeCodeList)
                .buffer(batchGetSize)
                .flatMap(timeCodes -> batchGetMono(id, timeCodes, timeCodes.size()))
                .flatMapIterable(lists -> lists)
                .collectSortedList(Comparator.comparing(PkResolver::getScore));
    }

    public Mono<List<CryptoDatapoint1hEntity>> batchGetMono(Integer cryptoId, List<Long> scores, Integer limit) {
        if (CollectionUtils.isEmpty(scores)) {
            return Mono.just(Lists.newArrayList());
        }
        ReadBatch.Builder<CryptoDatapoint1hEntity> dataPointEntityBuilder =
                ReadBatch.builder(CryptoDatapoint1hEntity.class)
                        .mappedTableResource(cryptoDataPoint1HDynamoDbAsyncTable);
        for (Long score : scores) {
            Key key = Key.builder()
                    .partitionValue(cryptoId + Constant.UNDERLINE + score)
                    .build();
            dataPointEntityBuilder.addGetItem(key);
        }
        ReadBatch readBatch = dataPointEntityBuilder.build();
        BatchGetItemEnhancedRequest batchGetItemEnhancedRequest =
                BatchGetItemEnhancedRequest.builder().readBatches(readBatch).build();
        return Mono.from(dynamoDbEnhancedAsyncClient.batchGetItem(batchGetItemEnhancedRequest).limit(limit))
                .map(data -> data.resultsForTable(cryptoDataPoint1HDynamoDbAsyncTable));
    }
}