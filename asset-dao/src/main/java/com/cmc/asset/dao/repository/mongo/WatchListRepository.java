package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.WatchListEntity;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/19 19:38
 * @description
 */
@Repository
public interface WatchListRepository extends ReactiveMongoRepository<WatchListEntity, ObjectId>,
        ReactiveCrudRepository<WatchListEntity, ObjectId> {

    /**
     * query the watchlist by userid
     *
     * @param userId
     * @return
     */
    Flux<WatchListEntity> findAllByUserIdEqualsAndActivedTrue(String userId);

    /**
     * query the watchlist by userid and watchlistid
     *
     * @param id
     * @return
     */
    Flux<WatchListEntity> findByIdEqualsAndActivedTrue(ObjectId id);

    /**
     * @param id
     * @param userId
     * @return
     */
    Flux<WatchListEntity> findByIdEqualsAndUserIdEqualsAndActivedTrue(ObjectId id, String userId);

    /**
     * get the watch list by ids
     *
     * @param id
     * @return
     */
    Flux<WatchListEntity> findAllByIdInAndActivedTrue(List<ObjectId> id);

    /**
     * query the main watchlist by userid
     *
     * @param userId
     * @return
     */
    Flux<WatchListEntity> findAllByUserIdEqualsAndActivedTrueAndMainTrue(String userId);

    /**
     * get the public watchlists;
     *
     * @param pageable
     * @return
     */
    Flux<WatchListEntity> findAllBySharedTrueAndActivedTrueOrderByFollowsDesc(Pageable pageable);

    /**
     * query the watchlist by watchlistid & actived=true & shared=true
     *
     * @param id
     * @return
     */
    Flux<WatchListEntity> findByIdEqualsAndActivedTrueAndSharedTrue(ObjectId id);

    /**
     * query by id And userId
     * @param id watchList id
     * @param userId userId
     * @return mono
     */
    Mono<WatchListEntity> findByIdAndUserId(ObjectId id,String userId);


}
