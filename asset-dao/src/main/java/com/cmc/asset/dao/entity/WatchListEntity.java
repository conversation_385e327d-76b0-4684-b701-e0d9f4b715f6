package com.cmc.asset.dao.entity;

import com.cmc.asset.dao.BaseMongoEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:17
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_watchlist")
public class WatchListEntity extends BaseMongoEntity<ObjectId> {

    @Id
    private ObjectId id;

    /**
     * userid
     */
    //    @Indexed
    private String userId;

    /**
     * uid for user.Long type userId.
     */
    private Long uid;

    /**
     * watchlist name
     */
    private String name;

    /**
     * the watchlist description
     */
    private String description;

    /**
     * Number of followers
     */
    private Long follows;

    /**
     * 关注币列表
     */
    private Set<Integer> cryptos;

    /**
     * 关注交易所
     */
    private Set<Integer> exchanges;

    /**
     * 交易对
     */
    private Set<Integer> marketPairs;


    /**
     * 交易对
     */
    private List<String> dexTokens;

    /**
     * 是否共享
     */
    private Boolean shared;

    /**
     * 是否默认watchlist
     */
    private Boolean main;

    /**
     * 顺序
     */
    private Integer order;

    private String type;

    /**
     * dex pairs
     */
    private List<Long> dexPairs;

}
