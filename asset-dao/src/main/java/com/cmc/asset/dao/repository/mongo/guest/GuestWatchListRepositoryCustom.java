package com.cmc.asset.dao.repository.mongo.guest;

import com.cmc.asset.dao.entity.GuestWatchListEntity;
import java.util.Set;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/1/13
 */
public interface GuestWatchListRepositoryCustom {

    /**
     * find and remove by guestId
     * @param guestId guestId
     * @return deleted guest entity
     */
    Mono<GuestWatchListEntity> deleteByGuestId(Long guestId);

    /**
     * update cryptos by guestId
     * @param guestId
     * @param isAdd
     * @param cryptos
     * @return
     */
    Mono<GuestWatchListEntity> updateCryptosByGuestId(Long guestId, boolean isAdd, Set<Integer> cryptos);

    /**
     * update dexpair by guestId
     * @param guestId
     * @param isAdd
     * @param dexPairs
     * @return
     */
    Mono<GuestWatchListEntity> updateDexPairsByGuestId(Long guestId, boolean isAdd, Set<Long> dexPairs, Set<String> dexTokens);


    /**
     * update dexpair by guestId
     * @param guestId
     * @param isAdd
     * @param dexTokens
     * @return
     */
    Mono<GuestWatchListEntity> updateDexTokenByGuestId(Long guestId, boolean isAdd, Set<String> dexTokens);

}
