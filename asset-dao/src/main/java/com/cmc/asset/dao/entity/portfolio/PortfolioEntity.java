package com.cmc.asset.dao.entity.portfolio;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;
import org.springframework.data.domain.Persistable;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/11/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "portfolios_v3")
public class PortfolioEntity implements Persistable<ObjectId> {
    @Id
    private ObjectId id;

    private ObjectId userId;

    private Integer cryptocurrencyId;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal amount;

    private String transactionType;

    private Date transactionTime;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal price;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal fee;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal inputFee;

    private Integer inputUnitId;

    @Field(targetType = FieldType.DECIMAL128)
    private BigDecimal cryptoUnitInputPrice;

    private String note;

    private String portfolioSourceId;

    private Date timeUpdated ;

    private Date timeCreated ;

    @Version
    private Long version;

    @Override
    @JsonIgnore
    public boolean isNew() {
        return false;
    }
}
