package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioWalletDetailPO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * PortfolioWalletDetailRepository
 * <AUTHOR> ricky.x
 * @date : 2023/1/28 下午3:40
 */
@Repository
public interface PortfolioWalletDetailRepository extends ReactiveMongoRepository<PortfolioWalletDetailPO, ObjectId>, ReactiveCrudRepository<PortfolioWalletDetailPO, ObjectId>, PortfolioWalletDetailRepositoryCustom {
    Flux<PortfolioWalletDetailPO> findByUserId(ObjectId userId);

    Mono<PortfolioWalletDetailPO> findFirstByUserIdOrderByTimeUpdatedDesc(ObjectId userId);

    Mono<PortfolioWalletDetailPO> findFirstByUserIdAndPortfolioSourceIdOrderByTimeUpdatedDesc(ObjectId userId,String sourceId);

    Mono<PortfolioWalletDetailPO> findFirstByUserIdAndPortfolioSourceId(ObjectId userId, String portfolioSourceId);
}
