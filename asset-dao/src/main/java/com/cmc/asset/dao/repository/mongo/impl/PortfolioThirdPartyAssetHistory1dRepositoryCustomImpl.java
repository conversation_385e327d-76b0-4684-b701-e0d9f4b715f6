package com.cmc.asset.dao.repository.mongo.impl;

import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyAssetHistory1dEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyAssetHistoryAggregatedEntity;
import com.cmc.asset.dao.repository.mongo.PortfolioThirdPartyAssetHistory1dRepositoryCustom;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Range;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/5/10 17:44
 */
@Repository
public class PortfolioThirdPartyAssetHistory1dRepositoryCustomImpl
    implements PortfolioThirdPartyAssetHistory1dRepositoryCustom {

    private final ReactiveMongoTemplate reactiveMongoTemplate;

    public PortfolioThirdPartyAssetHistory1dRepositoryCustomImpl(ReactiveMongoTemplate reactiveMongoTemplate) {
        this.reactiveMongoTemplate = reactiveMongoTemplate;
    }

    @Override
    public Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity> aggregationByTime(ObjectId thirdPartyId,
        Range<Date> dateRange) {
        Criteria criteria = Criteria.where("thirdPartyId").is(thirdPartyId).and("cryptoId").exists(true);
        if (dateRange != null) {
            Optional<Date> start = dateRange.getLowerBound().getValue();
            Optional<Date> end = dateRange.getUpperBound().getValue();
            if (start.isPresent() && end.isPresent()) {
                if (dateRange.getUpperBound().isInclusive()) {
                    criteria.andOperator(Criteria.where("syncTime").gte(start.get()),
                            Criteria.where("syncTime").lte(end.get()));
                } else {
                    criteria.andOperator(Criteria.where("syncTime").gte(start.get()),
                            Criteria.where("syncTime").lt(end.get()));
                }
            }
        }
        Query query = Query.query(criteria);
        
        return reactiveMongoTemplate.find(query, PortfolioThirdPartyAssetHistory1dEntity.class)
            .groupBy(entity -> entity.getSyncTime().toString() + "_" + entity.getCryptoId())
            .flatMap(group -> group.reduce(
                new PortfolioThirdPartyAssetHistoryAggregatedEntity(),
                (aggregated, entity) -> {
                    aggregated.setSyncTime(entity.getSyncTime());
                    aggregated.setCryptoId(entity.getCryptoId());
                    if (aggregated.getTotalBalance() == null) {
                        aggregated.setTotalBalance(BigDecimal.ZERO);
                    }
                    if (entity.getBalance() != null) {
                        aggregated.setTotalBalance(aggregated.getTotalBalance().add(entity.getBalance()));
                    }
                    if (aggregated.getTotalValue() == null) {
                        aggregated.setTotalValue(BigDecimal.ZERO);
                    }
                    if (entity.getValue() != null) {
                        aggregated.setTotalValue(aggregated.getTotalValue().add(entity.getValue()));
                    }
                    return aggregated;
                }
            ));
    }

    @Override
    public Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity> aggregationByMinTime(ObjectId thirdPartyId,
                                                                                      Date startDate,
                                                                                      boolean includeStart, Integer cryptoId) {
        if (startDate == null) {
            return Flux.empty();
        }
        Criteria criteria = Criteria.where("thirdPartyId").is(thirdPartyId);
        if (includeStart) {
            criteria = criteria.and("syncTime").lte(startDate);
        } else {
            criteria = criteria.and("syncTime").lt(startDate);
        }

        if (cryptoId != null) {
            criteria = criteria.and("cryptoId").is(cryptoId);
        } else {
            criteria = criteria.and("cryptoId").exists(true);
        }
        MatchOperation matchOperation = Aggregation.match(criteria);
        SortOperation sortOperation = Aggregation.sort(Sort.Direction.ASC, "cryptoId")
                .and(Sort.Direction.DESC, "syncTime");
        GroupOperation groupOperation = Aggregation.group("thirdPartyId", "cryptoId")
                .first("syncTime")
                .as("syncTime")
                .first("balance")
                .as("totalBalance")
                .first("value")
                .as("totalValue");
        ProjectionOperation projectionOperation =
                Aggregation.project("thirdPartyId", "totalBalance", "totalValue", "syncTime", "cryptoId");

        Aggregation aggregation = Aggregation.newAggregation(matchOperation, sortOperation, groupOperation, projectionOperation);
        return reactiveMongoTemplate.aggregate(aggregation, PortfolioThirdPartyAssetHistory1dEntity.class,
                PortfolioThirdPartyAssetHistoryAggregatedEntity.class);
    }

}
