package com.cmc.asset.dao.entity;

import com.cmc.asset.dao.BaseMongoEntity;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "app_section")
public class AppSectionEntity extends BaseMongoEntity<ObjectId> {

    @Id
    private ObjectId id;

    private String code;

    private String title;

    private String description;

    private Boolean main;

    private Integer order;

    private List<PlatformEntity> includePlatform;

    private Boolean defaultSection;
}