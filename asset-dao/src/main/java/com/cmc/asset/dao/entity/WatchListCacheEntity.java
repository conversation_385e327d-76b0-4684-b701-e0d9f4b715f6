package com.cmc.asset.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/1/22 20:15
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchListCacheEntity {

    private String id;

    /**
     * userid
     */
    private String userId;

    /**
     * watchlist name
     */
    private String name;

    /**
     * the watchlist description
     */
    private String description;

    /**
     * Number of followers
     */
    private Long follows;

    private Integer cryptoSize;

    private Integer assetSize;

    private Set<Integer> cryptos;

    /**
     * 是否共享
     */
    private Boolean shared;

    /**
     * 是否默认watchlist
     */
    private Boolean main;

    /**
     * 顺序
     */
    private Integer order;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

    private boolean actived;
}
