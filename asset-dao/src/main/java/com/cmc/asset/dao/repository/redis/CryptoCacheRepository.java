package com.cmc.asset.dao.repository.redis;

import com.cmc.asset.dao.entity.vo.CryptoHistoryDataCacheVo;
import com.cmc.asset.dao.entity.vo.DexTokenDetailCacheVo;
import com.cmc.asset.dao.repository.redis.template.AssetRedisTemplate;
import com.cmc.asset.model.common.Constant;
import com.cmc.data.common.utils.JacksonUtils;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.Random;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @Date 2022/9/14
 */
@Slf4j
@Repository
public class CryptoCacheRepository extends BaseRedisRepository<AssetRedisTemplate> {

    private static final BigDecimal ADJUST_BASE = new BigDecimal("0.5");

    public Mono<List<Pair<Long, String>>> getCryptoHistoryData(Integer cryptoId, List<Long> timeCodes) {
        if (CollectionUtils.isEmpty(timeCodes)) {
            return Mono.just(Lists.newArrayList());
        }
        return reactiveRedisTemplate.execute(connection ->
            Flux.fromIterable(timeCodes)
            .flatMap(timeCode -> {
                String cacheKey = String.format(Constant.CR_HISTORY_DATA, cryptoId, timeCode);
                return connection.stringCommands()
                    .get(ByteBuffer.wrap(cacheKey.getBytes()))
                    .map(byteBuffer -> Pair.of(timeCode, new String(byteBuffer.array())));
            })).collectList();
    }


    public Flux<String> getDexTokens(Set<String> uniKeySet) {
        if (CollectionUtils.isEmpty(uniKeySet)) {
            return Flux.empty();
        }
        return reactiveRedisTemplate.execute(connection ->
                Flux.fromIterable(uniKeySet)
                        .flatMap(uniKey -> {
                            String cacheKey = String.format(Constant.DEX_TOKEN_DATA_V2,uniKey);
                            return connection.stringCommands()
                                    .get(ByteBuffer.wrap(cacheKey.getBytes()))
                                    .map(byteBuffer -> new String(byteBuffer.array()));
                        }));
    }


    public Mono<Boolean> cacheDexTokens(List<DexTokenDetailCacheVo> dexTokenAddress, long expireTime) {
        if (CollectionUtils.isEmpty(dexTokenAddress)) {
            return Mono.just(Boolean.TRUE);
        }
        return reactiveRedisTemplate.execute(connection ->
                Flux.fromIterable(dexTokenAddress)
                        .flatMap(cacheVo -> {
                            String cacheKey = String.format(Constant.DEX_TOKEN_DATA_V2, cacheVo.getUniKey());
                            return connection.stringCommands().setEX(ByteBuffer.wrap(cacheKey.getBytes()),ByteBuffer.wrap(
                                    JacksonUtils.toJsonString(cacheVo).getBytes()), Expiration.seconds(getRandomExpireTime(expireTime)));
                        })).collectList()
                .thenReturn(Boolean.TRUE)
                .publishOn(Schedulers.boundedElastic());
    }

    public Mono<List<Boolean>> addCryptoHistoryData(List<CryptoHistoryDataCacheVo> cryptoHistoryDataCacheVoList, Long expireTime) {
        if (CollectionUtils.isEmpty(cryptoHistoryDataCacheVoList)) {
            return Mono.just(Lists.newArrayList());
        }
        return reactiveRedisTemplate.execute(connection ->
            Flux.fromIterable(cryptoHistoryDataCacheVoList)
                .flatMap(cacheVo -> {
                    String cacheKey = String.format(Constant.CR_HISTORY_DATA, cacheVo.getId(), cacheVo.getScore());
                    return connection.stringCommands().setEX(ByteBuffer.wrap(cacheKey.getBytes()),ByteBuffer.wrap(
                        JacksonUtils.toJsonString(cacheVo).getBytes()), Expiration.seconds(getRandomExpireTime(expireTime)));
                })).collectList();
    }

    public Mono<List<Boolean>> addEmptyCryptoHistoryData(Integer cryptoId, Set<Long> timeCodes,Long expireTime) {
        if (CollectionUtils.isEmpty(timeCodes)) {
            return Mono.just(Lists.newArrayList());
        }
        return reactiveRedisTemplate.execute(connection ->
            Flux.fromIterable(timeCodes)
                .flatMap(timeCode -> {
                    String cacheKey = String.format(Constant.CR_HISTORY_DATA, cryptoId, timeCode);
                    return connection.stringCommands().setEX(ByteBuffer.wrap(cacheKey.getBytes()),ByteBuffer.wrap(
                        "{}".getBytes()), Expiration.seconds(getRandomExpireTime(expireTime)));
                })).collectList();
    }

    private  Long getRandomExpireTime(Long expireTime) {
        Random random = new Random();
        BigDecimal randomFactor = ADJUST_BASE.add(BigDecimal.valueOf(random.nextDouble()));
        return randomFactor.multiply(BigDecimal.valueOf(expireTime)).longValue();
    }


}
