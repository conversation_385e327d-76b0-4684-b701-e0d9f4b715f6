package com.cmc.asset.dao.entity.portfolio;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "portfolio_calculation_log_v2")
public class PortfolioCalculationLogEntity {
    @Id
    private ObjectId id;

    private String userId;

    private String portfolioSourceId;

    private Date calculationStartTime;

    private Date calculationEndTime;

    private Date updateTime;

    private Date timeCreated;
}
