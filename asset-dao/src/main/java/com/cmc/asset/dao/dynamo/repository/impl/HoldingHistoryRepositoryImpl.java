package com.cmc.asset.dao.dynamo.repository.impl;

import com.cmc.asset.dao.dynamo.entity.HoldingHistoryEntity;
import com.cmc.asset.dao.dynamo.repository.HoldingHistoryRepository;
import com.cmc.data.common.utils.JacksonUtils;
import com.google.common.collect.Lists;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchGetItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteResult;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.ReadBatch;
import software.amazon.awssdk.enhanced.dynamodb.model.WriteBatch;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/5/18
 */
@Component
@ConditionalOnBean({DynamoDbClient.class, DynamoDbEnhancedClient.class})
public class HoldingHistoryRepositoryImpl implements HoldingHistoryRepository {
    @Autowired
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Value("${com.cmc.asset.ddb.holdingHistoryTableName:portfolio_holdings_history_yearly}")
    private String holdingHistoryTableName;

    private DynamoDbTable<HoldingHistoryEntity> dynamoDbTable;

    @PostConstruct
    public void init() {
        dynamoDbTable = dynamoDbEnhancedClient.table(holdingHistoryTableName, TableSchema.fromBean(HoldingHistoryEntity.class));
    }

    @Override
    public void save(HoldingHistoryEntity historyEntity) {
        historyEntity.setHoldingAll(JacksonUtils.getInstance().serialize(historyEntity.getMapByDay()));
        dynamoDbTable.putItem(historyEntity);
    }

    @Override
    public BatchWriteResult deleteAll(Collection<HoldingHistoryEntity> toDeleteList) {
        WriteBatch.Builder<HoldingHistoryEntity> builder =
            WriteBatch.builder(HoldingHistoryEntity.class).mappedTableResource(dynamoDbTable);
        for (HoldingHistoryEntity entity : toDeleteList) {
            Key key = Key.builder().partitionValue(entity.getUserPortfolioSource()).sortValue(entity.getYear()).build();
            builder.addDeleteItem(key);
        }
        WriteBatch writeBatch = builder.build();
        BatchWriteItemEnhancedRequest batchWriteItemEnhancedRequest =
            BatchWriteItemEnhancedRequest.builder().writeBatches(writeBatch).build();
        return dynamoDbEnhancedClient.batchWriteItem(batchWriteItemEnhancedRequest);
    }

    @Override
    /**
     * max batch size is 25
     * */
    public BatchWriteResult saveAll(Collection<HoldingHistoryEntity> updatedSet) {
        WriteBatch.Builder<HoldingHistoryEntity> writeBatchBuilder =
            WriteBatch.builder(HoldingHistoryEntity.class)
                .mappedTableResource(dynamoDbTable);
        for (HoldingHistoryEntity entity : updatedSet) {
            entity.setHoldingAll(JacksonUtils.getInstance().serialize(entity.getMapByDay()));
            writeBatchBuilder.addPutItem(entity);
        }
        WriteBatch writeBatch = writeBatchBuilder.build();
        BatchWriteItemEnhancedRequest batchWriteItemEnhancedRequest =
            BatchWriteItemEnhancedRequest.builder().writeBatches(writeBatch).build();
        return dynamoDbEnhancedClient.batchWriteItem(batchWriteItemEnhancedRequest);
    }

    @Override
    public List<HoldingHistoryEntity> findAllByKeys(Collection<Key> keys) {
        if(CollectionUtils.isEmpty(keys)){
            return List.of();
        }
        ReadBatch.Builder<HoldingHistoryEntity> builder = ReadBatch.builder(HoldingHistoryEntity.class).mappedTableResource(dynamoDbTable);
        for (Key key : keys) {
            builder.addGetItem(key);
        }
        ReadBatch readBatch = builder.build();
        BatchGetItemEnhancedRequest batchGetItemEnhancedRequest = BatchGetItemEnhancedRequest.builder()
            .readBatches(readBatch)
            .build();
     return dynamoDbEnhancedClient.batchGetItem(batchGetItemEnhancedRequest).resultsForTable(dynamoDbTable).stream().collect(Collectors.toList());
    }

    @Override
    public List<HoldingHistoryEntity> getByPartitionKey(String partitionKey) {
        return Lists.newArrayList(dynamoDbTable
            .query(r -> r.queryConditional(QueryConditional.keyEqualTo(k -> k.partitionValue(partitionKey)))).items());
    }
}
