package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface PortfolioMultiRepositoryCustom {

    Flux<PortfolioMultiEntity> findPortfolioByType(ObjectId userId, String portfolioSourceId, PortfolioTypeEnum portfolioTypeEnum);

    Mono<UpdateResult> updateThirdPartyId(ObjectId id, ObjectId thirdPartyId);

    Mono<DeleteResult> deleteFirstByUserIdAndPortfolioSourceId(ObjectId objectId, String portfolioSourceId);

    Mono<UpdateResult> updateById(ObjectId id, String state);

    Mono<UpdateResult> updateMainById(ObjectId id, Boolean main);

    Mono<UpdateResult> updateSortByUserId(ObjectId userId, Integer sortIndexGt);

}
