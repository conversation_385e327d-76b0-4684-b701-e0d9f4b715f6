package com.cmc.asset.dao.entity.portfolio;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.domain.Persistable;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

/**
 * portfolio_snapshot_5m entity
 * <AUTHOR> ricky.x
 * @date : 2023/6/14 15:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "portfolio_snapshot_5m")
public class PortfolioSnapshot5mPO implements Persistable<ObjectId> {
    @Id
    private ObjectId id;

    private ObjectId userId;

    private String portfolioSourceId;

    private Date recordTime;

    private List<CryptoDetailPO> cryptoDetails;

    private Date timeUpdated;

    private Date timeCreated;

    @Override
    @JsonIgnore
    public boolean isNew() {
        return false;
    }
}
