package com.cmc.asset.dao.repository.mongo.guest;

import com.cmc.asset.dao.entity.GuestWatchListEntity;
import com.cmc.data.common.utils.ExtUtils;
import com.cmc.framework.utils.CollectionUtils;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/1/13
 */
public class GuestWatchListRepositoryCustomImpl implements GuestWatchListRepositoryCustom {

    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;
    @Override
    public Mono<GuestWatchListEntity> deleteByGuestId(Long guestId) {
        return reactiveMongoTemplate
            .findAndRemove(Query.query(Criteria.where("guestId").is(guestId)),
                GuestWatchListEntity.class);
    }

    @Override
    public Mono<GuestWatchListEntity> updateCryptosByGuestId(Long guestId, boolean isAdd, Set<Integer> cryptos) {
        Update update =new Update();
        update.setOnInsert("guestId", guestId);
        update.setOnInsert("createdTime", ExtUtils.nowUTC());
        if (isAdd) {
            update.push("cryptos").each(cryptos);
        } else {
            update.pullAll("cryptos", cryptos.toArray());
        }
        update.set("updatedTime", ExtUtils.nowUTC());
        return reactiveMongoTemplate.findAndModify(Query.query(Criteria.where("guestId").is(guestId)),
            update, FindAndModifyOptions.options().upsert(true).returnNew(true), GuestWatchListEntity.class);
    }

    @Override
    public Mono<GuestWatchListEntity> updateDexPairsByGuestId(Long guestId, boolean isAdd, Set<Long> dexPairs, Set<String> dexTokens) {
        Update update =new Update();
        update.setOnInsert("guestId", guestId);
        update.setOnInsert("createdTime", ExtUtils.nowUTC());
        if (isAdd) {
            update.push("dexPairs").each(dexPairs);
        } else {
            update.pullAll("dexPairs", dexPairs.toArray());
        }
        if(CollectionUtils.isNotEmpty(dexTokens)){
            if (isAdd) {
                update.push("dexTokens").each(dexTokens);
            } else {
                update.pullAll("dexTokens", dexTokens.toArray());
            }
        }
        update.set("updatedTime", ExtUtils.nowUTC());
        return reactiveMongoTemplate.findAndModify(Query.query(Criteria.where("guestId").is(guestId)),
            update, FindAndModifyOptions.options().upsert(true).returnNew(true), GuestWatchListEntity.class);
    }

    @Override
    public Mono<GuestWatchListEntity> updateDexTokenByGuestId(Long guestId, boolean isAdd, Set<String> dexTokens) {
        Update update =new Update();
        update.setOnInsert("guestId", guestId);
        update.setOnInsert("createdTime", ExtUtils.nowUTC());
        if (isAdd) {
            update.push("dexTokens").each(dexTokens);
        } else {
            update.pullAll("dexTokens", dexTokens.toArray());
        }
        update.set("updatedTime", ExtUtils.nowUTC());
        return reactiveMongoTemplate.findAndModify(Query.query(Criteria.where("guestId").is(guestId)),
                update, FindAndModifyOptions.options().upsert(true).returnNew(true), GuestWatchListEntity.class);
    }

}
