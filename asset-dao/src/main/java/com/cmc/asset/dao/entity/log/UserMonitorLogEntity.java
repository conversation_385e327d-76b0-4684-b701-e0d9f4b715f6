package com.cmc.asset.dao.entity.log;

import com.cmc.asset.dao.BaseMongoEntity;
import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_monitor_log")
public class UserMonitorLogEntity extends BaseMongoEntity<ObjectId> {

    @Id
    private ObjectId id;
    private String userId;
    private String sourceType;
    private String sourceId;
    private String flowId;
    private String ip;
    private String countryCode;
    private String fvideoid;
    private Date createTime;
    private Date updateTime;
}
