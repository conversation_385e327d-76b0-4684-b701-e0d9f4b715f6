package com.cmc.asset.dao.entity;

import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_point_task_log")
public class UserPointTaskLogEntity {
    @Id
    private ObjectId id;
    private Integer taskType;
    private Integer configType;
    private String userId;
    private String taskId;
    private String configId;
    private Integer score;
    private Date logDate;
    private Date updateDate;
    private Date createdDate;
}
