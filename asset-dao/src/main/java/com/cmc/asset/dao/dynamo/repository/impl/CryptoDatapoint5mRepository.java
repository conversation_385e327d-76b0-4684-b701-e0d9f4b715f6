package com.cmc.asset.dao.dynamo.repository.impl;

import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint5mEntity;
import com.cmc.asset.dao.entity.dynamondb.PkResolver;
import com.cmc.asset.model.common.Constant;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.UnicastProcessor;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchGetItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.ReadBatch;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Component
@ConditionalOnBean(value = {DynamoDbEnhancedAsyncClient.class, DynamoDbAsyncClient.class,
    DynamoDbAsyncTable.class}, name = "cryptoDatapoint5mTable")
@Slf4j
public class CryptoDatapoint5mRepository {

    @Autowired
    private DynamoDbAsyncClient dynamoDbAsyncClient;

    @Autowired
    private DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient;

    @Autowired
    private DynamoDbAsyncTable<CryptoDatapoint5mEntity> cryptoDataPoint5MDynamoDbAsyncTable;

    @Value("${com.cmc.asset.crypto-5m.batch-get-size:98}")
    private Integer batchGetSize;

    public Mono<List<CryptoDatapoint5mEntity>> getCryptoDatapointByTimeRange(Integer cryptoId, Long startTime,
        Long endTime) {
        UnicastProcessor<Map<String, AttributeValue>> messageProcessor = UnicastProcessor.create();
        AtomicLong pageSize = new AtomicLong(0);
        Mono<List<CryptoDatapoint5mEntity>> mono = messageProcessor.flatMap(
            exclusiveStartKey -> getCryptoDatapointByPage(cryptoId, startTime, endTime, exclusiveStartKey).doOnError(
                error -> messageProcessor.sink()
                    .error(error))
                .doOnNext(page -> {
                    if (page == null || page.lastEvaluatedKey() == null) {
                        messageProcessor.sink()
                            .complete();
                    } else {
                        messageProcessor.sink()
                            .next(page.lastEvaluatedKey());
                    }
                }))
            .collectList()
            .map(pageList -> {
                List<CryptoDatapoint5mEntity> cryptoDatapoint5mEntityList = new ArrayList<>();
                pageList.forEach(page -> {
                    if (CollectionUtils.isNotEmpty(page.items())) {
                        cryptoDatapoint5mEntityList.addAll(page.items());
                    }
                });
                cryptoDatapoint5mEntityList.sort(Comparator.comparing(CryptoDatapoint5mEntity::getScore));
                return cryptoDatapoint5mEntityList;
            })
            .doOnError(throwable -> {
                log.error(
                    "com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint5mRepository#getCryptoDatapointByTimeRange error",
                    throwable);
                messageProcessor.sink()
                    .error(throwable);
            });
        messageProcessor.sink()
            .next(Maps.newHashMap());
        return mono;
    }

    public Mono<List<CryptoDatapoint5mEntity>> getLatestCryptoDataByRange(Integer cryptoId, Long startTime, Long endTime) {
        Map<String, AttributeValue> expressionAttributeValues = new HashMap<String, AttributeValue>();
        expressionAttributeValues.put(":val", AttributeValue.builder()
            .n(cryptoId.toString())
            .build());
        expressionAttributeValues.put(":start", AttributeValue.builder()
            .n(startTime.toString())
            .build());
        expressionAttributeValues.put(":end", AttributeValue.builder()
            .n(endTime.toString())
            .build());
        QueryRequest queryRequest = QueryRequest.builder()
            .tableName(cryptoDataPoint5MDynamoDbAsyncTable.tableName())
            .keyConditionExpression("id = :val and score between :start and :end")
            .expressionAttributeValues(expressionAttributeValues)
            .scanIndexForward(false)
            .limit(1)
            .build();
        return Mono.fromFuture(dynamoDbAsyncClient.query(queryRequest))
            .map(QueryResponse::items)
            .map(items -> items.stream()
                .map(cryptoDataPoint5MDynamoDbAsyncTable.tableSchema()::mapToItem)
                .collect(Collectors.toList()))
            .doOnError(throwable -> log.error(
                "com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint5mRepository#getLatestCryptoDataByRange",
                throwable));
    }

    private Mono<Page<CryptoDatapoint5mEntity>> getCryptoDatapointByPage(Integer cryptoId, Long startTime, Long endTime,
        Map<String, AttributeValue> exclusiveStartKey) {
        Key startKey = Key.builder()
            .partitionValue(cryptoId)
            .sortValue(startTime)
            .build();
        Key endKey = Key.builder()
            .partitionValue(cryptoId)
            .sortValue(endTime)
            .build();
        QueryEnhancedRequest request;
        if (MapUtils.isNotEmpty(exclusiveStartKey)) {
            request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBetween(startKey, endKey))
                .exclusiveStartKey(exclusiveStartKey)
                .limit(100)
                .build();
        } else {
            request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBetween(startKey, endKey))
                .limit(100)
                .build();
        }
        return Mono.from(cryptoDataPoint5MDynamoDbAsyncTable.query(request))
            .doOnError(throwable -> log.error(
                "com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint5mRepository#getCryptoDatapointByPage",
                throwable));
    }

    public Mono<List<CryptoDatapoint5mEntity>> getOldestCryptoDataPoint(Integer cryptoId) {
        Map<String, AttributeValue> expressionAttributeValues = new HashMap<String, AttributeValue>();
        expressionAttributeValues.put(":val", AttributeValue.builder()
            .n(cryptoId.toString())
            .build());
        QueryRequest queryRequest = QueryRequest.builder()
            .tableName(cryptoDataPoint5MDynamoDbAsyncTable.tableName())
            .keyConditionExpression("id = :val")
            .expressionAttributeValues(expressionAttributeValues)
            .scanIndexForward(true)
            .limit(1)
            .build();
        return Mono.fromFuture(dynamoDbAsyncClient.query(queryRequest))
            .map(QueryResponse::items)
            .map(items -> items.stream()
                .map(cryptoDataPoint5MDynamoDbAsyncTable.tableSchema()::mapToItem)
                .collect(Collectors.toList()))
            .doOnError(throwable -> log.error(
                "com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint5mRepository#getOldestCryptoDataPoint",
                throwable));
    }

    public Mono<List<CryptoDatapoint5mEntity>> getCryptoDataPoint5m(int id, List<Long> timeCodeList) {
        if (CollectionUtils.isEmpty(timeCodeList)) {
            return Mono.just(Lists.newArrayList());
        }
        return Flux.fromIterable(timeCodeList)
                .buffer(batchGetSize)
                .flatMap(timeCodes -> batchGetMono(id, timeCodes, timeCodes.size()))
                .flatMapIterable(lists -> lists)
                .collectSortedList(Comparator.comparing(PkResolver::getScore));
    }

    public Mono<List<CryptoDatapoint5mEntity>> batchGetMono(Integer cryptoId, List<Long> scores, Integer limit) {
        if(CollectionUtils.isEmpty(scores)){
            return Mono.just(Lists.newArrayList());
        }
        ReadBatch.Builder<CryptoDatapoint5mEntity> dataPointEntityBuilder = ReadBatch.builder(CryptoDatapoint5mEntity.class)
                .mappedTableResource(cryptoDataPoint5MDynamoDbAsyncTable);
        for (Long score : scores) {
            Key key = Key.builder()
                    .partitionValue(cryptoId + Constant.UNDERLINE + score)
                    .build();
            dataPointEntityBuilder.addGetItem(key);
        }
        ReadBatch readBatch = dataPointEntityBuilder.build();
        BatchGetItemEnhancedRequest batchGetItemEnhancedRequest = BatchGetItemEnhancedRequest.builder()
                .readBatches(readBatch)
                .build();
        return Mono.from(dynamoDbEnhancedAsyncClient.batchGetItem(batchGetItemEnhancedRequest).limit(limit))
                .map(data -> data.resultsForTable(cryptoDataPoint5MDynamoDbAsyncTable));
    }

}