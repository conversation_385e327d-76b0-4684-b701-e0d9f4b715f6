package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.audit.UserAuditEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
public interface UserAuditRepository
    extends ReactiveMongoRepository<UserAuditEntity, ObjectId>, ReactiveCrudRepository<UserAuditEntity, ObjectId>,
    UserAuditRepositoryCustom {

    Mono<UserAuditEntity> findFirstByIdAndStatusNot(ObjectId objectId, Integer status);

    Mono<UserAuditEntity> findFirstByUserIdAndType(String userId, Integer type);
}
