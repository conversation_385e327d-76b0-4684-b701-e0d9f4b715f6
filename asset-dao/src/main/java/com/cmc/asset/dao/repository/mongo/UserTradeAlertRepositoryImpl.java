package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.alert.UserTradeAlertEntity;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2023/2/2 下午6:19
 * @description
 */
@Repository
public class UserTradeAlertRepositoryImpl implements UserTradeAlertRepositoryCustom {
    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;
    @Autowired
    @Qualifier("primaryReactiveMongoTemplate")
    private ReactiveMongoTemplate primaryReactiveMongoTemplate;

    @Override
    public Mono<UserTradeAlertEntity> upsert(String userId, Long uid, String bizId, String type, Integer threshold,
        String extraInfo) {
        Update update = new Update();
        update.set("updatedTime", new Date());
        update.setOnInsert("createdTime", new Date());
        update.set("threshold", threshold);
        update.set("extraInfo", extraInfo);
        update.set("uid", uid);

        Query query = Query.query(Criteria.where("userId").is(userId).and("bizId").is(bizId).and("type").is(type));

        return reactiveMongoTemplate.findAndModify(query,
            update,
            FindAndModifyOptions.options().upsert(true).returnNew(true),
            UserTradeAlertEntity.class);
    }

    @Override
    public Mono<Boolean> remove(String userId, String bizId, String type) {
        Query query = Query.query(Criteria.where("userId").is(userId).and("bizId").is(bizId).and("type").is(type));
        return reactiveMongoTemplate.remove(query, UserTradeAlertEntity.class)
            .map(deleteResult -> deleteResult.getDeletedCount() > 0);
    }


    @Override
    public Flux<UserTradeAlertEntity> find(String userId, String type) {
        Query query = Query.query(Criteria.where("userId").is(userId).and("type").is(type));
        return primaryReactiveMongoTemplate.find(query, UserTradeAlertEntity.class);
    }
}
