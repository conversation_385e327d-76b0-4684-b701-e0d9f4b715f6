package com.cmc.asset.dao.dynamo.repository.impl;

import com.cmc.asset.dao.dynamo.entity.HoldingHistoryEntity;
import com.cmc.asset.dao.dynamo.repository.AsyncHoldingHistoryRepository;
import java.util.List;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/5/18
 */
@Component
@ConditionalOnBean({DynamoDbAsyncClient.class, DynamoDbEnhancedAsyncClient.class})
public class AsyncHoldingHistoryRepositoryImpl implements AsyncHoldingHistoryRepository {
    @Autowired
    private DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient;

    @Value("${com.cmc.asset.ddb.holdingHistoryTableName:portfolio_holdings_history_yearly}")
    private String holdingHistoryTableName;

    private DynamoDbAsyncTable<HoldingHistoryEntity> dynamoDbTable;

    @PostConstruct
    public void init() {
        dynamoDbTable = dynamoDbEnhancedAsyncClient.table(holdingHistoryTableName, TableSchema.fromBean(HoldingHistoryEntity.class));
    }

    @Override
    public Mono<HoldingHistoryEntity> getByKey(Key key) {
        return Mono.fromFuture(dynamoDbTable.getItem(key));
    }

    @Override
    public Mono<List<HoldingHistoryEntity>> getByPartitionKey(String partitionKey){
        return Mono.from(dynamoDbTable
            .query(r -> r.queryConditional(QueryConditional.keyEqualTo(k -> k.partitionValue(partitionKey)))))
            .map(Page::items);
    }
}
