package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyAssetHistoryAggregatedEntity;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Range;
import reactor.core.publisher.Flux;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface PortfolioThirdPartyAssetHistory5mRepositoryCustom {

    Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity> aggregationByTime(ObjectId thirdPartyId, Range<Date> dateRange);

    Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity> aggregationByMinTime(ObjectId thirdPartyId, Date startDate, boolean includeStart, Integer cryptoId);

}
