package com.cmc.asset.dao.entity.votecountry;

import java.util.Date;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/6/24 下午4:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_predict_country_crypto")
public class UserPredictCountryCryptoEntity {

    @Id
    private ObjectId id;

    private String userId;

    private Set<String> countries;

    private List<PredictInfoEntity> predictInfo;

    private String activityId;

    private ClaimInfoEntity claimInfo;

    private Date createDate;
}
