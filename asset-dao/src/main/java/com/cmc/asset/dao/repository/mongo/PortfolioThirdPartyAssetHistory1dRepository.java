package com.cmc.asset.dao.repository.mongo;

import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyAssetHistory1dEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/5 15:24
 */
@Repository
public interface PortfolioThirdPartyAssetHistory1dRepository
    extends ReactiveMongoRepository<PortfolioThirdPartyAssetHistory1dEntity, ObjectId>,
    ReactiveCrudRepository<PortfolioThirdPartyAssetHistory1dEntity, ObjectId>,
    PortfolioThirdPartyAssetHistory1dRepositoryCustom {

    /**
     * Find asset history records within specified time range
     * @param thirdPartyId Third-party account ID
     * @param startTime Start time
     * @param endTime End time
     * @return Flux of asset history records
     */
    Flux<PortfolioThirdPartyAssetHistory1dEntity> findByThirdPartyIdAndSyncTimeBetween(
            ObjectId thirdPartyId,
            Date startTime,
            Date endTime
    );


}
