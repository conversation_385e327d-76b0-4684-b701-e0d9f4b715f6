package com.cmc.asset.integration;

import com.cmc.asset.model.contract.user.UserAvatarSyncRequestDTO;
import com.cmc.asset.model.contract.useraudit.SyncAuditRequestDTO;
import com.cmc.auth.common.enums.ErrorCode;
import com.cmc.data.common.ApiResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * Gravity api client
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class GravityApiClient extends BaseServiceClient {

    @Value("${com.cmc.asset.client.gravity}")
    private String gravityBaseUrl;

    public Mono<Boolean> syncUserBannerRef(UserAvatarSyncRequestDTO syncRequestDTO) {
        String url = gravityBaseUrl + "/system/v3/gravity/profile/banner/sync";
        return client.post(url, syncRequestDTO)
                     .map(response -> {
                         ApiResponse<Boolean> apiResponse = JSON.parseObject(response.getBody(), new TypeReference<>() {
                         });
                         if (apiResponse != null && (ErrorCode.SUCCESS.getCode() + "").equals(apiResponse.getStatus().getError_code())
                             && apiResponse.getData() != null) {
                             return apiResponse.getData();
                         }
                         return false;
                     })
                     .defaultIfEmpty(false)
                     .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
                     .doOnError(throwable -> log.error("sync user avatar ref failed", throwable));
    }

    public Mono<Boolean> syncAuditResult(SyncAuditRequestDTO syncRequestDTO) {
        if (syncRequestDTO == null) {
            return Mono.just(false);
        }
        log.info("syncAuditResult:{}", syncRequestDTO);
        String url = gravityBaseUrl + "/system/v3/gravity/syncAuditResult";
        return client.post(url, syncRequestDTO)
                     .map(response -> {
                         ApiResponse<Boolean> apiResponse = JSON.parseObject(response.getBody(), new TypeReference<>() {
                         });
                         if (apiResponse != null && (ErrorCode.SUCCESS.getCode() + "").equals(apiResponse.getStatus().getError_code())
                             && apiResponse.getData() != null) {
                             return apiResponse.getData();
                         }
                         return false;
                     })
                     .defaultIfEmpty(false)
                     .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
                     .doOnError(throwable -> log.error("sync user avatar ref failed", throwable));
    }

}
