package com.cmc.asset.integration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cmc.asset.domain.client.content.SensitiveWordCheckParamDTO;
import com.cmc.asset.domain.client.content.SensitiveWordCheckResultDTO;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.data.common.ApiResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/20 10:40
 * @description
 */
@Component
public class ContentServiceClient extends BaseServiceClient {

    @Value("${com.cmc.asset.client.content}")
    private String url;

    /**
     * get the crypto map
     *
     * @return
     */
    public Mono<Boolean> checkSenitiveWord(List<String> texts) {

        if (CollectionUtils.isEmpty(texts)) {
            return Mono.just(false);
        }
        SensitiveWordCheckParamDTO request = new SensitiveWordCheckParamDTO();
        request.setTexts(texts);
        return client.post(url + "/v3/sensitive/word/check", request)
                .map(response -> JSON.parseObject(response.getBody(), new TypeReference<ApiResponse<SensitiveWordCheckResultDTO>>() {
                }))
                .filter(f -> f.getData() != null)
                .map(data -> ExtUtils.getDefaultValue(data.getData().getState()))
                .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)));
    }
}
