package com.cmc.asset.integration;

import com.cmc.asset.model.common.Constant;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoAggregationVo;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoDetailCacheV3VO;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyQuotesLatestCacheV3VO;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyTiming;
import com.cmc.asset.model.contract.crypto.CryptoMarketPairDto;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.framework.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @package com.cmc.data.integration
 * @Classname BaseDataServiceClient
 * @Description get crypto currency data from baseDataService
 * <AUTHOR>
 * @Date 2021/11/30 14:53
 */
@Slf4j
@Component
public class BaseDataServiceClient extends BaseServiceClient {

    private static final String QUOTES_DETAIL_EXTRA_URL = "/system/v3/crypto/currency/quotes-detail-extra?ids=";
    private static final String DETAIL_ALL_URL = "/system/v3/crypto/currency/detail-all";
    private static final String DETAIL_PAGE_URL = "/system/v3/crypto/currency/detail-page";
    private static final String GET_ALL_CRYPTO_LATEST_TIMINGS = "/system/v3/detail-other/crypto/get-all-crypto-latest-timings";

    private static final String MARKET_PAIR_BY_EXCHANGE = "/system/v3/market-pair/get-per-exchange-market-pair-5m-sorting";
    private static final String MARKET_PAIR_LATEST_URL = "/system/v3/detail-other/market-pair-latest?ids=";
    private static final String LATEST_QUOTES_V3 = "/system/v3/latest/quotes/v3?ids=";

    @Value("${com.cmc.asset.client.base-data}")
    private String baseDataService;

    @Value("${com.cmc.assert-service.integration.query-batch:300}")
    private Integer batchSize;

    @Value("${com.cmc.asset-service.market_pairs_batch_size:300}")
    private Integer marketPairsBatchSize;

    @Value("${com.cmc.asset-service.market_pairs_default_sort:cmc_rank_advanced}")
    private String marketPairsDefaultSort;

    /**
     * get crypto currency details.
     * @return Flux<CryptoCurrencyInfoDetailCacheV3VO>
     */
    public Mono<List<CryptoCurrencyInfoDetailCacheV3VO>> getCryptoCurrencyDetailAll() {
        String targetUrl = baseDataService + DETAIL_ALL_URL;
        return client.getList(targetUrl, null, getHttpHeadersConsumer(), CryptoCurrencyInfoDetailCacheV3VO.class)
            .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
            .map(response -> {
                if (!response.getStatusCode().is2xxSuccessful()) {
                    log.warn("getCryptoCurrencyDetailAll failed : {}", response);
                    throw new BusinessException("getCryptoCurrencyDetailAll from baseDataService failed");
                }
                if (response.getBody() == null) {
                    log.warn("quotes-detail-extra null");
                    throw new BusinessException("getCryptoCurrencyDetailAll from baseDataService null");
                }
                log.debug("getCryptoCurrencyDetailAll,res:{}", response.getBody().size());
                return response.getBody();
            });
    }

    public Mono<Map<Integer, CryptoCurrencyTiming>> getAllCryptoLatestTimings() {
        String targetUrl = baseDataService + GET_ALL_CRYPTO_LATEST_TIMINGS;
        return client.get(targetUrl, null, getHttpHeadersConsumer())
            .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
            .map(response -> {
                if (!response.getStatusCode().is2xxSuccessful()) {
                    log.warn("getAllCryptoLatestTimings failed : {}", response);
                    throw new BusinessException("getAllCryptoLatestTimings from baseDataService failed");
                }
                if (response.getBody() == null) {
                    log.warn("getAllCryptoLatestTimings null");
                    throw new BusinessException("getAllCryptoLatestTimings from baseDataService null");
                }
                return JacksonUtils.deserialize(response.getBody(), new TypeReference<>() {
                });
            });
    }


    public Mono<List<CryptoCurrencyInfoDetailCacheV3VO>> getCryptoCurrencyDetailPage(Integer start, Integer size) {
        String targetUrl = baseDataService + DETAIL_PAGE_URL;
        String queryString = String.format("?start=%s&size=%d", start, size);
        return client.getList(targetUrl, queryString, getHttpHeadersConsumer(), CryptoCurrencyInfoDetailCacheV3VO.class)
            .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS))).map(response -> {
                if (!response.getStatusCode().is2xxSuccessful()) {
                    log.warn("getCryptoCurrencyDetailPage failed : {}", response);
                    throw new BusinessException("getCryptoCurrencyDetailPage from baseDataService failed");
                }
                if (response.getBody() == null) {
                    log.warn("getCryptoCurrencyDetailPage null");
                    throw new BusinessException("getCryptoCurrencyDetailPage from baseDataService null");
                }
                return response.getBody();
            });
    }

    /**
     * get crypto currency latest quotes detail extra.
     *
     * @param ids crypto currency id collection
     * @return Flux<CryptoCurrencyInfoAggregationVo>
     */
    public Mono<CryptoCurrencyInfoAggregationVo> getCryptoCurrencyQuotesDetailExtra(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Mono.empty();
        }
        return getCryptoCurrencyInfoAggregation(ids);
    }

    @NotNull
    private Mono<CryptoCurrencyInfoAggregationVo> getCryptoCurrencyInfoAggregation(Collection<Integer> ids) {
        if (ids.size() <= batchSize) {
            return getCrLatestQuotesAndDetailAndExtraByBatch(ids);
        }
        return Flux.fromIterable(ids)
            .window(batchSize)
            .flatMapSequential(integerFlux -> integerFlux.collectList()
                .flatMap(this::getCrLatestQuotesAndDetailAndExtraByBatch))
            .reduce((aggregationOne, aggregationTwo) -> {
                if (aggregationOne.getQuotesLatestCacheV3VOS() == null) {
                    aggregationOne.setQuotesLatestCacheV3VOS(Lists.newArrayList());
                }
                if (!CollectionUtils.isEmpty(aggregationTwo.getQuotesLatestCacheV3VOS())) {
                    aggregationOne.getQuotesLatestCacheV3VOS().addAll(aggregationTwo.getQuotesLatestCacheV3VOS());
                }
                if (aggregationOne.getDetailCacheV3VOs() == null) {
                    aggregationOne.setDetailCacheV3VOs(Lists.newArrayList());
                }
                if (!CollectionUtils.isEmpty(aggregationTwo.getDetailCacheV3VOs())) {
                    aggregationOne.getDetailCacheV3VOs().addAll(aggregationTwo.getDetailCacheV3VOs());
                }
                return aggregationOne;
            });
    }

    /**
     * get crypto currency details.
     * ids.size() should be less than batch_size
     * @param ids crypto currency id collection
     * @return Flux<CryptoCurrencyInfoAggregationVo>
     */
    private Mono<CryptoCurrencyInfoAggregationVo> getCrLatestQuotesAndDetailAndExtraByBatch(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids) || ids.size() > batchSize) {
            BusinessException.throwIfMessage("ids are null or ids.size > " + batchSize);
        }
        String targetUrl = baseDataService + QUOTES_DETAIL_EXTRA_URL + StringUtils.join(ids,',');
        return client.get(targetUrl, null, getHttpHeadersConsumer(), CryptoCurrencyInfoAggregationVo.class)
            .onErrorResume(e -> {
                log.error("getCrLatestQuotesAndDetailAndExtraByBatch error,url:{},error:",targetUrl,e);
                return Mono.error(e);
            })
            .retryWhen(Retry.max(2))
            .map(response -> {
                if (!response.getStatusCode().is2xxSuccessful()) {
                    log.warn("quotes-detail-extra failed : {}", response);
                    throw new BusinessException("query quotes from baseDataService failed");
                }
                if (response.getBody() == null) {
                    log.warn("quotes-detail-extra null");
                    throw new BusinessException("query quotes from baseDataService null");
                }
                return response.getBody();
            });
    }


    /**
     * 设置header：不采用压缩，因为内部网络测试时，压缩效率低
     * @return Consumer<HttpHeaders>
     */
    @NotNull
    private Consumer<HttpHeaders> getHttpHeadersConsumer() {
        return header -> header.put("Accept-Encoding", List.of());
    }

    public Mono<List<Integer>> getAllExchangeMarketPairByExchangeId(Integer exchangeId) {
        return this.getExchangeMarketPairByExchangeId(exchangeId, marketPairsDefaultSort, 0, -1);
    }

    /**
     * get the exchange's market pairs by category
     *
     * @param exchangeId
     * @param start
     * @param end
     * @return
     */
    public Mono<List<Integer>> getExchangeMarketPairByExchangeId(Integer exchangeId, String sort, int start, int end) {
        String targetUrl = baseDataService + MARKET_PAIR_BY_EXCHANGE;
        String apply = Constant.EXCHANGE_MARKET_PAIR_RANK.apply(exchangeId, sort);
        String queryString = String.format("?redisKey=%s&start=%s&end=%s", apply, start, end);
        return client.getList(targetUrl, queryString, getHttpHeadersConsumer(), Integer.class)
                     .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
                     .map(response -> {
                         if (!response.getStatusCode().is2xxSuccessful()) {
                             log.warn("query the exchange's market pairs failed : {}", response);
                             throw new BusinessException("query the exchange's market pairs from baseDataService failed");
                         }
                         if (response.getBody() == null) {
                             log.warn("query the exchange's market pairs null");
                             throw new BusinessException("query the exchange's market pairs from baseDataService null");
                         }
                         return response.getBody();
                     });
    }

    /**
     * query latest market pair info. (replace redis key 'v2:market-pairs-latest:5m')
     * @param ids market pair id, length of ids must smaller than 500
     * @return
     */
    public Mono<List<CryptoMarketPairDto>> getMarketPairs(Collection<Integer> ids) {
        String targetUrl = baseDataService + MARKET_PAIR_LATEST_URL + StringUtils.join(ids,',');
         return client.getList(targetUrl, null, getHttpHeadersConsumer(), CryptoMarketPairDto.class)
             .map(HttpEntity::getBody)
             .defaultIfEmpty(List.of())
             .onErrorResume(throwable -> {
                 log.warn("call : {} failed, ex is : {} ", targetUrl, throwable.getMessage());
                 return Mono.just(List.of());
             });

    }

    /**
     * batch getMarketPairs
     * @param ids ids
     * @return return
     */
    public Mono<List<CryptoMarketPairDto>> getMarketPairsBatch(Collection<Integer> ids) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(ids)) {
            return Mono.empty();
        }
        if (ids.size() <= batchSize) {
            return getMarketPairs(ids);
        }
        log.info("getMarketPairsBatch more than batchSize,tradeSize:{}", ids.size());
        return Flux.fromIterable(ids).window(marketPairsBatchSize)
            .flatMapSequential(integerFlux -> integerFlux.collectList().flatMap(this::getMarketPairs))
            .reduce((aggregationOne, aggregationTwo) -> {
                if (org.apache.commons.collections.CollectionUtils.isEmpty(aggregationOne)) {
                    aggregationOne = Lists.newArrayList();
                }
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(aggregationTwo)) {
                    aggregationOne.addAll(aggregationTwo);
                }
                return aggregationOne;
            });
    }

    public Mono<List<CryptoCurrencyQuotesLatestCacheV3VO>> queryLatestQuotesV3WithBatch(Collection<Integer> ids) {
        if (ids.size() <= batchSize) {
            return queryLatestQuotesV3(ids);
        }
        return Flux.fromIterable(ids)
                .buffer(batchSize)
                .flatMap(this::queryLatestQuotesV3)
                .collectList()
                .flatMap(lists -> Mono.just(lists.stream().flatMap(List::stream).collect(Collectors.toList())));
    }

    /**
     * query crypto latest quotes data by ids. (replace redis key 'v3:cr:latest-quotes:5m')
     * @param ids
     * @return
     */
    public Mono<List<CryptoCurrencyQuotesLatestCacheV3VO>> queryLatestQuotesV3(Collection<Integer> ids) {
        String targetUrl = baseDataService + LATEST_QUOTES_V3 + StringUtils.join(ids, ',');
        return client.getList(targetUrl, null, getHttpHeadersConsumer(), CryptoCurrencyQuotesLatestCacheV3VO.class)
                .onErrorResume(e -> {
                    log.error("queryLatestQuotesV3 error,url:{},error:", targetUrl, e);
                    return Mono.error(e);
                })
                .retryWhen(Retry.max(2))
                .map(response -> {
                    if (!response.getStatusCode().is2xxSuccessful()) {
                        log.warn("queryLatestQuotesV3 failed : {}", response);
                        throw new BusinessException("query quotes from baseDataService failed");
                    }
                    if (response.getBody() == null) {
                        log.warn("queryLatestQuotesV3 null");
                        throw new BusinessException("query quotes from baseDataService null");
                    }
                    return response.getBody();
                });
    }
}
