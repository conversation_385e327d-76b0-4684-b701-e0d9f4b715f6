package com.cmc.asset.integration;

import com.cmc.asset.domain.portfolio.thirdparty.BinanceAssetResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.BinanceCommonResponse;
import com.cmc.asset.domain.portfolio.thirdparty.BinanceUserInfoDTO;
import com.cmc.asset.utils.CommonUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2023/5/5 15:02
 */
@Component
@Slf4j
public class BinanceOAuth2ApiClient extends DefaultOAuth2ApiClient {

    public Mono<BinanceUserInfoDTO> getUserInfo(String uri, Map<String, String> params, Map<String, String> headers) {
        return commonGet(uri, params, headers, new TypeReference<>() {
        });
    }

    public Mono<List<BinanceAssetResponseDTO>> getAsset(String uri, Map<String, String> params,
        Map<String, String> headers) {
        return commonGet(uri, params, headers, new TypeReference<>() {
        });
    }

    public <T> Mono<T> commonGet(String uri, Map<String, String> params,
        TypeReference<BinanceCommonResponse<T>> responseType) {
        return commonGet(uri, params, null, responseType);
    }

    public <T> Mono<T> commonGet(String uri, Map<String, String> params, Map<String, String> headers,
        TypeReference<BinanceCommonResponse<T>> responseType) {
        Consumer<HttpHeaders> headersConsumer = httpHeaders -> {
            if (headers != null) {
                headers.forEach(httpHeaders::add);
            }
        };
        return client.get(CommonUtils.buildUrl(uri, params), headersConsumer).filter(stringResponseEntity -> {
            log.debug("get result, uri: {}, params: {}, response: {}", uri, params, stringResponseEntity.getBody());
            return stringResponseEntity.getStatusCode().is2xxSuccessful() && stringResponseEntity.getBody() != null;
        }).map(response -> JacksonUtils.deserialize(response.getBody(), responseType)).flatMap(res -> {
            if (res.getSuccess()) {
                return Mono.just(res.getData());
            } else {
                log.error("get error: uri: {}, params: {}, code = {}, message = {}", uri, params, res.getCode(),
                    res.getMessage());
                return Mono.empty();
            }
        });
    }

}
