package com.cmc.asset.integration;

import com.cmc.asset.model.contract.user.OldUserDTO;
import com.cmc.asset.model.contract.user.UserAvatarSyncRequestDTO;
import com.cmc.auth.common.enums.ErrorCode;
import com.cmc.data.common.ApiResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * user api client
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserApiClient extends BaseServiceClient {

    @Value("${com.cmc.asset.client.user}")
    private String userBaseUrl;
    @Value("${com.cmc.asset.client.user.timeout-millis:300}")
    private Integer defaultTimeout;

    public Mono<OldUserDTO> getUserInfo(String userId) {
        String url = userBaseUrl + "/system/v3/user-info/get?userId=%s";
        return client.get(String.format(url, userId))
            .map(response -> {
                ApiResponse<OldUserDTO> oldUserDTOApiResponse =
                    JSON.parseObject(response.getBody(), new TypeReference<>() {
                    });
                if (!(ErrorCode.SUCCESS.getCode() + "").equals(oldUserDTOApiResponse.getStatus().getError_code())) {
                    oldUserDTOApiResponse.setData(null);
                }
                return oldUserDTOApiResponse.getData();
            })
            .retryWhen(Retry.backoff(2, Duration.of(defaultTimeout, ChronoUnit.MILLIS)))
            .doOnError(throwable -> log.error("get userInfo failed", throwable));
    }

    public Mono<Boolean> syncUserAvatarRef(UserAvatarSyncRequestDTO syncRequestDTO) {
        String url = userBaseUrl + "/system/v3/user-info/avatar/sync";
        return client.post(url, syncRequestDTO)
                     .map(response -> {
                         ApiResponse<Boolean> apiResponse = JSON.parseObject(response.getBody(), new TypeReference<>() {
                         });
                         if (apiResponse != null && (ErrorCode.SUCCESS.getCode() + "").equals(apiResponse.getStatus().getError_code())
                             && apiResponse.getData() != null) {
                             return apiResponse.getData();
                         }
                         return false;
                     })
                     .defaultIfEmpty(false)
                     .retryWhen(Retry.backoff(2, Duration.of(defaultTimeout, ChronoUnit.MILLIS)))
                     .doOnError(throwable -> log.error("sync user avatar ref failed", throwable));
    }

}
