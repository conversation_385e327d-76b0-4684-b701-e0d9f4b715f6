package com.cmc.asset.integration;

import com.cmc.asset.domain.dex.BatchQueryTokenParamDTO;
import com.cmc.asset.domain.dex.BatchQueryTokenRespDTO;
import com.cmc.asset.domain.dex.DataHubTokenDetailDTO;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;


/**
 * DexDataHubClient
 * <AUTHOR> ricky.x
 * @date: 2025/6/6 15:12
 */
@Component
@Slf4j
public class DexDataHubClient extends BaseServiceClient {

    @Value("${com.cmc.asset.client.data.hub:https://dapi.coinmarketcap.com/}")
    private String url;

    private static final String BATCH_TOKEN_INFO_API = "dex/v1/tokens/batch-query";

    private static final String SUCCESS_CODE = "0";


    public Mono<List<DataHubTokenDetailDTO>> getTokenDetail(String platFormName, List<String> address) {
        if (CollectionUtils.isEmpty(address)) {
            return Mono.empty();
        }
        String endPoint = url.concat(BATCH_TOKEN_INFO_API);
        BatchQueryTokenParamDTO request = new BatchQueryTokenParamDTO();
        request.setPlatform(platFormName);
        request.setAddresses(address);
        return client.post(endPoint, request)
                .map(response -> JacksonUtils.deserialize(response.getBody(), BatchQueryTokenRespDTO.class))
                .map(responseDto -> {
                    if (responseDto == null || responseDto.getStatus() == null || !SUCCESS_CODE.equals(responseDto.getStatus().getError_code())) {
                        log.error("DexDataHubClient getTokenDetail failed, url:{}, param:{} resp:{}", endPoint, request, responseDto);
                        return new ArrayList<DataHubTokenDetailDTO>();
                    }
                    return responseDto.getData();
                })
                .onErrorResume(throwable -> {
                    log.error("DexDataHubClient getTokenDetail error, url:{}, param:{} msg:", endPoint, request, throwable);
                    return Mono.just(new ArrayList<>());
                })
                .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)));
    }
}
