package com.cmc.asset.integration;

import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.dexer.PlatformDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.data.common.ApiResponse;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * dexer api client
 */
@Component
@Slf4j
public class DexerApiClient extends BaseServiceClient {

    @Value("${com.cmc.asset.client.dexer}")
    private String dexerBaseUrl;

    @Value("${com.cmc.asset.job.integration.dex-query.url:}")
    private String dqueryServiceBaseUrl;

    public static final String DEXER_QUERY_POOL_API = "%s/system/v3/dexer/pools";
    public static final String DEXER_PLATFORM_API = "%s/v3/dexer/platforms";
    public static final String DQUERY_PLATFORM_API = "%s/v1/platform/list";
    public static final String DQUERY_BATCH_QUERY_TOKEN_API = "%s/v1/token/price/batch";

    public Mono<List<PairInfoDTO>> getPairs(List<Long> poolIdList) {
        final String url = String.format(DEXER_QUERY_POOL_API, dexerBaseUrl);

        // 分批处理每100个元素
        return Flux.fromIterable(partitionList(poolIdList, 100))
                .flatMap(subList ->
                        client.post(url, subList)
                                .flatMap(responseEntity -> {
                                    ApiResponse<List<PairInfoDTO>> response =
                                            JacksonUtils.deserialize(responseEntity.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {});
                                    return Mono.just(response.getData());
                                })
                                .onErrorResume(throwable -> {
                                    log.error("getPairs failed, url:{}, param:{}", url, subList, throwable);
                                    return Mono.just(new ArrayList<>());
                                })
                )
                .collectList()
                .map(lists -> lists.stream().flatMap(List::stream).collect(Collectors.toList()));
    }

    private <T> List<List<T>> partitionList(List<T> list, int size) {
        List<List<T>> partitioned = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitioned.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitioned;
    }

    public Mono<List<PlatformDTO>> getPlatforms() {
        final String url = String.format(DEXER_PLATFORM_API, dexerBaseUrl);
        return client.get(url)
                .filter(responseEntity -> responseEntity.getStatusCode().is2xxSuccessful() && StringUtils.isNotBlank(responseEntity.getBody()))
                .flatMap(responseEntity -> {
                    ApiResponse<List<PlatformDTO>> response =
                            JacksonUtils.deserialize(responseEntity.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {});
                    return Mono.just(response.getData());
                })
                .onErrorResume(throwable -> {
                    log.error("getPairs failed, url:{}", url, throwable);
                    return Mono.just(new ArrayList<>());
                });
    }

    public Mono<List<PlatformNewDTO>> getPlatformsFromDeQuery() {
        final String url = String.format(DQUERY_PLATFORM_API, dqueryServiceBaseUrl);
        return client.get(url)
                .filter(responseEntity -> responseEntity.getStatusCode().is2xxSuccessful() && StringUtils.isNotBlank(responseEntity.getBody()))
                .flatMap(responseEntity -> {
                    ApiResponse<List<PlatformNewDTO>> response =
                            JacksonUtils.deserialize(responseEntity.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {});
                    return Mono.just(response.getData());
                })
                .onErrorResume(throwable -> {
                    log.error("getPairs failed, url:{}", url, throwable);
                    return Mono.just(new ArrayList<>());
                });
    }

    public Mono<List<TokenPriceDTO>> getPairListByDeQuery(List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens) {
        if(CollectionUtils.isEmpty(tokens)){
            return Mono.just(List.of());
        }
        BatchPlatformTokenRequestDTO requestDTO = BatchPlatformTokenRequestDTO.builder().tokens(tokens).build();
        return client.post(String.format(DQUERY_BATCH_QUERY_TOKEN_API, dqueryServiceBaseUrl), requestDTO)
            .filter(res -> res.getStatusCode().is2xxSuccessful() && res.getBody() != null)
            .map(response -> {
                ApiResponse<List<TokenPriceDTO>> tokenPriceDTO =
                    JacksonUtils.deserialize(response.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {
                    });
                if (tokenPriceDTO != null && tokenPriceDTO.getData() != null) {
                    return tokenPriceDTO.getData();
                }
                return List.<TokenPriceDTO>of();
            })
            .retryWhen(Retry.backoff(2, Duration.of(3000, ChronoUnit.MILLIS)))
            .publishOn(Schedulers.boundedElastic())
            .doOnError(throwable -> log.error("dquery getPairList failed", throwable));
    }

}
