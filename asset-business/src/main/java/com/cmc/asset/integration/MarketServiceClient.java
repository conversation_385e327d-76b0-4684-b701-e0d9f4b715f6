package com.cmc.asset.integration;

import com.alibaba.fastjson.JSON;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.util.retry.Retry;

/**
 * <AUTHOR>
 * @date 2021-08-05 14:46:46
 * @description
 */
@Component
public class MarketServiceClient extends BaseServiceClient {

    @Value("${com.cmc.asset.client.market-service}")
    private String url;

    /**
     * get the stable coin  ids
     *
     * @return
     */
    public Flux<Integer> getStableCoin() {

        return client.get(String.format("%s/markets/cryptocurrency/getAllStablecoin", url))
            .map(response -> JSON.parseArray(response.getBody(), Integer.class))
            .flatMapIterable(data -> ObjectUtils.defaultIfNull(data, List.of()))
            .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)));
    }
}
