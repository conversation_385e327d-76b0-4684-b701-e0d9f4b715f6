package com.cmc.asset.utils;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * <AUTHOR>
 * @date 2022/12/20
 */
public class CommonUtils {

    public static List<Long> toLongList(String nums, String separatorChars) {
        if (StringUtils.isBlank(nums)) {
            return null;
        } else {
            String[] numArray = StringUtils.split(nums, separatorChars);
            List<Long> numInts = Lists.newArrayListWithCapacity(numArray.length);
            for (String s : numArray) {
                numInts.add(parseInt(s));
            }
            return numInts;
        }
    }

    public static List<String> toStringList(String nums, String separatorChars) {
        if (StringUtils.isBlank(nums)) {
            return null;
        } else {
            return List.of(StringUtils.split(nums, separatorChars));
        }
    }


    public static Integer getExistAssetSize(List<Long> dexPairs, Set<Integer> cryptos) {
        if (dexPairs == null && cryptos == null) {
            return 0;
        }
        if (dexPairs == null) {
            return cryptos.size();
        } else  {
            return dexPairs.size();
        }
    }

    public static Long parseInt(String str) {
        return !StringUtils.isBlank(str) && NumberUtils.isCreatable(str) ? Double.valueOf(str.trim()).longValue() : null;
    }

    public static String buildUrl(String url, Map<String, String> queryParams) {
        StringBuilder urlBuilder = new StringBuilder(url);
        if (queryParams != null && !queryParams.isEmpty()) {
            urlBuilder.append('?');
            urlBuilder.append(buildQueryString(queryParams));
        }
        return urlBuilder.toString();
    }

    public static String buildQueryString(Map<String, String> queryParams) {
        StringBuilder urlBuilder = new StringBuilder();

        if (queryParams != null && !queryParams.isEmpty()) {
            boolean first = true;

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (first) {
                    first = false;
                } else {
                    urlBuilder.append('&');
                }

                urlBuilder.append(entry.getKey());
                urlBuilder.append('=');
                urlBuilder.append(entry.getValue());
            }
        }

        return urlBuilder.toString();
    }

}
