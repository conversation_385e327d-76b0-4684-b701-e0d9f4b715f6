package com.cmc.asset.business.priceprediction.impl;

import com.alibaba.fastjson.JSON;
import com.cmc.asset.business.BaseService;
import com.cmc.asset.business.priceprediction.PricePredictionParamValidator;
import com.cmc.asset.business.priceprediction.PricePredictionService;
import com.cmc.asset.dao.entity.PricePredictionEntity;
import com.cmc.asset.dao.repository.mongo.PricePredictionRepository;
import com.cmc.asset.dao.repository.redis.CacheRepository;
import com.cmc.asset.message.SenderMessage;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.contract.priceprediction.*;
import com.cmc.asset.model.contract.task.UserDailyTaskDTO;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.data.common.utils.DatetimeUtils;
import com.cmc.data.common.utils.JacksonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;

/**
 * <AUTHOR> Jiang
 * @since 2021/4/20
 **/
@Service
@Slf4j
public class PricePredictionServiceImpl extends BaseService implements PricePredictionService {

    public static final int MAX_INCLUDE_MONTH_COUNT = 6;

    @Autowired(required = false)
    private PricePredictionRepository pricePredictionRepository;

    @Autowired
    private PricePredictionParamValidator newPredictionValidator;

    @Autowired
    private PricePredictionParamValidator queryPredictionValidator;

    @Autowired
    private QueryByUserIdParamValidator queryByUserIdParamValidator;

    @Autowired
    private UpdatePricePredictionValidator updatePricePredictionValidator;

    @Autowired(required = false)
    private CacheRepository cacheRepository;

    @Value("${cmc.asset.kafka.taskTopic}")
    public String TASK_TOPIC;

    @Override
    public Mono<PricePredictionDTO> save(CreatePricePredictionParamDTO param) {

        newPredictionValidator.validate(param);

        PricePredictionEntity entity = convertToEntity(param);

        Date now = ExtUtils.now();

        return pricePredictionRepository.countByUserIdAndCryptoIdAndCreateDateBetween(entity.getUserId(),entity.getCryptoId(), DatetimeUtils.getNowdayBegin(now),ExtUtils.getNowDayEnd(now))
                .map(val->val.equals(0L))
                .onErrorReturn(false)
                .flatMap(flag-> pricePredictionRepository.save(entity)
                    .map(this::convertToViewObject)
                    .doOnNext(result ->{
                        if(flag){
                            sendMQ(SenderMessage.builder().userId(entity.getUserId()).topic(TASK_TOPIC)
                                .data(UserDailyTaskDTO.builder().configType(7).userId(entity.getUserId()).taskResourceId(entity.getId().toHexString()).build()).build());
                        }
                        expirePricePredictionKey(result.getUserId(), result.getCryptoId()).subscribe();
                    }));
    }

    @Override
    public Mono<PricePredictionDTO> update(UpdatePricePredictionParamDTO param) {

        validateUpdateParams(param);

        return pricePredictionRepository.queryById(param.getId())
                .map(entity -> {

                    UpdatePricePredictionParamDTO paramDTO = new UpdatePricePredictionParamDTO();
                    paramDTO.setCryptoId(entity.getCryptoId());
                    paramDTO.setTargetMonth(entity.getTargetMonth());
                    paramDTO.setTargetYear(entity.getTargetYear());
                    paramDTO.setTargetUserId(entity.getUserId());
                    paramDTO.setOperator(param.getHeader().getUserId());
                    paramDTO.setPredictedPrice(param.getPredictedPrice());

                    updatePricePredictionValidator.validate(paramDTO);

                    return entity;
                })
                .then(pricePredictionRepository.updatePricePrediction(param.getId(), param.getPredictedPrice()))
                .map(this::convertToViewObject)
                .doOnNext(result ->
                        expirePricePredictionKey(result.getUserId(), result.getCryptoId()).subscribe()
                );

    }

    @Override
    public Mono<PricePredictionDTO> query(QueryPricePredictionParamDTO param) {

        queryPredictionValidator.validate(param);

        return pricePredictionRepository.queryPricePrediction(param)
                .map(this::convertToViewObject);
    }

    @Override
    public Mono<PricePredictionCombinedResultDTO> queryHalfYear(QueryPricePredictionParamDTO request) {

        queryByUserIdParamValidator.validate(request);


        List<DetailedPricePredictionDTO> nextHalfYearPredictions = constructPredictionsWindow(request);

        Mono<List<DetailedPricePredictionDTO>> presetPredictionsMono = Mono.just(nextHalfYearPredictions);

        Mono<List<PricePredictionEntity>> pricePredictionHalfYearMono
                = pricePredictionRepository.queryPricePredictionsInRecentHalfYearByUserId(request)
                .collectList()
                .defaultIfEmpty(Collections.emptyList())
                .onErrorResume(e -> Mono.just(Collections.emptyList()));

        String cacheKey = getPricePredictionLevelTwoCacheKey(request.getHeader().getUserId(), request.getCryptoId());

        Mono<PricePredictionCombinedResultDTO> queryFromDataSourceAndCacheResult
                //query data from mongodb
                = Mono.zip(presetPredictionsMono, pricePredictionHalfYearMono)
                .map(this::mergePricePredictions)
                //save query result to 2level cache
                .doOnNext(result -> {
                    String serializedResultDto = JSON.toJSONString(result);
                    cacheRepository.cacheInString(cacheKey, serializedResultDto, Duration.ofMinutes(5)).subscribe();
                });

        return Mono.just(cacheKey)
                //looking data from 2level cache first
                .flatMap(key -> cacheRepository.getCachedValueInString(key))
                .onErrorResume(e -> Mono.just(StringUtils.EMPTY))
                .map(cachedStr -> JacksonUtils.getInstance().deserialize(cachedStr, PricePredictionCombinedResultDTO.class))
                //if data is not found in 2level cache, try mongodb and save to 2level cache
                .switchIfEmpty(Mono.defer(() -> queryFromDataSourceAndCacheResult));
    }

    private PricePredictionCombinedResultDTO mergePricePredictions(Tuple2<List<DetailedPricePredictionDTO>, List<PricePredictionEntity>> tuple) {

        List<DetailedPricePredictionDTO> details = tuple.getT1();
        List<PricePredictionEntity> predictionEntities = tuple.getT2();

        constructPricePredictions(details, predictionEntities);

        return PricePredictionCombinedResultDTO.builder()
                .predictions(details)
                .build();
    }

    private void constructPricePredictions(List<DetailedPricePredictionDTO> details, List<PricePredictionEntity> entities) {

        boolean hasPredictions = CollectionUtils.isNotEmpty(entities);

        for (DetailedPricePredictionDTO detail : details) {

            if (hasPredictions) {

                Optional<PricePredictionEntity> matchingPrediction = findMatchingPrediction(detail, entities);

                matchingPrediction.ifPresent(prediction -> {
                    detail.setYourPrice(prediction.getPredictedPrice());
                    detail.setPredictionId(prediction.getId().toHexString());
                });
            }
        }
    }

    private Optional<PricePredictionEntity> findMatchingPrediction(DetailedPricePredictionDTO dto, List<PricePredictionEntity> entities) {

        return entities.stream().filter(entity ->
                entity.getTargetMonth().equals(dto.getTargetMonth())
                        && entity.getTargetYear().equals(dto.getTargetYear()))
                .findFirst();
    }

    private List<DetailedPricePredictionDTO> constructPredictionsWindow(QueryPricePredictionParamDTO request) {

        List<DetailedPricePredictionDTO> result = Lists.newArrayListWithCapacity(6);

        LocalDate today = LocalDate.now(ZoneOffset.UTC);

        for (int i = 0; i < MAX_INCLUDE_MONTH_COUNT; i++) {

            LocalDate nextMonth = today.plusMonths(i);
            LocalDate nextMonthTime = ExtUtils.getLastTimeEndOfSpecificMonth(nextMonth);

            assert nextMonthTime != null;

            Date targetDate = ExtUtils.toUtcDate(nextMonthTime);

            result.add(
                    DetailedPricePredictionDTO.builder()
                            .targetYear(nextMonthTime.getYear())
                            .targetMonth(nextMonthTime.getMonthValue())
                            .targetDate(targetDate)
                            .build()
            );
        }

        return result;
    }

    private void validateUpdateParams(UpdatePricePredictionParamDTO param) {
        if (Objects.isNull(param.getPredictedPrice()) || param.getPredictedPrice().compareTo(BigDecimal.ZERO) < 0) {
            BusinessException.throwIfMessage(MessageCode.SYS_PARAMETER_ERROR.getCode(), "Not valid crypto price.");
        }

        if (StringUtils.isEmpty(param.getId())) {
            BusinessException.throwIfMessage(MessageCode.SYS_PARAMETER_ERROR.getCode(), "Id not provided.");
        }
    }

    private PricePredictionEntity convertToEntity(CreatePricePredictionParamDTO param) {

        LocalDate middleOfMonth = LocalDate.of(param.getTargetYear(), param.getTargetMonth(), 15);
        LocalDate lastTimeEndOfSpecificMonth = ExtUtils.getLastTimeEndOfSpecificMonth(middleOfMonth);

        assert lastTimeEndOfSpecificMonth != null;

        Date specificDate = Date.from(lastTimeEndOfSpecificMonth.atStartOfDay(ZoneOffset.UTC).toInstant());

        return PricePredictionEntity.builder()
                .createDate(new Date())
                .lastModifyDate(new Date())
                .cryptoId(param.getCryptoId())
                .predictedPrice(param.getPredictedPrice())
                .userId(param.getHeader().getUserId())
                .targetYear(param.getTargetYear())
                .targetMonth(param.getTargetMonth())
                .targetDate(specificDate)
                .build();
    }

    public PricePredictionDTO convertToViewObject(PricePredictionEntity entity) {

        return PricePredictionDTO.builder()
                .id(entity.getId().toString())
                .userId(entity.getUserId())
                .cryptoId(entity.getCryptoId())
                .predictedPrice(entity.getPredictedPrice())
                .targetMonth(entity.getTargetMonth())
                .targetYear(entity.getTargetYear())
                .build();
    }

    private String getPricePredictionLevelTwoCacheKey(String userId, Integer cryptoId) {
        return String.format("%s-%d", userId, cryptoId);
    }

    private Mono<Long> expirePricePredictionKey(String userId, Integer cryptoId) {

        String cacheKey = getPricePredictionLevelTwoCacheKey(userId, cryptoId);

        return cacheRepository.delete(cacheKey);
    }
}
