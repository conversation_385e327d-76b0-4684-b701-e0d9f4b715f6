package com.cmc.asset.business.oauth.impl;

import com.cmc.asset.config.OAuth2Config;
import com.cmc.asset.domain.portfolio.thirdparty.GetAccessTokenResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.ThirdPartyAssetDTO;
import com.cmc.asset.domain.portfolio.thirdparty.ThirdPartyUserInfoDTO;
import com.cmc.asset.integration.OkxOAuth2ApiClient;
import com.cmc.asset.model.enums.OAuth2ProviderType;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OkxOAuth2Client extends AbstractOAuth2Client {

    @Value("${com.cmc.asset.integration.okx.yubibao.enable:false}")
    private Boolean enableYuBiBao;
    @Value("${com.cmc.asset.integration.okx.earnCoin.enable:true}")
    private Boolean enableEarnCoin;

    private OkxOAuth2ApiClient okxOAuth2ApiClient;

    public OkxOAuth2Client(OAuth2Config properties, OkxOAuth2ApiClient okxOAuth2ApiClient) {
        super(properties, okxOAuth2ApiClient);
        this.okxOAuth2ApiClient = okxOAuth2ApiClient;
    }

    @Override
    public OAuth2ProviderType getProvider() {
        return OAuth2ProviderType.OKX;
    }

    @Override
    public Mono<ThirdPartyUserInfoDTO> getUserInfo(String accessToken) {
        // uid can be found by /api/v5/account/config, but it's not necessary
        String openId = getUidByToken(accessToken);
        return Mono.just(ThirdPartyUserInfoDTO.builder().openId(openId).build());
    }

    @Override
    public Mono<GetAccessTokenResponseDTO> exchangeCodeForAccessToken(String code, Boolean isApp) {
        Map<String, String> params =
                Map.of("grant_type", "authorization_code", "code", code, "client_id", registration.getClientId(),
                        "client_secret", registration.getClientSecret());
        return okxOAuth2ApiClient.exchangeCodeForAccessToken(provider.getTokenUri(), params);
    }

    @Override
    public Mono<List<ThirdPartyAssetDTO>> getAsset(String accessToken, String openId) {
        return Mono.zip(getTradeAccountAsset("/api/v5/account/balance", accessToken).defaultIfEmpty(List.of()),
                getFundAccountAsset("/api/v5/asset/balances", accessToken).defaultIfEmpty(List.of()),
                getYuBiBaoAsset("/api/v5/finance/savings/balance", accessToken).defaultIfEmpty(List.of()),
                getEarnCoinAsset("/api/v5/finance/staking-defi/orders-active", accessToken).defaultIfEmpty(List.of())).map(tuple -> {
            List<ThirdPartyAssetDTO> tradeAccountAsset = tuple.getT1();
            List<ThirdPartyAssetDTO> fundAccountAsset = tuple.getT2();
            List<ThirdPartyAssetDTO> yuBiBaoAsset = tuple.getT3();
            List<ThirdPartyAssetDTO> earnCoinAsset = tuple.getT4();

            List<ThirdPartyAssetDTO> resultList = new ArrayList<>();
            resultList.addAll(tradeAccountAsset);
            resultList.addAll(fundAccountAsset);
            resultList.addAll(yuBiBaoAsset);
            resultList.addAll(earnCoinAsset);

            return this.aggregateDTO(resultList);
        });
    }

    private List<ThirdPartyAssetDTO> aggregateDTO(List<ThirdPartyAssetDTO> originalList) {
        return originalList.stream()
                .filter(Objects::nonNull)
                .filter(dto -> StringUtils.isNotBlank(dto.getCryptoSymbol()))
                .collect(Collectors.groupingBy(dto -> Optional.ofNullable(dto.getCryptoSymbol()).orElse("")
                        + "_" + Optional.ofNullable(dto.getCryptoName()).orElse("")))
                .values().stream()
                .map(thirdPartyAssetDTOs -> {
                    ThirdPartyAssetDTO newDto = new ThirdPartyAssetDTO();
                    thirdPartyAssetDTOs.stream().findFirst().ifPresent(dto -> {
                        newDto.setCryptoSymbol(dto.getCryptoSymbol());
                        newDto.setCryptoName(dto.getCryptoName());
                    });
                    BigDecimal sumBalance = thirdPartyAssetDTOs.stream()
                            .filter(Objects::nonNull)
                            .map(ThirdPartyAssetDTO::getBalance)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    newDto.setBalance(sumBalance);
                    return newDto;
                }).collect(Collectors.toList());
    }

    private Mono<List<ThirdPartyAssetDTO>> getTradeAccountAsset(String api, String accessToken) {
        return okxOAuth2ApiClient.getSubAsset(provider.getBaseUri() + api, Map.of(),
                Map.of("Authorization", "Bearer " + accessToken)).map(okxAssetResponseDTO -> {
            if (CollectionUtils.isEmpty(okxAssetResponseDTO)) {
                return List.of();
            }
            return okxAssetResponseDTO.stream().flatMap(dto -> dto.getDetails().stream())
                    .map(asset -> ThirdPartyAssetDTO.builder()
                            .cryptoName(asset.getCcy())
                            .cryptoSymbol(asset.getCcy())
                            .balance(new BigDecimal(asset.getCashBal()))
                            .value(new BigDecimal(asset.getEqUsd()))
                            .build())
                    .collect(Collectors.toList());
        });
    }

    private Mono<List<ThirdPartyAssetDTO>> getFundAccountAsset(String api, String accessToken) {
        return okxOAuth2ApiClient.getSubBalancesAsset(provider.getBaseUri() + api, Map.of(),
                Map.of("Authorization", "Bearer " + accessToken)).map(okxAssetResponseDTO -> {
            if (CollectionUtils.isEmpty(okxAssetResponseDTO)) {
                return List.of();
            }
            return okxAssetResponseDTO.stream()
                    .map(asset -> ThirdPartyAssetDTO.builder()
                            .cryptoName(asset.getCcy())
                            .cryptoSymbol(asset.getCcy())
                            .balance(new BigDecimal(asset.getBal()))
                            .build())
                    .collect(Collectors.toList());
        });
    }

    private Mono<List<ThirdPartyAssetDTO>> getYuBiBaoAsset(String api, String accessToken) {
        if (!enableYuBiBao) {
            return Mono.just(List.of());
        }
        return okxOAuth2ApiClient.getSubFinanceAsset(provider.getBaseUri() + api, Map.of(), Map.of("Authorization", "Bearer " + accessToken))
                .map(okxAssetResponseDTO -> {
                    if (CollectionUtils.isEmpty(okxAssetResponseDTO)) {
                        return List.of();
                    }
                    return okxAssetResponseDTO.stream()
                            .map(asset -> ThirdPartyAssetDTO.builder()
                                    .cryptoName(asset.getCcy())
                                    .cryptoSymbol(asset.getCcy())
                                    .balance(new BigDecimal(asset.getAmt()))
                                    .build())
                            .collect(Collectors.toList());
                });
    }

    private Mono<List<ThirdPartyAssetDTO>> getEarnCoinAsset(String api, String accessToken) {
        if (!enableEarnCoin) {
            return Mono.just(List.of());
        }
        return okxOAuth2ApiClient.getSubFinanceStakingAsset(provider.getBaseUri() + api, Map.of(),
                Map.of("Authorization", "Bearer " + accessToken)).map(okxAssetResponseDTO -> {
            if (CollectionUtils.isEmpty(okxAssetResponseDTO)) {
                return List.of();
            }
            return okxAssetResponseDTO.stream().flatMap(dto -> dto.getInvestData().stream())
                    .map(asset -> ThirdPartyAssetDTO.builder()
                            .cryptoName(asset.getCcy())
                            .cryptoSymbol(asset.getCcy())
                            .balance(new BigDecimal(asset.getAmt()))
                            .build())
                    .collect(Collectors.toList());
        });
    }

    private static String getUidByToken(String token) {
        try {
            String[] parts = token.split("\\.");
            String payloadBase64 = parts[1];
            byte[] payloadBytes = Base64.getUrlDecoder().decode(payloadBase64);
            String payload = new String(payloadBytes, StandardCharsets.UTF_8);
            if (StringUtils.isNotBlank(payload)) {
                return JacksonUtils.readTree(payload).get("uid").asText("");
            } else {
                return null;
            }
        } catch (NumberFormatException e) {
            log.error("json web token getUidByToken failed");
            return null;
        }
    }

}