package com.cmc.asset.business.portfolio.manager;

import com.cmc.asset.business.portfolio.IPortfolioTradeStrategy;
import com.cmc.asset.business.portfolio.strategy.PortfolioStrategyBeanFactory;
import com.cmc.asset.dao.repository.mongo.PortfolioPLRepository;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioStatDTO;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import com.cmc.framework.utils.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.function.Supplier;

/**
 * portfolio 盈亏率相关manager
 * <AUTHOR> ricky.x
 * @date : 2023/2/21 下午6:03
 */
@Service
@Slf4j
public class PortfolioValueManager {

    @Resource
    @Qualifier("assetRedisTemplate")
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;

    @Autowired
    private PortfolioStrategyBeanFactory portfolioStrategyBeanFactory;

    @Resource
    private PortfolioPLRepository portfolioPLRepository;

    @Value("${com.cmc.asset-service.portfolio.cache-time-mills:30000}")
    private Integer cacheTimeMills;

    /**
     * getCurrentValue
     * @param userId userId
     * @param source source
     * @return BigDecimal
     */
    public Mono<BigDecimal> getCurrentValue(ObjectId userId, String source, PortfolioTypeEnum type, Integer cryptoUnit) {
        IPortfolioTradeStrategy tradeStrategy = portfolioStrategyBeanFactory.getTradeService(type);
        return wrapperCache(userId, source, RedisConstants.KEY_PORTFOLIO_MULTI_AMOUNT,
                () -> tradeStrategy.portfolioTotalValue(userId, source, cryptoUnit));
    }

    /**
     * yesterday value
     * @param userId userId
     * @param source source
     * @return BigDecimal
     */
    public Mono<BigDecimal> getYesterdayValue(ObjectId userId, String source, PortfolioTypeEnum typeEnum, Integer cryptoUnit) {
        return wrapperCache(userId, source, RedisConstants.KEY_PORTFOLIO_MULTI_YESTERDAY_AMOUNT,
                () -> portfolioStrategyBeanFactory.getTradeService(typeEnum)
                        .getYesterdayHoldingsV2(userId.toHexString(), source, ExtUtils.getNear5minTime(ExtUtils.now()), cryptoUnit));
    }

    /**
     * Wrapper method to handle caching logic.
     * @param userId userId
     * @param source source
     * @param cacheKeyPattern cache key pattern
     * @param fallback function to retrieve value if not in cache
     * @return BigDecimal
     */
    private Mono<BigDecimal> wrapperCache(ObjectId userId, String source, String cacheKeyPattern, Supplier<Mono<BigDecimal>> fallback) {
        String cacheKey = String.format(cacheKeyPattern, userId.toHexString(), source);
        return assetRedisTemplate.opsForValue().get(cacheKey)
                .map(BigDecimal::new)
                .switchIfEmpty(Mono.defer(() -> fallback.get()
                        .defaultIfEmpty(BigDecimal.ZERO)
                        .onErrorReturn(BigDecimal.ZERO)
                        .publishOn(Schedulers.boundedElastic())
                        .doOnNext(value -> assetRedisTemplate.opsForValue().set(cacheKey, value.toString(), Duration.ofMillis(cacheTimeMills)).subscribe())));
    }

    public Mono<PortfolioStatDTO> getAllTimePL(ObjectId userId, String source, PortfolioTypeEnum typeEnum, Integer cryptoUnit) {
        return portfolioStrategyBeanFactory.getTradeService(typeEnum)
                .getAllTimePL(userId.toHexString(), source, ExtUtils.getNear5minTime(ExtUtils.now()), cryptoUnit)
                .defaultIfEmpty(PortfolioStatDTO.builder().build())
                .onErrorReturn(PortfolioStatDTO.builder().build());
    }

    /**
     * hasBuySpent
     * @param userId userId
     * @param source source
     * @return BigDecimal
     */
    public Mono<Boolean> hasBuySpent(ObjectId userId, String source,PortfolioTypeEnum typeEnum) {
        if(typeEnum == PortfolioTypeEnum.WALLET){
            return Mono.just(true);
        }
        return portfolioPLRepository.findByUserIdAndPortfolioSourceId(userId, source)
            .flatMap(plEntity -> {
                if(CollectionUtils.isEmpty(plEntity.getPortfolios())){
                    return Mono.just(false);
                }
                BigDecimal allTotalBuy = plEntity.getPortfolios().stream().map
                    (e -> MathUtils.getOrZero(e.getTotalBuyV2())).reduce(BigDecimal.ZERO, BigDecimal::add);
                if(allTotalBuy.compareTo(BigDecimal.ZERO) > 0){
                    return Mono.just(true);
                }
                return Mono.just(false);
            }).defaultIfEmpty(Boolean.FALSE);
    }
}
