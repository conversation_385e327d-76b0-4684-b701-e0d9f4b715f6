package com.cmc.asset.business.priceprediction.impl;

import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import com.cmc.asset.business.priceprediction.PricePredictionParamValidator;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.cache.StableCoinCache;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.model.contract.priceprediction.BasePricePredictionParamDTO;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.exception.BusinessException;

/**
 * <AUTHOR>
 * @since 2021/4/23
 **/
public class BasePricePredictionValidator implements PricePredictionParamValidator {

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;
    @Autowired
    private StableCoinCache stableCoinCache;

    @Override
    public void validate(BasePricePredictionParamDTO request) {

        Integer targetYear = request.getTargetYear();
        Integer targetMonth = request.getTargetMonth();

        if (Objects.isNull(request.getCryptoId()) ||
                Objects.isNull(targetYear) ||
                Objects.isNull(targetMonth)) {

            BusinessException.throwIfMessage(MessageCode.SYS_PARAMETER_ERROR.getCode(), "Invalid parameters");
        }

        //validate target month and year
        if (targetMonth > 12 || targetMonth < 1) {
            BusinessException.throwIfMessage(MessageCode.SYS_PARAMETER_ERROR.getCode(), "Invalid target month");
        }

        if (targetYear < PRICE_PREDICTION_START_YEAR) {
            BusinessException.throwIfMessage(MessageCode.SYS_PARAMETER_ERROR.getCode(), "Invalid target year");
        }

        CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(request.getCryptoId());

        if (Objects.isNull(cryptoCurrencyInfoDTO)) {
            BusinessException.throwIfMessage(MessageCode.SYS_VALID.getCode(), "The crypto is not found.");
        }

        Integer id = stableCoinCache.getById(request.getCryptoId());

        if (!Objects.isNull(id)) {  //稳定币不参与估价
            BusinessException.throwIfMessage(MessageCode.SYS_VALID.getCode(), "The crypto is stable coin.");
        }


    }
}
