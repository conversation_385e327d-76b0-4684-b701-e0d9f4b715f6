package com.cmc.asset.business.portfolio.impl;

import static com.cmc.asset.model.common.Constant.UTC_TIME_PATTERN;
import static com.cmc.asset.model.contract.portfolio.aggregation.PortfolioChangePeriodEnum.getPortfolioChangePeriodEnum;

import com.cmc.asset.business.portfolio.IPortfolioAggregationService;
import com.cmc.asset.business.portfolio.IPortfolioDashboardService;
import com.cmc.asset.business.portfolio.IPortfolioQueryService;
import com.cmc.asset.business.portfolio.IPortfolioService;
import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.model.common.CurrencyUtils;
import com.cmc.asset.model.common.PortfolioPageVo;
import com.cmc.asset.model.contract.portfolio.PortfolioAggrAllocationRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioAggrAllocationResponse;
import com.cmc.asset.model.contract.portfolio.PortfolioAssetAllocationRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartQueryDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQueryResultDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQuerySummaryRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioQuerySummaryResponse;
import com.cmc.asset.model.contract.portfolio.PortfolioStatisticsDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.asset.model.contract.portfolio.WalletSummaryDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.HoldingDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.ManualSummaryResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioLineChartQueryDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioLineHistoricalChartResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioStatDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioStatisticsResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsQueryRequest;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsQueryResponse;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsRequest;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsResponse;
import com.cmc.asset.model.contract.portfolio.aggregation.ThirdPartySummaryResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.WalletSummaryResponseDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.HoldingsQueryRequestDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryLineChartRequestDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryLineChartResponseDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryPieChartRequestDTO;
import com.cmc.asset.model.contract.portfolio.thirdparty.ThirdPartySummaryDTO;
import com.cmc.asset.model.converter.PortfolioAggrConverter;
import com.cmc.asset.model.enums.ErrorCodeEnums;
import com.cmc.asset.model.enums.PortfolioCryptoVerificationStatus;
import com.cmc.asset.model.enums.PortfolioModuleEnum;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import com.cmc.asset.model.enums.SortByEnum;
import com.cmc.asset.model.enums.SortTypeEnum;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.framework.utils.MathUtils;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * PortfolioAggregationServiceImpl
 * <AUTHOR> ricky.x
 * @date : 2023/4/12 下午6:58
 */
@Service
@Slf4j
public class PortfolioAggregationServiceImpl implements IPortfolioAggregationService {

    @Autowired
    private IPortfolioDashboardService dashboardService;
    @Autowired
    private IPortfolioService portfolioService;
    @Autowired
    private IPortfolioQueryService portfolioQueryService;
    @Autowired
    private PortfolioBaseService portfolioBaseService;
    @Autowired
    private CurrencyPriceService currencyPriceService;
    @Value("${com.cmc.asset-service.portfolio_aggr.allocation_limit:4}")
    private Integer allocationLimit;
    @Value("${com.cmc.asset-service.portfolio_aggr.point-size:90}")
    private Integer pointSize;

    @Override
    public Mono<PortfolioAggrAllocationResponse> aggrAssetAllocation(PortfolioAggrAllocationRequest request) {
        String userId = request.getHeader().getUserId();
        PortfolioModuleEnum moduleEnum = PortfolioModuleEnum.findByCode(request.getModuleType());
        String portfolioSourceId = request.getPortfolioSourceId();
        if (checkAssetAllocation(moduleEnum)) {
            return Mono.error(new BusinessException(ErrorCodeEnums.PARAMS_CHECK_ERROR));
        }
        //查询dashBord
        if (moduleEnum == PortfolioModuleEnum.DASHBOARD || StringUtils.isBlank(portfolioSourceId)) {
            QueryPieChartRequestDTO requestDTO = new QueryPieChartRequestDTO();
            requestDTO.getHeader().setUserId(userId);
            return dashboardService.queryPieChart(requestDTO).flatMap(
                response -> Mono.just(PortfolioAggrConverter.buildAllocationResponse(response, allocationLimit)));
        }
        //查询portfolio
        PortfolioAssetAllocationRequest assetAllocationRequest = new PortfolioAssetAllocationRequest();
        assetAllocationRequest.getHeader().setUserId(userId);
        assetAllocationRequest.setPortfolioSourceId(portfolioSourceId);
        assetAllocationRequest.setChainId(request.getChainId());
        assetAllocationRequest.setCryptoVerificationStatus(request.getCryptoVerificationStatus());
        return portfolioService.assetAllocation(assetAllocationRequest)
            .flatMap(response -> Mono.just(PortfolioAggrConverter.buildAllocationResponse(response, allocationLimit)));
    }

    private boolean checkAssetAllocation(PortfolioModuleEnum moduleEnum){
        return moduleEnum == null;
    }

    private Mono<Map<Integer, PortfolioCryptoInfoDTO>> getConvertQuotes(List<Integer> cryptoIds) {
        if (CollectionUtils.isEmpty(cryptoIds)) {
            return Mono.just(Map.of());
        }
        return currencyPriceService.queryCurrentPrices(cryptoIds);
    }

    @Override
    public Mono<PortfolioSummaryStatisticsResponse> querySummaryStatistics(
            PortfolioSummaryStatisticsRequest querySummaryRequest) {

//        querySummaryRequest.setCryptoVerificationStatus(PortfolioCryptoVerificationStatus.VERIFIED.getCode());
        List<Integer> cryptoIds = List.of(querySummaryRequest.getConvertCryptoId(), querySummaryRequest.getConvertFiatId());

        return portfolioService.querySummaryForApp(getPortfolioDTO(querySummaryRequest).getT1())
                .flatMap(portfolioSummary ->
                        portfolioService.queryStatistics(getPortfolioDTO(querySummaryRequest).getT2())
                                .zipWith(this.getConvertQuotes(cryptoIds))
                                .map(tuple -> {
                                    var portfolioStatistics = tuple.getT1();
                                    var convertQuotes = tuple.getT2();

                                    return PortfolioSummaryStatisticsResponse.builder()
                                            .portfolioSummary(covertPortfolioSummaryDTO(portfolioSummary, convertQuotes.get(querySummaryRequest.getConvertCryptoId()), convertQuotes.get(querySummaryRequest.getConvertFiatId()), querySummaryRequest.getChangePeriod()))
                                            .portfolioStatistics(covertPortfolioStatisticsDTO(portfolioStatistics, portfolioSummary))
                                            .build();
                                })
                );
    }


    @Override
    public Mono<PortfolioSummaryStatisticsQueryResponse> querySummaryStatisticsSimple(
            PortfolioSummaryStatisticsQueryRequest querySummaryRequest) {
        String userId = querySummaryRequest.getHeader().getUserId();
        String portfolioSourceId = querySummaryRequest.getPortfolioSourceId();
        String module = StringUtils.defaultIfEmpty(querySummaryRequest.getModule(), "");
        querySummaryRequest.setCryptoVerificationStatus(PortfolioCryptoVerificationStatus.VERIFIED.getCode());
        List<Integer> cryptoIds = List.of(querySummaryRequest.getConvertCryptoId(), querySummaryRequest.getConvertFiatId());
        Integer assetLimit = querySummaryRequest.getAssetLimit();

        HoldingsQueryRequestDTO requestDTO = HoldingsQueryRequestDTO.builder()
                .sortBy(SortByEnum.HOLDINGS.getCode())
                .sortType(SortTypeEnum.DESC.getCode())
                .assetLimit(assetLimit)
                .build();
        Mono<PortfolioStatDTO> statMono;
        if (module.contains("statistics")) {
            statMono = portfolioQueryService.getStatistics(userId, portfolioSourceId);
        } else {
            statMono = Mono.just(PortfolioStatDTO.builder().build());
        }
        Mono<List<HoldingDTO>> holdingsListMono;
        if (module.contains("asset")) {
            holdingsListMono = portfolioQueryService.getHoldingsList(requestDTO, userId, portfolioSourceId).defaultIfEmpty(List.of());
        } else {
            holdingsListMono = Mono.just(List.of());
        }
        Mono<PortfolioMultiEntity> multiEntityMono;
        if (StringUtils.isNotBlank(portfolioSourceId)) {
            multiEntityMono = portfolioBaseService.getAndRefreshCache(userId, portfolioSourceId);
        } else {
            multiEntityMono = Mono.just(PortfolioMultiEntity.builder().portfolioName("overview").build());
        }

        return multiEntityMono.flatMap(multiEntity -> Mono.zip(statMono, holdingsListMono, this.getConvertQuotes(cryptoIds))
                .map(tuple -> {
                    PortfolioStatDTO statDTO = tuple.getT1();
                    List<HoldingDTO> holdingDTOList = tuple.getT2();
                    Map<Integer, PortfolioCryptoInfoDTO> cryptoInfoDTOMap = tuple.getT3();

                    PortfolioCryptoInfoDTO fiatInfo = cryptoInfoDTOMap.get(querySummaryRequest.getConvertFiatId());
                    PortfolioCryptoInfoDTO cryptoInfoDTO = cryptoInfoDTOMap.get(querySummaryRequest.getConvertCryptoId());

                    holdingDTOList.forEach(holdingDTO -> {
                        Optional.ofNullable(fiatInfo)
                                .map(PortfolioCryptoInfoDTO::transferChangePerCent)
                                .map(changePercent -> getRelativeChangeRate(holdingDTO.getPriceChangePercent24h(),
                                        changePercent))
                                .ifPresent(holdingDTO::setFiatPriceChangePercent24h);
                        Optional.ofNullable(cryptoInfoDTO)
                                .map(PortfolioCryptoInfoDTO::transferChangePerCent)
                                .map(changePercent -> getRelativeChangeRate(holdingDTO.getPriceChangePercent24h(),
                                        changePercent))
                                .ifPresent(holdingDTO::setCryptoPriceChangePercent24h);
                    });

                    return PortfolioSummaryStatisticsQueryResponse.builder()
                            .portfolioSourceId(multiEntity.getPortfolioSourceId())
                            .portfolioName(multiEntity.getPortfolioName())
                            .portfolioStatistics(statDTO)
                            .assetList(holdingDTOList)
                            .build();
                })
                .flatMap(res -> portfolioBaseService.asynQueryCryptoInfo(querySummaryRequest, res, multiEntity.getCryptoUnit())));
    }

    private Tuple2<PortfolioQuerySummaryRequest, PortfolioCryptoDTO> getPortfolioDTO(
        PortfolioSummaryStatisticsRequest querySummaryRequest) {
        String portfolioSourceId = querySummaryRequest.getPortfolioSourceId();
        Integer fiatUnit = querySummaryRequest.getConvertFiatId();
        Integer cryptoUnit = querySummaryRequest.getConvertCryptoId();
        Integer chainId = querySummaryRequest.getChainId();
        String userId = querySummaryRequest.getHeader().getUserId();
        String channel = querySummaryRequest.getHeader().getChannel();
        String appVersion = querySummaryRequest.getHeader().getAppVersion();
        // summary request
        PortfolioQuerySummaryRequest portfolioQuerySummaryRequest = new PortfolioQuerySummaryRequest();
        portfolioQuerySummaryRequest.setPortfolioSourceId(portfolioSourceId);
        portfolioQuerySummaryRequest.setFiatUnit(fiatUnit);
        portfolioQuerySummaryRequest.setCryptoUnit(cryptoUnit);
        portfolioQuerySummaryRequest.setChainId(chainId);
        portfolioQuerySummaryRequest.getHeader().setUserId(userId);
        portfolioQuerySummaryRequest.getHeader().setChannel(channel);
        portfolioQuerySummaryRequest.getHeader().setAppVersion(appVersion);
        portfolioQuerySummaryRequest.setPageSize(Integer.MAX_VALUE);
        portfolioQuerySummaryRequest.setCurrentPage(1);

        // statistics request
        PortfolioCryptoDTO portfolioCryptoDTO = new PortfolioCryptoDTO();
        portfolioCryptoDTO.setPortfolioSourceId(portfolioSourceId);
        portfolioCryptoDTO.setFiatUnit(fiatUnit);
        portfolioCryptoDTO.setChainId(chainId);
        portfolioCryptoDTO.setCryptoUnit(cryptoUnit);
        portfolioCryptoDTO.getHeader().setUserId(userId);
        portfolioCryptoDTO.getHeader().setChannel(channel);
        portfolioCryptoDTO.getHeader().setAppVersion(appVersion);
        if (querySummaryRequest.getCryptoVerificationStatus() != null) {
            portfolioQuerySummaryRequest.setCryptoVerificationStatus(querySummaryRequest.getCryptoVerificationStatus());
            portfolioCryptoDTO.setCryptoVerificationStatus(querySummaryRequest.getCryptoVerificationStatus());
        } else {
            portfolioCryptoDTO.setCryptoVerificationStatus(PortfolioCryptoVerificationStatus.VERIFIED.getCode());
            portfolioQuerySummaryRequest.setCryptoVerificationStatus(PortfolioCryptoVerificationStatus.VERIFIED.getCode());
        }
        return Tuples.of(portfolioQuerySummaryRequest, portfolioCryptoDTO);
    }

    private Tuple2<PortfolioQuerySummaryRequest, PortfolioCryptoDTO> getPortfolioDTO(
        PortfolioSummaryStatisticsQueryRequest querySummaryRequest) {
        String portfolioSourceId = querySummaryRequest.getPortfolioSourceId();
        Integer fiatUnit = querySummaryRequest.getConvertFiatId();
        Integer cryptoUnit = querySummaryRequest.getConvertCryptoId();
        Integer chainId = querySummaryRequest.getChainId();
        String userId = querySummaryRequest.getHeader().getUserId();
        String channel = querySummaryRequest.getHeader().getChannel();
        String appVersion = querySummaryRequest.getHeader().getAppVersion();
        // summary request
        PortfolioQuerySummaryRequest portfolioQuerySummaryRequest = new PortfolioQuerySummaryRequest();
        portfolioQuerySummaryRequest.setPortfolioSourceId(portfolioSourceId);
        portfolioQuerySummaryRequest.setFiatUnit(fiatUnit);
        portfolioQuerySummaryRequest.setCryptoUnit(cryptoUnit);
        portfolioQuerySummaryRequest.setChainId(chainId);
        portfolioQuerySummaryRequest.getHeader().setUserId(userId);
        portfolioQuerySummaryRequest.getHeader().setChannel(channel);
        portfolioQuerySummaryRequest.getHeader().setAppVersion(appVersion);
        portfolioQuerySummaryRequest.setPageSize(Integer.MAX_VALUE);
        portfolioQuerySummaryRequest.setCurrentPage(1);
        portfolioQuerySummaryRequest.setCryptoVerificationStatus(PortfolioCryptoVerificationStatus.VERIFIED.getCode());
        // statistics request
        PortfolioCryptoDTO portfolioCryptoDTO = new PortfolioCryptoDTO();
        portfolioCryptoDTO.setPortfolioSourceId(portfolioSourceId);
        portfolioCryptoDTO.setFiatUnit(fiatUnit);
        portfolioCryptoDTO.setChainId(chainId);
        portfolioCryptoDTO.setCryptoUnit(cryptoUnit);
        portfolioCryptoDTO.getHeader().setUserId(userId);
        portfolioCryptoDTO.getHeader().setChannel(channel);
        portfolioCryptoDTO.getHeader().setAppVersion(appVersion);
        portfolioCryptoDTO.setCryptoVerificationStatus(PortfolioCryptoVerificationStatus.VERIFIED.getCode());
        return Tuples.of(portfolioQuerySummaryRequest, portfolioCryptoDTO);
    }


    private PortfolioStatisticsResponseDTO covertPortfolioStatisticsDTO(PortfolioStatisticsDTO portfolioStatisticsDTO, PortfolioQuerySummaryResponse portfolioQuerySummaryResponse) {
        List<PortfolioPageVo<PortfolioQueryResultDTO>> manualSummary = portfolioQuerySummaryResponse.getManualSummary();
        boolean hasBuySpent;
        if (CollectionUtils.isEmpty(manualSummary)) {
            hasBuySpent = true;
        }else {
            PortfolioPageVo<PortfolioQueryResultDTO> portfolioQueryResultDTOPortfolioPageVo = manualSummary.get(0);
            List<PortfolioQueryResultDTO> list = Objects.isNull(portfolioQueryResultDTOPortfolioPageVo) ? null : portfolioQueryResultDTOPortfolioPageVo.getList();
            if (CollectionUtils.isEmpty(list)) {
                hasBuySpent = true;
            }else {
                hasBuySpent = list.stream()
                        .anyMatch(portfolioPageVo -> portfolioPageVo.getTotalBuySpent() != null && portfolioPageVo.getTotalBuySpent()
                                .compareTo(BigDecimal.ZERO) > 0);
            }
        }

        PortfolioStatisticsResponseDTO portfolioStatisticsResponseDTO = new PortfolioStatisticsResponseDTO();
        portfolioStatisticsResponseDTO.setCryptoUnitPrice(portfolioStatisticsDTO.getCryptoUnitPrice());
        portfolioStatisticsResponseDTO.setFiatUnitPrice(portfolioStatisticsDTO.getFiatUnitPrice());
        portfolioStatisticsResponseDTO.setCurrentTotalHoldings(portfolioStatisticsDTO.getCurrentTotalHoldings());
        BigDecimal yesterdayChangeBalance = portfolioStatisticsDTO.getYesterdayChangeBalance();
        portfolioStatisticsResponseDTO.setPreChangeValue(yesterdayChangeBalance);
        portfolioStatisticsResponseDTO.setPreChangePercent(portfolioStatisticsDTO.getYesterdayBalancePercent());
        BigDecimal preTotalHoldings = Objects.nonNull(yesterdayChangeBalance) ? portfolioStatisticsDTO.getCurrentTotalHoldings()
            .subtract(yesterdayChangeBalance) : BigDecimal.ZERO;
        portfolioStatisticsResponseDTO.setPreTotalHoldings(preTotalHoldings);
        portfolioStatisticsResponseDTO.setHasBuySpent(BooleanUtils.isTrue(hasBuySpent));
        String lastUpdated =portfolioStatisticsDTO.getLastUpdated();
        String portfolioType = portfolioQuerySummaryResponse.getPortfolioType();
        if (PortfolioTypeEnum.WALLET.getCode().equals(portfolioType)
            && CollectionUtils.isNotEmpty(portfolioQuerySummaryResponse.getWalletSummary())) {
            lastUpdated = Optional.ofNullable(portfolioQuerySummaryResponse.getWalletSummary().get(0))
                .map(u -> {
                    if(u.getLastUpdateTime() == null){
                        return DatetimeUtils.nowISOString();
                    }
                    return DateFormatUtils.format(u.getLastUpdateTime(), UTC_TIME_PATTERN);
                })
                .orElse(DatetimeUtils.nowISOString());
        } else if (PortfolioTypeEnum.MANUAL.getCode().equals(portfolioType)) {
            portfolioStatisticsResponseDTO.setPlValue(portfolioStatisticsDTO.getTotalPlValue());
            portfolioStatisticsResponseDTO.setPlPercentValue(portfolioStatisticsDTO.getTotalPlpercentValue());
            portfolioStatisticsResponseDTO.setTotalBuySpent(portfolioStatisticsDTO.getAllTotalBuySpent());
            portfolioStatisticsResponseDTO.setRealizedProfit(portfolioStatisticsDTO.getRealizedProfit());
            portfolioStatisticsResponseDTO.setUnrealizedProfit(portfolioStatisticsDTO.getUnrealizedProfit());

        }
        portfolioStatisticsResponseDTO.setLastUpdated(StringUtils.isEmpty(lastUpdated) ? DatetimeUtils.nowISOString() : lastUpdated);
        portfolioStatisticsResponseDTO.setNeedReauthorize(portfolioStatisticsDTO.getNeedReauthorize());
        return portfolioStatisticsResponseDTO;
    }

    private BigDecimal getRelativeChangeRate(BigDecimal changePercent1, BigDecimal changePercent2) {
        return CurrencyUtils.getRelativeChangeRate(changePercent1, changePercent2);
    }

    private PortfolioSummaryResponseDTO covertPortfolioSummaryDTO(PortfolioQuerySummaryResponse portfolioStatisticsDTO,PortfolioCryptoInfoDTO cryptoInfoDTO, PortfolioCryptoInfoDTO fiatInfo,
        String changePeriod) {
        PortfolioSummaryResponseDTO portfolioStatisticsResponseDTO = new PortfolioSummaryResponseDTO();
        portfolioStatisticsResponseDTO.setPortfolioType(portfolioStatisticsDTO.getPortfolioType());

        List<PortfolioPageVo<PortfolioQueryResultDTO>> manualSummary = portfolioStatisticsDTO.getManualSummary();
        List<PortfolioSummaryDTO<ManualSummaryResponseDTO>> portfolioSummaryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(manualSummary)) {
            portfolioSummaryList = manualSummary.stream().map(item -> {
                PortfolioSummaryDTO<ManualSummaryResponseDTO> portfolioSummary = new PortfolioSummaryDTO<>();
                portfolioSummary.setJobFlag(item.getJobFlag());
                portfolioSummary.setIsCalculating(item.getJobFlag());
                portfolioSummary.setPortfolioName(item.getPortfolioName());
                portfolioSummary.setPortfolioSourceId(item.getPortfolioSourceId());
                portfolioSummary.setSortIndex(item.getSortIndex());
                portfolioSummary.setIsMain(item.getIsMain());
                portfolioSummary.setOwnWallet(getOwnWallet(item.getOwnWallet()));
                portfolioSummary.setPortfolioType(item.getPortfolioType());
                portfolioSummary.setPortfolioAvatar(item.getPortfolioAvatar());
                List<PortfolioQueryResultDTO> list = item.getList();
                List<ManualSummaryResponseDTO> collect;
                if (CollectionUtils.isEmpty(list)) {
                    collect = new ArrayList<>();
                }else {
                    collect = list.stream().map(res -> {
                        ManualSummaryResponseDTO manualSummaryResponseDTO = new ManualSummaryResponseDTO();
                        manualSummaryResponseDTO.setCryptoUnitPrice(res.getCryptoUnitPrice());
                        manualSummaryResponseDTO.setName(res.getName());
                        manualSummaryResponseDTO.setSymbol(res.getSymbol());
                        manualSummaryResponseDTO.setBalance(res.getAmount());
                        manualSummaryResponseDTO.setBuyAvgPrice(res.getBuyAvgPrice());
                        manualSummaryResponseDTO.setCryptoHoldings(res.getCryptoHoldings());
                        manualSummaryResponseDTO.setCurrentPrice(res.getCurrentPrice());
                        manualSummaryResponseDTO.setCryptocurrencyId(res.getCryptocurrencyId());
                        manualSummaryResponseDTO.setLastUpdated(res.getLastUpdated());
                        manualSummaryResponseDTO.setTotalBuySpent(res.getTotalBuySpent());
                        manualSummaryResponseDTO.setTotalFee(res.getTotalFee());
                        // pl 昨天的值  = 今天的值 /（1+ 昨天的涨跌幅）
                        // pl 昨天的值 * 昨天的涨跌幅  = 昨天的涨跌幅
                        manualSummaryResponseDTO.setPreChangeValue(res.getPlValue());
                        manualSummaryResponseDTO.setPreChangePercent(res.getPlPercentValue());
                        manualSummaryResponseDTO.setHoldingsPercent(res.getHoldingsPercent());
                        if (fiatInfo != null) {
                            BigDecimal changePercent = getRelativeChangeRate(res.getYesterdayChangePercent(),
                                fiatInfo.transferChangePerCent());
                            manualSummaryResponseDTO.setFiatPriceChangePercent24h(changePercent);
                        }
                        if (cryptoInfoDTO != null) {
                            BigDecimal changePercent = getRelativeChangeRate(res.getYesterdayChangePercent(),
                                cryptoInfoDTO.transferChangePerCent());
                            manualSummaryResponseDTO.setCryptoPriceChangePercent24h(changePercent);
                        }
                        return manualSummaryResponseDTO;
                    }).collect(Collectors.toList());
                }
                portfolioSummary.setList(collect);
                return portfolioSummary;
            }).collect(Collectors.toList());
        }

        List<PortfolioPageVo<WalletSummaryDTO>> walletSummary1 = portfolioStatisticsDTO.getWalletSummary();
        List<PortfolioSummaryDTO<WalletSummaryResponseDTO>> walletSummaryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(walletSummary1)) {
            walletSummaryList= walletSummary1.stream().map(item -> {
                PortfolioSummaryDTO<WalletSummaryResponseDTO> walletSummary = new PortfolioSummaryDTO<>();
                walletSummary.setPortfolioSourceId(item.getPortfolioSourceId());
                walletSummary.setPortfolioName(item.getPortfolioName());
                walletSummary.setSortIndex(item.getSortIndex());
                walletSummary.setIsMain(item.getIsMain());
                walletSummary.setOwnWallet(getOwnWallet(item.getOwnWallet()));
                walletSummary.setPortfolioType(item.getPortfolioType());
                walletSummary.setPortfolioAvatar(item.getPortfolioAvatar());
                walletSummary.setWalletAddress(item.getWalletAddress());
                walletSummary.setJobFlag(item.getJobFlag());
                walletSummary.setDataFlag(item.getDataFlag());
                walletSummary.setIsCalculating(item.getJobFlag());
                walletSummary.setSupportChainList(item.getSupportChainList());
                List<WalletSummaryDTO> list = item.getList();
                List<WalletSummaryResponseDTO> collect;
                if (CollectionUtils.isEmpty(list)) {
                    collect = new ArrayList<>();
                }else {
                    collect = list.stream().map(res -> {
                        WalletSummaryResponseDTO walletSummaryResponseDTO = new WalletSummaryResponseDTO();
                        walletSummaryResponseDTO.setName(res.getName());
                        walletSummaryResponseDTO.setLogo(res.getLogoUrl());
                        walletSummaryResponseDTO.setSymbol(res.getSymbol());
                        walletSummaryResponseDTO.setBalance(res.getBalance());
                        walletSummaryResponseDTO.setLastUpdated(res.getLastUpdated());
                        walletSummaryResponseDTO.setCurrentPrice(res.getCurrentPrice());
                        walletSummaryResponseDTO.setCryptocurrencyId(res.getCryptocurrencyId());
                        walletSummaryResponseDTO.setCryptoHoldings(res.getValue());
                        walletSummaryResponseDTO.setChainDetails(res.getChainDetails());
                        walletSummaryResponseDTO.setHoldingsPercent(res.getHoldingsPercent());
                        walletSummaryResponseDTO.setType(res.getType());
                        walletSummaryResponseDTO.setContractAddress(res.getContractAddress());
                        walletSummaryResponseDTO.setPlatformId(res.getPlatformId());
                        walletSummaryResponseDTO.setPlatformName(res.getPlatformName());
                        if (fiatInfo != null) {
                            BigDecimal changePercent = getRelativeChangeRate(res.getYesterdayChangePercent(),
                                fiatInfo.transferChangePerCent());
                            walletSummaryResponseDTO.setFiatPriceChangePercent24h(changePercent);
                        }
                        if (cryptoInfoDTO != null) {
                            BigDecimal changePercent = getRelativeChangeRate(res.getYesterdayChangePercent(),
                                cryptoInfoDTO.transferChangePerCent());
                            walletSummaryResponseDTO.setCryptoPriceChangePercent24h(changePercent);
                        }
                        if (res.getCurrentPrice() != null) {
                            switch (getPortfolioChangePeriodEnum(changePeriod)) {
                                case ONE_HOUR:
                                    BigDecimal oneHourChangePercent = res.getOneHourChangePercent();
                                    walletSummaryResponseDTO.setPreCryptoChangePercent(
                                            getRelativeChangeRate(oneHourChangePercent, cryptoInfoDTO.transferOneHourChangePerCent()));
                                    walletSummaryResponseDTO.setPreFiatChangePercent(
                                            getRelativeChangeRate(oneHourChangePercent, fiatInfo.transferOneHourChangePerCent()));
                                    // 当前总之- 昨天值
                                    // 昨天的价格* （1+ 百分比）=今天价格
                                    BigDecimal oneHour = BigDecimal.ONE.add(!Objects.equals(BigDecimal.ZERO, oneHourChangePercent) ?
                                            oneHourChangePercent.divide(new BigDecimal(100)) : BigDecimal.ZERO);
                                    BigDecimal oneHourPrePrice;
                                    if (Objects.equals(BigDecimal.ZERO, oneHour)) {
                                        oneHourPrePrice = null;
                                    } else {
                                        oneHourPrePrice = res.getCurrentPrice()
                                                .divide(oneHour, 5, RoundingMode.HALF_DOWN);
                                    }
                                    walletSummaryResponseDTO.setPrePrice(oneHourPrePrice);
                                    walletSummaryResponseDTO.setPreChangeValue(
                                            oneHourChangePercent.multiply(res.getBalance()).multiply(oneHourPrePrice));
                                    break;
                                case ONE_WEEK:
                                    BigDecimal sevenDaysChangePercent = res.getSevenDaysChangePercent();
                                    walletSummaryResponseDTO.setPreCryptoChangePercent(
                                            getRelativeChangeRate(sevenDaysChangePercent, cryptoInfoDTO.transferSevenDaysChangePerCent()));
                                    walletSummaryResponseDTO.setPreFiatChangePercent(
                                            getRelativeChangeRate(sevenDaysChangePercent, fiatInfo.transferSevenDaysChangePerCent()));
                                    BigDecimal sevenDay = BigDecimal.ONE.add(!Objects.equals(BigDecimal.ZERO, sevenDaysChangePercent) ?
                                            sevenDaysChangePercent.divide(new BigDecimal(100)) : BigDecimal.ZERO);
                                    BigDecimal sevenDayPrePrice;
                                    if (Objects.equals(BigDecimal.ZERO, sevenDay)) {
                                        sevenDayPrePrice = null;
                                    } else {
                                        sevenDayPrePrice = res.getCurrentPrice()
                                                .divide(sevenDay, 5, RoundingMode.HALF_DOWN);
                                    }
                                    walletSummaryResponseDTO.setPrePrice(sevenDayPrePrice);
                                    walletSummaryResponseDTO.setPreChangeValue(
                                            sevenDaysChangePercent.multiply(res.getBalance())
                                                    .multiply(sevenDayPrePrice));
                                    break;
                                case ONE_MONTH:
                                    BigDecimal thirtyDaysChangePercent = res.getThirtyDaysChangePercent();
                                    walletSummaryResponseDTO.setPreCryptoChangePercent(
                                            getRelativeChangeRate(thirtyDaysChangePercent, cryptoInfoDTO.transferThirtyDaysChangePerCent()));
                                    walletSummaryResponseDTO.setPreFiatChangePercent(
                                            getRelativeChangeRate(thirtyDaysChangePercent, fiatInfo.transferThirtyDaysChangePerCent()));

                                    BigDecimal thirtyDay = BigDecimal.ONE.add(!Objects.equals(BigDecimal.ZERO, thirtyDaysChangePercent) ?
                                            thirtyDaysChangePercent.divide(new BigDecimal(100)) : BigDecimal.ZERO);
                                    BigDecimal thirtyDayPrePrice;
                                    if (Objects.equals(BigDecimal.ZERO, thirtyDay)) {
                                        thirtyDayPrePrice = null;
                                    } else {
                                        thirtyDayPrePrice = res.getCurrentPrice()
                                                .divide(thirtyDay, 5, RoundingMode.HALF_DOWN);
                                    }
                                    walletSummaryResponseDTO.setPrePrice(thirtyDayPrePrice);
                                    walletSummaryResponseDTO.setPreChangeValue(
                                            thirtyDaysChangePercent.multiply(res.getBalance())
                                                    .multiply(thirtyDayPrePrice));
                                    break;
                                case ONE_DAY:
                                default:
                                    BigDecimal yesterdayChangePercent = res.getYesterdayChangePercent();
                                    if (Objects.isNull(yesterdayChangePercent)) {
                                        yesterdayChangePercent = BigDecimal.ZERO;
                                    }
                                    walletSummaryResponseDTO.setPreCryptoChangePercent(
                                            getRelativeChangeRate(yesterdayChangePercent, cryptoInfoDTO.transferChangePerCent()));
                                    walletSummaryResponseDTO.setPreFiatChangePercent(
                                            getRelativeChangeRate(yesterdayChangePercent, fiatInfo.transferChangePerCent()));

                                    BigDecimal yesterday = BigDecimal.ONE.add(!Objects.equals(BigDecimal.ZERO, yesterdayChangePercent) ?
                                            yesterdayChangePercent.divide(new BigDecimal(100)) : BigDecimal.ZERO);
                                    BigDecimal yesterdayPrePrice;
                                    if (Objects.equals(BigDecimal.ZERO, yesterday)) {
                                        yesterdayPrePrice = BigDecimal.ZERO;
                                    } else {
                                        yesterdayPrePrice = res.getCurrentPrice()
                                                .divide(yesterday, 5, RoundingMode.HALF_DOWN);
                                    }
                                    walletSummaryResponseDTO.setPrePrice(yesterdayPrePrice);
                                    walletSummaryResponseDTO.setPreChangeValue(
                                            yesterdayChangePercent.multiply(res.getBalance())
                                                    .multiply(yesterdayPrePrice));
                            }
                        }
                        return walletSummaryResponseDTO;
                    }).collect(Collectors.toList());
                }
                walletSummary.setSupportChainList(item.getSupportChainList());
                walletSummary.setList(collect);
                return walletSummary;
            }).collect(Collectors.toList());
        }

        List<PortfolioPageVo<ThirdPartySummaryDTO>> thirdPartySummary = portfolioStatisticsDTO.getThirdPartySummary();
        List<PortfolioSummaryDTO<ThirdPartySummaryResponseDTO>> thirdPartySummaryList =
            Lists.newArrayListWithCapacity(CollectionUtils.isEmpty(thirdPartySummary) ? 0 : thirdPartySummary.size());
        if (CollectionUtils.isNotEmpty(thirdPartySummary)) {
            thirdPartySummaryList = thirdPartySummary.stream().map(item -> {
                PortfolioSummaryDTO<ThirdPartySummaryResponseDTO> thirdPartySummaryDTO = new PortfolioSummaryDTO<>();
                thirdPartySummaryDTO.setJobFlag(false);
                thirdPartySummaryDTO.setIsCalculating(false);
                thirdPartySummaryDTO.setPortfolioName(item.getPortfolioName());
                thirdPartySummaryDTO.setPortfolioSourceId(item.getPortfolioSourceId());
                thirdPartySummaryDTO.setSortIndex(item.getSortIndex());
                thirdPartySummaryDTO.setIsMain(item.getIsMain());
                thirdPartySummaryDTO.setOwnWallet(getOwnWallet(item.getOwnWallet()));
                thirdPartySummaryDTO.setPortfolioType(item.getPortfolioType());
                thirdPartySummaryDTO.setPortfolioAvatar(item.getPortfolioAvatar());
                if (CollectionUtils.isNotEmpty(item.getList())) {
                    List<ThirdPartySummaryDTO> dtoList = item.getList();
                    List<ThirdPartySummaryResponseDTO> list = dtoList.stream().map(dto -> {
                        ThirdPartySummaryResponseDTO responseDTO = new ThirdPartySummaryResponseDTO();
                        responseDTO.setCryptocurrencyId(dto.getCryptocurrencyId());
                        responseDTO.setName(dto.getName());
                        responseDTO.setSymbol(dto.getSymbol());
                        responseDTO.setSlug(dto.getSlug());
                        responseDTO.setBalance(dto.getBalance());
                        responseDTO.setValue(dto.getValue());
                        responseDTO.setLastUpdated(dto.getLastUpdated());
                        responseDTO.setCurrentPrice(dto.getCurrentPrice());
                        responseDTO.setCryptoHoldings(dto.getValue());
                        responseDTO.setHoldingsPercent(dto.getHoldingsPercent());
                        if (fiatInfo != null) {
                            BigDecimal changePercent = getRelativeChangeRate(dto.getYesterdayChangePercent(),
                                fiatInfo.transferChangePerCent());
                            responseDTO.setFiatPriceChangePercent24h(changePercent);
                        }
                        if (cryptoInfoDTO != null) {
                            BigDecimal changePercent = getRelativeChangeRate(dto.getYesterdayChangePercent(),
                                cryptoInfoDTO.transferChangePerCent());
                            responseDTO.setCryptoPriceChangePercent24h(changePercent);
                        }
                        if (responseDTO.getCurrentPrice() != null) {
                            BigDecimal yesterdayChangePercent = dto.getYesterdayChangePercent();
                            responseDTO.setPreCryptoChangePercent(
                                    getRelativeChangeRate(yesterdayChangePercent, cryptoInfoDTO.transferOneHourChangePerCent()));
                            responseDTO.setPreFiatChangePercent(
                                    getRelativeChangeRate(yesterdayChangePercent, fiatInfo.transferOneHourChangePerCent()));
                            BigDecimal yesterday = BigDecimal.ONE.add(MathUtils.getOrZero(yesterdayChangePercent).divide(new BigDecimal(100)));
                            if (yesterday.compareTo(BigDecimal.ZERO) == 0 || yesterdayChangePercent == null || responseDTO.getBalance() == null) {
                                responseDTO.setPrePrice(null);
                                responseDTO.setPreChangeValue(null);
                            } else {
                                BigDecimal yesterdayPrice = responseDTO.getCurrentPrice().divide(yesterday, 5, RoundingMode.HALF_DOWN);
                                responseDTO.setPrePrice(yesterdayPrice);
                                responseDTO.setPreChangeValue(
                                        yesterdayChangePercent.multiply(responseDTO.getBalance()).multiply(yesterdayPrice));
                            }
                        }
                        return responseDTO;
                    }).collect(Collectors.toList());

                    thirdPartySummaryDTO.setList(list);
                }
                return thirdPartySummaryDTO;
            }).collect(Collectors.toList());
        }

        portfolioStatisticsResponseDTO.setWalletSummary(
            CollectionUtils.isEmpty(walletSummaryList) ? null : walletSummaryList.get(0));
        portfolioStatisticsResponseDTO.setManualSummary(
            CollectionUtils.isEmpty(portfolioSummaryList) ? null : portfolioSummaryList.get(0));
        portfolioStatisticsResponseDTO.setThirdPartySummary(
            CollectionUtils.isEmpty(thirdPartySummaryList) ? null : thirdPartySummaryList.get(0));
        return portfolioStatisticsResponseDTO;
    }

    private boolean getOwnWallet(Boolean ownWallet) {
        return Optional.ofNullable(ownWallet).orElse(true);
    }

    /**
     * 查询portfolio的summary
     *
     * @param portfolioLineChartQueryDTO request
     * @return result
     */
    @Override
    public Mono<PortfolioLineHistoricalChartResponseDTO> queryForLineHistoricalChart(
        PortfolioLineChartQueryDTO portfolioLineChartQueryDTO) {
        PortfolioModuleEnum moduleEnum = PortfolioModuleEnum.findByCode(portfolioLineChartQueryDTO.getModuleType());
        String portfolioSourceId = portfolioLineChartQueryDTO.getPortfolioSourceId();
        if (checkAssetAllocation(moduleEnum)) {
            return Mono.error(new BusinessException(ErrorCodeEnums.PARAMS_CHECK_ERROR));
        }
        String userId = portfolioLineChartQueryDTO.getHeader().getUserId();
        String channel = portfolioLineChartQueryDTO.getHeader().getChannel();
        String appVersion = portfolioLineChartQueryDTO.getHeader().getAppVersion();

        PortfolioCryptoVerificationStatus verificationStatus = PortfolioCryptoVerificationStatus.getByCode(portfolioLineChartQueryDTO
            .getCryptoVerificationStatus());

        // verificationStatus为null，默认返回verified tokens
        verificationStatus = Objects.isNull(verificationStatus) ? PortfolioCryptoVerificationStatus.VERIFIED : verificationStatus;
        if (PortfolioModuleEnum.DASHBOARD.equals(moduleEnum) || StringUtils.isBlank(portfolioSourceId)) {
            QueryLineChartRequestDTO queryLineChartRequestDTO = new QueryLineChartRequestDTO();
            BeanUtils.copyProperties(portfolioLineChartQueryDTO, queryLineChartRequestDTO);

            queryLineChartRequestDTO.getHeader().setUserId(userId);
            queryLineChartRequestDTO.getHeader().setChannel(channel);
            queryLineChartRequestDTO.getHeader().setAppVersion(appVersion);
            queryLineChartRequestDTO.setCryptoVerificationStatus(verificationStatus.getCode());
            return dashboardService.queryLineChart(queryLineChartRequestDTO)
                .map(this::convertDashboard);
        } else {
            PortfolioHistoricalChartQueryDTO portfolioHistoricalChartQueryDTO = new PortfolioHistoricalChartQueryDTO();
            BeanUtils.copyProperties(portfolioLineChartQueryDTO, portfolioHistoricalChartQueryDTO);
            portfolioHistoricalChartQueryDTO.getHeader().setUserId(userId);
            portfolioHistoricalChartQueryDTO.getHeader().setChannel(channel);
            portfolioHistoricalChartQueryDTO.getHeader().setAppVersion(appVersion);
            portfolioHistoricalChartQueryDTO.setCryptoVerificationStatus(verificationStatus.getCode());
            return portfolioService.queryForHistoricalChartV2(portfolioHistoricalChartQueryDTO)
                .map(this::convertHistorical);
        }
    }

    private PortfolioLineHistoricalChartResponseDTO convertHistorical(List<PortfolioHistoricalChartDTO> result) {
        List<PortfolioHistoricalChartDTO> collect = result.stream().peek(item -> {
            List<PortfolioTotalDTO> totalList = item.getTotalList();
            int size = totalList.size();
            int i = size / pointSize;

            if (i > 0 && size > 2) {
                List<PortfolioTotalDTO> newList = new ArrayList<>();
                newList.add(totalList.get(0)); // Retain the first point

                newList.addAll(IntStream.range(1, size - 1) // Iterate over the middle points
                        .filter(index -> index % i == 0)
                        .mapToObj(totalList::get)
                        .collect(Collectors.toList()));

                newList.add(totalList.get(size - 1)); // Retain the last point

                item.setTotalList(newList);
            }
        }).collect(Collectors.toList());
        return PortfolioLineHistoricalChartResponseDTO.builder().portfolioChart(collect).build();
    }

    private PortfolioLineHistoricalChartResponseDTO convertDashboard(List<QueryLineChartResponseDTO> result) {
        List<QueryLineChartResponseDTO> collect = result.stream().peek(item -> {
            int i = item.getTotalList().size() / pointSize;
            if (i > 0) {
                AtomicInteger index = new AtomicInteger(0);
                item.setTotalList(item.getTotalList().stream().filter(s -> index.getAndIncrement() % i == 0).collect(Collectors.toList()));
            }
        }).collect(Collectors.toList());
        return PortfolioLineHistoricalChartResponseDTO.builder().dashboardChart(collect).build();
    }
}
