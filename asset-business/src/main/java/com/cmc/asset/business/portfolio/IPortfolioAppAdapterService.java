package com.cmc.asset.business.portfolio;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.data.common.Header;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * IPortfolioAppAdapterService
 * <AUTHOR> ricky.x
 * @date : 2023/2/21 下午5:21
 */
public interface IPortfolioAppAdapterService {

    /**
     * 根据appVersion和platform查询对应的portfolio
     * @param portfolioMultiList user all portfolioMulti
     * @param requestVersion appVersion
     * @param platform platform
     * @return portfolio
     */
    Mono<PortfolioMultiEntity> getAppDefaultPortfolio(String method, List<PortfolioMultiEntity> portfolioMultiList,
        String platform, String requestVersion);

    boolean isSupportBinanceVersion(Header header);

    boolean isFromApp(Header header);

    boolean isSupportPriceUnitVersion(Header header);

    Boolean checkNeedAddDashboard(Header header, Boolean enableWebOverview);

}
