package com.cmc.asset.business.watchlist;

import com.cmc.asset.model.contract.watchlist.AutoFillFollowParamDTO;
import com.cmc.asset.model.contract.watchlist.DeleteWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.FollowWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QuerySharedWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicRequest;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicResponse;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicV2Request;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicV2Response;
import com.cmc.asset.model.contract.watchlist.QueryWatchListMultiParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListRespDTO;
import com.cmc.asset.model.contract.watchlist.WatchAssetInfoRespDTO;
import com.cmc.asset.model.contract.watchlist.WatchListAssetParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListGuestToRegularDTO;
import com.cmc.asset.model.contract.watchlist.WatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResourceResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultMultiDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSharedResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSummaryDTO;
import com.cmc.data.common.BaseRequest;
import java.util.List;
import java.util.Set;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2020/10/19 15:06
 * @description
 */
public interface WatchListService {

    /**
     * subscribe / unsubscribe watchlist.
     *
     * @param param
     * @return
     */
    Mono<WatchListSubscribeResultDTO> subscribe(WatchListSubscribeParamDTO param);

    /**
     * fill follow
     *
     * @return
     */
    Mono<WatchListResultDTO> fillFollow(AutoFillFollowParamDTO autoFillFollowParamDTO);

    /**
     * query the user's watchlist.
     *
     * @param param
     * @return
     */
    Mono<WatchListResultDTO> query(QueryWatchListParamDTO param);

    /**
     * query base info
     * @param param
     * @return
     */
    Mono<QueryWatchListBasicResponse> queryBasic(QueryWatchListBasicRequest param);

    Mono<QueryWatchListBasicV2Response> queryBasicV2(QueryWatchListBasicV2Request queryWatchListBasicRequest);

    /**
     * create / update watchlist
     *
     * @param param
     * @return
     */
    Mono<String> save(WatchListParamDTO param);

    /**
     * delete watchlist
     *
     * @param param
     * @return
     */
    Mono<String> delete(DeleteWatchListParamDTO param);

    /**
     * follow watch list
     *
     * @param param
     * @return
     */
    Mono<String> followWatchList(FollowWatchListParamDTO param);

    /**
     *  query shared watchlist.
     * @return
     */
    Mono<WatchListSharedResultDTO> querySharedWatchLists(QuerySharedWatchListParamDTO param);

    /**
     * create main WatchList.
     * @param param
     * @return
     */
    Mono<String> createMainWatchList(BaseRequest param);

    /**
     *  guest watchlist to regular main watchlist.
     * @param param request param
     * @return boolean
     */
    Mono<String> guestToRegularMainWatchList(WatchListGuestToRegularDTO param);

    /**
     * create query watchlist crypto ids for userId
     * @param param
     * @return
     */
    Mono<Set<Integer>> queryWatchListCryptoIds(QueryWatchListParamDTO param);

    /**
     * query the multi watchlist by watchListId list.
     *
     * @param param
     * @return
     */
    Mono<WatchListResultMultiDTO> queryMulti(QueryWatchListMultiParamDTO param);


    Mono<WatchAssetInfoRespDTO> queryWatchAsset(WatchListAssetParamDTO param);
    /**
     * get watchList by id
     * @param param
     * @return
     */
    Mono<QueryWatchListRespDTO> getById(WatchListParamDTO param);


    Mono<WatchListResourceResultDTO> queryByResourceId(@RequestBody QueryWatchListParamDTO param);

    Mono<List<WatchListSummaryDTO>> queryWatchlistSummary(BaseRequest baseRequest);
}
