package com.cmc.asset.business.userpoint;

import com.cmc.asset.model.contract.BaseCaptchaRequest;
import com.cmc.asset.model.contract.userpoint.CheckInInfoDTO;
import com.cmc.asset.model.contract.userpoint.CheckInResultDTO;
import com.cmc.asset.model.contract.userpoint.GetPointFlowParamDTO;
import com.cmc.asset.model.contract.userpoint.GetPointFlowResultDTO;
import com.cmc.asset.model.contract.userpoint.QueryLatestCheckInLogsResultDTO;
import com.cmc.asset.model.contract.userpoint.QueryPointSummaryRequestDTO;
import com.cmc.asset.model.contract.userpoint.QueryUserpointListParamDTO;
import com.cmc.asset.model.contract.userpoint.QueryUserpointListResultDTO;
import com.cmc.asset.model.contract.userpoint.QueryUserpointSummaryResultDTO;
import com.cmc.asset.model.contract.userpoint.UpdateUserPointParamDTO;
import com.cmc.asset.model.contract.userpoint.UpdateUserPointResultDTO;
import com.cmc.data.common.BaseRequest;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR> Yin
 * @Description user point service interface
 * @date 2021/6/16 上午11:59
 */
public interface UserpointService {

    /**
     * query check-in info
     * @param request
     * @return reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.CheckInInfoDTO>
     */
    Mono<CheckInInfoDTO> checkInInfo(BaseRequest request);

    /**
     * query check-in info
     * @param request
     * @return reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.CheckInInfoDTO>
     */
    Mono<CheckInInfoDTO> queryCheckInLog(BaseRequest request);

    /**
    * @Description: user check in service
    * param baseRequest
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.CheckInResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/6/16
    */
    Mono<CheckInResultDTO> checkIn(BaseCaptchaRequest baseRequest);

    /**
    * @Description: user latest check in logs service
    * param baseRequest
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.QueryLatestCheckInLogsResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/6/16
    */
    Mono<List<QueryLatestCheckInLogsResultDTO>> latestCheckInLogs(BaseRequest baseRequest);

    /**
    * @Description: user point list
    * param userpointParamDTO
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.QueryUserpointListResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/6/16
    */
    Mono<QueryUserpointListResultDTO> userpointList(QueryUserpointListParamDTO userpointParamDTO);

    /**
    * @Description: user point summary service
    * param baseRequest
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.QueryUserpointSummaryResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/6/16
    */
    Mono<QueryUserpointSummaryResultDTO> getUserPointBalance(String userId);

    /**
     * Get the user points balance in a batch query
     *
     * @param userIds the user id list
     * @return the points list
     */
    Mono<List<QueryUserpointSummaryResultDTO>> getUserPointBalanceInBatch(List<String> userIds);

    /**
    * @Description: update user point
    * param param
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.UpdateUserPointResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/7/13
    */
    Mono<UpdateUserPointResultDTO> updateUserPoint(UpdateUserPointParamDTO param);

    /**
    * @Description: query user point for admin
    * param param
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.QueryUserpointListResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/8/5
    */
    Mono<QueryUserpointListResultDTO> userPointList4Admin(QueryUserpointListParamDTO param);

    /**
    * @Description: get point flow record
    * param param
    * @return: reactor.core.publisher.Mono<com.cmc.asset.model.contract.userpoint.GetPointFlowResultDTO>
    * @Author: Ivory Yin
    * @Date: 2021/9/2
    */
    Mono<GetPointFlowResultDTO> getPointFlow(GetPointFlowParamDTO param);

    /**
     * user point summary service for internal system
     *
     * @param request
     * @return
     */
    Mono<QueryUserpointSummaryResultDTO> getUserPointBalanceForSystem(QueryPointSummaryRequestDTO request);
}
