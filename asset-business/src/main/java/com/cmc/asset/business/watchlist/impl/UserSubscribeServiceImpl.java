package com.cmc.asset.business.watchlist.impl;

import com.cmc.asset.business.watchlist.UserSubscribeService;
import com.cmc.asset.dao.repository.mongo.UserSubscribeRepository;
import com.cmc.asset.domain.watchlist.UserSubscribeVO;
import com.cmc.asset.model.contract.user.UserSubscribeAdminListDTO;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * @ClassName UserSubscribeServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/12/24 上午10:14
 **/
@Service
@Slf4j
public class UserSubscribeServiceImpl implements UserSubscribeService {

    @Autowired
    private UserSubscribeRepository userSubscribeRepository;

    @Value("${com.cmc.asset.subscribe.queryTime:14}")
    private int subscribeQueryTime;

    @Override
    public Mono<List<UserSubscribeVO>> list(UserSubscribeAdminListDTO request) {
        return userSubscribeRepository.queryByUidAndTime(request.getUid(), DatetimeUtils.getDaysAgo(new Date(), subscribeQueryTime))
            .map(list -> {
                if (CollectionUtils.isEmpty(list)) {
                    return Collections.emptyList();
                }
                HashMap<Integer, UserSubscribeVO> map = Maps.newLinkedHashMap();
                list.forEach(entity -> {
                    if (SubscribeTypeEnum.SUBSCRIBE.name().equals(entity.getSubscribeType())) {
                        map.remove(entity.getResourceId());
                    } else if (SubscribeTypeEnum.UNSUBSCRIBE.name().equals(entity.getSubscribeType())) {
                        if (!map.containsKey(entity.getResourceId())) {
                            map.put(entity.getResourceId(), UserSubscribeVO.builder().uid(entity.getUid())
                                .resourceId(entity.getResourceId()).build());
                        }
                    }
                });
                return CollectionUtils.isEmpty(map.values()) ? Collections.emptyList() : Lists.newArrayList(map.values());
            });
    }
}
