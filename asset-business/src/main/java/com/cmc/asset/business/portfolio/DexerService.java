package com.cmc.asset.business.portfolio;

import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.dexer.PlatformDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import java.util.List;
import java.util.Map;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 * @since 2024/7/15 09:37
 */
public interface DexerService {

    Mono<Map<Long, PairInfoDTO>> getPairs(List<Long> poolIdList);

    Mono<List<PlatformDTO>> getPlatforms();

    Mono<List<PlatformNewDTO>> getPlatformsFromDeQuery();

    /**
     * Batch get token price info from <PERSON>Query by platform and address list
     */
    Mono<List<TokenPriceDTO>> getPairsFromDeQuery(List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens);

}
