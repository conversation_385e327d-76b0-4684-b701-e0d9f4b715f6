package com.cmc.asset.business.task;

import com.cmc.asset.model.contract.task.DailyTaskCompleteDTO;
import com.cmc.asset.model.contract.task.TaskProcessDTO;
import com.cmc.asset.model.contract.task.UserTaskProcessDTO;
import com.cmc.data.common.BaseRequest;

import java.util.Map;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/8/9
 */
public interface IDailyTaskService {
    Mono<Boolean> completeTaskAndLog(DailyTaskCompleteDTO dailyTaskCompleteDTO);

    Mono<Map<String, UserTaskProcessDTO>> getTaskProcess(TaskProcessDTO taskProcessDTO);
}
