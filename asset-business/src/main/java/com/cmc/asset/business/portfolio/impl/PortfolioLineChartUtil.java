package com.cmc.asset.business.portfolio.impl;

import com.cmc.asset.config.DynamicApolloRefreshConfig;
import com.cmc.asset.dao.entity.portfolio.CryptoDetailPO;
import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyAssetHistoryAggregatedEntity;
import com.cmc.asset.model.common.Constant;
import com.cmc.asset.model.common.CurrencyUtils;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.contract.portfolio.PortfolioChangePointDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.data.common.utils.DatetimeUtils;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.MathUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

/**
 * <AUTHOR>
 * @since 2023/8/17 08:05
 */
@Component
@Slf4j
public class PortfolioLineChartUtil {

    @Autowired
    private CurrencyPriceService currencyPriceService;
    @Autowired
    private PortfolioBaseService portfolioBaseService;

    @Autowired
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Value("${com.cmc.asset.portfolio.chart.desired-point-count:289}")
    private Integer desiredPointCount;
    @Value("${com.cmc.asset.portfolio.chart.last-5m-delay:60}")
    private Long last5mDelay;
    @Value("${com.cmc.asset.portfolio.chart.default-all-range:90}")
    private Integer defaultAllRange;

    public Mono<List<PortfolioTotalDTO>> getHistoryPoints(List<Long> timeCodeList, Map<Date, List<CryptoDetailPO>> balanceMap) {
        if (CollectionUtils.isEmpty(balanceMap)) {
            return Mono.just(List.of());
        }
        Map<Integer, TreeMap<Long, BigDecimal>> cryptoHistoryMap = new HashMap<>();
        balanceMap.forEach((date, cryptoList) -> {
            cryptoList.forEach(crypto -> {
                if (crypto.getCryptoId() != null && date != null && crypto.getAmount() != null) {
                    cryptoHistoryMap.computeIfAbsent(crypto.getCryptoId(), k -> new TreeMap<>())
                            .put(date.getTime() / 1000L, crypto.getAmount());
                }
            });
        });

        return getHistoryByCryptoId(timeCodeList, cryptoHistoryMap);
    }

    public Mono<List<PortfolioTotalDTO>> getHistoryPointsForWallet(List<Long> timeCodeList, Map<Integer, Tuple2<Date, BigDecimal>> balanceMap) {
        if (CollectionUtils.isEmpty(balanceMap)) {
            return Mono.just(List.of());
        }
        Map<Integer, TreeMap<Long, BigDecimal>> cryptoHistoryMap = new HashMap<>();
        Optional<Long> min = timeCodeList.stream().min(Long::compareTo);
        balanceMap.forEach((cryptoId, tuple2) -> {
            cryptoHistoryMap.computeIfAbsent(cryptoId, k -> new TreeMap<>())
                    .put(min.orElse(tuple2.getT1().getTime() / 1000L), tuple2.getT2());
        });

        return getHistoryByCryptoId(timeCodeList, cryptoHistoryMap);
    }

    public Mono<List<PortfolioTotalDTO>> getHistoryPointsForThirdParty(List<Long> timeCodeList, List<PortfolioThirdPartyAssetHistoryAggregatedEntity> balanceList) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return Mono.just(List.of());
        }
        Map<Integer, TreeMap<Long, BigDecimal>> cryptoHistoryMap = new HashMap<>();
        balanceList.forEach(portfolio -> {
            if (portfolio.getCryptoId() != null && portfolio.getSyncTime() != null && portfolio.getTotalBalance() != null) {
                cryptoHistoryMap.computeIfAbsent(portfolio.getCryptoId(), k -> new TreeMap<>())
                        .put(portfolio.getSyncTime().getTime() / 1000L, portfolio.getTotalBalance());
            }
        });

        return getHistoryByCryptoId(timeCodeList, cryptoHistoryMap, true);
    }

    private Mono<List<PortfolioTotalDTO>> getHistoryByCryptoId(List<Long> timeCodeList, Map<Integer, TreeMap<Long, BigDecimal>> cryptoHistoryMap) {
        return this.getHistoryByCryptoId(timeCodeList, cryptoHistoryMap, false);
    }

    private Mono<List<PortfolioTotalDTO>> getHistoryByCryptoId(List<Long> timeCodeList, Map<Integer, TreeMap<Long, BigDecimal>> cryptoHistoryMap, boolean ratioMapping) {
        return currencyPriceService.queryCurrentPrices(cryptoHistoryMap.keySet(), ratioMapping)
                .flatMap(currentPriceMap -> {
                    Long now = ExtUtils.nowUTCSecond();
                    return Flux.fromIterable(cryptoHistoryMap.entrySet()).flatMap(cryptoEntry -> {
                        Integer cryptoId = cryptoEntry.getKey();
                        TreeMap<Long, BigDecimal> balanceMap = cryptoEntry.getValue();
                        return currencyPriceService.loadPrices(cryptoId, timeCodeList, "getHistoryByCryptoId")
                                .map(priceMap -> {
                                    List<Long> additionalTimeCodes = new ArrayList<>(timeCodeList);

                                    if (currentPriceMap.get(cryptoId) != null) {
                                        additionalTimeCodes.add(now);
                                        priceMap.put(now, currentPriceMap.get(cryptoId).transferPrice());
                                    }

                                    return additionalTimeCodes.stream().map(timestamp -> {
                                        Date date = new Date(timestamp * 1000L);
                                        BigDecimal price = currencyPriceService.getPrice(priceMap, timestamp);
                                        BigDecimal balance = Optional.ofNullable(balanceMap.floorEntry(timestamp))
                                                .map(Map.Entry::getValue)
                                                .orElse(null);
                                        if (price == null || balance == null) {
                                            return null;
                                        }
                                        log.debug("getHistoryByCryptoId, cryptoId:{}, timestamp:{}, price:{}, balance:{}", cryptoId, timestamp, price, balance);
                                        BigDecimal value = price.multiply(balance).stripTrailingZeros();
                                        return PortfolioTotalDTO.builder().timestamp(date).value(value).build();
                                    }).filter(Objects::nonNull).collect(Collectors.toList());
                                });
                    }).collectList().map(this::mergeList);
                });
    }

    public Mono<List<PortfolioChangePointDTO>> getUserPortfolioKline(List<Long> timeCodeList, Map<Date, List<CryptoDetailPO>> balanceMap, Integer portfolioCryptoUnitId) {
        if (CollectionUtils.isEmpty(balanceMap)) {
            return Mono.just(List.of());
        }
        Map<Integer, TreeMap<Long, CryptoDetailPO>> cryptoHistoryMap = new HashMap<>();
        balanceMap.forEach((date, cryptoList) -> {
            cryptoList.forEach(crypto -> {
                if (crypto.getCryptoId() != null && date != null && crypto.getAmount() != null) {
                    cryptoHistoryMap.computeIfAbsent(crypto.getCryptoId(), k -> new TreeMap<>())
                            .put(date.getTime() / 1000L, crypto);
                }
            });
        });

        if (portfolioBaseService.requiresUnitCalculation(portfolioCryptoUnitId)) {
            Mono<NavigableMap<Long, BigDecimal>> unitCryptoIdPriceMapMono = currencyPriceService.loadPrices(portfolioCryptoUnitId, timeCodeList);
            return unitCryptoIdPriceMapMono
                    .flatMap(unitCryptoIdPriceMap -> calcUserLineInUnit(timeCodeList, cryptoHistoryMap, unitCryptoIdPriceMap).collectList()
                            .map(this::mergePortfolioChangePointDTO));
        }
        return calcUserLineInUSD(timeCodeList, cryptoHistoryMap)
                .collectList()
                .map(this::mergePortfolioChangePointDTO);
    }

    private @NotNull Flux<List<PortfolioChangePointDTO>> calcUserLineInUnit(List<Long> timeCodeList, Map<Integer, TreeMap<Long, CryptoDetailPO>> cryptoHistoryMap, NavigableMap<Long, BigDecimal> unitPriceMap) {
        return Flux.fromIterable(cryptoHistoryMap.entrySet())
                .flatMap(cryptoEntry -> {
                    Integer cryptoId = cryptoEntry.getKey();
                    TreeMap<Long, CryptoDetailPO> map = cryptoEntry.getValue();
                    Mono<NavigableMap<Long, BigDecimal>> requestCryptoIdPriceMapMono = currencyPriceService.loadPrices(cryptoId, timeCodeList);
                    return requestCryptoIdPriceMapMono
                            .map(priceMap -> timeCodeList.stream().map(timestamp -> {
                                Date date = new Date(timestamp * 1000L);
                                BigDecimal requestPrice = currencyPriceService.getPrice(priceMap, timestamp);
                                BigDecimal unitPrice = currencyPriceService.getPrice(unitPriceMap, timestamp);
                                if (requestPrice.compareTo(BigDecimal.ZERO) == 0 || unitPrice.compareTo(BigDecimal.ZERO) == 0) {
                                    return null;
                                }
                                BigDecimal price = CurrencyUtils.calculateRelativePrice(requestPrice, unitPrice);
                                CryptoDetailPO cryptoDetailPO = Optional.ofNullable(map.floorEntry(timestamp))
                                        .map(Map.Entry::getValue)
                                        .orElse(null);
                                if (cryptoDetailPO == null) {
                                    return null;
                                }
                                BigDecimal balance = cryptoDetailPO.getAmount();
                                BigDecimal totalBuy = MathUtils.getOrZero(cryptoDetailPO.getTotalBuyInUnit());
                                BigDecimal totalSell = MathUtils.getOrZero(cryptoDetailPO.getTotalSellInUnit());
                                boolean hasBuySpent = totalBuy != null && totalBuy.compareTo(BigDecimal.ZERO) != 0;
                                if (balance == null) {
                                    return PortfolioChangePointDTO.builder()
                                            .timestamp(date)
                                            .totalBuy(totalBuy)
                                            .plPercentage(BigDecimal.ZERO)
                                            .plValue(BigDecimal.ZERO)
                                            .hasBuySpent(hasBuySpent)
                                            .build();
                                }
                                BigDecimal holding = price.multiply(balance);
                                BigDecimal pl = holding.subtract(totalBuy).add(totalSell).stripTrailingZeros();
                                log.debug("calcUserLineInUnit, cryptoId:{}, timestamp:{}, price:{}, balance:{}, totalBuy:{}, totalSell:{}, pl:{}", cryptoId, timestamp, price, balance, totalBuy, totalSell, pl);

                                return PortfolioChangePointDTO.builder()
                                        .timestamp(date)
                                        .totalBuy(totalBuy)
                                        .plValue(pl)
                                        .hasBuySpent(hasBuySpent)
                                        .build();
                            }).filter(Objects::nonNull).collect(Collectors.toList()));
                });
    }

    private @NotNull Flux<List<PortfolioChangePointDTO>> calcUserLineInUSD(List<Long> timeCodeList, Map<Integer, TreeMap<Long, CryptoDetailPO>> cryptoHistoryMap) {
        return Flux.fromIterable(cryptoHistoryMap.entrySet())
                .flatMap(cryptoEntry -> {
                    Integer cryptoId = cryptoEntry.getKey();
                    TreeMap<Long, CryptoDetailPO> map = cryptoEntry.getValue();
                    Mono<NavigableMap<Long, BigDecimal>> requestCryptoIdPriceMapMono = currencyPriceService.loadPrices(cryptoId, timeCodeList);
                    return requestCryptoIdPriceMapMono
                            .map(priceMap -> timeCodeList.stream().map(timestamp -> {
                                Date date = new Date(timestamp * 1000L);
                                BigDecimal price = currencyPriceService.getPrice(priceMap, timestamp);
                                if (price.compareTo(BigDecimal.ZERO) == 0) {
                                    return null;
                                }
                                CryptoDetailPO cryptoDetailPO = Optional.ofNullable(map.floorEntry(timestamp))
                                        .map(Map.Entry::getValue)
                                        .orElse(null);
                                if (cryptoDetailPO == null) {
                                    return null;
                                }
                                BigDecimal balance = cryptoDetailPO.getAmount();
                                BigDecimal totalBuy = cryptoDetailPO.getTotalBuy();
                                BigDecimal totalSell = cryptoDetailPO.getTotalSell();
                                boolean hasBuySpent = totalBuy.compareTo(BigDecimal.ZERO) != 0;
                                if (balance == null) {
                                    return PortfolioChangePointDTO.builder()
                                            .timestamp(date)
                                            .totalBuy(totalBuy)
                                            .plPercentage(BigDecimal.ZERO)
                                            .plValue(BigDecimal.ZERO)
                                            .hasBuySpent(hasBuySpent)
                                            .build();
                                }
                                BigDecimal holding = price.multiply(balance);
                                BigDecimal pl = holding.subtract(totalBuy).add(totalSell);

                                return PortfolioChangePointDTO.builder()
                                        .timestamp(date)
                                        .totalBuy(totalBuy)
                                        .plValue(pl)
                                        .hasBuySpent(hasBuySpent)
                                        .build();
                            }).filter(Objects::nonNull).collect(Collectors.toList()));
                });
    }

    public Mono<List<PortfolioTotalDTO>> mergeMonoList(List<Mono<List<PortfolioTotalDTO>>> monoList) {
        return Flux.fromIterable(monoList)
                .flatMap(mono -> mono)
                .collectList()
                .map(this::mergeList);
    }

    private List<PortfolioTotalDTO> mergeList(List<List<PortfolioTotalDTO>> dtos) {
        List<TreeMap<Date, BigDecimal>> mapList = dtos.stream()
                .map(points -> points.stream()
                        .collect(Collectors.toMap(PortfolioTotalDTO::getTimestamp, PortfolioTotalDTO::getValue, (k1, k2) -> k2,
                                TreeMap::new)))
                .collect(Collectors.toList());

        List<Date> allPoints = dtos.stream()
                .flatMap(points -> points.stream().map(PortfolioTotalDTO::getTimestamp))
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        List<PortfolioTotalDTO> portfolioTotalDTOS = allPoints.stream().map(date -> {
            BigDecimal sumValue = mapList.stream()
                    .map(map -> map.get(date))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .stripTrailingZeros();
            return PortfolioTotalDTO.builder().timestamp(date).value(sumValue).build();
        }).collect(Collectors.toList());

        if (portfolioTotalDTOS.stream().anyMatch(point -> point.getValue().compareTo(BigDecimal.ZERO) != 0)) {
            return removeZeroFirst(portfolioTotalDTOS);
        }
        return portfolioTotalDTOS;
    }

    @NotNull
    public static List<PortfolioTotalDTO> removeZeroFirst(List<PortfolioTotalDTO> portfolioTotalDTOS) {
        return portfolioTotalDTOS.stream()
                .filter(point -> point != null && point.getTimestamp() != null && point.getValue() != null)
                .dropWhile(point -> point.getValue().compareTo(BigDecimal.ZERO) == 0)
                .collect(Collectors.toList());
    }

    public List<PortfolioChangePointDTO> mergePortfolioChangePointDTO(List<List<PortfolioChangePointDTO>> dtoLists) {
        return dtoLists.stream()
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(
                        PortfolioChangePointDTO::getTimestamp,
                        Collectors.reducing(
                                new PortfolioChangePointDTO(),
                                (dto1, dto2) -> {
                                    BigDecimal plValueSum = (dto1.getPlValue() != null ? dto1.getPlValue() : BigDecimal.ZERO)
                                            .add(dto2.getPlValue() != null ? dto2.getPlValue() : BigDecimal.ZERO);
                                    BigDecimal totalBuySum = (dto1.getTotalBuy() != null ? dto1.getTotalBuy() : BigDecimal.ZERO)
                                            .add(dto2.getTotalBuy() != null ? dto2.getTotalBuy() : BigDecimal.ZERO);
                                    boolean hasBuySpent = BooleanUtils.isTrue(dto1.getHasBuySpent()) || BooleanUtils.isTrue(dto2.getHasBuySpent());

                                    PortfolioChangePointDTO newDto = new PortfolioChangePointDTO();
                                    newDto.setTimestamp(dto2.getTimestamp());
                                    newDto.setPlValue(plValueSum.stripTrailingZeros());
                                    if (totalBuySum.compareTo(BigDecimal.ZERO) != 0) {
                                        newDto.setPlPercentage(plValueSum.divide(totalBuySum, 5, RoundingMode.HALF_EVEN)
                                                .stripTrailingZeros());
                                    }
                                    newDto.setTotalBuy(totalBuySum);
                                    newDto.setHasBuySpent(hasBuySpent);
                                    return newDto;
                                }
                        )
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(PortfolioChangePointDTO::getTimestamp))
                .collect(Collectors.toList());
    }

    public List<Long> timePointByIntervalType(int days) {
        long nowDate = Instant.now().getEpochSecond();
        long endTime = DatetimeUtils.truncateTo5MinuteForwardWithSeconds(nowDate);
        long startTime = endTime - (days * Constant.ONE_DAY_SECONDS);
        return timePointByIntervalType(startTime, endTime);
    }

    public List<Long> timePointByIntervalType(Long startTime) {
        Long seconds = DatetimeUtils.truncateTo5MinuteBackwardWithSeconds(startTime);
        return timePointByIntervalType(seconds, Duration.ofDays(defaultAllRange));
    }

    public List<Long> timePointByIntervalType(Long startTime, Duration duration) {
        long nowDate = Instant.now().getEpochSecond();
        long endTime = DatetimeUtils.truncateTo5MinuteForwardWithSeconds(nowDate);
        if (startTime == null) {
            startTime = endTime - duration.getSeconds();
        }
        int dynamicInterval = calculateInterval(startTime, endTime);
        List<Long> timeCodeList = DatetimeUtils.divideTimeRangeIntervalMinute(startTime,
                endTime, dynamicInterval);
        //添加最近的数据点
        addLatestTimeCode(timeCodeList);
        return timeCodeList.stream().distinct().sorted().collect(Collectors.toList());
    }

    public List<Long> timePointByIntervalType(long startTime, long endTime) {
        int dynamicInterval = calculateInterval(startTime, endTime);
        List<Long> timeCodeList = DatetimeUtils.divideTimeRangeIntervalMinute(startTime,
                endTime, dynamicInterval);
        //添加最近的数据点
        addLatestTimeCode(timeCodeList);
        return timeCodeList.stream().distinct().sorted().collect(Collectors.toList());
    }

    /**
     * Calculate the time interval based on the given start and end times.
     *
     * @param startTime The start time in epoch seconds.
     * @param endTime   The end time in epoch seconds.
     * @return The time interval in minutes.
     */
    private int calculateInterval(long startTime, long endTime) {
        // Calculate total minutes between the start and end times
        long totalMinutes = (endTime - startTime) / 60;
        int interval = (int) (totalMinutes / desiredPointCount);

        // Ensure the interval is at least 5 minutes
        interval = Math.max(5, interval);

        // Round down to the nearest multiple of 5
        interval = interval - (interval % 5);

        return interval;
    }

    private void addLatestTimeCode(List<Long> result) {
        //添加最近5min数据点
        long epochSecond = Instant.now().getEpochSecond();
        Long lastTime = DatetimeUtils.truncateTo5MinuteForwardWithSeconds(epochSecond);

        if ((epochSecond - lastTime) < last5mDelay) {
            result.add(lastTime - Constant.FIVE_MINUTE_BY_SECONDS);
        }

        if (!result.contains(lastTime)) {
            result.add(lastTime);
        }

        if (!result.contains(epochSecond)) {
            result.add(epochSecond);
        }
    }

}
