package com.cmc.asset.business.anti;

import com.cmc.framework.captcha.entity.CaptchaProperties;
import com.cmc.framework.captcha.entity.ValidateVO;
import com.cmc.framework.captcha.entity.constant.CaptchaTypeEnum;
import com.cmc.framework.captcha.service.ICaptchaService;
import com.cmc.framework.common.enums.MessageCode;
import com.cmc.framework.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/9/14
 */
@Service
@Slf4j
public class AntiBotServiceImpl implements AntiBotService{

    public static final DateTimeFormatter NOW_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    private static final Mono<ApiResponse<ValidateVO>> SUCCESS_MONO = Mono.just(ApiResponse.getSuccessResult());

    @Autowired
    private Map<String, ICaptchaService> multiServiceMap;



    @Override
    public Mono<ApiResponse<ValidateVO>> securityCheck(ServerHttpRequest request, String validType, String bizId) {
        String logKey = "cmc.captcha." + bizId;

        CaptchaTypeEnum captchaTypeEnum = CaptchaTypeEnum.of(validType);
        CaptchaProperties.ServiceCaptchaProperty serviceCaptchaProperty =
            CaptchaProperties.ServiceCaptchaProperty.builder().bizId(bizId).validType(validType).build();
        if (CaptchaTypeEnum.NO_CHECK.equals(captchaTypeEnum)) {
            log.debug("biz: [{}] captcha not support", bizId);
            return SUCCESS_MONO;
        }

        return multiServiceMap.get(captchaTypeEnum.getServiceName())
            .validate(request, serviceCaptchaProperty)
            .flatMap(validateVO -> {
                if (validateVO.isSuccess()) {
                    log.debug("key:{}, security check success", logKey);
                    return SUCCESS_MONO;
                }
                MessageCode errCode = validateVO.getErrCode();
                if (MessageCode.API_CAPTCHA_VALID_ERROR.equals(errCode)) {
                    log.warn("key:{} security check error, reason:{}", logKey, validateVO.getErrMsg());
                }
                return Mono.just(ApiResponse.getErrorResult(errCode, validateVO));
            });
    }
}
