package com.cmc.asset.business.portfolio.manager;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.dao.repository.mongo.PortfolioMultiRepository;
import com.cmc.asset.model.common.Constant;
import com.cmc.data.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2024/5/27 10:41
 */
@Service
@Slf4j
public class PortfolioMultiManager {

    @Autowired
    private PortfolioMultiRepository portfolioMultiRepository;

    public Mono<PortfolioMultiEntity> findPortfolioMultiEntity(String userId, String portfolioSourceId) {
        if (Constant.DEFAULT_PORTFOLIO_SOURCE.equalsIgnoreCase(portfolioSourceId)) {
            return portfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(new ObjectId(userId), portfolioSourceId)
                    .switchIfEmpty(Mono.defer(() -> portfolioMultiRepository.findFirstByUserIdAndIsMain(new ObjectId(userId), true)))
                    .switchIfEmpty(Mono.error(new BusinessException(com.cmc.asset.model.common.ErrorCodeEnums.ERROR_PORTFOLIO_PARAMS.getCode(), "portfolio not exist")));
        }
        if (!ObjectId.isValid(portfolioSourceId)) {
            return Mono.error(new BusinessException(com.cmc.asset.model.common.ErrorCodeEnums.ERROR_PORTFOLIO_PARAMS.getCode(), "portfolio not exist"));
        }
        return portfolioMultiRepository.findFirstByIdAndUserId(new ObjectId(portfolioSourceId), new ObjectId(userId))
                .switchIfEmpty(Mono.error(new BusinessException(com.cmc.asset.model.common.ErrorCodeEnums.ERROR_PORTFOLIO_PARAMS.getCode(), "portfolio not exist")));

    }

}
