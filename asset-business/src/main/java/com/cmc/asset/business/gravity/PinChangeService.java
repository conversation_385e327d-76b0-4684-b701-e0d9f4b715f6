package com.cmc.asset.business.gravity;

import com.cmc.asset.business.BaseService;
import com.cmc.asset.domain.gravity.AutoFollowDTO;
import com.cmc.asset.domain.gravity.PinChangeDTO;
import com.cmc.asset.message.SenderMessage;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.kafka.sender.KafkaSender;

/**
 * @ClassName PinChangeService.java
 * <AUTHOR>
 * @Date 2022/11/9 19:22
 */
@Slf4j
@Service
public class PinChangeService extends BaseService {

    @Resource(name = "pinChangeProducer")
    private KafkaSender<String,String> kafkaSender;

    @Value("${cmc.asset.kafka.producer.pinChange.topic}")
    private String topic;

    public void sendMessage(PinChangeDTO pinChangeDTO) {
        SenderMessage message =
            SenderMessage.builder()
                .topic(topic)
                .userId(pinChangeDTO.getUserId())
                .data(pinChangeDTO)
                .build();
        sendMQ(message, kafkaSender);
    }
}
