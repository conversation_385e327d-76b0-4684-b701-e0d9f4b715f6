package com.cmc.asset.business.portfolio.strategy;

import com.cmc.asset.business.portfolio.IPortfolioChartDataStrategy;
import com.cmc.asset.dao.entity.portfolio.PortfolioHoldingCalculationPo;
import com.cmc.asset.dao.entity.portfolio.PortfolioHoldingEntity;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/2/24
 */
public class OverSevenDaysChartDataStrategy implements IPortfolioChartDataStrategy {

    @Override
    public List<PortfolioTotalDTO> getChartData(int days, List<PortfolioHoldingEntity> portfolioHoldingEntities) {
        return portfolioHoldingEntities.stream().map(portfolioHoldingEntity -> {
            PortfolioHoldingCalculationPo calculationPo =
                portfolioHoldingEntity.getCurrentValue().get(portfolioHoldingEntity.getCurrentValue().size() - 1);
            return PortfolioTotalDTO.builder().value(calculationPo.getCalculationValue())
                .timestamp(calculationPo.getCalculationTime()).build();
        }).collect(Collectors.toList());

    }
}
