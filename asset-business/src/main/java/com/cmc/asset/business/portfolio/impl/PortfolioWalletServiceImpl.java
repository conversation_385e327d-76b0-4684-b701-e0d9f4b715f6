package com.cmc.asset.business.portfolio.impl;

import com.cmc.asset.business.portfolio.PortfolioWalletService;
import com.cmc.asset.dao.entity.PortfolioWalletEntity;
import com.cmc.asset.dao.repository.mongo.PortfolioWalletRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Description
 * <AUTHOR> Z
 * @CreateTime 2024-08-02
 */
@Service
@Slf4j
public class PortfolioWalletServiceImpl implements PortfolioWalletService {

    @Autowired
    private PortfolioWalletRepository portfolioWalletRepository;

    @Override
    public Mono<Integer> calcTotalPage(Long assetFetchTime, Integer pageSize) {
        return portfolioWalletRepository.queryPageCount(assetFetchTime)
            .map(totalCount -> {
                Long totalPages = (totalCount + pageSize - 1) / pageSize;
                return totalPages.intValue();
            });
    }

    @Override
    public Flux<PortfolioWalletEntity> queryPage(Long assetFetchTime, Integer pageNo, Integer pageSize) {
        int offset = (pageNo - 1) * pageSize;
        return portfolioWalletRepository.queryPage(assetFetchTime, offset, pageSize);
    }

    @Override
    public Mono<PortfolioWalletEntity> findByAddress(String walletAddress) {
        return portfolioWalletRepository.findOne(Example.of(PortfolioWalletEntity.builder().walletAddress(walletAddress).build()));
    }

    @Override
    public Mono<Boolean> batchInsertOrUpdate(List<PortfolioWalletEntity> entityList, Boolean upsert) {
        return portfolioWalletRepository.batchInsertOrUpdate(entityList, upsert);
    }
}
