package com.cmc.asset.business.portfolio.impl;

import static com.cmc.asset.model.common.Constant.DEFAULT_SCALE;

import com.cmc.asset.business.portfolio.IChartPointService;
import com.cmc.asset.business.portfolio.IPortfolioSnapshotService;
import com.cmc.asset.business.portfolio.manager.SnapshotManager;
import com.cmc.asset.config.DynamicApolloRefreshConfig;
import com.cmc.asset.dao.entity.portfolio.CryptoDetailPO;
import com.cmc.asset.dao.entity.vo.CryptoHistoryDataCacheVo;
import com.cmc.asset.domain.portfolio.ChartPointDTO;
import com.cmc.asset.model.common.CurrencyUtils;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.asset.model.enums.HistoricalChartDayEnum;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * HistoricalSnapshotChartPointImpl
 *
 * <AUTHOR> ricky.x
 * @date : 2023/7/16 23:36
 */
@Service
@Slf4j
public class PortfolioSnapshotServiceImpl implements IPortfolioSnapshotService {

    @Autowired
    private CurrencyPriceService currencyPriceService;

    @Autowired
    private PortfolioLineChartUtil portfolioLineChartUtil;

    @Autowired
    private IChartPointService chartPointService;

    @Autowired
    private SnapshotManager snapshotManager;
    @Autowired
    private PortfolioBaseService portfolioBaseService;

    @Autowired
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Value("${com.cmc.asset.portfolio.snapshot.enable-log:true}")
    private Boolean enableLog;

    @Override
    public Mono<List<PortfolioTotalDTO>> findHistoricalHoldingChart(String userId, String portfolioSourceId, HistoricalChartDayEnum chartDayEnum) {
        //获取时间点
        Mono<List<Long>> datePoint = queryTimeCodes(userId, portfolioSourceId, chartDayEnum);
        return queryHistoryBySnapshotData(userId, portfolioSourceId, chartDayEnum, datePoint);
    }

    @Override
    public Mono<List<Long>> queryTimeCodes(String userId, String portfolioSourceId, HistoricalChartDayEnum chartDayEnum) {
        return chartPointService.getDataPoint(
                ChartPointDTO.builder().userId(userId).sourceId(portfolioSourceId).chartDayEnum(chartDayEnum).build());
    }

    @NotNull
    private Mono<List<PortfolioTotalDTO>> queryHistoryBySnapshotData(String userId, String portfolioSourceId, HistoricalChartDayEnum chartDayEnum, Mono<List<Long>> datePoint) {
        //获取所有的快照数据
        Mono<Map<Date, List<CryptoDetailPO>>> snapshotCryptoDetailMap = snapshotManager.getSnapshotByDays(userId, portfolioSourceId, chartDayEnum, null);
        //获取对应时间点的价格,计算holding值,然后展示
        return Mono.zip(datePoint, snapshotCryptoDetailMap).flatMap(tuple2 -> {
            List<Long> timeCodeList = tuple2.getT1();
            Map<Date, List<CryptoDetailPO>> balanceMap = tuple2.getT2();

            // merge migrated tokens from balance map
            balanceMap.forEach((date, cryptoDetailList) -> {
                balanceMap.put(date, mapAndMergeMigrationTokens(cryptoDetailList));
            });

            if (enableLog) {
                log.info("findHistoricalHoldingChart userId:{}, portfolioSourceId:{}, chartDayEnum:{}, timeCodeList size: {}, balance map size: {}, timeCodeList:{}, balanceMap:{}",
                        userId, portfolioSourceId, chartDayEnum, timeCodeList.size(), balanceMap.size(), timeCodeList, balanceMap);
            }

            if (CollectionUtils.isEmpty(timeCodeList) || CollectionUtils.isEmpty(balanceMap)) {
                return Mono.just(List.of());
            }

            return portfolioLineChartUtil.getHistoryPoints(timeCodeList, balanceMap);
        });
    }

    @Override
    public Mono<BigDecimal> getYesterdayHolding(String userId, String portfolioSourceId, Integer cryptoId, Date currentTime, Integer cryptoUnit) {
        Date dateStart = DatetimeUtils.getDaysAgo(currentTime, 1);
        Long dateStartTime = dateStart.getTime() / 1000L;
        return snapshotManager.getSnapshotByDate(userId, portfolioSourceId, dateStart, cryptoUnit)
                .flatMap(snapshot -> {
                    if (CollectionUtils.isEmpty(snapshot.getCryptoDetails())) {
                        return Mono.just(BigDecimal.ZERO);
                    }
                    Map<Integer, BigDecimal> cryptoAmountMap = snapshot.getCryptoDetails().stream()
                            .filter(e -> cryptoId == null || cryptoId.equals(e.getCryptoId()))
                            .collect(Collectors.toMap(CryptoDetailPO::getCryptoId, CryptoDetailPO::getAmount));
                    return getPriceCellTable(new ArrayList<>(cryptoAmountMap.keySet()), dateStartTime, cryptoUnit)
                            .flatMap(priceCellTable -> calHoldingByPriceAndAmount(portfolioSourceId, cryptoAmountMap, priceCellTable, dateStartTime));
                })
                .defaultIfEmpty(BigDecimal.ZERO)
                .onErrorResume(throwable -> {
                    log.error("findHistoricalHoldingChart getYesterdayHolding error,userId:{},portfolioSourceId:{}", userId,
                            portfolioSourceId, throwable);
                    return Mono.just(BigDecimal.ZERO);
                });
    }

    @Override
    public Mono<Map<Date, List<CryptoDetailPO>>> getSnapshotByDays(String userId, String portfolioSourceId, HistoricalChartDayEnum dayEnum, Integer cryptoUnit) {
        return snapshotManager.getSnapshotByDays(userId, portfolioSourceId, dayEnum, cryptoUnit);
    }

    @Override
    public Mono<Boolean> duplicateData(ObjectId userId, String originPortfolioSourceId, ObjectId newPortfolioSourceId) {
        return snapshotManager.duplicateData(userId, originPortfolioSourceId, newPortfolioSourceId);
    }

    private Mono<Table<Integer, Long, BigDecimal>> getPriceCellTable(List<Integer> cryptoIdList,
                                                                     Long timestamp, Integer cryptoUnit) {
        List<Tuple2<Integer, Long>> tupleList =
                cryptoIdList.stream().map(e -> Tuples.of(e, timestamp)).collect(Collectors.toList());
        Mono<List<CryptoHistoryDataCacheVo>> cryptoPrices = currencyPriceService.getPrices(tupleList).collectList();
        if (portfolioBaseService.requiresUnitCalculation(cryptoUnit)) {
            Mono<BigDecimal> cryptoUnitPriceMono = currencyPriceService.queryHistoryCurrentPrice(cryptoUnit, timestamp)
                    .map(CryptoHistoryDataCacheVo::getPriceUsd);
            return Mono.zip(cryptoPrices, cryptoUnitPriceMono).flatMap(tuple -> {
                List<CryptoHistoryDataCacheVo> priceList = tuple.getT1();
                BigDecimal cryptoUnitPrice = tuple.getT2();
                if (enableLog) {
                    log.info("getPriceCellTable, cryptoIdList:{}, timestamp:{}, cryptoUnit:{}, cryptoUnitPrice:{}, priceList:{}",
                            cryptoIdList, timestamp, cryptoUnit, cryptoUnitPrice, priceList);
                }

                if (CollectionUtils.isEmpty(priceList)) {
                    return Mono.empty();
                }
                Table<Integer, Long, BigDecimal> priceCellTable = createPriceCellTable(priceList, cryptoUnitPrice);
                if (priceCellTable.isEmpty()) {
                    return Mono.empty();
                }
                return Mono.just(priceCellTable);
            });
        }
        return cryptoPrices.flatMap(priceList -> {
            if (CollectionUtils.isEmpty(priceList)) {
                return Mono.empty();
            }
            Table<Integer, Long, BigDecimal> priceCellTable = createPriceCellTable(priceList, null);
            if (priceCellTable.isEmpty()) {
                return Mono.empty();
            }
            return Mono.just(priceCellTable);
        });
    }

    private Table<Integer, Long, BigDecimal> createPriceCellTable(List<CryptoHistoryDataCacheVo> cryptoPriceList, BigDecimal cryptoUnitPrice) {
        Table<Integer, Long, BigDecimal> priceCellTable = HashBasedTable.create();
        for (CryptoHistoryDataCacheVo cryptoPrice : cryptoPriceList) {
            BigDecimal price = cryptoUnitPrice == null ? cryptoPrice.getPriceUsd() : CurrencyUtils.calculateRelativePrice(cryptoPrice.getPriceUsd(), cryptoUnitPrice);
            if (cryptoPrice.getScore() == null || price == null) {
                log.warn("cryptoPriceService return empty,cryptoPrice:{},cryptoUnitPrice:{}", cryptoPrice, cryptoUnitPrice);
                continue;
            }
            priceCellTable.put(cryptoPrice.getId(), cryptoPrice.getScore(), price);
        }
        return priceCellTable;
    }

    private Mono<BigDecimal> calHoldingByPriceAndAmount(String portfolioSourceId, Map<Integer, BigDecimal> cryptoAmountMap,
                                                        Table<Integer, Long, BigDecimal> priceCellTable, Long dateStart) {
        List<BigDecimal> holdingList = new ArrayList<>();
        for (Integer saveCryptoId : cryptoAmountMap.keySet()) {
            BigDecimal balance= cryptoAmountMap.get(saveCryptoId);
            if (balance == null) {
                continue;
            }
            if (!priceCellTable.containsRow(saveCryptoId)) {
                log.debug("calHoldingByPriceAndAmount return empty,portfolioSourceId:{},saveCryptoId:{},cryptoAmountMap:{},cryptoPrice:{}",
                        portfolioSourceId, saveCryptoId, cryptoAmountMap, priceCellTable);
                continue;
            }
            BigDecimal price = priceCellTable.get(saveCryptoId, dateStart);
            if (price == null) {
                continue;
            }
            if (enableLog) {
                log.info("calHoldingByPriceAndAmount, portfolioSourceId:{},saveCryptoId:{},price:{},amount:{}", portfolioSourceId, saveCryptoId, price, balance);
            }
            holdingList.add(price.multiply(balance));
        }
        BigDecimal holding = holdingList.stream().reduce(BigDecimal.ZERO, BigDecimal::add).stripTrailingZeros();
        return Mono.just(holding);
    }


    private List<CryptoDetailPO> mapAndMergeMigrationTokens(List<CryptoDetailPO> portfolios) {
        Map<Integer, Integer> tokenMigrationMap = dynamicApolloRefreshConfig.getTokenMigrationMap();
        List<CryptoDetailPO> mergedPortfolioList = new ArrayList<>();
        Map<Integer, CryptoDetailPO> plMap = new HashMap<>();
        portfolios.forEach(pl -> {
            if (tokenMigrationMap.containsKey(pl.getCryptoId())) {
                pl.setCryptoId(tokenMigrationMap.get(pl.getCryptoId()));
            }

            if (plMap.containsKey(pl.getCryptoId())) {
                CryptoDetailPO mergedPl = plMap.get(pl.getCryptoId());
                BigDecimal totalAmount = mergedPl.getAmount().add(pl.getAmount());
                BigDecimal totalCost = mergedPl.getTotalBuy().add(pl.getTotalBuy());
                mergedPl.setAmount(totalAmount);
                mergedPl.setTotalBuy(totalCost);

                if (mergedPl.getBuyTotal() != null && pl.getBuyTotal() != null) {
                    mergedPl.setBuyTotal(mergedPl.getBuyTotal().add(pl.getBuyTotal()));
                }

                if (mergedPl.getTotalSell() != null && pl.getTotalSell() != null) {
                    mergedPl.setTotalSell(mergedPl.getTotalSell().add(pl.getTotalSell()));
                }

                BigDecimal totalBuyAmount = Objects.isNull(mergedPl.getBuyAmount()) ? BigDecimal.ZERO : mergedPl.getBuyAmount();
                totalBuyAmount = totalBuyAmount.add(Objects.isNull(pl.getBuyAmount()) ? BigDecimal.ZERO : pl.getBuyAmount());
                if (totalBuyAmount.compareTo(BigDecimal.ZERO) == 1) {
                    mergedPl.setBuyAvgPrice(totalCost.divide(totalBuyAmount, DEFAULT_SCALE, RoundingMode.HALF_EVEN));
                } else {
                    mergedPl.setBuyAvgPrice(BigDecimal.ZERO);
                }

                mergedPl.setBuyAmount(totalBuyAmount);
                return;
            }

            plMap.put(pl.getCryptoId(), pl);
            mergedPortfolioList.add(pl);
        });

        return mergedPortfolioList;
    }

}
