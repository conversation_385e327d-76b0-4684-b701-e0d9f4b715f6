package com.cmc.asset.business.dex.impl;

import com.cmc.asset.business.dex.DexDataHubService;
import com.cmc.asset.cache.DexerPlatformCache;
import com.cmc.asset.dao.entity.vo.DexTokenDetailCacheVo;
import com.cmc.asset.dao.repository.redis.CryptoCacheRepository;
import com.cmc.asset.domain.dex.DataHubTokenDetailDTO;
import com.cmc.asset.integration.DexDataHubClient;
import com.cmc.asset.mapstruct.DataHubDexTokenMapper;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;


/**
 * DexDataHubServiceImpl
 * <AUTHOR> ricky.x
 * @date: 2025/6/4 17:09
 */
@Service
@Slf4j
public class DexDataHubServiceImpl implements DexDataHubService {

    @Autowired
    private DexDataHubClient dexDataHubClient;

    @Autowired
    private CryptoCacheRepository cryptoCacheRepository;

    @Autowired
    private DexerPlatformCache dexerPlatformCache;

    private static final int BATCH_SIZE = 100;

    private static final String ADDRESS_SEPARATOR = "_";

    @Value("${cmc.asset.datahub.dex-token.cache-time:120}")
    private Long cacheTimeSec;

    @Override
    public Mono<Map<String, DexTokenDetailCacheVo>> batchGetDexTokenMapWithCache(Set<String> chainTokenStrList) {
        return batchGetDexTokenWithCache(chainTokenStrList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream().collect(Collectors.toMap(DexTokenDetailCacheVo::getUniKey, Function.identity(), (o1, o2) -> o1)))
                .defaultIfEmpty(Map.of());
    }


    @Override
    public Mono<List<DexTokenDetailCacheVo>> batchGetDexTokenWithCache(Set<String> uniKeySet) {
        if (CollectionUtils.isEmpty(uniKeySet)) {
            return Mono.just(List.of());
        }
        return getDexTokensFromrRedis(uniKeySet)
            .flatMap(cachedResults -> {
                Set<String> uncachedAddresses = missTokens(cachedResults, uniKeySet);
                if (CollectionUtils.isEmpty(uncachedAddresses)) {
                    return Mono.just(new ArrayList<>(cachedResults.values()));
                }
                return processUncachedTokensAndCache(uncachedAddresses)
                        .map(fetchedTokens -> combineResults(cachedResults, fetchedTokens));
            });
    }

    /**
     * 从缓存中获取token数据
     */
    private Mono<Map<String, DexTokenDetailCacheVo>> getDexTokensFromrRedis(Set<String> uniKeySet) {
        return cryptoCacheRepository.getDexTokens(uniKeySet)
                .filter(StringUtils::isNotBlank)
                .map(e -> JacksonUtils.deserialize(e, DexTokenDetailCacheVo.class))
                .collectList()
                .map(list -> list.stream().collect(Collectors.toMap(DexTokenDetailCacheVo::getUniKey,e -> e)))
                .publishOn(Schedulers.boundedElastic());
    }

    /**
     * 分离缓存命中和未命中的token
     */
    private Set<String> missTokens(Map<String, DexTokenDetailCacheVo>cachedResults, Set<String> allAddresses) {
        if(CollectionUtils.isEmpty(cachedResults)){
            return allAddresses;
        }
        return allAddresses
                .stream().filter(e -> !cachedResults.containsKey(e)).collect(Collectors.toSet());
    }

    /**
     * 处理未缓存的token请求
     */
    private Mono<List<DexTokenDetailCacheVo>> processUncachedTokensAndCache(Set<String> uncachedAddresses) {
        return Flux.fromIterable(uncachedAddresses)
                .buffer(BATCH_SIZE)
                .flatMap(this::processBatch, 4)
                .flatMapIterable(e -> e)
                .collectList()
                .map(e -> e.stream().map(x -> {
                    DexTokenDetailCacheVo cacheVo = DataHubDexTokenMapper.INSTANCE.buildDexTokenCacheVo(x);
                    cacheVo.setUniKey(buildUniKey(x.getPlatformId(), x.getAddress()));
                    return cacheVo;
                }).collect(Collectors.toList()))
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(cacheList -> cryptoCacheRepository.cacheDexTokens(cacheList, cacheTimeSec).subscribe());
    }


    private static String buildUniKey(Integer platformId,String address) {
        return String.valueOf(platformId).concat(ADDRESS_SEPARATOR).concat(address);
    }

    /**
     * 处理单个批次的请求
     */
    private Mono<List<DataHubTokenDetailDTO>> processBatch(List<String> batch) {
        Map<String,List<String>> batchPlatformMap = groupByPlatform(batch);
        return Flux.fromIterable(batchPlatformMap.entrySet())
            .flatMap(entry -> fetchAndCacheTokens(entry.getKey(), entry.getValue()))
            .collectList()
            .map(tokensList -> tokensList.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList()));
    }

    /**
     * 按平台分组
     */
    private Map<String, List<String>> groupByPlatform(List<String> uniKeySet) {
        return uniKeySet.stream()
                .filter(Objects::nonNull)
                .filter(s -> s.contains(ADDRESS_SEPARATOR))
                .map(s -> {
                    int sepIndex = s.indexOf(ADDRESS_SEPARATOR);
                    if (sepIndex == -1 || sepIndex == 0 || sepIndex == s.length() - 1) {
                        return null;
                    }
                    String platform = s.substring(0, sepIndex);
                    String address = s.substring(sepIndex + ADDRESS_SEPARATOR.length());
                    return new AbstractMap.SimpleEntry<>(platform, address);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
    }

    /**
     * 获取并缓存token数据
     */
    private Mono<List<DataHubTokenDetailDTO>> fetchAndCacheTokens(String platform, List<String> addresses) {
        PlatformNewDTO platformDTO = dexerPlatformCache.getPlatformsById(Integer.parseInt(platform));
        if(platformDTO == null || StringUtils.isBlank(platformDTO.getDn())){
            return Mono.just(List.of());
        }
        return dexDataHubClient.getTokenDetail(platformDTO.getDn(), addresses)
                .defaultIfEmpty(List.of())
                .onErrorReturn(List.of());
    }


    /**
     * 合并缓存和新获取的数据
     */
    private List<DexTokenDetailCacheVo> combineResults(Map<String, DexTokenDetailCacheVo> cachedTokens, List<DexTokenDetailCacheVo> fetchedTokens) {
        if(CollectionUtils.isEmpty(cachedTokens)){
            return fetchedTokens;
        }
        if(CollectionUtils.isEmpty(fetchedTokens)){
            return new ArrayList<>(cachedTokens.values());
        }
        List<DexTokenDetailCacheVo> result = new ArrayList<>(cachedTokens.values());
        result.addAll(fetchedTokens);
        return result;
    }
}