package com.cmc.asset.business.watchlist.impl;

import com.cmc.asset.business.portfolio.DexerService;
import com.cmc.asset.business.watchlist.strategy.WatchListHandleStrategy;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.GuestWatchListEntity;
import com.cmc.asset.dao.repository.mongo.guest.GuestWatchListRepository;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.locale.LocaleConstants;
import com.cmc.asset.model.contract.dexer.DexTokenDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.enums.ErrorMessageEnum;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.exception.ArgumentException;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.data.common.utils.LocaleMessageSource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/2/1
 */
@Service
@Slf4j
public class GuestWatchlistHandleStrategyImpl implements WatchListHandleStrategy {
    @Value("${com.cmc.asset.business.watchlist.guest.list-size}")
    private int size;

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Autowired
    private GuestWatchListRepository guestWatchListRepository;


    @Autowired
    private DexerService dexerService;

    @Override
    public boolean isResourceTypeMatched(ResourceTypeEnum resourceType) {
        return false;
    }

    @Override
    public boolean isMatched(SubscribeParamDTO param) {
        return null != param.getGuestId();
    }

    @Override
    public Mono<WatchListSubscribeResultDTO> subscribe(SubscribeParamDTO param, BaseRequest request) {
        //subscribe
        return guestSubscribe(param)
            .map(m -> WatchListSubscribeResultDTO.builder()
            .watchListId(m.getId().toHexString())
            .main(Boolean.TRUE)
            .build());
    }

    private Mono<GuestWatchListEntity> guestSubscribe(SubscribeParamDTO param) {
        ResourceTypeEnum resourceType = param.getResourceType();
        switch (resourceType) {
            case CRYPTO:
                return doSubscribeCrypto(param);
            case DEX_TOKEN:
                return subscribeDexToken(param);
            default:
                return subscribeDexPair(param);
        }
    }

    private Mono<GuestWatchListEntity> doSubscribeCrypto(SubscribeParamDTO param) {
        final Long guestId = param.getGuestId();
        log.info("guest crypto subscribe: {}", param);
        Set<Integer> cryptoIds = null;

        List<Integer> inputList = param.getResourceIds().stream().map(Math::toIntExact).collect(
            Collectors.toList());

        cryptoIds = cryptoCurrencyCache.getByIds(inputList);

        if (CollectionUtils.isEmpty(cryptoIds)) {
            BusinessException.throwIfMessage(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND));
        }

        boolean isAdded = SubscribeTypeEnum.SUBSCRIBE.equals(param.getSubscribeType());

        List<Integer> finalInputList = inputList;
        return guestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(guestId)
            .onErrorResume(throwable -> {
                log.error("find watchlist failed.", throwable);
                return Mono.empty();
            })
            .defaultIfEmpty(GuestWatchListEntity.builder().cryptos(new HashSet<>(finalInputList.size())).guestId(guestId).build())
            .filter(guestWatchListEntity -> CollectionUtils.isEmpty(guestWatchListEntity.getCryptos()) ||
                guestWatchListEntity.getCryptos().size() < size || !isAdded)
            .flatMap(guestWatchListEntity -> {
                int existingSize = CollectionUtils.isEmpty(guestWatchListEntity.getCryptos()) ? 0 :
                    guestWatchListEntity.getCryptos().size();
                int remainingAddSize = size - existingSize;
                Set<Integer> finalAddSet = isAdded ? finalInputList.stream().limit(remainingAddSize)
                    .collect(Collectors.toSet()) : new HashSet<>(finalInputList);

                if (remainingAddSize < finalInputList.size() && isAdded) {
                    return guestWatchListRepository.updateCryptosByGuestId(guestId, isAdded, finalAddSet)
                        .then(Mono.error(new ArgumentException(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getCode(),
                            String.format(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getDesc(), size))));
                } else {
                    return guestWatchListRepository.updateCryptosByGuestId(guestId, isAdded, finalAddSet);
                }
            })
            .switchIfEmpty(Mono.error(new ArgumentException(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getCode(),
                String.format(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getDesc(), size))));

    }

    private Mono<GuestWatchListEntity> subscribeDexPair(SubscribeParamDTO param){
        log.info("guest dex pair subscribe: {}", param);
        // check parameter
        final Long guestId = param.getGuestId();
        List<Long> inputList = param.getResourceIds();

        if (CollectionUtils.isEmpty(inputList)) {
            BusinessException.throwIfMessage(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND));
        }
        boolean isAdded = SubscribeTypeEnum.SUBSCRIBE.equals(param.getSubscribeType());

        List<Long> finalInputList = inputList;
        return guestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(guestId)
            .onErrorResume(throwable -> {
                log.error("find watchlist failed.", throwable);
                return Mono.empty();
            })
            .defaultIfEmpty(GuestWatchListEntity.builder().cryptos(new HashSet<>())
                    .dexPairs(new ArrayList<>(finalInputList.size())).guestId(guestId).build())
            .filter(guestWatchListEntity -> CollectionUtils.isEmpty(guestWatchListEntity.getDexPairs()) ||
                guestWatchListEntity.getDexPairs().size() < size || !isAdded)
                .zipWith(getDexTokenFormDexPair(inputList))
            .flatMap(tuple2 -> {
                int existingSize = CollectionUtils.isEmpty(tuple2.getT1().getDexPairs()) ? 0 :
                        tuple2.getT1().getDexPairs().size();
                int remainingAddSize = size - existingSize;
                Set<Long> finalAddSet = isAdded ? finalInputList.stream().limit(remainingAddSize)
                    .collect(Collectors.toSet()) : new HashSet<>(finalInputList);

                if (remainingAddSize < finalInputList.size() && isAdded) {
                    return guestWatchListRepository.updateDexPairsByGuestId(guestId, isAdded, finalAddSet,tuple2.getT2())
                        .then(Mono.error(new ArgumentException(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getCode(),
                            String.format(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getDesc(), size))));
                } else {
                    return guestWatchListRepository.updateDexPairsByGuestId(guestId, isAdded, finalAddSet, tuple2.getT2());
                }
            })
            .switchIfEmpty(Mono.error(new ArgumentException(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getCode(),
                String.format(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getDesc(), size))));
    }



    private Mono<GuestWatchListEntity> subscribeDexToken(SubscribeParamDTO param){
        log.info("guest dex token subscribe: {}", param);
        // check parameter
        final Long guestId = param.getGuestId();
        List<String> inputList = param.getDexTokenAddress();

        if (CollectionUtils.isEmpty(inputList)) {
            BusinessException.throwIfMessage(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND));
        }
        boolean isAdded = SubscribeTypeEnum.SUBSCRIBE.equals(param.getSubscribeType());

        return guestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(guestId)
                .onErrorResume(throwable -> {
                    log.error("find watchlist failed.", throwable);
                    return Mono.empty();
                })
                .defaultIfEmpty(GuestWatchListEntity.builder().cryptos(new HashSet<>())
                        .dexTokens(List.of()).guestId(guestId).build())
                .filter(guestWatchListEntity -> CollectionUtils.isEmpty(guestWatchListEntity.getDexTokens()) ||
                        guestWatchListEntity.getDexTokens().size() < size || !isAdded)
                .flatMap(entity -> {
                    int existingSize = CollectionUtils.isEmpty(entity.getDexPairs()) ? 0 :
                            entity.getDexPairs().size();
                    int remainingAddSize = size - existingSize;
                    Set<String> finalAddSet = isAdded ? inputList.stream().limit(remainingAddSize)
                            .collect(Collectors.toSet()) : new HashSet<>(inputList);
                    if (remainingAddSize < inputList.size() && isAdded) {
                        return guestWatchListRepository.updateDexTokenByGuestId(guestId, isAdded, finalAddSet)
                                .then(Mono.error(new ArgumentException(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getCode(),
                                        String.format(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getDesc(), size))));
                    } else {
                        return guestWatchListRepository.updateDexTokenByGuestId(guestId, isAdded, finalAddSet);
                    }
                })
                .switchIfEmpty(Mono.error(new ArgumentException(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getCode(),
                        String.format(ErrorMessageEnum.GUEST_WATCHLIST_LIMIT.getDesc(), size))));
    }


    private Mono<Set<String>> getDexTokenFormDexPair(List<Long> resourceIds){
        if(CollectionUtils.isEmpty(resourceIds)){
            return Mono.just(Set.of());
        }
        return dexerService.getPairs(resourceIds)
                .map(map -> new ArrayList<>(map.values()))
                .map(e -> e.stream().map(this::buildDexTokenStr).filter(StringUtils::isNotBlank).collect(Collectors.toSet()))
                .onErrorResume(error -> Mono.just(Set.of()))
                .defaultIfEmpty(Set.of());
    }

    private String buildDexTokenStr(PairInfoDTO pairInfo){
        if(pairInfo == null || pairInfo.getPlatformId() == null || pairInfo.getBaseToken() == null) {
            log.warn("DefaultWatchListHandleStrategyImpl buildDexTokenStr empty:{}", pairInfo);
            return null;
        }
        DexTokenDTO baseToken = pairInfo.getBaseToken();
        if(StringUtils.isBlank(baseToken.getAddress())){
            log.warn("DefaultWatchListHandleStrategyImpl buildDexTokenStr empty:{}", pairInfo);
            return null;
        }
        return String.valueOf(pairInfo.getPlatformId()).concat(":").concat(baseToken.getAddress());
    }

}
