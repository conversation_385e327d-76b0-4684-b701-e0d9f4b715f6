package com.cmc.asset.business.watchlist.impl;

import static com.cmc.asset.model.enums.TopCoinsSourceEnum.WATCHLIST;

import com.cmc.asset.business.coins.IUserCoinService;
import com.cmc.asset.business.gravity.AutoFollowService;
import com.cmc.asset.business.gravity.SubscribeChangeService;
import com.cmc.asset.business.portfolio.DexerService;
import com.cmc.asset.business.watchlist.AsyncWatchListService;
import com.cmc.asset.business.watchlist.strategy.WatchListHandleStrategy;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.cache.ExchangeCache;
import com.cmc.asset.dao.entity.FollowWatchListEntity;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.dao.repository.mongo.CryptoWatchCountRepository;
import com.cmc.asset.dao.repository.mongo.FollowWatchListRepositoryImpl;
import com.cmc.asset.dao.repository.mongo.WatchListRespositoryImpl;
import com.cmc.asset.domain.gravity.AutoFollowDTO;
import com.cmc.asset.domain.gravity.SubscribeChangeDTO;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.locale.LocaleConstants;
import com.cmc.asset.model.contract.dexer.DexTokenDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.data.common.utils.LocaleMessageSource;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;

/** Default watchlist handle strategy for crypto, exchange, market pair
 * <AUTHOR>
 * @date 2022/12/14
 */
@Service
@Slf4j
public class DefaultWatchListHandleStrategyImpl implements WatchListHandleStrategy {
    @Value("${com.cmc.asset.autoFillFollow:false}")
    private boolean autoFillFollow;
    @Value("${com.cmc.asset.autoAddFollow:false}")
    private boolean autoAddFollow;
    @Value("${com.cmc.asset.pinChange:false}")
    private boolean pinChange;
    @Value("${com.cmc.asset.subscribeChange:false}")
    private boolean subscribeChange;

    @Autowired
    private WatchListRespositoryImpl watchListRespository;
    @Autowired
    private FollowWatchListRepositoryImpl followWatchListRepository;
    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;
    @Autowired
    private ExchangeCache exchangeCache;
    @Autowired
    private AsyncWatchListService asyncWatchListService;
    @Autowired
    private IUserCoinService userCoinService;
    @Autowired
    private CryptoWatchCountRepository cryptoWatchCountRepository;
    @Autowired
    private AutoFollowService autoFollowService;
    @Autowired
    private SubscribeChangeService subscribeChangeService;

    @Autowired
    private DexerService dexerService;

    /**
     * 防止灰度机器中数据不统一 等发版完成后统一设置为true放开
     */
    @Value("${com.cmc.asset.addDexPairMatched:false}")
    private boolean addDexPairMatched;

    @Override
    public boolean isResourceTypeMatched(ResourceTypeEnum resourceType) {
        if(addDexPairMatched){
            return resourceType == null || ResourceTypeEnum.CRYPTO == resourceType
                || ResourceTypeEnum.EXCHANGE == resourceType || ResourceTypeEnum.MARKETPAIR == resourceType
                || ResourceTypeEnum.DEX_PAIR == resourceType    || ResourceTypeEnum.DEX_TOKEN == resourceType;
        }
        return resourceType == null || ResourceTypeEnum.CRYPTO == resourceType
            || ResourceTypeEnum.EXCHANGE == resourceType || ResourceTypeEnum.MARKETPAIR == resourceType;
    }

    @Override
    public boolean isMatched(SubscribeParamDTO param) {
        ResourceTypeEnum resourceType = param.getResourceType();
        return  isResourceTypeMatched(param.getResourceType()) && StringUtils.isNotEmpty(param.getUserId())
            && ObjectUtils.isEmpty(param.getGuestId());
    }

    @Override
    public Mono<WatchListSubscribeResultDTO> subscribe(SubscribeParamDTO subscribeParam, BaseRequest request) {
        ResourceTypeEnum resourceType = subscribeParam.getResourceType();
        switch (resourceType) {
            case DEX_PAIR:
                return doSubscribeDexPair(subscribeParam,request);
            case DEX_TOKEN:
                return doSubscribeDexToken(subscribeParam,request);
            case EXCHANGE:
            case MARKETPAIR:
            case CRYPTO:
                return doSubscribeCrypto(subscribeParam,request);
            default:
                return doSubscribeCrypto(subscribeParam,request);
        }
    }

    private Mono<WatchListSubscribeResultDTO> doSubscribeDexToken(SubscribeParamDTO subscribeParam, BaseRequest request) {
        // check parameter
        if (CollectionUtils.isEmpty(subscribeParam.getDexTokenAddress())) {
            BusinessException.throwIfMessage(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND));
        }
        ResourceTypeEnum resourceType = subscribeParam.getResourceType();
        boolean isAdded = subscribeParam.getSubscribeType() == SubscribeTypeEnum.SUBSCRIBE;
        return findMainWatchList(subscribeParam)
                .flatMap(mainWatch -> watchListRespository.addDexSource(mainWatch, resourceType,
                        subscribeParam.getDexTokenAddress().toArray(new String[0]), isAdded, subscribeParam.getNeedReplaceAllResourceIds()))
                .map(w -> WatchListSubscribeResultDTO.builder().watchListId(w.getId().toHexString()).build())
                .onErrorResume(throwable -> {
                    log.error("find watchlist failed.", throwable);
                    return Mono.empty();
                });
    }


    private Mono<WatchListEntity> findMainWatchList(SubscribeParamDTO subscribeParam) {
        WatchListEntity watchListEntity = WatchListEntity.builder()
                .id(StringUtils.isBlank(subscribeParam.getWatchListId()) ? null : new ObjectId(subscribeParam.getWatchListId()))
                .userId(subscribeParam.getUserId())
                .uid(subscribeParam.getUid())
                .build();
        return watchListRespository.findAndCombineMainWatchList(watchListEntity)
                .map(Tuple2::getT2);
    }

    private Mono<WatchListSubscribeResultDTO> doSubscribeCrypto(SubscribeParamDTO subscribeParam, BaseRequest request){
        return doSubscribe(subscribeParam)
            .map(m -> WatchListSubscribeResultDTO.builder()
                .watchListId(m.getId().toHexString())
                .main(m.getMain())
                .build())
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(v -> {
                resetTopCoins(request);
                log.info("user {} {} a watchList: {}", subscribeParam.getUserId(),
                    subscribeParam.getSubscribeType().name(), subscribeParam);
                if (autoAddFollow && subscribeParam.getSubscribeType() == SubscribeTypeEnum.SUBSCRIBE) {
                    autoFollowService.sendMessage(
                        createAutoFollowDTO(subscribeParam.getResourceIds().stream().map(Math::toIntExact).collect(
                            Collectors.toList()), subscribeParam.getUserId(), "add"));
                }

                if (subscribeChange && subscribeParam.getResourceType() == ResourceTypeEnum.CRYPTO &&
                    Boolean.TRUE.equals(v.getMain())) {
                    subscribeChangeService.sendMessage(createSubscribeChangeDTO(subscribeParam));
                }
            });
    }

    private Mono<WatchListSubscribeResultDTO> doSubscribeDexPair(SubscribeParamDTO subscribeParam, BaseRequest request){
        // check parameter
        if (CollectionUtils.isEmpty(subscribeParam.getResourceIds())) {
            BusinessException.throwIfMessage(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND));
        }
        return findMainWatchList(subscribeParam)
            .flatMap(mainWatch -> saveDexPairAndToken(mainWatch, subscribeParam))
            .map(w -> WatchListSubscribeResultDTO.builder().watchListId(w.getId().toHexString()).build())
            .onErrorResume(throwable -> {
                log.error("find watchlist failed.", throwable);
                return Mono.empty();
            });
    }


    private Mono<WatchListEntity> saveDexPairAndToken(WatchListEntity watchListEntity,SubscribeParamDTO subscribeParam){
        boolean isAdded = subscribeParam.getSubscribeType() == SubscribeTypeEnum.SUBSCRIBE;
        Boolean needReplaceAllResourceIds = subscribeParam.getNeedReplaceAllResourceIds();
        List<Long> resourceIds = subscribeParam.getResourceIds();
        return getDexTokenFormDexPair(resourceIds)
                .flatMap(e -> watchListRespository.addDexPairAndToken(watchListEntity,
                        resourceIds.toArray(new Object[0]),e.toArray(new Object[0]),isAdded,needReplaceAllResourceIds));
    }


    private Mono<List<String>> getDexTokenFormDexPair(List<Long> resourceIds){
        if(CollectionUtils.isEmpty(resourceIds)){
            return Mono.just(List.of());
        }
        return dexerService.getPairs(resourceIds)
                .map(map -> new ArrayList<>(map.values()))
                .map(e -> e.stream().map(this::buildDexTokenStr).filter(StringUtils::isNotBlank).collect(Collectors.toList()))
                .onErrorResume(error -> Mono.just(List.of()))
                .defaultIfEmpty(List.of());
    }

    private String buildDexTokenStr(PairInfoDTO pairInfo){
        if(pairInfo == null || pairInfo.getPlatformId() == null || pairInfo.getBaseToken() == null) {
            log.warn("DefaultWatchListHandleStrategyImpl buildDexTokenStr empty:{}", pairInfo);
            return null;
        }
        DexTokenDTO baseToken = pairInfo.getBaseToken();
        if(StringUtils.isBlank(baseToken.getAddress())){
            log.warn("DefaultWatchListHandleStrategyImpl buildDexTokenStr empty:{}", pairInfo);
            return null;
        }
        return String.valueOf(pairInfo.getPlatformId()).concat("_").concat(baseToken.getAddress());
    }

    private Mono<WatchListEntity> followResource(
        Boolean initWatchlist,
        ObjectId id,
        WatchListEntity watchListEntity,
        Boolean isAdded,
        Boolean needReplaceAllResourceIds) {
        watchListEntity.setId(id);
        if(initWatchlist){
            return followWatchListRepository.findAndModify(FollowWatchListEntity.builder()
                    .userId(watchListEntity.getUserId())
                    .build(), null, 0, false)
                .filter(f -> f.getVersion() == 1)
                .flatMap(f -> watchListRespository.followResources(watchListEntity, isAdded, needReplaceAllResourceIds,false));
        }
        return watchListRespository.followResources(watchListEntity, isAdded, needReplaceAllResourceIds,false);
    }

    /**
     * TODO 产品要求是精确度不高，后续精确要高的只能用JOB处理，
     * 在币的subscribe/unsubscribe时，只做了单个币计算。多个币的情况是只有cookie合并才会发生的，因为这部分计算
     * 已经加入到统计了，不然就重复计算了。
     * 为了统这个专门查一DB ，后续优化的可以直接去掉。
     */
    private void addWatchCryptoStats(SubscribeParamDTO param, boolean isAdded, WatchListEntity f) {

        if (param.getResourceType() == ResourceTypeEnum.CRYPTO
            && CollectionUtils.size(param.getResourceIds()) > 0) {
            int id = Math.toIntExact(param.getResourceIds().get(0));
            int num;
            if (CollectionUtils.isNotEmpty(f.getCryptos())) {
                num = isAdded ? !f.getCryptos().contains(id) ? 1 : 0 :
                    f.getCryptos().contains(id) ? -1 : 0;
            } else {
                num = 1;
            }
            try {
                if (num != 0) {
                    cryptoWatchCountRepository.incrCryptoWatchCount(id, num).subscribe();
                }
            } catch (Exception ex) {
                log.error("add Watch Crypto Stats failed.", ex);
            }
        }
    }

    private Mono<WatchListEntity> doSubscribe(SubscribeParamDTO param) {

        Set<Integer> cryptoIds = null;
        Set<Integer> exchangeIds = null;
        Set<Integer> marketPairs = null;
        Set<Integer> inputSet = param.getResourceIds().stream().map(Math::toIntExact).collect(
            Collectors.toSet());
        if (param.getResourceType() == ResourceTypeEnum.CRYPTO) {
            cryptoIds = cryptoCurrencyCache.getByIds(inputSet);
        } else if (param.getResourceType() == ResourceTypeEnum.EXCHANGE) {
            exchangeIds = exchangeCache.getByIds(inputSet);
        } else if (param.getResourceType() == ResourceTypeEnum.MARKETPAIR) {
            marketPairs = Sets.newHashSet(inputSet);
        }
        if (CollectionUtils.isEmpty(cryptoIds) && CollectionUtils.isEmpty(exchangeIds) && CollectionUtils
            .isEmpty(marketPairs)) {
            BusinessException.throwIfMessage(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND));
        }
        WatchListEntity watchListEntity =
            WatchListEntity.builder().cryptos(cryptoIds).exchanges(exchangeIds).marketPairs(marketPairs)
                .id(StringUtils.isBlank(param.getWatchListId()) ? null : new ObjectId(param.getWatchListId()))
                .shared(param.getShared()).userId(param.getUserId()).uid(param.getUid()).build();
        boolean isAdded = param.getSubscribeType() == SubscribeTypeEnum.SUBSCRIBE;
        return watchListRespository.findAndCombineMainWatchList(watchListEntity)
            .flatMap( tuple2 -> followResource(tuple2.getT1().get(), tuple2.getT2().getId(), watchListEntity, isAdded,
                param.getNeedReplaceAllResourceIds())
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(w -> {
                    addWatchCryptoStats(param, isAdded, w);
                    asyncWatchListService.asyncCalcSubscribeWatchListCoin(param, isAdded, w);
                }))
            .onErrorResume(throwable -> {
                log.error("find watchlist failed.", throwable);
                return Mono.empty();
            });
    }

    private void resetTopCoins(BaseRequest request) {
        userCoinService.resetTopCoins(request, WATCHLIST.getSourceId()).subscribe();
    }

    private AutoFollowDTO createAutoFollowDTO(List<Integer> cryptoIds, String userId, String type) {
        return AutoFollowDTO.builder().business("watchlist").status("init").cryptoIds(cryptoIds).type(type)
            .userId(userId).timeCreated(new Date()).build();
    }

    private SubscribeChangeDTO createSubscribeChangeDTO(SubscribeParamDTO paramDTO) {
        return SubscribeChangeDTO.builder().resourceType(paramDTO.getResourceType()).userId(paramDTO.getUserId())
            .watchListId(paramDTO.getWatchListId()).uid(paramDTO.getUid()).resourceIds(paramDTO.getResourceIds()
                .stream().map(Math::toIntExact).collect(Collectors.toList()))
            .subscribeType(paramDTO.getSubscribeType()).shared(paramDTO.getShared()).timeCreated(new Date()).build();
    }
}
