package com.cmc.asset.business.coins;

import com.cmc.asset.model.contract.news.QueryTopCoinsParamDTO;
import com.cmc.asset.model.contract.news.QueryTopCoinsResultDTO;
import com.cmc.asset.model.contract.news.QueryUserTopCoinsParamDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsRequestDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsResponseDTO;
import com.cmc.data.common.BaseRequest;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/3/31 09:53:39
 */
public interface IUserCoinService {

    /**
     * query top 5 coin list
     *
     * 1. query top 5 coin list in main portfolio
     * 2. if none, query top 5 coin list in main watchlist
     * @param queryTopCoinsParamDTO  - param
     * @return QueryTopCoinsResultDTO
     */
    Mono<QueryTopCoinsResultDTO> getTopCoins(QueryTopCoinsParamDTO queryTopCoinsParamDTO);

    /**
     * query top N coin list
     *
     * 1. query top N coin list in main portfolio
     * 2. if none, combine top N coin list in main watchlist
     * @param queryUserTopCoinsParamDTO
     * @return
     */
    Mono<QueryTopCoinsResultDTO> getUserTopCoins(QueryUserTopCoinsParamDTO queryUserTopCoinsParamDTO);

    /**
     * reset top coins to database when main portfolio/watchlist changes
     * @param request - request
     * @param source - source
     * @return result
     */
    Mono<Boolean> resetTopCoins(BaseRequest request, String source);

    /**
     * get user watchlist coins and portfolio coins
     * @param userRelevantCoinsRequestDTO
     * @return
     */
    Mono<UserRelevantCoinsResponseDTO> getUserRelevantCoins(UserRelevantCoinsRequestDTO userRelevantCoinsRequestDTO);
}
