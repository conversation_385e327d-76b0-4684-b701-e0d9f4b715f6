package com.cmc.asset.business.watchlist.impl;

import static com.cmc.asset.model.common.RedisConstants.KEY_WATCHLIST_ID;
import static com.cmc.asset.model.enums.TopCoinsSourceEnum.WATCHLIST;

import com.cmc.asset.business.coins.IUserCoinService;
import com.cmc.asset.business.dex.DexDataHubService;
import com.cmc.asset.business.gravity.AutoFollowService;
import com.cmc.asset.business.gravity.PinChangeService;
import com.cmc.asset.business.watchlist.AsyncWatchListService;
import com.cmc.asset.business.watchlist.WatchListService;
import com.cmc.asset.business.watchlist.manager.CryptoInfoManager;
import com.cmc.asset.business.watchlist.strategy.WatchListHandleStrategy;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.cache.DexerPoolCache;
import com.cmc.asset.dao.entity.FollowWatchListEntity;
import com.cmc.asset.dao.entity.GuestWatchListEntity;
import com.cmc.asset.dao.entity.MarketPairEntity;
import com.cmc.asset.dao.entity.WatchListCacheEntity;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.dao.entity.vo.DexTokenDetailCacheVo;
import com.cmc.asset.dao.repository.mongo.FollowWatchListRepositoryImpl;
import com.cmc.asset.dao.repository.mongo.WatchListRespositoryImpl;
import com.cmc.asset.dao.repository.mongo.guest.GuestWatchListRepository;
import com.cmc.asset.dao.repository.redis.AssetCacheRepository;
import com.cmc.asset.dao.repository.redis.CacheRepository;
import com.cmc.asset.dao.repository.redis.WatchListCacheRepository;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.domain.crypto.CryptoCurrencyListResultDTO;
import com.cmc.asset.domain.crypto.CryptoCurrencyListResultDTO.CryptoCurrencyDTO;
import com.cmc.asset.domain.gravity.AutoFollowDTO;
import com.cmc.asset.domain.gravity.PinChangeDTO;
import com.cmc.asset.domain.portfolio.WatchListBasicDTO;
import com.cmc.asset.domain.watchlist.QuerySharedWatchListDTO;
import com.cmc.asset.domain.watchlist.QueryWatchListDTO;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.integration.BaseDataServiceClient;
import com.cmc.asset.integration.ContentServiceClient;
import com.cmc.asset.integration.DataApiServiceClient;
import com.cmc.asset.locale.LocaleConstants;
import com.cmc.asset.mapstruct.DataHubDexTokenMapper;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.dexer.DexTokenDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.watchlist.AutoFillFollowParamDTO;
import com.cmc.asset.model.contract.watchlist.DeleteWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.FollowWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QuerySharedWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicRequest;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicResponse;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicV2Request;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicV2Response;
import com.cmc.asset.model.contract.watchlist.QueryWatchListMultiParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListRespDTO;
import com.cmc.asset.model.contract.watchlist.WatchAssetInfoRespDTO;
import com.cmc.asset.model.contract.watchlist.WatchListAssetParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListCacheDTO;
import com.cmc.asset.model.contract.watchlist.WatchListGuestToRegularDTO;
import com.cmc.asset.model.contract.watchlist.WatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResourceResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.ExchangeDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.MarketPairDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.WatchListDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.WatchListType;
import com.cmc.asset.model.contract.watchlist.WatchListResultMultiDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSharedResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSummaryDTO;
import com.cmc.asset.model.enums.ErrorCodeEnums;
import com.cmc.asset.model.enums.ErrorMessageEnum;
import com.cmc.asset.model.enums.FollowTypeEnum;
import com.cmc.asset.model.enums.ListingStatus;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import com.cmc.asset.model.enums.WatchListTypeEnum;
import com.cmc.asset.utils.CommonUtils;
import com.cmc.auth.common.utils.AuthUtils;
import com.cmc.auth.common.utils.AuthUtils.AuthResult;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.exception.ArgumentException;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.data.common.utils.ExtUtils;
import com.cmc.data.common.utils.ExtUtils.LocalDateFormatEnum;
import com.cmc.data.common.utils.JacksonUtils;
import com.cmc.data.common.utils.LocaleMessageSource;
import com.cmc.data.common.utils.ParamConditions;
import com.cmc.framework.utils.secret.Md5Utils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.time.Duration;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * WatchListServiceImpl
 *
 * <AUTHOR>
 * @date 2020/10/19 16:30
 * @description
 */
@Service
@Slf4j
public class WatchListServiceImpl implements WatchListService {

    private static final int MAX_FOLLOW = 500;
    private static final String COPY = "Copy - ";
    public static final String MARK_CHAR = "******";

    public static final String REMINDER_WATCH_LIST_MAX = "You can only create a max of %s watchlists";

    @Value("${com.cmc.asset.s3}")
    private String shareImageUrl;

    @Autowired
    private WatchListRespositoryImpl watchListRespository;

    @Autowired
    private FollowWatchListRepositoryImpl followWatchListRepository;

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Autowired
    private DataApiServiceClient dataApiServiceClient;

    @Autowired
    private ContentServiceClient contentServiceClient;

    @Autowired
    private WatchListCacheRepository watchListCacheRepository;

    @Autowired
    private AsyncWatchListService asyncWatchListService;

    @Autowired
    private IUserCoinService userCoinService;

    @Autowired
    private BaseDataServiceClient baseDataServiceClient;

    @Autowired
    private AutoFollowService autoFollowService;

    @Autowired(required = false)
    private CacheRepository cacheRepository;

    @Autowired
    private AssetCacheRepository assetCacheRepository;

    @Autowired
    private PinChangeService pinChangeService;

    @Autowired
    private List<WatchListHandleStrategy> watchListHandleStrategies;

    @Autowired
    private CryptoInfoManager cryptoInfoManager;

    @Autowired
    private GuestWatchListRepository guestWatchListRepository;

    @Autowired
    private DexerPoolCache dexerPoolCache;


    @Autowired
    private DexDataHubService dataHubService;

    @Value("${com.cmc.asset.watch-count-enable:true}")
    private boolean watchCountEnable;
    @Value("${com.cmc.asset.autoFillFollow:false}")
    private boolean autoFillFollow;
    @Value("${com.cmc.asset.autoAddFollow:false}")
    private boolean autoAddFollow;
    @Value("${com.cmc.asset.pinChange:false}")
    private boolean pinChange;

    @Value("${com.cmc.asset.watchlist.max.size:100}")
    private int watchlistMaxSize;


    @Value("${com.cmc.asset.datahub-token.batch.max.size:20}")
    private int dataHubBatchSize;
    @Value("${com.cmc.asset-service.watch_list_cache_time:1}")
    private Long cacheTimeMinutes;

    @Value("${com.cmc.asset-service.use_fix_security_question:false}")
    private Boolean fixSecurityQuestion;

    @Value("${com.cmc.asset.watchlist.top.crypto.size:3}")
    private int topCryptoSize;

    @Override
    public Mono<WatchListSubscribeResultDTO> subscribe(WatchListSubscribeParamDTO param) {
        SubscribeParamDTO subscribeParam = convertSubscribeParam(param);
        validatedSubscribeParam(subscribeParam);
        return watchListHandleStrategies.stream()
            .filter(strategy -> strategy.isMatched(subscribeParam))
            .findFirst()
            .orElseThrow()
            .subscribe(subscribeParam, param)
            .flatMap(result ->
                removeWatchListCache(subscribeParam.getUserId(),subscribeParam.getGuestId(),"subscribe", param.getResourceId())
                    .flatMap(removeWatchList -> Mono.just(result))
                    .defaultIfEmpty(result)
                    .onErrorResume(e -> {
                        log.warn("subscribe removeWatchListCache error, userId:{},guestId:{},e:",subscribeParam.getUserId(),subscribeParam.getGuestId(),e);
                        return Mono.just(result);
                    })
            );
    }

    private Mono<Long> removeWatchListCache(String userId,Long guestId,String operate, String resourceId){
        String userMapKey = buildUserMapKek(userId,guestId);
        if(StringUtils.isEmpty(userMapKey)){
            return Mono.just(0L);
        }

        Mono<Long> deleteV2Cache = assetCacheRepository.delete(getWatchlistCacheKeyV2(null, userId, guestId, resourceId));
        // In order to support different requests that involve deleting with or without a resourceId.
        Mono<Long> deleteV3Cache = Mono.just(0L);
        if (StringUtils.isNotEmpty(resourceId)) {
            deleteV3Cache = assetCacheRepository.delete(getWatchlistCacheKeyV2(null, userId, guestId, ""));
        }

        return Mono.zip(Mono.just(userMapKey),assetCacheRepository.getOpsSet(userMapKey), deleteV2Cache, deleteV3Cache)
            .flatMap(tuple2 -> {
                if(CollectionUtils.isEmpty(tuple2.getT2())){
                    return Mono.just(0L);
                }
                tuple2.getT2().add(tuple2.getT1());
                return Flux.fromIterable(tuple2.getT2()).flatMap(e -> assetCacheRepository.delete(e))
                    .collectList().map(list -> list.stream().reduce(0L, Long::sum));
            }).onErrorResume(e -> {
                log.error("removeWatchListCache error,userMapKey:{},e:",userMapKey,e);
                return Mono.just(0L);
            });
    }

    @Override
    public Mono<WatchListResultDTO> fillFollow(AutoFillFollowParamDTO param) {
        QueryWatchListDTO queryWatchListParamDTO =
            QueryWatchListDTO.builder().limit(50).start(0)
            .convertIds("1")
            .cryptoAux("ath")
            .userName(param.getHeader().getUserName())
            .verifiedByCmc(false)
            .aux(5).watchListType(WatchListResultDTO.WatchListType.ORDINARY)
            .userId(param.getHeader().getUserId()).build();
        return queryWatchList(queryWatchListParamDTO);
    }

    @Override
    public Mono<WatchListResultDTO> query(QueryWatchListParamDTO param) {
        QueryWatchListDTO queryWatchListDTO = convertQueryWatchListParam(param);
        validatedQueryWatchListParamParam(param);
        // 定义set索引键
        String indexKey = buildQueryIndexKey(queryWatchListDTO.getUserId(), queryWatchListDTO.getGuestId());
        // 定义redisKey
        String redisKey = buildWatchListRedisKey(queryWatchListDTO, RedisConstants.KEY_WATCH_LIST_QUERY_API);
        Integer limit = param.getLimit();
        if (StringUtils.isEmpty(indexKey) || StringUtils.isEmpty(redisKey)) {
            //此时可能md5加密失败或者入参中没有userId与guestId的特殊请求
            log.warn("watchList query unexpected request:{}", param);
            return queryWatchList(queryWatchListDTO)
                    .map(result -> checkAndLimitItemSize(result,limit))
                    .flatMap(e -> fillDexTokenInfo(e,param.getContainDexToken()));
        }
        return queryWatchListByCache(redisKey, indexKey, this::queryWatchList, queryWatchListDTO,WatchListResultDTO.class)
                .map(result -> checkAndLimitItemSize(result,limit))
                .flatMap(e -> fillDexTokenInfo(e,param.getContainDexToken()));
    }

    private Mono<WatchListResultDTO> fillDexTokenInfo(WatchListResultDTO result,Boolean containDexToken) {
        if (!Boolean.TRUE.equals(containDexToken)) {
            return Mono.just(result);
        }
        Set<String> tokenUniIdSet = result.getWatchLists().stream()
                .flatMap(watch -> Optional.ofNullable(watch.getDexTokenUniIds()).stream().flatMap(List::stream)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(tokenUniIdSet)) {
            return Mono.just(result);
        }
        return dataHubService.batchGetDexTokenMapWithCache(tokenUniIdSet)
                .filter(com.cmc.framework.utils.CollectionUtils::isNotEmpty)
                .map(tokenMap -> {
                    result.getWatchLists().forEach(watchList -> {
                        if (CollectionUtils.isNotEmpty(watchList.getDexTokenUniIds())) {
                            List<WatchListResultDTO.DexTokenDTO> dexTokens = watchList.getDexTokenUniIds()
                                    .stream().map(tokenMap::get).filter(Objects::nonNull).map(DataHubDexTokenMapper.INSTANCE::buildDexTokenByCache).collect(Collectors.toList());
                            watchList.setDexTokens(dexTokens);
                        }
                    });
                    return result;
                }).defaultIfEmpty(result);
    }


    private WatchListResultDTO checkAndLimitItemSize(WatchListResultDTO result,Integer limit) {
        if (limit == null || limit <= 0 || CollectionUtils.isEmpty(result.getWatchLists())) {
            return result;
        }
        result.getWatchLists().forEach(watchList -> {
            if (CollectionUtils.isNotEmpty(watchList.getCryptoCurrencies()) &&
                    watchList.getCryptoCurrencies().size() > limit) {
                watchList.setCryptoCurrencies(
                        watchList.getCryptoCurrencies().subList(0, limit)
                );
            }

            if (CollectionUtils.isNotEmpty(watchList.getDexPairIds()) &&
                    watchList.getDexPairIds().size() > limit) {
                watchList.setDexPairIds(
                        watchList.getDexPairIds().subList(0, limit)
                );
            }
            if (CollectionUtils.isNotEmpty(watchList.getDexTokenUniIds()) &&
                    watchList.getDexTokenUniIds().size() > limit) {
                watchList.setDexTokenUniIds(watchList.getDexTokenUniIds().subList(0, limit));
            }
        });
        return result;
    }

    @Override
    public Mono<QueryWatchListBasicResponse> queryBasic(QueryWatchListBasicRequest param) {
        // 定义set索引键
        String indexKey = buildQueryIndexKey(param.getHeader().getUserId(), param.getHeader().getGuestId());
        // 定义redisKey
        String redisKey = buildWatchListRedisKey(param, RedisConstants.KEY_WATCH_LIST_QUERY_BASIC_API);
        if(StringUtils.isEmpty(indexKey) || StringUtils.isEmpty(redisKey)){
            return queryWatchListBasic(param);
        }
        return queryWatchListByCache(redisKey,indexKey,this::queryWatchListBasic,param, QueryWatchListBasicResponse.class);
    }

    @Override
    public Mono<QueryWatchListBasicV2Response> queryBasicV2(QueryWatchListBasicV2Request param) {
        Long guestId = param.getHeader().getGuestId();
        String userId = param.getHeader().getUserId();
        if (guestId == null && StringUtils.isEmpty(userId)) {
            return Mono.error(new BusinessException(ErrorCodeEnums.WATCHLIST_INVALID_PARAMETER));
        }
        return this.queryWatchListBasicV2(param);
    }

    private static String getWatchlistCacheKeyV2(String watchListId, String userId, Long guestId, String resourceId) {
        String userKey = StringUtils.defaultString(userId, String.valueOf(guestId));
        String param1 = StringUtils.defaultIfEmpty(watchListId, "");
        resourceId = StringUtils.defaultString(resourceId, "");
        return String.format(RedisConstants.KEY_WATCH_LIST_QUERY_BASIC_API_V2, userKey, param1, resourceId);
    }

    /**
     * watchList Query相关的通用缓存方法
     * @param redisKey 缓存key
     * @param indexKey 索引key
     * @param param 执行查询的入参
     * @param function 执行查询的方法
     * @param resultType 返回结果类型
     * @return Mono<T>
     * @param <T> 返回结果类型
     * @param <P> 入参类型
     */
    private <T, P> Mono<T> queryWatchListByCache(String redisKey,String indexKey,
        Function<P, Mono<T>> function,P param, Class<T> resultType) {
        return assetCacheRepository.get(redisKey).filter(resultStr -> !StringUtils.isEmpty(resultStr))
            .map(resultStr -> com.cmc.framework.utils.JacksonUtils.deserialize(resultStr, resultType))
            .switchIfEmpty(Mono.defer(() -> function.apply(param).publishOn(Schedulers.boundedElastic()).doOnNext(e -> {
                assetCacheRepository.cacheInString(redisKey, com.cmc.framework.utils.JacksonUtils.serialize(e),
                    Duration.ofMinutes(cacheTimeMinutes)).subscribe();
                if (StringUtils.isNotEmpty(indexKey)) {
                    assetCacheRepository.addOpsSet(indexKey, redisKey)
                        .flatMap(result -> assetCacheRepository.expire(indexKey, Duration.ofMinutes(cacheTimeMinutes + 1)))
                        .subscribe();
                }
            })));
    }


    private String buildQueryIndexKey(String userId,Long guestId) {
        if(StringUtils.isNotEmpty(userId)){
            return String.format(RedisConstants.KEY_WATCH_LIST_USER, userId);
        }
        if(guestId != null){
            return String.format(RedisConstants.KEY_WATCH_LIST_USER, guestId);
        }
        return null;
    }

    private String buildUserMapKek(String userId,Long guestId) {
        if(StringUtils.isNotEmpty(userId)){
            return String.format(RedisConstants.KEY_WATCH_LIST_USER, userId);
        }
        if(guestId != null){
            return String.format(RedisConstants.KEY_WATCH_LIST_USER, guestId);
        }
        return null;

    }

    private <T> String buildWatchListRedisKey(T queryWatchListDTO,String baseRedisKey) {
        String md5 = Md5Utils.encrypt(JacksonUtils.toJsonString(queryWatchListDTO));
        if (StringUtils.isEmpty(md5)) {
            log.error("watchList query build redisKey error,queryWatchListDTO:{}", queryWatchListDTO);
            return null;
        }
        return String.format(baseRedisKey, md5);
    }

    private Mono<QueryWatchListBasicResponse> queryWatchListBasic(QueryWatchListBasicRequest param) {
        //设置默认参数
        WatchListTypeEnum queryType = WatchListTypeEnum.findByCode(param.getWatchListType());
        if (queryType == null) {
            queryType = WatchListTypeEnum.ORDINARY;
        }
        Long guestId = param.getHeader().getGuestId();
        String userId = param.getHeader().getUserId();
        if (guestId == null && StringUtils.isEmpty(userId)) {
            return Mono.error(new BusinessException(ErrorCodeEnums.WATCHLIST_INVALID_PARAMETER));
        }
        Mono<List<WatchListBasicDTO>> watchListMono = Mono.empty();
        switch (queryType) {
            case ORDINARY:
                watchListMono = guestId != null ? getGuestWatchListInfo(guestId) :
                    getUserWatchListInfo(userId, param.getWatchListId());
                break;
            case FOLLOWED:
                watchListMono = getFlowWatchListInfo(userId);
                break;
            default:
                break;
        }
        return watchListMono.map(res -> {
            res = res.stream().filter(e -> StringUtils.isEmpty(param.getWatchListId()) ||
                param.getWatchListId().equals(e.getWatchListId())).collect(Collectors.toList());
            return convertBasicResponse(res);
        }).defaultIfEmpty(convertBasicResponse(null)).onErrorResume(throwable -> {
            log.error("queryWatchListBaseInfo error", throwable);
            return Mono.just(convertBasicResponse(null));
        });
    }

    private Mono<QueryWatchListBasicV2Response> queryWatchListBasicV2(QueryWatchListBasicV2Request param) {
        Long guestId = param.getHeader().getGuestId();
        String userId = param.getHeader().getUserId();
        if (guestId == null && StringUtils.isEmpty(userId)) {
            return Mono.error(new BusinessException(ErrorCodeEnums.WATCHLIST_INVALID_PARAMETER));
        }
        Integer cryptoId = param.getCryptoId();
        Flux<WatchListEntity> watchListMono = getWatchListMono(guestId, userId, param)
                .map(e -> {
                    e.setDexTokens(reserveAndDistinct(e.getDexTokens()));
                    return e;
                });
        return watchListMono.collectSortedList(Comparator
                        .<WatchListEntity>comparingInt(watchListEntity -> (watchListEntity.getMain() != null && watchListEntity.getMain()) ? 0 : 1)
                        .thenComparing(WatchListEntity::getCreatedTime))
                .map(res -> {
                    res = res.stream().filter(e -> StringUtils.isEmpty(param.getWatchListId()) ||
                            param.getWatchListId().equals(e.getId().toString())).collect(Collectors.toList());
                    return convertBasicResponseV2(res, cryptoId);
                }).defaultIfEmpty(convertBasicResponseV2(null, cryptoId)).onErrorReturn(convertBasicResponseV2(null, cryptoId));
    }

    private Flux<WatchListEntity> getWatchListMono(Long guestId, String userId, QueryWatchListBasicV2Request param) {
        return guestId != null ? getGuestWatchListEntityFlux(guestId) :
                getUserWatchListEntityFluxV2(userId, param.getWatchListId(), param.getMain());
    }

    private QueryWatchListBasicResponse convertBasicResponse(List<WatchListBasicDTO> basicList) {
        if (CollectionUtils.isEmpty(basicList)) {
            return QueryWatchListBasicResponse.builder().cryptoCurrencies(List.of()).dexPairIds(List.of())
                .exchanges(List.of()).marketPairs(List.of()).build();
        }
        List<Integer> cryptoCurrencies = new ArrayList<>();
        List<Integer> exchanges = new ArrayList<>();
        List<Integer> marketPairs = new ArrayList<>();
        List<Long> dexPairIds = new ArrayList<>();
        for (WatchListBasicDTO watchLists : basicList) {
            if (CollectionUtils.isNotEmpty(watchLists.getCryptoCurrencies())) {
                cryptoCurrencies.addAll(watchLists.getCryptoCurrencies());
            }
            if (CollectionUtils.isNotEmpty(watchLists.getExchanges())) {
                exchanges.addAll(watchLists.getExchanges());
            }
            if (CollectionUtils.isNotEmpty(watchLists.getMarketPairs())) {
                marketPairs.addAll(watchLists.getMarketPairs());
            }
            if (CollectionUtils.isNotEmpty(watchLists.getDexPairIds())) {
                dexPairIds.addAll(watchLists.getDexPairIds());
            }
        }
        cryptoCurrencies = cryptoCurrencies.stream().distinct().collect(Collectors.toList());
        exchanges = exchanges.stream().distinct().collect(Collectors.toList());
        marketPairs = marketPairs.stream().distinct().collect(Collectors.toList());
        dexPairIds = dexPairIds.stream().distinct().collect(Collectors.toList());
        return QueryWatchListBasicResponse.builder().cryptoCurrencies(cryptoCurrencies).exchanges(exchanges)
            .marketPairs(marketPairs).dexPairIds(dexPairIds).build();
    }

    private QueryWatchListBasicV2Response convertBasicResponseV2(List<WatchListEntity> watchListEntities,
        Integer cryptoId) {
        if (CollectionUtils.isEmpty(watchListEntities)) {
            return QueryWatchListBasicV2Response.builder().watchLists(List.of()).build();
        }
        List<QueryWatchListBasicV2Response.WatchListDTO> watchLists = new ArrayList<>(watchListEntities.size());
        for (WatchListEntity watchList : watchListEntities) {
            boolean isCryptoIn = false;
            List<QueryWatchListBasicV2Response.CryptoCurrencyDTO> cryptoCurrencies = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(watchList.getCryptos())) {
                isCryptoIn = null != cryptoId && watchList.getCryptos().contains(cryptoId);
                cryptoCurrencies = watchList.getCryptos().stream().distinct()
                        .map(id -> cryptoCurrencyCache.getById(id))
                        .filter(Objects::nonNull)
                        .map(currencyInfoDTO -> QueryWatchListBasicV2Response.CryptoCurrencyDTO.builder()
                                .id(currencyInfoDTO.getId())
                                .name(currencyInfoDTO.getName())
                                .symbol(currencyInfoDTO.getSymbol())
                                .slug(currencyInfoDTO.getSlug())
                                .rank(currencyInfoDTO.getRank() == null ? Integer.MAX_VALUE : currencyInfoDTO.getRank())
                                .build())
                        .sorted(Comparator.comparing(f -> f.getRank() == null ? Integer.MAX_VALUE : f.getRank()))
                        .collect(Collectors.toList());
            }
            List<QueryWatchListBasicV2Response.DexPairDTO> dexPairs =
                CollectionUtils.isEmpty(watchList.getDexPairs()) ? List.of() : watchList.getDexPairs()
                    .stream()
                    .map(id -> QueryWatchListBasicV2Response.DexPairDTO.builder().poolId(id).build())
                    .collect(Collectors.toList());

            List<QueryWatchListBasicV2Response.DexTokenUniqueDTO> dexTokens =
                    CollectionUtils.isEmpty(watchList.getDexTokens()) ? List.of() : watchList.getDexTokens()
                            .stream()
                            .map(id -> QueryWatchListBasicV2Response.DexTokenUniqueDTO.builder().dexTokenUniqueId(id).build())
                            .collect(Collectors.toList());
            QueryWatchListBasicV2Response.WatchListDTO watchListDTO = QueryWatchListBasicV2Response.WatchListDTO.builder()
                    .cryptoCurrencies(cryptoCurrencies)
                    .watchListId(watchList.getId().toHexString())
                    .name(watchList.getName())
                    .main(watchList.getMain())
                    .contentType(watchList.getType())
                    .isCryptoIn(isCryptoIn)
                    .dexPairs(dexPairs)
                    .dexTokens(dexTokens)
                    .build();
            watchLists.add(watchListDTO);
        }
        return QueryWatchListBasicV2Response.builder().watchLists(watchLists).build();
    }

    @Override
    public Mono<String> save(WatchListParamDTO param) {
        validateWatchListParamDTO(param);

        final Mono<Long> watchListSizeMono = watchListRespository.countWatchList(param.getHeader().getUserId());

        return watchListSizeMono.flatMap((Function<Long, Mono<String>>)size -> {
            log.info("saveWatchListSize:{}", size);
            if (size >= watchlistMaxSize && (Boolean.TRUE.equals(param.getDuplicateWatchList()) || StringUtils.isBlank(
                param.getId()))) {
                throw new ArgumentException("1001", String.format(REMINDER_WATCH_LIST_MAX, watchlistMaxSize));
            } else {
                return saveWatchlist(param)
                    .doOnNext(e -> removeWatchListCache(param.getHeader().getUserId(),null,"save", null).subscribe());
            }
        });
    }

    private Mono<String> saveWatchlist(WatchListParamDTO param) {
        Mono<String> resultMono;
        // duplicate
        if (ExtUtils.getDefaultValue(param.getDuplicateWatchList())) {
            resultMono = watchListRespository.getWatchListRepository().findById(new ObjectId(param.getId()))
                .flatMap(f -> {
                    if (!f.getUserId().equals(param.getHeader().getUserId()) && !f.getShared()) {
                        return Mono.error(new BusinessException(ErrorMessageEnum.WATCHLIST_NOT_PUBLIC.getCode(), ErrorMessageEnum.WATCHLIST_NOT_PUBLIC.getDesc()));
                    }
                    f.setUserId(param.getHeader().getUserId());
                    f.setUid(param.getHeader().getUid());
                    f.setId(null);
                    f.setMain(false);
                    f.setFollows(0L);
                    f.setShared(false);
                    if (!StringUtils.contains(f.getName(), COPY)) {
                        f.setName(COPY + f.getName());
                    }
                    return setMainForFirstCreation(f, param, ResourceTypeEnum.findByType(f.getType()))
                        .flatMap(entity -> watchListRespository.save(entity));
                }).map(m -> m.getId().toHexString());
        } else {
            resultMono = getUpdatedWatchlistEntity(param)
                .flatMap(watchListEntity -> checkSensitiveWord(watchListEntity)
                    .flatMap(f -> watchListRespository.save(watchListEntity)
                    .doOnNext(watchlist -> {
                        if (!Boolean.TRUE.equals(param.getForDefault())) {
                            return;
                        }
                        asyncWatchListService.asyncCalcSubscribeWatchListCoin(SubscribeParamDTO.builder().build(), true, watchlist);
                    })
                    .map(m -> m.getId().toHexString())));
        }
        return resultMono.publishOn(Schedulers.boundedElastic()).doOnNext(v -> {
            resetTopCoins(param);
            log.info("user {} save a watchList: {}", param.getHeader().getUserId(), param);
            sendPinChangeEvent(param, param.getHeader().getUserId(), false);
        });
    }

    private Mono<WatchListEntity> getUpdatedWatchlistEntity(WatchListParamDTO param) {
        ResourceTypeEnum resourceType = ResourceTypeEnum.findByCode(param.getResourceType());
        if (StringUtils.isNotBlank(param.getId())) {

            if(fixSecurityQuestion){
                return watchListRespository.getWatchListRepository().findByIdAndUserId(new ObjectId(param.getId()),
                        param.getHeader().getUserId())
                    .map(watchListEntity -> {
                        watchListEntity.setName(param.getName());
                        watchListEntity.setMain(param.getForDefault());
                        watchListEntity.setDescription(param.getDescription());
                        watchListEntity.setShared(param.getShared());
                        return watchListEntity;
                    })
                    .switchIfEmpty(Mono.defer(() -> {
                        log.warn("fixSecurityQuestion find watchlist return null,userId:{} id:{}",
                            param.getHeader().getUserId(),param.getId());
                        return Mono.error(new BusinessException(ErrorCodeEnums.WATCHLIST_INVALID_PARAMETER));
                        })
                    );
            }

            return watchListRespository.getWatchListRepository().findById(new ObjectId(param.getId()))
                .map(watchListEntity -> {
                    watchListEntity.setName(param.getName());
                    watchListEntity.setMain(param.getForDefault());
                    watchListEntity.setDescription(param.getDescription());
                    watchListEntity.setShared(param.getShared());
                    return watchListEntity;
                })
                .defaultIfEmpty(buildWatchlistFromParam(resourceType, param));
        }
        WatchListEntity watchListEntity = buildWatchlistFromParam(resourceType, param);
        return setMainForFirstCreation(watchListEntity, param, resourceType);
    }
    public Mono<WatchListEntity> setMainForFirstCreation(WatchListEntity watchListEntity, WatchListParamDTO param,
        ResourceTypeEnum resourceType) {
        return watchListRespository.existsMainWatchlist(resourceType, param.getHeader().getUserId())
            .filter(k -> !k)
            .doOnNext(exist -> watchListEntity.setMain(true))
            .thenReturn(watchListEntity);
    }

    private WatchListEntity buildWatchlistFromParam(ResourceTypeEnum resourceType, WatchListParamDTO param) {
        return WatchListEntity.builder()
            .id(null)
            .userId(param.getHeader().getUserId())
            .uid(param.getHeader().getUid())
            .name(param.getName())
            .type(resourceType != null ? resourceType.getTypeName() : null)
            .main(param.getForDefault())
            .description(param.getDescription())
            .shared(param.getShared())
            .build();
    }

    private void sendPinChangeEvent(WatchListParamDTO param, String userId, Boolean isDelete) {
        if (!pinChange) {
            return;
        }

        if (ExtUtils.getDefaultValue(param.getDuplicateWatchList())) {
            return;
        }

        if (Boolean.TRUE.equals(isDelete)) {
            pinChangeService.sendMessage(createPinChangeDTO(param.getId(), userId,
                "delete", null));
        }

        if (Boolean.FALSE.equals(param.getShared())) {
            pinChangeService.sendMessage(createPinChangeDTO(param.getId(), userId,
                "unPublic", null));
        } else if (StringUtils.isNotBlank(param.getName())) {
            pinChangeService.sendMessage(createPinChangeDTO(param.getId(), userId,
                "name", Map.of("name", param.getName())));
        }
    }

    @Override
    public Mono<String> delete(DeleteWatchListParamDTO param) {

        ParamConditions.checkArgument(param != null && StringUtils.isNotBlank(param.getId()),
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "id"));

        return watchListRespository.delete(new ObjectId(param.getId()), param.getHeader().getUserId())
            .publishOn(Schedulers.boundedElastic())
            .doOnSuccess(v -> {
                resetTopCoins(param);
                log.info("user {} delete a watchList: {}", param.getHeader().getUserId(), param);
                sendPinChangeEvent(WatchListParamDTO.builder().id(param.getId()).build(), param.getHeader().getUserId(), true);
            })
            .doOnNext(e -> removeWatchListCache(param.getHeader().getUserId(),null,"delete", null).subscribe());
    }

    @Override
    public Mono<String> followWatchList(FollowWatchListParamDTO param) {

        ParamConditions.checkArgument(param != null && StringUtils.isNotBlank(param.getWatchListId()),
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "id"));
        FollowTypeEnum followTypeEnum = FollowTypeEnum.findByCode(param.getFollowType());
        ParamConditions.checkArgument(followTypeEnum != null,
                LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_FOLLOW_PARAM_FOLLOW_TYPE_ERROR));
        return watchListRespository.getWatchListRepository().findById(new ObjectId(param.getWatchListId()))
                .switchIfEmpty(Mono.error(
                        new BusinessException(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND))))
                .flatMap(r -> {
                    if (followTypeEnum == FollowTypeEnum.FOLLOW &&
                            (!(r.getShared() && r.isActived()) || StringUtils.equals(r.getUserId(), param.getHeader().getUserId()))) {
                        return Mono.error(
                                new BusinessException(LocaleMessageSource.getMessage(LocaleConstants.RESPONSE_ERROR_NO_FOUND)));
                    }
                    FollowWatchListEntity followWatchList =
                            FollowWatchListEntity.builder().userId(param.getHeader().getUserId())
                                    .watchListIds(Set.of(param.getWatchListId())).build();
                    return followTypeEnum == FollowTypeEnum.FOLLOW ?
                            followWatchList(followWatchList, param.getWatchListId()) :
                            unFollowWatchList(followWatchList, param.getWatchListId());
                });
    }


    @Override
    public Mono<WatchAssetInfoRespDTO> queryWatchAsset(WatchListAssetParamDTO param) {
        WatchAssetInfoRespDTO defaultResp = WatchAssetInfoRespDTO.builder().dexTokenInfos(List.of()).build();
        if (CollectionUtils.isEmpty(param.getDexTokenIds()) || param.getDexTokenIds().size() > dataHubBatchSize) {
            return Mono.just(defaultResp);
        }
        return dataHubService.batchGetDexTokenWithCache(new HashSet<>(param.getDexTokenIds()))
                .map(list -> list.stream().map(DataHubDexTokenMapper.INSTANCE::buildDexTokenByCache).collect(Collectors.toList()))
                .defaultIfEmpty(List.of())
                .onErrorResume(error -> {
                    log.error("queryWatchAsset error,tokenAddress:{} msg:", param.getDexTokenIds(), error);
                    return Mono.just(List.of());
                })
                .map(e -> WatchAssetInfoRespDTO.builder().dexTokenInfos(e).build());
    }




    @NotNull
    private Mono<String> followWatchList(FollowWatchListEntity param, String watchListId) {

        return followWatchListRepository.findAndModify(param, watchListId, MAX_FOLLOW, true)
                .map(m -> m.getId().toHexString())
                .publishOn(Schedulers.boundedElastic())
                .doOnSuccess(c -> watchListRespository.incrByFollows(watchListId, 1).subscribe());
    }

    @NotNull
    private Mono<String> unFollowWatchList(FollowWatchListEntity param, String watchListId) {

        return followWatchListRepository.findAndModify(param, watchListId, MAX_FOLLOW, false)
                .map(m -> m.getId().toHexString())
                .publishOn(Schedulers.boundedElastic())
                .doOnSuccess(c -> watchListRespository.incrByFollows(watchListId, -1).subscribe());
    }

    @Override
    public Mono<WatchListSharedResultDTO> querySharedWatchLists(QuerySharedWatchListParamDTO param) {

        QuerySharedWatchListDTO querySharedWatchList = convertQuerySharedWatchListParam(param);
        ParamConditions.checkArgument(param.getStart() >= 0,
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_LARGER_THEN, "start", 1));
        ParamConditions.checkArgument(param.getLimit() > 0 && param.getLimit() <= 500,
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_INTERVAL, "limit", 0, 500));

        return getSharedWatchListSharedResult(querySharedWatchList, false);
    }

    @Override
    public Mono<String> createMainWatchList(BaseRequest param) {

        ParamConditions.checkArgument(StringUtils.isNotEmpty(param.getHeader().getUserId()),
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "userId"));
        WatchListEntity watchListEntity =
                WatchListEntity.builder().cryptos(Set.of()).exchanges(Set.of()).marketPairs(Set.of())
                        .id(null)
                        .userId(param.getHeader().getUserId())
                        .uid(param.getHeader().getUid()).build();
        return watchListRespository.doInitMainWatchlist(watchListEntity)
                .map(w -> w.getId().toHexString())
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(v -> {
                    resetTopCoins(param);
                    log.info("user {} create main watchList: {}", param.getHeader().getUserId(), param);
                });
    }

    @Override
    public Mono<Set<Integer>> queryWatchListCryptoIds(QueryWatchListParamDTO param) {

        Flux<WatchListEntity> watchListEntityFlux = watchListRespository.getWatchListRepository()
                .findAllByUserIdEqualsAndActivedTrueAndMainTrue(param.getHeader().getUserId());
        return watchListEntityFlux
            .map(entity -> Optional.ofNullable(entity.getCryptos()).orElse(new HashSet<Integer>()))
            .collectList()
            .map(list -> {
                Set<Integer> cryptoIds = new HashSet<>();
                list.forEach(set -> cryptoIds.addAll(set));
                return cryptoIds;
            });
    }

    @Override
    public Mono<WatchListResultMultiDTO> queryMulti(QueryWatchListMultiParamDTO paramDTO) {
        if (CollectionUtils.isEmpty(paramDTO.getWatchListIds())) {
            return Mono.just(WatchListResultMultiDTO.builder().watchLists(Collections.emptyMap()).watchListType(WatchListResultDTO.WatchListType
                .valueOf(StringUtils.defaultIfBlank(paramDTO.getWatchListType(), WatchListType.FOLLOWED.name())))
                .build());
        }
        ParamConditions.checkArgument(CollectionUtils.isNotEmpty(paramDTO.getWatchListIds()),
            LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "watchListIds"));
        paramDTO.setNeedCheck(paramDTO.getNeedCheck() == null ? Boolean.FALSE : paramDTO.getNeedCheck());
        return Flux.fromIterable(paramDTO.getWatchListIds())
            .flatMap(watchListId -> {
                QueryWatchListDTO param = QueryWatchListDTO.builder()
                    .watchListType(WatchListResultDTO.WatchListType.valueOf(
                        StringUtils.defaultIfBlank(paramDTO.getWatchListType(), WatchListType.FOLLOWED.name())))
                    .userId(paramDTO.getHeader().getUserId()).watchListId(watchListId).build();
                return getWatchListResultSingle(param, paramDTO.getNeedCheck() && StringUtils.isNotBlank(param.getUserId()))
                    .defaultIfEmpty(Collections.emptyList())
                    .map(list -> Pair.of(watchListId, CollectionUtils.isNotEmpty(list) ? list.get(0) : null));
            })
            .collectMap(Pair::getKey, Pair::getValue)
            .map(res -> WatchListResultMultiDTO.builder().watchLists(res).watchListType(WatchListResultDTO.WatchListType
                .valueOf(StringUtils.defaultIfBlank(paramDTO.getWatchListType(), WatchListType.FOLLOWED.name())))
                .build());
    }

    @Override
    public Mono<QueryWatchListRespDTO> getById(WatchListParamDTO param) {
        return watchListRespository.getWatchListRepository().findByIdEqualsAndActivedTrue(new ObjectId(param.getId()))
            .map(entity -> QueryWatchListRespDTO.builder().userId(entity.getUserId()).name(entity.getName())
                .watchListId(entity.getId().toHexString()).shared(entity.getShared()).build())
            .singleOrEmpty();
    }

    @Override
    public Mono<WatchListResourceResultDTO> queryByResourceId(QueryWatchListParamDTO param) {
        ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.findByCode(param.getResourceType());
        ParamConditions.checkArgument(resourceTypeEnum != null,
            LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_SUBSCRIBE_PARAM_SUBSCRIBE_TYPE_ERROR));
        ParamConditions.checkArgument(StringUtils.isNotBlank(param.getResourceId()),
            LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "resourceId"));
        return watchListRespository.findByResourceId(resourceTypeEnum, List.of(Long.valueOf(param.getResourceId())),
                param.getHeader().getUserId())
            .map(result -> WatchListResourceResultDTO.builder()
                .resourceId(param.getResourceId())
                .watchlistIds(result.stream().map(k -> k.getId().toHexString())
                    .collect(Collectors.toList()))
                .build());
    }

    @Override
    public Mono<List<WatchListSummaryDTO>> queryWatchlistSummary(BaseRequest baseRequest) {
        String userId = baseRequest.getHeader().getUserId();
        Long guestId = baseRequest.getHeader().getGuestId();

        Mono<List<WatchListSummaryDTO>> ownWatchlistMono;
        if (guestId != null) {
            ownWatchlistMono = getGuestWatchListEntityFlux(guestId)
                    .map(e -> {
                        e.setDexTokens(reserveAndDistinct(e.getDexTokens()));
                        return e;
                    })
                    .flatMap(entity -> convertEntityToSummaryDTO(entity, WatchListTypeEnum.ORDINARY))
                    .collectSortedList(Comparator.comparing(WatchListSummaryDTO::getMain).reversed().thenComparing(WatchListSummaryDTO::getCreatedTime));
        } else if (StringUtils.isNotEmpty(userId)) {
            ownWatchlistMono = watchListRespository.getWatchListRepository()
                    .findAllByUserIdEqualsAndActivedTrue(userId)
                    .map(e -> {
                        e.setDexTokens(reserveAndDistinct(e.getDexTokens()));
                        return e;
                    })
                    .flatMap(entity -> convertEntityToSummaryDTO(entity, WatchListTypeEnum.ORDINARY))
                    .collectSortedList(Comparator.comparing(WatchListSummaryDTO::getMain).reversed().thenComparing(WatchListSummaryDTO::getCreatedTime));
        } else {
            ownWatchlistMono = Mono.just(Collections.emptyList());
        }

        return ownWatchlistMono.defaultIfEmpty(Collections.emptyList());
    }

    private Mono<WatchListSummaryDTO> convertEntityToSummaryDTO(WatchListEntity entity, WatchListTypeEnum type) {
        List<Integer> topCryptos = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(entity.getCryptos())) {
            topCryptos = entity.getCryptos().stream()
                    .map(cryptoId -> {
                        CryptoCurrencyInfoDTO info = cryptoCurrencyCache.getById(cryptoId);
                        return new AbstractMap.SimpleEntry<>(cryptoId, info != null && info.getRank() != null ? info.getRank() : Integer.MAX_VALUE);
                    })
                    .sorted(Comparator.comparing(Map.Entry::getValue))
                    .limit(topCryptoSize)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        }

        Integer assetSize = CollectionUtils.size(entity.getDexPairs()) + CollectionUtils.size(entity.getCryptos());
        Integer totalAssetSize = CollectionUtils.size(entity.getDexTokens()) + CollectionUtils.size(entity.getCryptos());

        WatchListSummaryDTO summaryDTO = WatchListSummaryDTO.builder()
                .watchListId(entity.getId().toHexString())
                .name(entity.getName())
                .type(type.getCode())
                .cryptoSize(CollectionUtils.size(entity.getCryptos()))
                .assetSize(assetSize)
                .totalAssetSize(totalAssetSize)
                .followerSize(getFollowSize(entity.getFollows()))
                .shared(entity.getShared())
                .main(entity.getMain())
                .description(entity.getDescription())
                .topCrypto(topCryptos)
                .topDexBaseCryptoIds(List.of())
                .createdTime(entity.getCreatedTime())
                .build();
        return topDexTopInfo(entity)
                .map(tuple2 -> {
                    summaryDTO.setTopDexBaseCryptoIds(tuple2.getT1());
                    summaryDTO.setTopDexTokenLogos(tuple2.getT2());
                    return summaryDTO;
                });
    }


    private Mono<Tuple2<List<Long>,List<String>>> topDexTopInfo(WatchListEntity entity){
        return Mono.zip(getDexTopPairId(entity.getDexPairs()),getDexTopTokenLogo(entity.getDexTokens()));
    }

    private Mono<List<Long>> getDexTopPairId(List<Long> dexPairs) {
        if (CollectionUtils.isEmpty(dexPairs)) {
            return Mono.just(List.of());
        }
        List<Long> topPairs = dexPairs.stream().limit(topCryptoSize).collect(Collectors.toList());
        Mono<Map<Long, PairInfoDTO>> allPairsMono = dexerPoolCache.getAll(topPairs);
        return allPairsMono
                .map(pairs -> {
                    if (pairs.isEmpty()) {
                        return List.of();
                    }
                    return pairs.values()
                            .stream()
                            .map(PairInfoDTO::getBaseToken)
                            .filter(Objects::nonNull)
                            .map(DexTokenDTO::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                });
    }

    private Mono<List<String>> getDexTopTokenLogo(List<String> dexTokens) {
        if (CollectionUtils.isEmpty(dexTokens)) {
            return Mono.just(List.of());
        }
        List<String> topTokens = dexTokens.stream().limit(topCryptoSize).collect(Collectors.toList());
        return dataHubService.batchGetDexTokenMapWithCache(new HashSet<>(topTokens))
                .map(allTokenMap -> topTokens.stream().map(e -> extractLogoSafe(allTokenMap.get(e)))
                .collect(Collectors.toList()));
    }


    private String extractLogoSafe(DexTokenDetailCacheVo vo) {
        return vo != null && StringUtils.isNotBlank(vo.getLogo()) ? vo.getLogo() : "";
    }

    @Override
    public Mono<String> guestToRegularMainWatchList(WatchListGuestToRegularDTO param) {

        WatchListEntity.WatchListEntityBuilder builder= WatchListEntity.builder()
            .exchanges(Set.of())
            .marketPairs(Set.of())
            .id(null)
            .userId(param.getUserId())
            .uid(param.getUid());

        return guestWatchListRepository.deleteByGuestId(param.getGuestId())
            .map(guestWatchList -> builder
                    .cryptos(guestWatchList.getCryptos())
                    .dexPairs(guestWatchList.getDexPairs())
                    .dexTokens(guestWatchList.getDexTokens())
                    .build())
            .defaultIfEmpty(builder
                .cryptos(Set.of())
                .dexPairs(List.of())
                .dexTokens(List.of())
                .build())
            .flatMap(watchListEntity -> watchListRespository.doInitMainWatchlist(watchListEntity))
            .doOnNext(watchlist -> {
                asyncWatchListService.asyncCalcSubscribeWatchListCoin(SubscribeParamDTO.builder().build(), true, watchlist);
            })
            .map(w -> w.getId().toHexString())
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(v -> {
                resetTopCoins(param);
            });
    }

    /**
     * --->Cache---Result
     * |-->NULL--->DB--Result
     *
     * @param querySharedWatchList
     * @param recommend
     * @return
     */
    @NotNull
    private Mono<WatchListSharedResultDTO> getSharedWatchListSharedResult(
            QuerySharedWatchListDTO querySharedWatchList, boolean recommend) {

        Mono<FollowWatchListEntity> followWatchListEntityMono = null;
        if (StringUtils.isNotBlank(querySharedWatchList.getUserId())) {
            followWatchListEntityMono = followWatchListRepository.findByUserId(querySharedWatchList.getUserId())
                    .defaultIfEmpty(FollowWatchListEntity.builder().build()).onErrorResume(throwable -> {
                        log.error("get the follow of user error", throwable);
                        return Mono.empty();
                    });
        } else {
            followWatchListEntityMono = Mono.just(FollowWatchListEntity.builder().build());
        }
        Mono<Tuple2<List<WatchListCacheEntity>, Long>> sharedWatchListMono;
        if (recommend) {
            sharedWatchListMono = recommendWatchList();
        } else {
            sharedWatchListMono = watchListCacheRepository
                    .getPopularWatchListsPage(querySharedWatchList.getStart(), querySharedWatchList.getLimit())
                    .onErrorResume(throwable -> {
                        log.error(" query shared watchlist.", throwable);
                        return Mono.empty();
                    })
                    .switchIfEmpty(Mono.defer(() -> watchListRespository.querySharedWatchList(querySharedWatchList.getStart(), querySharedWatchList.getLimit())));
        }
        return Mono.zip(followWatchListEntityMono, sharedWatchListMono).map(r -> {
            Tuple2<List<WatchListCacheEntity>, Long> d = r.getT2();
            return WatchListSharedResultDTO.builder().count(d.getT2()).watchLists(
                    buildSharedWatchList(querySharedWatchList.getUserId(), d.getT1(), r.getT1().getWatchListIds())).build();
        });
    }

    /**
     * recommend watchlist
     *
     * @return
     */
    private Mono<Tuple2<List<WatchListCacheEntity>, Long>> recommendWatchList() {

        return watchListCacheRepository.getPopularWatchSize()
                .map(m -> m > 20 ? 20 : m)
                .flatMap(s -> watchListCacheRepository.getPopularWatchLists(RandomUtils.nextInt(0, (s.intValue() - 3)), 3))
                .onErrorResume(throwable -> {
                    log.error("get recommen watchlist from cache", throwable);
                    return Mono.empty();
                })
                .switchIfEmpty(Mono.defer(() -> watchListRespository.getSharedWatchListSize()
                        .map(m -> m > 20 ? 20 : m)
                        .flatMap(s -> watchListRespository.getSharedWatchList(RandomUtils.nextInt(0, s.intValue() - 3), 3))))
                .map(m -> Tuples.of(m, 0L));
    }

    /**
     * validate create or update watchlist param.
     *
     * @param param
     */
    private void validateWatchListParamDTO(WatchListParamDTO param) {

        if (StringUtils.isBlank(param.getId())) {
            ParamConditions.checkArgument(param != null && StringUtils.isNotBlank(param.getName()),
                    LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "name"));
        }
        if (StringUtils.isNotBlank(param.getName())) {
            ParamConditions.checkArgument(StringUtils.length(param.getName()) <= 120,
                    LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_PARAM_CHARACTERS_MAX, "name", 120));
        }
        if (StringUtils.isNotBlank(param.getDescription())) {
            ParamConditions.checkArgument(StringUtils.length(param.getDescription()) <= 180,
                    LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_PARAM_CHARACTERS_MAX, "description", 180));
        }
        ParamConditions.checkArgument(StringUtils.isNotEmpty(param.getHeader().getUserId()),
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "userId"));
    }

    /**
     * checkSensitiveWord
     *
     * @param watchListEntity
     * @return
     */
    private Mono<Boolean> checkSensitiveWord(@NotNull WatchListEntity watchListEntity) {

        List<String> texts = Lists.newArrayListWithCapacity(2);
        if (StringUtils.isNotBlank(watchListEntity.getName())) {
            texts.add(watchListEntity.getName());
        }
        if (StringUtils.isNotBlank(watchListEntity.getDescription())) {
            texts.add(watchListEntity.getDescription());
        }
        return contentServiceClient.checkSenitiveWord(texts)
                .flatMap(f -> f ? Mono.error(new BusinessException(ErrorMessageEnum.SENSITIVE_WORD.getCode(), "Your watchlist name / description contains sensitive words!"))
                        : Mono.just(f));
    }

    /**
     * convert to SubscribeParamDTO
     *
     * @param param
     * @return
     */
    private SubscribeParamDTO convertSubscribeParam(WatchListSubscribeParamDTO param) {
        SubscribeParamDTO subscribeParam = SubscribeParamDTO.builder()
                .resourceType(ResourceTypeEnum.findByCode(param.getResourceType()))
                .subscribeType(SubscribeTypeEnum.findByCode(param.getSubscribeType())).watchListId(param.getWatchListId())
                .shared(param.getShared())
                .needReplaceAllResourceIds(ExtUtils.getDefaultValue(param.getNeedReplaceAllResourceIds()))
                .userId(param.getHeader().getUserId())
                .uid(param.getHeader().getUid())
                .guestId(param.getHeader().getGuestId()).build();
        if(subscribeParam.getResourceType() == ResourceTypeEnum.DEX_TOKEN){
            subscribeParam.setDexTokenAddress(CommonUtils.toStringList(param.getResourceId(), ","));
        }else{
            subscribeParam.setResourceIds(CommonUtils.toLongList(param.getResourceId(), ","));
        }
        return subscribeParam;
    }

    /**
     * validate the subscribe param
     *
     * @param param
     */
    private void validatedSubscribeParam(SubscribeParamDTO param) {

        if (!param.getNeedReplaceAllResourceIds()) {
            ParamConditions.checkArgument(param.getSubscribeType() != null,
                    LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_SUBSCRIBE_PARAM_SUBSCRIBE_TYPE_ERROR));
        }
        ParamConditions.checkArgument(ObjectUtils.anyNotNull(param.getUserId(), param.getGuestId()),
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "userId","guestId"));
        ParamConditions.checkArgument(param.getResourceType() != null,
                LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_SUBSCRIBE_PARAM_RESOURCE_TYPE_ERROR));
        if(param.getResourceType() == ResourceTypeEnum.DEX_TOKEN){
            ParamConditions.checkArgument(CollectionUtils.size(param.getDexTokenAddress()) <= 500,
                    LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_SUBSCRIBE_PARAM_RESOURCE_SIZE_LIMIT, 500));
        }else{
            ParamConditions.checkArgument(CollectionUtils.isNotEmpty(param.getResourceIds()),
                    LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "resourceId"));
            ParamConditions.checkArgument(CollectionUtils.size(param.getResourceIds()) <= 500,
                    LocaleMessageSource.getMessage(LocaleConstants.WATCHLIST_SUBSCRIBE_PARAM_RESOURCE_SIZE_LIMIT, 500));
        }
    }

    /**
     * set query watchlist default value
     *
     * @param param
     * @return
     */
    private QueryWatchListDTO convertQueryWatchListParam(QueryWatchListParamDTO param) {

        param.setStart(param.getStart() == null || param.getStart() <= 0 ? 0 : param.getStart());
        param.setLimit(param.getLimit() == null || param.getLimit() <= 0 ? 0 : param.getLimit());
        return QueryWatchListDTO.builder().limit(param.getLimit()).start(param.getStart())
                .convertIds(param.getConvertIds())
                .cryptoAux(param.getCryptoAux())
                .isMain(param.getIsMain())
                .containDexToken(param.getContainDexToken())
                .userName(param.getHeader().getUserName())
                .verifiedByCmc(ExtUtils.getDefaultValue(param.getHeader().getVerifiedUserType()) == 1)
                .aux(ExtUtils.getDefaultValue(param.getAux(), 1)).watchListType(WatchListResultDTO.WatchListType.valueOf(
                        StringUtils.defaultIfBlank(param.getWatchListType(), WatchListResultDTO.WatchListType.ORDINARY.name())))
                .userId(param.getHeader().getUserId()).watchListId(param.getWatchListId())
                .guestId(param.getHeader().getGuestId()).build();
    }

    /**
     * set query shared watchlist default value
     *
     * @param param
     * @return
     */
    private QuerySharedWatchListDTO convertQuerySharedWatchListParam(QuerySharedWatchListParamDTO param) {

        param.setStart(param.getStart() == null || param.getStart() <= 0 ? 0 : param.getStart());
        param.setLimit(param.getLimit() == null || param.getLimit() <= 0 ? 50 : param.getLimit());
        return QuerySharedWatchListDTO.builder().limit(param.getLimit()).start(param.getStart())
                .userId(param.getHeader().getUserId()).build();
    }

    /**
     * validate the query watchlist param
     *
     * @param param
     */
    private void validatedQueryWatchListParamParam(QueryWatchListParamDTO param) {

        ParamConditions.checkArgument(param.getStart() >= 0,
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_LARGER_THEN, "start", 0));
        ParamConditions.checkArgument(param.getLimit() >= 0 && param.getLimit() <= 500,
                LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_INTERVAL, "limit", 0, 500));
    }


    private Mono<List<WatchListBasicDTO>> getGuestWatchListInfo(Long guestId) {
        if(guestId == null){
            return Mono.empty();
        }
        Flux<GuestWatchListEntity> guestWatchListEntityMono = guestWatchListRepository
                .queryGuestWatchListEntityByGuestId(guestId);
        return guestWatchListEntityMono
                .flatMap(guestEntity -> {
                    WatchListBasicDTO watchList = WatchListBasicDTO.builder()
                        .watchListId(guestEntity.getId().toHexString())
                        .cryptoCurrencies(setToList(guestEntity.getCryptos()))
                        .dexPairIds(guestEntity.getDexPairs())
                        .marketPairs(setToList(guestEntity.getMarketPairs()))
                        .exchanges(setToList(guestEntity.getExchanges()))
                        .build();
                    return Mono.just(watchList);
                }).collectList();
    }

    private Mono<List<WatchListBasicDTO>> getUserWatchListInfo(String userId,String watchListId) {
        if(StringUtils.isEmpty(userId)){
            return Mono.empty();
        }
        Flux<WatchListEntity> userWatchListEntityMono = getUserWatchListEntityFlux(userId, watchListId);
        return userWatchListEntityMono
            .flatMap(userWatchListEntity -> {
                WatchListBasicDTO watchList = WatchListBasicDTO.builder()
                    .watchListId(userWatchListEntity.getId().toHexString())
                    .cryptoCurrencies(setToList(userWatchListEntity.getCryptos()))
                    .dexPairIds(userWatchListEntity.getDexPairs())
                    .marketPairs(setToList(userWatchListEntity.getMarketPairs()))
                    .exchanges(setToList(userWatchListEntity.getExchanges()))
                    .build();
                return Mono.just(watchList);
            }).collectList();
    }

    private Flux<WatchListEntity> getUserWatchListEntityFlux(String userId, String watchListId) {
        Flux<WatchListEntity> userWatchListEntityMono;
        if(StringUtils.isEmpty(watchListId)){
            userWatchListEntityMono = watchListRespository
                .getWatchListRepository().findAllByUserIdEqualsAndActivedTrueAndMainTrue(userId);
        }else{
            userWatchListEntityMono = watchListRespository
                .getWatchListRepository().findByIdEqualsAndUserIdEqualsAndActivedTrue(new ObjectId(watchListId), userId);
        }
        return userWatchListEntityMono;
    }

    private Flux<WatchListEntity> getUserWatchListEntityFluxV2(String userId, String watchListId, Boolean main) {
        Flux<WatchListEntity> userWatchListEntityMono;
        if (BooleanUtils.isTrue(main)) {
            userWatchListEntityMono =
                watchListRespository.getWatchListRepository().findAllByUserIdEqualsAndActivedTrueAndMainTrue(userId);
        } else if (StringUtils.isEmpty(watchListId)) {
            userWatchListEntityMono =
                watchListRespository.getWatchListRepository().findAllByUserIdEqualsAndActivedTrue(userId);
        } else {
            userWatchListEntityMono = watchListRespository.getWatchListRepository()
                .findByIdEqualsAndUserIdEqualsAndActivedTrue(new ObjectId(watchListId), userId);
        }
        return userWatchListEntityMono;
    }

    public static <T> List<T> setToList(Set<T> set) {
        if (set == null || set.isEmpty()) {
            return new ArrayList<>();
        }
        return new ArrayList<>(set);
    }


    private Mono<List<WatchListBasicDTO>> getFlowWatchListInfo(String userId) {
        if(StringUtils.isEmpty(userId)){
            return Mono.empty();
        }
        Mono<FollowWatchListEntity> guestWatchListEntityMono = followWatchListRepository.findByUserId(userId);
        return guestWatchListEntityMono
            .flatMap(guestEntity -> {
                List<Integer> watchListIds = null;
                if(CollectionUtils.isNotEmpty(guestEntity.getWatchListIds())){
                    watchListIds = guestEntity.getWatchListIds().stream().map(Integer::parseInt).collect(
                        Collectors.toList());
                }
                WatchListBasicDTO watchList = WatchListBasicDTO.builder()
                    .watchListId(guestEntity.getId().toHexString())
                    .cryptoCurrencies(watchListIds)
                    .build();
                return Mono.just(List.of(watchList));
            });
    }





    /**
     * query watchlist
     *
     * @param param
     * @return
     */
    private Mono<WatchListResultDTO> queryWatchList(QueryWatchListDTO param) {

        Mono<List<WatchListResultDTO.WatchListDTO>> watchListMono = Mono.empty();
        Flux<WatchListEntity> watchListEntityFlux = null;
        if (param.getWatchListType() == WatchListResultDTO.WatchListType.ORDINARY) {
            if(ObjectUtils.isNotEmpty(param.getGuestId())) {
                //guest watchlist
                watchListMono = getGuestWatchlistMono(param)
                    .onErrorResume(
                        throwable -> {
                            log.error("get Guest WatchListMono ", throwable);
                            return Mono.just(List.of());
                        }
                    );
            } else {
                watchListMono = getOrdinaryWatchListMono(param)
                    .onErrorResume(
                        throwable -> {
                            if(throwable instanceof BusinessException){
                                log.warn("getOrdinaryWatchListMono business exception:{}", ((BusinessException)throwable).getDesc());
                            }else{
                                log.error("getOrdinaryWatchListMono ", throwable);
                            }
                            return Mono.just(List.of());
                        }
                    );
            }
        }
        if (param.getWatchListType() == WatchListResultDTO.WatchListType.FOLLOWED) {
            if (StringUtils.isNotBlank(param.getWatchListId())) {
                watchListEntityFlux = watchListRespository.getWatchListRepository()
                        .findByIdEqualsAndActivedTrue(new ObjectId(param.getWatchListId()))
                        .doOnNext(w -> {
                            if (!w.getShared()) {
                                param.setAux(0);
                            }
                        });
                watchListMono = getWatchListResult(param, WatchListResultDTO.WatchListType.FOLLOWED, watchListEntityFlux)
                        .flatMap(w -> getUserInfo(w));
            } else {
                return getFollowedWatchListResultMono(param);
            }
        }
        return watchListMono.map(
                res -> WatchListResultDTO.builder().watchListType(param.getWatchListType()).watchLists(res)
                    .addFlag(this.canCreatWatchlist(res)).build())
                .doOnNext(watchListResultDTO -> {
                    if (autoFillFollow && param.getWatchListType() == WatchListType.ORDINARY) {
                        Set<Integer> cryptoIds = Sets.newHashSet();
                        watchListResultDTO.getWatchLists().forEach(watchListDTO -> watchListDTO.getCryptoCurrencies().forEach(cryptoCurrencyDTO -> cryptoIds.add(cryptoCurrencyDTO.getId())));
                        if (!cryptoIds.isEmpty()) {
                            autoFollowService.sendMessage(createAutoFollowDTO(Lists.newArrayList(cryptoIds), param.getUserId(), "fill"));
                        }
                    }
                });
    }

    private boolean canCreatWatchlist(List<WatchListDTO> watchLists) {
        int size = CollectionUtils.isEmpty(watchLists) ? 0 : watchLists.size();
        return size < watchlistMaxSize;
    }

    private AutoFollowDTO createAutoFollowDTO(List<Integer> cryptoIds, String userId, String type) {
        return AutoFollowDTO.builder().business("watchlist").status("init").cryptoIds(cryptoIds).type(type)
            .userId(userId).timeCreated(new Date()).build();
    }

    private PinChangeDTO createPinChangeDTO(String watchListId, String userId, String action, Map<String, String> changeMap) {
        return PinChangeDTO.builder().id(watchListId).type("watchlist").action(action).changeMap(changeMap)
            .userId(userId).timeCreated(new Date()).build();
    }

    @NotNull
    private Mono<List<WatchListDTO>> getUserInfo(List<WatchListDTO> w) {

        WatchListDTO watchList = CollectionUtils.size(w) > 0 ? w.get(0) : null;
        if (watchList == null) {
            return Mono.just(w);
        }
        return Mono.fromCallable(() -> AuthUtils.getUserInfo(watchList.getUserId()))
                .onErrorResume(throwable -> {
                    log.error("get userinfo failed", throwable);
                    return Mono.empty();
                })
                .defaultIfEmpty(new AuthResult())
                .timeout(Duration.ofMillis(1000), Mono.just(new AuthResult()))
                .map(m -> {
                    watchList.setCreator(maskString(m.getUsername()));
                    watchList.setVerifiedByCmc(m.getVerifiedByCMC());
                    return w;
                });
    }

    private static String maskString(String name) {

        name = StringUtils.trim(name);
        int length = StringUtils.length(name);
        if (length == 0) {
            return name;
        }
        if (length == 1) {
            return name + MARK_CHAR;
        }
        int end = length;
        int start = (int) Math.ceil(end * 0.25);
        end = (int) Math.floor(end * 0.75);
        return name.substring(0, start) + MARK_CHAR + name.substring(end);
    }

    @NotNull
    private Mono<WatchListResultDTO> getFollowedWatchListResultMono(QueryWatchListDTO param) {

        return getFollowWatchListByUserId(param).filter(f -> CollectionUtils.isNotEmpty(f))
                .map(res -> WatchListResultDTO.builder().watchListType(param.getWatchListType()).watchLists(res).build())
                .switchIfEmpty(Mono.defer(() -> {
                    QuerySharedWatchListDTO queryShared =
                            QuerySharedWatchListDTO.builder().userId(param.getUserId()).build();
                    return getSharedWatchListSharedResult(queryShared, true).map(
                            m -> WatchListResultDTO.builder().watchListType(WatchListResultDTO.WatchListType.NONE)
                                    .watchLists(m.getWatchLists()).build());
                }));
    }

    /**
     * @param param
     * @return
     */
    @NotNull
    private Mono<List<WatchListResultDTO.WatchListDTO>> getOrdinaryWatchListMono(QueryWatchListDTO param) {

        Flux<WatchListEntity> watchListEntityFlux;
        if (StringUtils.isBlank(param.getWatchListId())) {
            if (StringUtils.isBlank(param.getUserId())) {
                log.warn("getOrdinaryWatchListMono warn,userId is null,request:{}",param);
                return Mono.error(new BusinessException(ErrorMessageEnum.TOKEN_INVALID.getCode(), ErrorMessageEnum.TOKEN_INVALID.getDesc()));
            }
            if (Boolean.TRUE.equals(param.getIsMain())) {
                watchListEntityFlux =
                        watchListRespository.getWatchListRepository().findAllByUserIdEqualsAndActivedTrueAndMainTrue(param.getUserId());
            } else {
                watchListEntityFlux =
                        watchListRespository.getWatchListRepository().findAllByUserIdEqualsAndActivedTrue(param.getUserId());
            }

            return getWatchListResult(param, WatchListResultDTO.WatchListType.ORDINARY, watchListEntityFlux)
                    .map(m -> {
                        m.sort(Comparator.comparing(WatchListDTO::getMain).reversed().thenComparing(WatchListDTO::getCreatedTime));
                        return m;
                    });

        } else {
            watchListEntityFlux = watchListRespository.getWatchListRepository()
                    .findByIdEqualsAndUserIdEqualsAndActivedTrue(new ObjectId(param.getWatchListId()), param.getUserId());
        }
        return getWatchListResult(param, WatchListResultDTO.WatchListType.ORDINARY, watchListEntityFlux)
                .map(w -> {
                    WatchListDTO watchList = CollectionUtils.size(w) > 0 ? w.get(0) : null;
                    if (watchList != null) {
                        watchList.setVerifiedByCmc(param.getVerifiedByCmc());
                        watchList.setCreator(param.getUserName());
                    }
                    return w;
                });
    }

    /**
     * get guest watch list
     * @param param
     * @return
     */
    private Mono<List<WatchListResultDTO.WatchListDTO>> getGuestWatchlistMono(QueryWatchListDTO param) {
        final Long guestId = param.getGuestId();
        Flux<WatchListEntity> watchListEntityFlux = getGuestWatchListEntityFlux(guestId);

        return getWatchListResult(param, WatchListResultDTO.WatchListType.ORDINARY, watchListEntityFlux)
            .map(w -> {
                WatchListDTO watchList = CollectionUtils.size(w) > 0 ? w.get(0) : null;
                if (watchList != null) {
                    watchList.setVerifiedByCmc(param.getVerifiedByCmc());
                    watchList.setCreator(param.getUserName());
                }
                return w;
            });
    }

    @NotNull
    private Flux<WatchListEntity> getGuestWatchListEntityFlux(Long guestId) {
        Flux<WatchListEntity> watchListEntityFlux;

        Flux<GuestWatchListEntity> guestWatchListEntityMono = guestWatchListRepository
            .queryGuestWatchListEntityByGuestId(guestId);
        watchListEntityFlux = guestWatchListEntityMono.map(guestWatchListEntity -> {
            WatchListEntity entity = new WatchListEntity();
            entity.setId(guestWatchListEntity.getId());
            entity.setCryptos(guestWatchListEntity.getCryptos());
            entity.setDexPairs(guestWatchListEntity.getDexPairs());
            entity.setDexTokens(reserveAndDistinct(guestWatchListEntity.getDexTokens()));
            entity.setMain(Boolean.TRUE);
            entity.setShared(Boolean.FALSE);
            entity.setActived(true);
            return entity;
        });
        return watchListEntityFlux;
    }

    private Mono<Tuple2<List<WatchListEntity>, List<WatchListEntity>>> getCryptoAndExchangeSet(String userId,Long guestId,
        Flux<WatchListEntity> watchListEntityFlux, int aux) {
        return watchListEntityFlux.collectList().map(entityList -> {
            int count = entityList.size();
            if (count > 20) {
                log.info("user watchList count beyond expectation,userId:{},guestId:{},size is:{}",
                    userId, guestId, count);
            }
            List<WatchListEntity> cryptoMonoOne = Collections.synchronizedList(new ArrayList<>(count));
            List<WatchListEntity> marketPairMono = Collections.synchronizedList(new ArrayList<>(count));
            for (WatchListEntity data : entityList) {
                boolean takeMainData = (aux & 4) == 4;
                takeMainData = !takeMainData || ExtUtils.getDefaultValue(data.getMain());
                if (takeMainData && (aux & 1) == 1) {
                    cryptoMonoOne.add(data);
                }
                if (takeMainData && (aux & 8) == 8) {
                    marketPairMono.add(data);
                }
            }
            return Tuples.of(cryptoMonoOne, marketPairMono);
        });
    }

    @NotNull
    private Mono<List<WatchListResultDTO.WatchListDTO>> getWatchListResult(QueryWatchListDTO param,
        WatchListResultDTO.WatchListType watchListType, Flux<WatchListEntity> watchListEntityFlux) {
        Mono<Tuple2<List<WatchListEntity>,List<WatchListEntity>>> waitTradeList = getCryptoAndExchangeSet(
            param.getUserId(),param.getGuestId(),watchListEntityFlux, param.getAux());
        return waitTradeList.flatMap(tupleSet2 -> Mono
                .zip(cryptoInfoManager.getCryptoListingMap(tupleSet2.getT1(), param.getConvertIds(), param.getCryptoAux()),
                    cryptoInfoManager.getMarketPairsMap(tupleSet2.getT2())))
            .flatMap(tuple2 -> getWatchListResulByCal(param, watchListType, watchListEntityFlux, tuple2.getT1() ,tuple2.getT2()));

    }

    private Mono<List<WatchListResultDTO.WatchListDTO>> getWatchListResulByCal(QueryWatchListDTO param,
                                                                           WatchListResultDTO.WatchListType watchListType, Flux<WatchListEntity> watchListEntityFlux
    ,Map<Integer, CryptoCurrencyDTO> cryptoInfoMap,Map<Integer, MarketPairEntity> exchangeInfoMap) {
        Mono<Optional<Boolean>> hasFollowedMono = getHasFollowedMono(param);
        //int end = param.getStart() + param.getLimit();
        return watchListEntityFlux.flatMap(data -> {
            int aux = param.getAux();
            WatchListType wt =
                    ObjectUtils.defaultIfNull(getFollowWatchListType(param, data), watchListType);
            Mono<List<WatchListResultDTO.CryptoCurrencyDTO>> cryptoMono = Mono.just(List.of());
            Mono<List<ExchangeDTO>> exchangeMono = Mono.just(List.of());
            Mono<List<MarketPairDTO>> marketPairMono = Mono.just(List.of());
            Boolean takeMainData = (aux & 4) == 4;
            takeMainData = takeMainData ? ExtUtils.getDefaultValue(data.getMain()) : true;
            if (takeMainData && (aux & 1) == 1) {
                cryptoMono = getCryptoDetailInfos(data, param.getConvertIds(), param.getCryptoAux(), cryptoInfoMap);
            }
            if (takeMainData && (aux & 2) == 2) {
                exchangeMono = getExchangeInfos(data);
            }
            if (takeMainData && (aux & 8) == 8) {
                marketPairMono = getMarketPairs(data.getMarketPairs(), exchangeInfoMap);
            }
            if (takeMainData && (aux & 16) == 16) {
                cryptoMono = getCryptoSimpleInfos(data.getCryptos());
            }
            return Flux.zip(cryptoMono, exchangeMono, hasFollowedMono, marketPairMono).map(
                    result -> {
                        var wl = WatchListDTO.builder().main(ExtUtils.getDefaultValue(data.getMain()))
                            .userId(data.getUserId()).contentType(data.getType()).name(data.getName())
                            .order(data.getOrder()).type(wt).shared(data.getShared())
                            .followerSize(getFollowSize(data.getFollows()))
                            .dexPairIds(data.getDexPairs())
                            .dexTokenUniIds(reserveAndDistinct(data.getDexTokens()))
                            .cryptoSize(CollectionUtils.size(data.getCryptos()))
                            .assetSize(CommonUtils.getExistAssetSize(data.getDexPairs(), data.getCryptos()))
                            .watchListId(data.getId().toHexString())
                            .cryptoCurrencies(result.getT1()).exchanges(result.getT2()).marketPairs(result.getT4())
                            .description(data.getDescription()).followType(result.getT3().isPresent() ?
                                result.getT3().get() ? FollowTypeEnum.FOLLOW : FollowTypeEnum.UNFOLLOW : null)
                            .createdTime(ExtUtils.parseDateToString(data.getCreatedTime(),
                                LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS_Z)).updatedTime(
                                ExtUtils.parseDateToString(data.getUpdatedTime(),
                                    LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS_Z)).build();
                        wl.setSharedImageUrl(getSharedImageUrl(data.getId().toHexString(), wl.getAssetSize(),
                            data.getShared()));
                        return wl;
                    });
        }).collectList();
    }

    private List<String> reserveAndDistinct(List<String> tokenUniIds) {
        if (CollectionUtils.isEmpty(tokenUniIds)) {
            return tokenUniIds;
        }
        List<String> result = tokenUniIds.stream().distinct()
                .collect(Collectors.toList());
        Collections.reverse(result);
        return result;
    }

    @NotNull
    private Mono<List<WatchListResultDTO.WatchListDTO>> getWatchListResultSingle(QueryWatchListDTO param, Boolean needCheck) {
        Mono<Optional<Boolean>> hasFollowedMono = getHasFollowedMono(param);
        String cacheKey = String.format(KEY_WATCHLIST_ID, param.getWatchListId());
        Mono<WatchListCacheDTO> watchListCacheDTOMonoBase = watchListRespository.getWatchListRepository()
            .findByIdEqualsAndActivedTrueAndSharedTrue(new ObjectId(param.getWatchListId()))
            .singleOrEmpty()
            .map(data -> {
                    List<WatchListResultDTO.CryptoCurrencyDTO> cryptoCurrencyDTOS = getSingleCryptoSimpleInfos(data.getCryptos());
                    return WatchListCacheDTO.builder().main(ExtUtils.getDefaultValue(data.getMain()))
                        .userId(data.getUserId())
                        .name(data.getName()).shared(data.getShared())
                        .followerSize(getFollowSize(data.getFollows()))
                        .type(WatchListResultDTO.WatchListType.FOLLOWED)
                        .cryptoSize(CollectionUtils.size(data.getCryptos()))
                        .dexPairSize(CollectionUtils.size(data.getDexPairs()))
                        .dexTokenSize(CollectionUtils.size(data.getDexTokens()))
                        .assetSize(CommonUtils.getExistAssetSize(data.getDexPairs(), data.getCryptos()))
                        .watchListId(data.getId().toHexString()).cryptoCurrencies(cryptoCurrencyDTOS)
                        .description(data.getDescription()).createdTime(
                            ExtUtils.parseDateToString(data.getCreatedTime(),
                                LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS_Z)).updatedTime(ExtUtils
                            .parseDateToString(data.getUpdatedTime(), LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS_Z))
                        .build();
                });
        Mono<WatchListCacheDTO> watchListCacheDTOMono;

        if (needCheck) {
            // query from db
            watchListCacheDTOMono = watchListCacheDTOMonoBase;
        } else {
            watchListCacheDTOMono = cacheRepository.getCachedValueInString(cacheKey)
                .map(cacheValue -> JacksonUtils.getInstance().deserialize(cacheValue, WatchListCacheDTO.class))
                .switchIfEmpty(Mono.defer(() -> watchListCacheDTOMonoBase
                    .publishOn(Schedulers.boundedElastic())
                    .doOnNext(watchListDTO -> {
                        String valueString = JacksonUtils.toJsonString(watchListDTO);
                        cacheRepository.cacheInString(cacheKey, valueString, Duration.ofMinutes(5)).subscribe();
                    })));
        }

        return watchListCacheDTOMono.map(data -> {
                WatchListType wt;
                if (StringUtils.isNotBlank(param.getUserId())) {
                    wt = StringUtils.equals(data.getUserId(), param.getUserId()) ? WatchListResultDTO.WatchListType.ORDINARY :
                        WatchListResultDTO.WatchListType.FOLLOWED;
                } else {
                    wt = WatchListResultDTO.WatchListType.FOLLOWED;
                }
                data.setType(wt);
            return WatchListDTO.builder().main(data.getMain()).name(data.getName())
                .shared(data.getShared()).followerSize(data.getFollowerSize()).type(data.getType())
                .assetSize(data.getAssetSize() != null? data.getAssetSize():data.getCryptoSize())
                .cryptoSize(data.getCryptoSize())
                .dexPairSize(data.getDexPairSize())
                .dexTokenSize(data.getDexTokenSize())
                .watchListId(data.getWatchListId())
                .cryptoCurrencies(data.getCryptoCurrencies()).description(data.getDescription())
                .createdTime(data.getCreatedTime()).updatedTime(data.getUpdatedTime()).build();
            })
            .flatMap(data -> hasFollowedMono.map(result -> {
                data.setFollowType(
                    result.map(followStatus -> followStatus ? FollowTypeEnum.FOLLOW : FollowTypeEnum.UNFOLLOW).orElse(null));
                return data;
            }))
            .map(Lists::newArrayList);
    }

    /**
     * @param cryptos
     * @return
     * @top top == 0 all.
     */
    private List<WatchListResultDTO.CryptoCurrencyDTO> getSingleCryptoSimpleInfos(Set<Integer> cryptos) {

        if (CollectionUtils.isEmpty(cryptos)) {
            return List.of();
        }
        List<WatchListResultDTO.CryptoCurrencyDTO> cryptoCurrencyList = Lists.newArrayListWithCapacity(cryptos.size());
        for (Integer crypto : cryptos) {
            cryptoCurrencyList.add(WatchListResultDTO.CryptoCurrencyDTO.builder()
                .id(crypto).build());
        }
        return cryptoCurrencyList;
    }

    /**
     * getFollowSize
     *
     * @param size
     * @return
     */
    private static Long getFollowSize(Long size) {

        Long s = ExtUtils.getDefaultValue(size);
        return s < 0 ? 0 : s;
    }

    /**
     * getFollowWatchListType
     *
     * @param param
     * @param data
     * @return
     */
    private WatchListResultDTO.WatchListType getFollowWatchListType(QueryWatchListDTO param, WatchListEntity data) {

        boolean canFollowed = (param.getWatchListType() == WatchListResultDTO.WatchListType.FOLLOWED && StringUtils
                .isNotBlank(param.getWatchListId()));
        if (canFollowed && StringUtils.isNotBlank(param.getUserId())) {
            return StringUtils.equals(data.getUserId(), param.getUserId()) ? WatchListResultDTO.WatchListType.ORDINARY :
                    WatchListResultDTO.WatchListType.FOLLOWED;
        }
        return null;
    }

    @NotNull
    private Mono<Optional<Boolean>> getHasFollowedMono(QueryWatchListDTO param) {

        Mono<Optional<Boolean>> hasFollowedMono = Mono.just(Optional.empty());
        boolean canFollowed = param.getWatchListType() == WatchListResultDTO.WatchListType.FOLLOWED && StringUtils
                .isNotBlank(param.getWatchListId()) && StringUtils.isNotBlank(param.getUserId());
        if (canFollowed) {
            hasFollowedMono =
                    followWatchListRepository.existsByUserIdAndWatchListId(param.getUserId(), param.getWatchListId())
                            .map(m -> Optional.ofNullable(m)).switchIfEmpty(Mono.just(Optional.empty()))
                            .onErrorResume(throwable -> {
                                log.error("user has follows watchlist error ", throwable);
                                return Mono.just(Optional.empty());
                            });
        }
        return hasFollowedMono;
    }

    @NotNull
    private Mono<List<WatchListResultDTO.ExchangeDTO>> getExchangeInfos(WatchListEntity data) {

        return Flux.fromIterable(ObjectUtils.defaultIfNull(data.getExchanges(), Set.of()))
                .map(m -> Optional.ofNullable(cryptoCurrencyCache.getById(m))).filter(f -> f.isPresent()).map(
                        exchange -> WatchListResultDTO.ExchangeDTO.builder().id(exchange.get().getId())
                                .name(exchange.get().getName()).slug(exchange.get().getSlug()).status(exchange.get().getStatus())
                                .build()).collectList();
    }

    /**
     * get the market pairs;
     *
     * @param ids
     * @return
     */
    private Mono<List<WatchListResultDTO.MarketPairDTO>> getMarketPairs(Collection<Integer> ids, Map<Integer,MarketPairEntity> exchangeInfoMap) {
        if (CollectionUtils.isEmpty(ids)) {
            return Mono.just(List.of());
        }

        if (exchangeInfoMap != null) {
            return Mono.just(exchangeInfoMap)
                .flatMapMany(mps -> Flux.fromIterable(ids)
                    .map(m -> WatchListResultDTO.MarketPairDTO.builder().id(m).status(mps.containsKey(m)).build()))
                .collectList();
        }

        return baseDataServiceClient.getMarketPairsBatch(ids)
            .flatMapIterable(Function.identity())
            .collectMap(
                it -> it.getId(),
                it -> MarketPairEntity.builder()
                    .id(it.getId()).pairBase(it.getPairBase()).pairQuote(it.getPairQuote()).build()
            ).onErrorResume(throwable -> {
                log.warn("baseDataServiceClient.getMarketPairs exception.", throwable);
                return Mono.just(Map.of());
            }).flatMapMany(mps -> Flux.fromIterable(ids)
                .map(m -> WatchListResultDTO.MarketPairDTO.builder().id(m).status(mps.containsKey(m)).build()))
            .collectList();

    }

    @NotNull
    private Mono<List<WatchListResultDTO.CryptoCurrencyDTO>> getCryptoDetailInfos(WatchListEntity data, String convertIds,
                                                                                  String cryptoAux, Map<Integer, CryptoCurrencyDTO> cryptoInfoMap) {

        data.setCryptos(ObjectUtils.defaultIfNull(data.getCryptos(), Set.of()));
        if(cryptoInfoMap != null){
            return Mono.just(cryptoInfoMap)
                .flatMap(cryptoMap -> {
                    List<WatchListResultDTO.CryptoCurrencyDTO> cryptoCurrencyList = Lists.newArrayListWithCapacity(CollectionUtils.size(data.getCryptos()));
                    for (Integer id : data.getCryptos()) {
                        CryptoCurrencyDTO crypto = cryptoMap.get(id);
                        CryptoCurrencyInfoDTO cryptoCurrencyInfoOfCache = cryptoCurrencyCache.getById(id);
                        WatchListResultDTO.CryptoCurrencyDTO cryptoCurrency = buildCryptoCurrencyInfo(crypto, cryptoCurrencyInfoOfCache);
                        if (cryptoCurrency == null) {
                            continue;
                        }
                        cryptoCurrencyList.add(cryptoCurrency);
                    }
                    cryptoCurrencyList.sort(Comparator.comparing(f -> f.getRank() == null ? Integer.MAX_VALUE : f.getRank()));
                    return Mono.just(cryptoCurrencyList);
                })
                .defaultIfEmpty(List.of());
        }
       return dataApiServiceClient.getCryptoListingBatch(data.getCryptos(), convertIds, cryptoAux)
                .onErrorResume(throwable -> {
                    log.error("get crypto details  failed.", throwable);
                    return Mono.empty();
                })
                .filter(f -> CollectionUtils.isNotEmpty(f.getCryptoCurrencyList()))
                .defaultIfEmpty(CryptoCurrencyListResultDTO.builder().cryptoCurrencyList(List.of()).build())
                .map(cryptos -> {
                    Map<Integer, CryptoCurrencyDTO> cryptoMap = cryptos.getCryptoCurrencyList().stream()
                            .collect(Collectors.toMap(k -> k.getId(), v -> v, (k1, k2) -> k1));
                    List<WatchListResultDTO.CryptoCurrencyDTO> cryptoCurrencyList = Lists.newArrayListWithCapacity(CollectionUtils.size(data.getCryptos()));
                    for (Integer id : data.getCryptos()) {
                        CryptoCurrencyDTO crypto = cryptoMap.get(id);
                        CryptoCurrencyInfoDTO cryptoCurrencyInfoOfCache = cryptoCurrencyCache.getById(id);
                        WatchListResultDTO.CryptoCurrencyDTO cryptoCurrency = buildCryptoCurrencyInfo(crypto, cryptoCurrencyInfoOfCache);
                        if (cryptoCurrency == null) {
                            continue;
                        }
                        cryptoCurrencyList.add(cryptoCurrency);
                    }
                    cryptoCurrencyList.sort(Comparator.comparing(f -> f.getRank() == null ? Integer.MAX_VALUE : f.getRank()));
                    return cryptoCurrencyList;
                }).defaultIfEmpty(List.of());
    }

    /**
     * build crypto info
     *
     * @param crypto
     * @param cryptoCurrencyInfoOfCache
     * @return
     */
    @Nullable
    private WatchListResultDTO.CryptoCurrencyDTO buildCryptoCurrencyInfo(CryptoCurrencyDTO crypto, CryptoCurrencyInfoDTO cryptoCurrencyInfoOfCache) {

        WatchListResultDTO.CryptoCurrencyDTO cryptoCurrency = null;
        String status = cryptoCurrencyInfoOfCache == null ? Strings.EMPTY : cryptoCurrencyInfoOfCache.getStatus();
        if (crypto != null) {
            cryptoCurrency = WatchListResultDTO.CryptoCurrencyDTO.builder()
                    .id(crypto.getId())
                    .name(crypto.getName())
                    .rank(crypto.getCmcRank())
                    .wrappedStakedMcRank(crypto.getWrappedStakedMcRank())
                    .slug(crypto.getSlug())
                    .status(status)
                    .symbol(crypto.getSymbol())
                    .circulatingSupply(crypto.getCirculatingSupply())
                    .selfReportedCirculatingSupply(crypto.getSelfReportedCirculatingSupply())
                    .dateAdded(crypto.getDateAdded())
                    .lastUpdated(crypto.getLastUpdated())
                    .marketPairCount(crypto.getMarketPairCount())
                    .maxSupply(crypto.getMaxSupply())
                    .platform(crypto.getPlatform())
                    .quotes(crypto.getQuotes())
                    .totalSupply(crypto.getTotalSupply())
                    .tags(crypto.getTags())
                    .category(crypto.getCategory())
                    .ath(crypto.getAth())
                    .atl(crypto.getAtl())
                    .high24h(crypto.getHigh24h())
                    .isActive(crypto.getIsActive())
                    .low24h(crypto.getLow24h())
                    .isAudited(crypto.getIsAudited())
                    .auditInfoList(crypto.getAuditInfoList())
                    .build();
        } else {
            if (cryptoCurrencyInfoOfCache != null) {
                cryptoCurrency = WatchListResultDTO.CryptoCurrencyDTO.builder()
                        .id(cryptoCurrencyInfoOfCache.getId())
                        .name(cryptoCurrencyInfoOfCache.getName())
                        .rank(cryptoCurrencyInfoOfCache.getRank())
                        .status(ListingStatus.UNTRACKED.getCode())
                        .slug(cryptoCurrencyInfoOfCache.getSlug())
                        .symbol(cryptoCurrencyInfoOfCache.getSymbol()).build();
            }
        }
        return cryptoCurrency;
    }

    /***
     *
     * @param cryptos
     * @return
     */
    private Mono<List<WatchListResultDTO.CryptoCurrencyDTO>> getCryptoSimpleInfos(Set<Integer> cryptos) {

        return Mono.just(getCryptoSimpleInfosByCache(cryptos, 0));
    }

    /**
     * @param cryptos
     * @return
     * @top top == 0 all.
     */
    private List<WatchListResultDTO.CryptoCurrencyDTO> getCryptoSimpleInfosByCache(Set<Integer> cryptos, int top) {

        if (CollectionUtils.isEmpty(cryptos)) {
            return List.of();
        }
        List<WatchListResultDTO.CryptoCurrencyDTO> cryptoCurrencyList = Lists.newArrayListWithCapacity(cryptos.size());
        int index = 0;
        for (Integer crypto : cryptos) {
            CryptoCurrencyInfoDTO cryptoCurrencyInfo = cryptoCurrencyCache.getById(crypto);
            if (cryptoCurrencyInfo == null) {
                continue;
            }
            if (top > 0 && index >= top) {
                break;
            }
            cryptoCurrencyList.add(WatchListResultDTO.CryptoCurrencyDTO.builder()
                    .id(cryptoCurrencyInfo.getId())
                    .name(cryptoCurrencyInfo.getName())
                    .rank(cryptoCurrencyInfo.getRank())
                    .status(cryptoCurrencyInfo.getStatus())
                    .slug(cryptoCurrencyInfo.getSlug())
                    .symbol(cryptoCurrencyInfo.getSymbol()).build());
            index++;
        }
        return cryptoCurrencyList;
    }


    /**
     * build sharedWatchList
     *
     * @param watchLists
     * @return
     */
    private List<WatchListResultDTO.WatchListDTO> buildSharedWatchList(String userId, List<WatchListCacheEntity> watchLists,
                                                                       Set<String> follower) {

        if (CollectionUtils.isEmpty(watchLists)) {
            return List.of();
        }
        List<WatchListResultDTO.WatchListDTO> sharedWatchLists = Lists.newArrayListWithCapacity(watchLists.size());
        for (WatchListCacheEntity watchList : watchLists) {
            String id = watchList.getId();
            FollowTypeEnum followTypeEnum = null;
            if (StringUtils.equals(watchList.getUserId(), userId)) {
                followTypeEnum = FollowTypeEnum.ORDINARY;
            } else {
                followTypeEnum = follower == null ? FollowTypeEnum.UNFOLLOW :
                        follower.contains(id) ? FollowTypeEnum.FOLLOW : FollowTypeEnum.UNFOLLOW;
            }
            var wl = WatchListResultDTO.WatchListDTO.builder()
                    .watchListId(id)
                    .name(watchList.getName())
                    .order(watchList.getOrder())
                    .followerSize(getFollowSize(watchList.getFollows()))
                    .cryptoSize(ExtUtils.getDefaultValue(watchList.getCryptoSize()))
                    .assetSize(watchList.getAssetSize() != null?
                        ExtUtils.getDefaultValue(watchList.getAssetSize()):ExtUtils.getDefaultValue(watchList.getCryptoSize()))
                    .createdTime(ExtUtils.parseDateToString(watchList.getCreatedTime(), ExtUtils.LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS_Z))
                    .updatedTime(ExtUtils.parseDateToString(watchList.getUpdatedTime(), ExtUtils.LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS_Z))
                    .description(watchList.getDescription()).followType(followTypeEnum)
                    .cryptoCurrencies(getCryptoSimpleInfosByCache(watchList.getCryptos(), 2))
                    .build();
            wl.setSharedImageUrl(getSharedImageUrl(id, wl.getAssetSize(), watchList.getShared()));
            sharedWatchLists.add(wl);
        }
        return sharedWatchLists;
    }

    /**
     * @param param
     * @return
     */
    private Mono<List<WatchListResultDTO.WatchListDTO>> getFollowWatchListByUserId(QueryWatchListDTO param) {

        if (param == null || StringUtils.isBlank(param.getUserId())) {
            return Mono.empty();
        }
        return followWatchListRepository.findByUserId(param.getUserId()).flatMapMany(
                r -> {
                    if (CollectionUtils.isEmpty(r.getWatchListIds())) {
                        return Flux.empty();
                    }
                    return Flux.fromIterable(r.getWatchListIds()).window(50).flatMap(
                                    ids -> ids.map(id -> new ObjectId(id)).collectList().flatMap(
                                            followIds -> getWatchListResult(param, WatchListResultDTO.WatchListType.FOLLOWED,
                                                    watchListRespository.getWatchListRepository()
                                                            .findAllByIdInAndActivedTrue(followIds)
                                                            .filter(f -> !StringUtils.equals(f.getUserId(), param.getUserId())))))
                            .flatMapIterable(f -> f);
                }).onErrorResume(throwable -> {
            log.error("get-follow-watchlist-error", throwable);
            return Flux.empty();
        }).sort(Comparator.comparing(WatchListDTO::getUpdatedTime).reversed()).collectList();
    }

    /**
     * getSharedImageUrl
     *
     * @param id
     * @return
     */
    private String getSharedImageUrl(String id, Integer coinSize, Boolean shared) {

        if (!ExtUtils.getDefaultValue(shared) || coinSize == null || coinSize.intValue() <= 0) {
            return null;
        }
        return String.format(shareImageUrl + "/generated/watchlist/shared/%s/%s.png", getBucketOfIndex(id), id);
    }

    /**
     * @param key
     * @return
     */
    private final int hash(String key) {

        int h;
        return (key == null) ? 0 : (h = (Integer.parseInt(key.substring(0, 8), 16) * 1000)) ^ (h >>> 16);
    }

    /**
     * @param key
     * @return
     */
    private final int getBucketOfIndex(String key) {

        int h = hash(key);
        return h & 8191;
    }

    private void resetTopCoins(BaseRequest request) {
        userCoinService.resetTopCoins(request, WATCHLIST.getSourceId()).subscribe();
    }
}
