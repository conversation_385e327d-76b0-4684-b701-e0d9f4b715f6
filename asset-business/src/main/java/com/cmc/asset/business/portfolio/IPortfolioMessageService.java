package com.cmc.asset.business.portfolio;

import reactor.core.publisher.Mono;

/**
 * portfolio kafka trade service
 * <AUTHOR> ricky.x
 * @date : 2023/2/21 下午5:21
 */
public interface IPortfolioMessageService {

    /**
     * send wallet job message
     * @param walletAddress
     * @param portfolioType
     * @return reactor.core.publisher.Mono<java.lang.Boolean>
     **/
    Mono<Boolean> sendRefreshUserWallet(String walletAddress, String portfolioType);
}
