package com.cmc.asset.business.portfolio.strategy;

import static com.cmc.asset.model.common.Constant.DEFAULT_SCALE;

import com.cmc.asset.business.portfolio.IPortfolioTradeStrategy;
import com.cmc.asset.business.portfolio.WalletAssetService;
import com.cmc.asset.business.portfolio.WalletTransactionService;
import com.cmc.asset.business.portfolio.impl.CurrencyPriceService;
import com.cmc.asset.business.portfolio.impl.PortfolioBaseService;
import com.cmc.asset.business.portfolio.impl.PortfolioLineChartUtil;
import com.cmc.asset.business.portfolio.manager.PortfolioWalletDetailManager;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.cache.DexTokenCache;
import com.cmc.asset.cache.DexerPlatformCache;
import com.cmc.asset.config.DynamicApolloRefreshConfig;
import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.BlockChainTokenPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionTokenPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioWalletDetailPO;
import com.cmc.asset.dao.entity.portfolio.PortfolioWalletTransactionPO;
import com.cmc.asset.dao.repository.mongo.PortfolioMultiRepository;
import com.cmc.asset.dao.repository.mongo.PortfolioWalletDetailRepository;
import com.cmc.asset.dao.repository.redis.AssetCacheRepository;
import com.cmc.asset.dao.repository.redis.CacheRepository;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.domain.portfolio.PortfolioOkLinkMapVO;
import com.cmc.asset.domain.portfolio.PortfolioOkTransactionMapVO;
import com.cmc.asset.model.common.CommonPageVO;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartQueryDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQueryCryptoResponseDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioStatisticsDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioSupportChainDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioSupportDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.UnionPortfolio;
import com.cmc.asset.model.contract.portfolio.transaction.PortfolioTransactionRequest;
import com.cmc.asset.model.contract.portfolio.transaction.PortfolioTransactionResponse;
import com.cmc.asset.model.contract.portfolio.transaction.TransactionCryptosQueryRequest;
import com.cmc.asset.model.contract.portfolio.transaction.TransactionCryptosQueryResponse;
import com.cmc.asset.model.contract.portfolio.transaction.TransactionDTO;
import com.cmc.asset.model.enums.PortfolioCryptoVerificationStatus;
import com.cmc.asset.model.enums.PortfolioHistoricalDayEnum;
import com.cmc.asset.model.enums.PortfolioTokenTypeEnum;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import com.cmc.asset.model.enums.PortfolioWalletDataFlagEnum;
import com.cmc.asset.model.enums.TransactionTypeEnum;
import com.cmc.data.common.utils.JacksonUtils;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.framework.utils.MathUtils;
import com.cmc.framework.utils.StringUtils;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

/**
 * wallet trade service
 *
 * <AUTHOR> ricky.x
 * @date : 2023/2/11 下午1:24
 */
@Service
@Slf4j
public class PortfolioWalletStrategyImpl implements IPortfolioTradeStrategy {

    @Resource
    private PortfolioWalletDetailRepository portfolioWalletDetailRepository;

    @Autowired
    private CurrencyPriceService currencyPriceService;

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Autowired
    private CacheRepository cacheRepository;

    @Autowired
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Autowired
    private PortfolioLineChartUtil portfolioLineChartUtil;

    @Autowired
    private DexTokenCache dexTokenCache;

    @Autowired
    private DexerPlatformCache dexerPlatformCache;

    @Autowired
    private WalletTransactionService walletTransactionService;

    /**
     * portfolio wallet path cache time(5 minute)
     */
    @Value("${com.cmc.asset-service.portfolio_wallet.cache-time}")
    private Integer cacheTime;

    @Autowired
    private PortfolioWalletDetailManager portfolioWalletDetailManager;

    @Autowired
    private PortfolioMultiRepository portfolioMultiRepository;

    @Autowired
    private PortfolioBaseService portfolioBaseService;

    @Autowired
    private WalletAssetService walletAssetService;

    @Autowired
    private AssetCacheRepository assetCacheRepository;

    @Override
    public PortfolioTypeEnum getPortfolioType() {
        return PortfolioTypeEnum.WALLET;
    }

    @Override
    public Mono<BigDecimal> portfolioTotalValue(ObjectId userId, String sourceId, Integer cryptoUnit) {
       return portfolioTotalValue(userId, sourceId, true);
    }

    @Override
    public Mono<BigDecimal> portfolioTotalValue(ObjectId userId, String sourceId, boolean containUnverified) {
        if(userId == null){
            return Mono.just(BigDecimal.ZERO);
        }
        return portfolioMultiRepository.findOne(Example.of(PortfolioMultiEntity.builder()
            .userId(userId).id(new ObjectId(sourceId)).build(), ExampleMatcher.matching().withIgnoreNullValues()))
            .publishOn(Schedulers.boundedElastic())
            .flatMap(multi -> queryWalletDetail(null, multi, containUnverified)
                .filter(dto -> dto.getCurrentTotalHoldings() != null)
                .map(PortfolioStatisticsDTO::getCurrentTotalHoldings)
                .defaultIfEmpty(BigDecimal.ZERO));
    }

    @Override
    public Mono<PortfolioStatisticsDTO> portfolioStatistics(PortfolioCryptoDTO portfolioUserIdDTO,
                                                            PortfolioMultiEntity multi) {
        boolean containUnverified = PortfolioCryptoVerificationStatus.getByCode(portfolioUserIdDTO.getCryptoVerificationStatus()) != PortfolioCryptoVerificationStatus.VERIFIED;
        return queryWalletDetail(portfolioUserIdDTO.getChainId(), multi, containUnverified);
    }

    @Override
    public Mono<BigDecimal> getYesterdayHoldingsV2(String userId, String portfolioSourceId, Date time, Integer cryptoUnit) {
        if(userId == null){
            return Mono.just(BigDecimal.ZERO);
        }

        return portfolioMultiRepository.findOne(Example.of(PortfolioMultiEntity.builder()
            .userId(new ObjectId(userId))
            .id(new ObjectId(portfolioSourceId)).build(), ExampleMatcher.matching().withIgnoreNullValues()))
            .publishOn(Schedulers.boundedElastic())
            .flatMap(multi -> queryWalletDetail(null, multi, true)
                .filter(dto -> dto.getYesterdayTotalValue() != null)
                .map(PortfolioStatisticsDTO::getYesterdayTotalValue)
                .defaultIfEmpty(BigDecimal.ZERO));
    }

    @Override
    public Mono<BigDecimal> getYesterdayHoldingsV2(String userId, String portfolioSourceId, Date time, boolean containUnverified) {
        PortfolioMultiEntity multi =
                PortfolioMultiEntity.builder()
                        .userId(new ObjectId(userId))
                        .portfolioSourceId(portfolioSourceId)
                        .build();
        return queryWalletDetail(null, multi, containUnverified)
                .filter(dto -> dto.getYesterdayTotalValue() != null)
                .map(PortfolioStatisticsDTO::getYesterdayTotalValue)
                .defaultIfEmpty(BigDecimal.ZERO);
    }

    private Mono<PortfolioStatisticsDTO> queryWalletDetail(Integer filterChainId, PortfolioMultiEntity multi, boolean containUnverified) {
        return walletAssetService.getWalletDetailPOByAddress(multi.getWalletAddress(),multi.getUserId(), multi.getPortfolioSourceId(), true)
            .filter(d -> CollectionUtils.isNotEmpty(d.getBlockChains()))
            .flatMap(e -> {
                List<BlockChainPO> tradeList = (filterChainId == null) ? e.getBlockChains() :
                        e.getBlockChains()
                                .stream()
                                .filter(blockChain -> blockChain.getChainId().equals(filterChainId))
                                .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(tradeList)) {
                    return Mono.just(PortfolioStatisticsDTO.builder().build());
                }
                return getTotalValueGroupByChain(tradeList, containUnverified).flatMap(list -> {
                    BigDecimal totalValue = BigDecimal.ZERO;
                    BigDecimal yesterdayTotalValue = BigDecimal.ZERO;
                    for (Tuple2<BigDecimal, BigDecimal> tuple2 : list) {
                        totalValue = totalValue.add(tuple2.getT1());
                        yesterdayTotalValue = yesterdayTotalValue.add(tuple2.getT2());
                    }
                    PortfolioStatisticsDTO portfolioStatisticsDTO = PortfolioStatisticsDTO.builder().build();
                    portfolioStatisticsDTO.setCurrentTotalHoldings(totalValue);
                    portfolioStatisticsDTO.setYesterdayTotalValue(yesterdayTotalValue);
                    portfolioStatisticsDTO.setYesterdayChangeBalance(totalValue.subtract(yesterdayTotalValue));
                    if (yesterdayTotalValue.compareTo(BigDecimal.ZERO) > 0) {
                        portfolioStatisticsDTO.setYesterdayBalancePercent(portfolioStatisticsDTO.getYesterdayChangeBalance()
                                .divide(yesterdayTotalValue, DEFAULT_SCALE, RoundingMode.HALF_EVEN));
                    }
                    return Mono.just(portfolioStatisticsDTO);
                });
            }).defaultIfEmpty(PortfolioStatisticsDTO.builder().build());
    }

    private Mono<List<Tuple2<BigDecimal, BigDecimal>>> getTotalValueGroupByChain(List<BlockChainPO> blockChains, boolean isContainUnverified) {
        List<Integer> nativeTokenIds = blockChains.stream()
                .map(BlockChainPO::getNativeTokenId)
                .collect(Collectors.toList());

        return currencyPriceService.queryCurrentPrices(nativeTokenIds)
                .flatMap(prices -> Flux.fromIterable(blockChains)
                        .filter(chainPO -> chainPO.getNativeTokenId() != null && prices.containsKey(chainPO.getNativeTokenId()))
                        .filter(chainPO -> chainPO.getTotalTokenAmount() != null
                                && chainPO.getTotalVerifiedTokenAmount() != null
                                && chainPO.getTotalTokenAmount().compareTo(BigDecimal.ZERO) != 0
                                && chainPO.getTotalVerifiedTokenAmount().compareTo(BigDecimal.ZERO) != 0)
                        .map(chainPO -> {
                            PortfolioCryptoInfoDTO cryptoPrice = prices.get(chainPO.getNativeTokenId());
                            BigDecimal nowValue;
                            BigDecimal nowAmount;
                            if (isContainUnverified) {
                                nowValue = chainPO.getTotalTokenValue();
                                nowAmount = chainPO.getTotalTokenAmount();
                            } else {
                                nowValue = chainPO.getTotalVerifiedTokenValue();
                                nowAmount = chainPO.getTotalVerifiedTokenAmount();
                            }
                            if (nowValue == null) {
                                nowValue = cryptoPrice.transferPrice().multiply(nowAmount);
                            }
                            BigDecimal changeValue =
                                    (cryptoPrice.transferChangePerCent()
                                            .divide(new BigDecimal(100), DEFAULT_SCALE, RoundingMode.HALF_EVEN)).multiply(nowValue);
                            BigDecimal yesterdayValue = nowValue.subtract(changeValue);
                            return Tuples.of(nowValue, yesterdayValue);
                        }).collectList());
    }

    @Override
    public Mono<List<PortfolioTotalDTO>> historicalChart(PortfolioHistoricalChartQueryDTO queryDTO, PortfolioMultiEntity multi) {
        return walletAssetService.getWalletDetailPOByAddress(multi.getWalletAddress(), multi.getUserId(), multi.getPortfolioSourceId(), true)
            .filter(d -> CollectionUtils.isNotEmpty(d.getBlockChains()))
            .flatMap(walletDetailPO -> findWalletHistory(walletDetailPO, queryDTO.getChainId(), queryDTO.getCryptoVerificationStatus())
                .flatMap(historicalMap -> queryPortfolioTotalList(historicalMap, queryDTO))
                .map(holdingChartList -> {
                    if (CollectionUtils.isEmpty(holdingChartList) || CollectionUtils.isEmpty(walletDetailPO.getBlockChains())) {
                        return List.of();
                    }
                    if (walletDetailPO.getBlockChains()
                            .stream()
                            .map(BlockChainPO::getTotalTokenValue)
                            .allMatch(Objects::isNull)) {
                        return holdingChartList;
                    }
                    Date lastUpdated = walletDetailPO.getTimeUpdated();
                    if (lastUpdated == null && DatetimeUtils.getMinutesAgo(DatetimeUtils.now(), 5).compareTo(lastUpdated) <= 0) {
                        return holdingChartList;
                    }
                    BigDecimal totalHoldings = walletDetailPO.getBlockChains().stream()
                            .filter(chainPO -> {
                                if (queryDTO.getChainId() != null) {
                                    return queryDTO.getChainId().equals(chainPO.getChainId());
                                }
                                return true;
                            })
                            .map(chainPO -> {
                                if (PortfolioCryptoVerificationStatus.VERIFIED.getCode()
                                        .equals(queryDTO.getCryptoVerificationStatus())) {
                                    return chainPO.getTotalVerifiedTokenValue();
                                }
                                return chainPO.getTotalTokenValue();
                            })
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return holdingChartList.stream()
                            .filter(dto -> dto.getTimestamp().compareTo(lastUpdated) < 0)
                            .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                                list.add(PortfolioTotalDTO.builder()
                                        .value(totalHoldings)
                                        .timestamp(lastUpdated)
                                        .build());
                                return list.stream()
                                        .sorted(Comparator.comparing(PortfolioTotalDTO::getTimestamp))
                                        .collect(Collectors.toList());
                            }));
                }));
    }

    @Override
    public Mono<Boolean> hasAsset(String userId, String portfolioSourceId) {
        return portfolioBaseService.getAndRefreshCache(userId, portfolioSourceId)
            .flatMap(multiEntity -> walletAssetService.getWalletDetailPOByAddress(multiEntity.getWalletAddress(),
                multiEntity.getUserId(), multiEntity.getPortfolioSourceId(), false))
            .map(asset -> CollectionUtils.isNotEmpty(asset.getBlockChains()))
            .defaultIfEmpty(false);
    }

    @Override
    public Mono<PortfolioTransactionResponse> queryTransaction(PortfolioTransactionRequest requestDto, PortfolioMultiEntity multi) {
        return getWalletTransaction(multi)
            .flatMap(po -> {
                if (CollectionUtils.isEmpty(po.getChainTransactions())) {
                    return transactionResponse(false, null, null, null, requestDto);
                }
                return filterAndSortTransaction(po.getChainTransactions(), requestDto, multi)
                        .flatMap(tupleList -> transactionResponse(false, tupleList.getT1(), tupleList.getT2(), tupleList.getT3(), requestDto))
                        .switchIfEmpty(Mono.defer(() -> transactionResponse(false, null, null, null, requestDto)));
            });
    }

    @Override
    public Mono<PortfolioQueryCryptoResponseDTO> queryByCrypto(PortfolioCryptoDTO requestDto, PortfolioCryptoInfoDTO cryptoInfoDTO, boolean queryAll, List<PortfolioMultiEntity> multies, Integer cryptoUnit) {
        String userId = requestDto.getHeader().getUserId();
        Integer cryptocurrencyId = cryptoInfoDTO.getCryptoId();

        Map<String, PortfolioMultiEntity> walletAddressMap = multies.stream()
            .filter(d -> StringUtils.isNotBlank(d.getWalletAddress()))
            .collect(Collectors.toMap(PortfolioMultiEntity::getWalletAddress, Function.identity(), (o1, o2) -> o1));

        return Flux.fromStream(walletAddressMap.entrySet().stream())
            .flatMap(entrySet -> walletAssetService.getWalletDetailPOByAddress(entrySet.getValue().getWalletAddress(),
                entrySet.getValue().getUserId(), entrySet.getValue().getPortfolioSourceId(), true)
                .filter(entity -> CollectionUtils.isNotEmpty(entity.getBlockChains()))
                .map(entity -> Tuples.of(entity.getPortfolioSourceId(), getWalletTokenAmount(entity.getBlockChains(), cryptocurrencyId))))
            .collectList()
            .flatMap(tuples -> {
                Map<String, BigDecimal> amountMap = tuples.stream()
                    .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));
                Flux<PortfolioMultiEntity> allWalletMulti = Flux.fromIterable(multies).filter(multi -> amountMap.containsKey(multi.getPortfolioSourceId()));

                Mono<PortfolioQueryCryptoResponseDTO> portfolioQueryCryptoResponseDTOMono = allWalletMulti
                    .filter(multi -> getOwnWallet(multi.getOwnWallet()))
                    .map(multi -> amountMap.getOrDefault(multi.getPortfolioSourceId(), BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .map(totalAmount -> PortfolioQueryCryptoResponseDTO.builder()
                        .cryptocurrencyId(cryptoInfoDTO.getCryptoId())
                        .name(cryptoInfoDTO.getName())
                        .slug(cryptoInfoDTO.getSlug())
                        .symbol(cryptoInfoDTO.getSymbol())
                        .currentPrice(cryptoInfoDTO.transferPrice().setScale(DEFAULT_SCALE, RoundingMode.HALF_EVEN))
                        .cryptoHoldings(totalAmount.multiply(cryptoInfoDTO.transferPrice()).setScale(DEFAULT_SCALE, RoundingMode.HALF_EVEN))
                        .amount(totalAmount)
                        .build()).defaultIfEmpty(PortfolioQueryCryptoResponseDTO.builder()
                        .amount(BigDecimal.ZERO)
                        .build());

                if (queryAll) {
                    Set<String> walletPortfolioSourceIds = tuples.stream()
                        .map(Tuple2::getT1)
                        .collect(Collectors.toSet());

                    return portfolioQueryCryptoResponseDTOMono
                        .flatMap(responseDTO -> portfolioMultiRepository.findByUserIdAndPortfolioSourceIdIn(new ObjectId(userId), walletPortfolioSourceIds)
                            .map(multi -> buildPortfolioDTO(multi, amountMap, cryptoInfoDTO))
                            .filter(portfolioDTO -> portfolioDTO.getAmount().compareTo(BigDecimal.ZERO) != 0)
                            .collectList()
                            .map(portfolioDTOS -> {
                                responseDTO.setPortfolioList(portfolioDTOS);

                                BigDecimal yesterdayHoldings = portfolioDTOS.stream()
                                    .map(PortfolioDTO::getYesterdayValue)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                responseDTO.setYesterdayHoldings(yesterdayHoldings);

                                return responseDTO;
                            }));
                }

                return portfolioQueryCryptoResponseDTOMono;
            });
    }

    @Override
    public Mono<CommonPageVO<TransactionCryptosQueryResponse>> queryTransactionCryptos(TransactionCryptosQueryRequest requestDto, PortfolioMultiEntity multi) {
        return getWalletTransaction(multi)
                .flatMapMany(po -> {
                    if (CollectionUtils.isEmpty(po.getChainTransactions())) {
                        return Flux.empty();
                    }
                    Stream<TransactionCryptosQueryResponse> cryptoList = po.getChainTransactions()
                            .stream()
                            .filter(t -> CollectionUtils.isNotEmpty(t.getTokenTransactions()))
                            .flatMap(t -> t.getTokenTransactions().stream().map(ChainTransactionTokenPO::getCryptoId))
                            .distinct()
                            .map(cryptocurrencyId -> {
                                CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getById(cryptocurrencyId);
                                if (currencyInfoDTO != null) {
                                    return TransactionCryptosQueryResponse.builder()
                                            .cryptoId(cryptocurrencyId)
                                            .name(currencyInfoDTO.getName())
                                            .symbol(currencyInfoDTO.getSymbol())
                                            .slug(currencyInfoDTO.getSlug())
                                            .order(currencyInfoDTO.getRank())
                                            .build();
                                }
                                return TransactionCryptosQueryResponse.builder().build();
                            })
                            .filter(c -> c.getCryptoId() != null)
                            .sorted(Comparator.comparing(
                                    TransactionCryptosQueryResponse::getOrder,
                                    Comparator.nullsLast(Comparator.naturalOrder())
                            ));

                    return Flux.fromStream(cryptoList);
                })
                .distinct(TransactionCryptosQueryResponse::getCryptoId)
                .collectList()
                .map(cryptoList -> {
                    Integer currentPage = requestDto.getCurrentPage();
                    Integer pageSize = requestDto.getPageSize();

                    if (currentPage == null) {
                        CommonPageVO<TransactionCryptosQueryResponse> commonPageVO = new CommonPageVO<>();
                        commonPageVO.setList(cryptoList);
                        commonPageVO.setCurrentPage(1);
                        commonPageVO.setPageSize(cryptoList.size());
                        commonPageVO.setTotalNum(cryptoList.size());
                        return commonPageVO;
                    } else {
                        int fromIndex = (currentPage - 1) * pageSize;
                        int totalSize = cryptoList.size();

                        List<TransactionCryptosQueryResponse> subList = fromIndex > totalSize ? List.of() :
                                cryptoList.subList(fromIndex, Math.min(currentPage * pageSize, totalSize));
                        CommonPageVO<TransactionCryptosQueryResponse> commonPageVO = new CommonPageVO<>();
                        commonPageVO.setList(subList);
                        commonPageVO.setCurrentPage(currentPage);
                        commonPageVO.setPageSize(pageSize);
                        commonPageVO.setTotalNum(totalSize);
                        return commonPageVO;
                    }
                });
    }

    @Override
    public Mono<List<UnionPortfolio>> findPortfolios(PortfolioMultiEntity multi, Integer cryptoUnit, boolean isRefresh, Date now) {
        Map<Integer, PlatformNewDTO> platformMap = dexerPlatformCache.getVisibleOnDexScanPlatforms();

        return portfolioWalletDetailManager.getOneDetail(multi, isRefresh)
            .map(Tuple2::getT1)
            .filter(wd -> CollectionUtils.isNotEmpty(wd.getBlockChains()))
            .map(wd -> wd.getBlockChains().stream()
                .flatMap(chainPO -> chainPO.getTokens().stream()
                    .map(token -> UnionPortfolio.builder()
                        .portfolioSourceId(wd.getPortfolioSourceId())
                        .cryptocurrencyId(token.getCryptoId())
                        .name(token.getCryptoName())
                        .symbol(token.getCryptoName())
                        .slug(token.getCryptoName())
                        .contractAddress(token.getContractAddress())
                        .platformId(token.getPlatformId())
                        .platformName(Optional.ofNullable(platformMap.get(token.getPlatformId())).map(PlatformNewDTO::getDn).orElse(null))
                        .chainId(chainPO.getChainId())
                        .chainLogoId(getChainLogoId(chainPO.getChainId()))
                        .chainName(getChainName(chainPO.getChainId()))
                        .chainShortName(getChainShortName(chainPO.getChainId()))
                        .balance(token.getAmount())
                        .value(token.getValue())
                        .createTime(chainPO.getSyncTime())
                        .build())).collect(Collectors.toList()))
            .defaultIfEmpty(List.of());
    }

    private PortfolioDTO buildPortfolioDTO(PortfolioMultiEntity multi, Map<String, BigDecimal> amountMap, PortfolioCryptoInfoDTO cryptoInfoDTO) {
        BigDecimal price = cryptoInfoDTO.transferPrice();
        BigDecimal amount = amountMap.getOrDefault(multi.getPortfolioSourceId(), BigDecimal.ZERO);
        BigDecimal yesterdayChangePercent = cryptoInfoDTO.transferChangePerCent();
        BigDecimal cryptoHoldings = amount.multiply(price, MathContext.DECIMAL128);
        BigDecimal yesterdayChange = BigDecimal.ONE.add(yesterdayChangePercent)
                .compareTo(BigDecimal.ZERO) != 0 ? cryptoHoldings.divide(BigDecimal.ONE.add(yesterdayChangePercent), 5, RoundingMode.HALF_EVEN)
                .stripTrailingZeros() : BigDecimal.ZERO;
        BigDecimal yesterdayValue = cryptoHoldings.subtract(yesterdayChange);

        return PortfolioDTO.builder()
                .userId(multi.getUserId().toString())
                .portfolioSourceId(multi.getPortfolioSourceId())
                .multiId(multi.getId().toString())
                .portfolioName(multi.getPortfolioName())
                .bgColor(multi.getBgColor())
                .sortIndex(multi.getSortIndex())
                .ownWallet(getOwnWallet(multi.getOwnWallet()))
                .isMain(multi.getIsMain())
                .timeCreated(multi.getTimeCreated())
                .timeUpdated(multi.getTimeUpdated())
                .portfolioType(getPortfolioType(multi.getPortfolioType()))
                .portfolioAvatar(multi.getPortfolioAvatar())
                .walletAddress(multi.getWalletAddress())
                .amount(amount)
                .value(price.multiply(amount, MathContext.DECIMAL128))
                .yesterdayValue(yesterdayValue)
                .build();
    }

    private boolean getOwnWallet(Boolean ownWallet) {
        return Optional.ofNullable(ownWallet).orElse(true);
    }

    private String getPortfolioType(String portfolioType) {
        PortfolioTypeEnum typeEnum = PortfolioTypeEnum.findByCode(portfolioType);
        return Optional.ofNullable(typeEnum).orElse(PortfolioTypeEnum.MANUAL).getCode();
    }

    private BigDecimal getWalletTokenAmount(List<BlockChainPO> chainList, Integer targetId) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(chainList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = BigDecimal.ZERO;
        for (BlockChainPO chain : chainList) {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(chain.getTokens())) {
                continue;
            }
            BigDecimal sumAmount = chain.getTokens()
                    .stream()
                    .filter(e -> targetId.equals(e.getCryptoId()))
                    .map(BlockChainTokenPO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            result = result.add(sumAmount);
        }
        return result;

    }

    private Mono<PortfolioTransactionResponse> transactionResponse(Boolean jobFlag, List<PortfolioSupportDTO> cryptoList,
                                                                   List<TransactionDTO> dateList, List<PortfolioSupportChainDTO> supportChainList, PortfolioTransactionRequest request) {

        PortfolioTransactionResponse response = new PortfolioTransactionResponse();
        response.setJobFlag(jobFlag);
        response.setDataFlag(CollectionUtils.isEmpty(dateList) ? PortfolioWalletDataFlagEnum.SYNC.getStatus() : PortfolioWalletDataFlagEnum.SYNC_SUCCESS.getStatus());
        response.setSupportCryptos(cryptoList);
        response.setSupportChainList(CollectionUtils.isNotEmpty(supportChainList) ? supportChainList : null);
        response.setHasUnsupportedChain(hasUnsupportedChain(supportChainList));
        if (CollectionUtils.isEmpty(dateList)) {
            response.setTotalNum(0);
        } else {
            int currentPage = request.getCurrentPage();
            int pageSize = request.getPageSize();
            int size = dateList.size();
            response.setList(dateList.subList(Math.min((currentPage - 1) * pageSize, size), Math.min(currentPage * pageSize, size)));
            response.setCurrentPage(currentPage);
            response.setPageSize(pageSize);
            response.setTotalNum(size);
        }
        return Mono.just(response);
    }

    /**
     * true: There is transaction an unsupported chain (no need to care if the asset support is not supported)
     */
    private Boolean hasUnsupportedChain(List<PortfolioSupportChainDTO> supportChainList){
        if(CollectionUtils.isNotEmpty(supportChainList)){
            List<PortfolioSupportChainDTO> tempList = supportChainList.stream()
                .filter(p -> p.getSupportTransaction() != null && !p.getSupportTransaction())
                .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(tempList);
        }

        return Boolean.FALSE;
    }

    private Mono<Tuple3<List<PortfolioSupportDTO>, List<TransactionDTO>, List<PortfolioSupportChainDTO>>> filterAndSortTransaction(
            List<ChainTransactionPO> chainTransactions, PortfolioTransactionRequest request, PortfolioMultiEntity multi) {
        //过滤chain
        List<ChainTransactionPO> filterChains = chainTransactions.stream()
                .filter(c -> CollectionUtils.isNotEmpty(c.getTokenTransactions()))
                .collect(Collectors.toList());
        List<ChainTransactionTokenPO> transactionTokenPOS = filterChains.stream()
                .flatMap(c -> c.getTokenTransactions().stream())
                .collect(Collectors.toList());
        Set<Integer> allTransCryptoIds = transactionTokenPOS.stream()
                .map(ChainTransactionTokenPO::getCryptoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Mono<Map<Integer, PortfolioCryptoInfoDTO>> queryCurrentInfos = currencyPriceService.queryCurrentInfos(allTransCryptoIds);
        Map<Integer, PlatformNewDTO> platformMap = dexerPlatformCache.getVisibleOnDexScanPlatforms();

        Set<String[]> platformAndAddress = chainTransactions.stream()
            .filter(c -> c.getChainId() != null && platformMap.containsKey(c.getChainId()))
            .flatMap(c -> c.getTokenTransactions()
                .stream()
                .filter(t -> StringUtils.isNotEmpty(t.getTokenAddress()))
                .map(t -> new String[] {platformMap.get(c.getChainId()).getDn(), t.getTokenAddress()}))
            .collect(Collectors.toSet());
        Mono<Map<String, TokenPriceDTO>> tokenMapMono = dexTokenCache.getAll(platformAndAddress);
        return Mono.zip(queryCurrentInfos, tokenMapMono)
                .map(tuples -> {
                    Map<Integer, PortfolioCryptoInfoDTO> allTransCryptoMap = tuples.getT1();
                    Map<String, TokenPriceDTO> tokenMap = tuples.getT2();
                    Set<PortfolioSupportDTO> portfolioSupportDTOSet = new HashSet<>();
                    Set<Integer> supportChainIds = new HashSet<>();
                    PortfolioCryptoVerificationStatus cryptoVerificationStatus = Optional.ofNullable(request.getCryptoVerificationStatus())
                            .map(PortfolioCryptoVerificationStatus::getByCode)
                            .orElse(PortfolioCryptoVerificationStatus.VERIFIED);
                    PortfolioTokenTypeEnum walletTokenType = PortfolioCryptoVerificationStatus.toTokenType(cryptoVerificationStatus, null);

                    List<TransactionDTO> resultReturn = filterChains.stream()
                            .flatMap(chainPO -> {
                                Integer chainId = chainPO.getChainId();
                                List<ChainTransactionTokenPO> tokenTransactions = chainPO.getTokenTransactions();
                                if (CollectionUtils.isEmpty(tokenTransactions)) {
                                    return Stream.of();
                                }
                                String chainNativeTokenSymbol = getChainNativeTokenSymbol(chainId);
                                PlatformNewDTO platformDTO = platformMap.get(chainId);
                                List<TransactionDTO> transactionDTOs = tokenTransactions.stream()
                                        .filter(e -> StringUtils.isNotBlank(e.getTransactionTime()))
                                        .filter(e -> e.getCryptoId() != null || e.getPairId() != null)
                                        .filter(e -> e.getTransactionAddress() != null)
                                        .map(transactionPO -> convertTransactionDTO(chainId, chainNativeTokenSymbol, transactionPO, multi, allTransCryptoMap, tokenMap, platformDTO))
                                        .filter(dto -> walletTokenType == null || walletTokenType.getCode().equals(dto.getCryptoType()))
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(transactionDTOs)) {
                                    supportChainIds.add(chainId);
                                    portfolioSupportDTOSet.addAll(extractSupportCryptoInfo(transactionDTOs));
                                }
                                return transactionDTOs
                                        .stream()
                                        .filter(dto -> filterByRequest(dto, request));
                            })
                            .sorted(Comparator.comparingLong(TransactionDTO::getTransactionTime).reversed())
                            .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(resultReturn)) {
                        return Tuples.of(List.of(), List.of(), List.of());
                    }
                    List<PortfolioSupportDTO> portfolioSupportDTOS = new ArrayList<>(portfolioSupportDTOSet);
                    List<PortfolioSupportChainDTO> supportChainDTOS = buildSupportChains(request, supportChainIds);
                    return Tuples.of(portfolioSupportDTOS, resultReturn, supportChainDTOS);
                });
    }

    private static boolean filterByRequest(TransactionDTO dto, PortfolioTransactionRequest request) {
        Integer requestChainId = request.getChainId();
        List<Integer> cryptoIds = request.getCryptoIds();
        List<String> transactionTypes = request.getTransactionTypes();
        if (requestChainId != null && !dto.getChainId().equals(requestChainId)) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(cryptoIds) && !cryptoIds.contains(dto.getCryptoId())) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(transactionTypes) && !transactionTypes.contains(dto.getTransactionType())) {
            return false;
        }
        return true;
    }

    private List<PortfolioSupportDTO> extractSupportCryptoInfo(List<TransactionDTO> tokens) {
        return tokens.stream()
                .filter(trans -> trans.getCryptoId() != null && StringUtils.isNotBlank(trans.getCryptoName()) && StringUtils.isNotBlank(trans.getCryptoSymbol()))
                .distinct()
                .map(token -> PortfolioSupportDTO.builder()
                        .id(token.getCryptoId())
                        .name(token.getCryptoName())
                        .symbol(token.getCryptoSymbol())
                        .build())
                .collect(Collectors.toList());
    }

    private List<PortfolioSupportChainDTO> buildSupportChains(PortfolioTransactionRequest request, Set<Integer> supportChainIds) {
        if (request.getCurrentPage() != null && request.getCurrentPage() == 1 && CollectionUtils.isNotEmpty(supportChainIds)) {
            return supportChainIds.stream()
                    .filter(Objects::nonNull)
                    .map(chainId -> {
                        PortfolioOkLinkMapVO chainVo = dynamicApolloRefreshConfig.getByChainId(chainId);
                        return PortfolioSupportChainDTO.builder()
                                .chainId(chainVo.getChainId())
                                .chainName(chainVo.getChainName())
                                .chainShortName(chainVo.getChainShortName())
                                .chainLogoId(chainVo.getChainLogoId())
                                .supportTransaction(chainVo.getSupportTransaction())
                                .build();
                    }).collect(Collectors.toList());
        }
        return List.of();
    }

    private String getHashUrl(Integer chainId, String txId) {
        PortfolioOkTransactionMapVO mapVO = dynamicApolloRefreshConfig.getTransActionMap().get(chainId);
        if (mapVO == null) {
            return null;
        }
        return String.format(mapVO.getHashUrl(), txId);
    }

    private String getChainName(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getChainName();
    }

    private String getChainShortName(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getChainShortName();
    }

    private Integer getChainLogoId(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getChainLogoId();
    }

    private String getChainNativeTokenSymbol(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getNativeTokenSymbol();
    }

    private TransactionDTO convertTransactionDTO(Integer chainId, String chainNativeTokenSymbol,
        ChainTransactionTokenPO source, PortfolioMultiEntity multi, Map<Integer, PortfolioCryptoInfoDTO> allTransCryptoMap,
        Map<String, TokenPriceDTO> tokenMap, PlatformNewDTO platformDTO) {
        Map<Integer, OkLinkMapConfigItem> okLinkOkxMapConfig = dynamicApolloRefreshConfig.getOkLinkOkxMapConfig();
        TransactionDTO target = new TransactionDTO();
        String transactionType = Optional.ofNullable(TransactionTypeEnum.findByCode(source.getTransactionType()))
                .map(TransactionTypeEnum::getDesc)
                .orElse(source.getTransactionType());
        target.setTransactionType(transactionType);
        Integer cryptoId = source.getCryptoId();
        target.setId(source.getTxId());
        target.setCryptoId(cryptoId);
        target.setCryptoName(source.getCryptoName());
        target.setCryptoSymbol(source.getCryptoSymbol());
        target.setCryptoSlug(source.getCryptoSlug());
        target.setChainId(chainId);
        target.setChainName(getChainShortName(chainId));

        String tokenCacheKey = null;
        if (platformDTO != null) {
            String txHashFormat = platformDTO.getTxuf();
            if (StringUtils.isNotBlank(txHashFormat)) {
                String hashUrl = String.format(txHashFormat, source.getTxId());
                target.setHashUrl(hashUrl);
            }
            if (StringUtils.isNotEmpty(source.getTokenAddress())) {
                tokenCacheKey = DexTokenCache.buildKey(platformDTO.getDn(), source.getTokenAddress());
            }
        }
        PortfolioCryptoInfoDTO portfolioCryptoInfoDTO = allTransCryptoMap.get(cryptoId);
        if (portfolioCryptoInfoDTO != null && portfolioCryptoInfoDTO.isActive()) {
            if (portfolioCryptoInfoDTO.isActive()) {
                target.setCryptoType(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode());
                target.setPrice(portfolioCryptoInfoDTO.transferPrice());
            } else {
                target.setCryptoType(PortfolioTokenTypeEnum.UNTRACKED_TOKEN.getCode());
            }
            target.setCryptoName(portfolioCryptoInfoDTO.getName());
            target.setCryptoSymbol(portfolioCryptoInfoDTO.getSymbol());
            target.setCryptoSlug(portfolioCryptoInfoDTO.getSlug());
        } else if (tokenCacheKey != null && tokenMap.containsKey(tokenCacheKey)) {
            target.setCryptoType(PortfolioTokenTypeEnum.DEX_TOKEN.getCode());
            TokenPriceDTO tokenPriceDTO = tokenMap.get(tokenCacheKey);
            target.setContractAddress(tokenPriceDTO.getAddress());
            target.setPlatformId(tokenPriceDTO.getPlatformId());

            target.setCryptoSymbol(tokenPriceDTO.getSymbol());
            target.setCryptoName(tokenPriceDTO.getName());
            target.setCryptoSlug(tokenPriceDTO.getName());
            target.setCryptoLogoUrl(tokenPriceDTO.getLogoUrl());

            target.setPrice(tokenPriceDTO.getPrice());
        }
        if (TransactionTypeEnum.findByCode(source.getTransactionType()) == TransactionTypeEnum.IN) {
            target.setFromAddressList(List.of(source.getTransactionAddress()));
            target.setToAddressList(List.of(multi.getWalletAddress()));
        } else {
            target.setFromAddressList(List.of(multi.getWalletAddress()));
            target.setToAddressList(List.of(source.getTransactionAddress()));
        }
        target.setFeeCryptoSymbol(chainNativeTokenSymbol);
        target.setAmount(source.getAmount());
        target.setFee(source.getTxFee());
        if (okLinkOkxMapConfig.containsKey(chainId)) {
            target.setTransactionTime(Long.parseLong(source.getTransactionTime()));
        } else {
            target.setTransactionTime(Long.parseLong(source.getTransactionTime()) * 1000);
        }
        target.setTransactionHash(source.getTxId());
        target.setBlockHeight(source.getHeight());
        target.setStatus(source.getState());
        target.setPortfolioSourceId(multi.getId().toString());
        target.setPortfolioSourceName(multi.getPortfolioName());
        target.setPortfolioType(multi.getPortfolioType());
        target.setPortfolioAvatar(multi.getPortfolioAvatar());
        target.setPortfolioBgColor(multi.getBgColor());
        return target;
    }

    private Mono<PortfolioWalletTransactionPO> getWalletTransaction(PortfolioMultiEntity multi) {
        String walletAddress = multi.getWalletAddress();
        Supplier<Mono<String>> queryWalletMono = () -> walletTransactionService.getByAddress(walletAddress, true)
            .filter(transactionEntity -> StringUtils.isNotBlank(transactionEntity.getWalletAddress()))
            .map(transactionEntity -> {
                PortfolioWalletTransactionPO po = PortfolioWalletTransactionPO.builder()
                    .userId(multi.getUserId())
                    .portfolioSourceId(multi.getPortfolioSourceId())
                    .chainTransactions(transactionEntity.getChainTransactions())
                    .build();

                return JacksonUtils.toJsonString(po);
            });

        String redisKey = String.format(RedisConstants.KEY_PORTFOLIO_WALLET_TRANSACTION_ADDRESS, walletAddress);
        return assetCacheRepository.wrapperCache(redisKey, queryWalletMono, Duration.ofMinutes(cacheTime))
            .map(s -> JacksonUtils.getInstance().deserialize(s, PortfolioWalletTransactionPO.class))
            .onErrorResume(throwable -> {
                log.error("getWalletTransaction failed walletAddress:{}", walletAddress, throwable);
                return Mono.error(throwable);
            });
    }

    private Flux<PortfolioHistoricalChartDTO> fillPrice(int cryptoId, List<PortfolioTotalDTO> totalList) {
        List<Long> timeCodes =
                totalList.stream()
                        .map(PortfolioTotalDTO::getTimestamp)
                        .map(Date::getTime)
                        .map(time -> time / 1000L)
                        .collect(Collectors.toList());
        return Flux.just(cryptoId)
                .flatMap(e -> currencyPriceService.loadPrices(cryptoId, timeCodes, "wallet historicalChart fillPrice")
                        .map(priceMap -> {
                            BigDecimal currentPrice = priceMap.isEmpty() ? BigDecimal.ONE : priceMap.lastEntry()
                                    .getValue();
                            PortfolioHistoricalChartDTO chartDTO =
                                    PortfolioHistoricalChartDTO.builder()
                                            .cryptoId(cryptoId)
                                            .unitPrice(currentPrice)
                                            .build();
                            if (priceMap.isEmpty()) {
                                chartDTO.setTotalList(totalList);
                                return chartDTO;
                            }
                            List<PortfolioTotalDTO> tempList = new ArrayList<>();
                            for (PortfolioTotalDTO portfolioTotalDTO : totalList) {
                                BigDecimal unitPrice =
                                        currencyPriceService.getPrice(priceMap, portfolioTotalDTO.getTimestamp());
                                if (unitPrice.equals(BigDecimal.ONE)) {
                                    tempList.add(portfolioTotalDTO);
                                } else if (unitPrice.equals(BigDecimal.ZERO)) {
                                    tempList.add(PortfolioTotalDTO.builder().value(BigDecimal.ZERO)
                                            .timestamp(portfolioTotalDTO.getTimestamp()).build());
                                } else {
                                    tempList.add(PortfolioTotalDTO.builder()
                                            .value(
                                                    portfolioTotalDTO.getValue()
                                                            .divide(unitPrice, 18, RoundingMode.HALF_UP)
                                                            .stripTrailingZeros())
                                            .timestamp(portfolioTotalDTO.getTimestamp())
                                            .build());
                                }
                            }
                            chartDTO.setTotalList(tempList);
                            return chartDTO;
                        }));
    }

    /**
     * 将链上的所有token按照token标识(cryptoId/合约地址)分组,amount与value进行累加
     */
    private Mono<List<BlockChainTokenPO>> getTokensGroupByTokenId(List<BlockChainPO> blockChains) {

        HashMap<String, BlockChainTokenPO> tokenMap = new HashMap<>(128);
        for (BlockChainPO blockChainPo : blockChains) {
            if (CollectionUtils.isEmpty(blockChainPo.getTokens())) {
                continue;
            }
            for (BlockChainTokenPO blockChainTokenPo : blockChainPo.getTokens()) {
                String hashKey;
                if (blockChainTokenPo.getCanMap()) {
                    hashKey = blockChainTokenPo.getCryptoId().toString();
                } else {
                    hashKey = blockChainTokenPo.getContractAddress();
                }
                tokenMap.put(hashKey, distinctAndIncreaseAmount(tokenMap.get(hashKey), blockChainTokenPo));
            }
        }
        return Mono.just(new ArrayList<>(tokenMap.values()));
    }

    private BlockChainTokenPO distinctAndIncreaseAmount(BlockChainTokenPO hashValue,
                                                        BlockChainTokenPO blockChainToken) {
        if (hashValue == null) {
            return blockChainToken;
        }
        BigDecimal tokenAmount = (blockChainToken.getAmount() == null || blockChainToken.getAmount()
                .compareTo(BigDecimal.ZERO) <= 0) ?
                BigDecimal.ZERO : blockChainToken.getAmount();
        hashValue.setAmount(hashValue.getAmount().add(tokenAmount));
        if (!hashValue.getCanMap()) {
            BigDecimal tokenValue = (blockChainToken.getValue() == null || blockChainToken.getValue()
                    .compareTo(BigDecimal.ZERO) <= 0) ?
                    BigDecimal.ZERO : blockChainToken.getValue();
            hashValue.setValue(hashValue.getValue().add(tokenValue));
        }
        return hashValue;
    }

    /**
     * 组装链上数据并汇总
     *
     * @param historicalMap map key为原生代币 value为数量
     * @param queryDTO      query
     * @return list
     */
    private Mono<List<PortfolioTotalDTO>> queryPortfolioTotalList(Map<Integer, Tuple2<Date, BigDecimal>> historicalMap,
                                                                  PortfolioHistoricalChartQueryDTO queryDTO) {
        PortfolioHistoricalDayEnum dayEnum = PortfolioHistoricalDayEnum.findByCode(queryDTO.getDays());
        if (dayEnum == null) {
            return Mono.just(List.of());
        }
        //根据query中的days参数,得到数据point列表
        List<Long> timeCode;
        if (PortfolioHistoricalDayEnum.CHART_ALL == dayEnum) {
            Long daysAgo = com.cmc.data.common.utils.DatetimeUtils.truncateTo5MinuteForwardWithSeconds(DatetimeUtils.getDaysAgo(DatetimeUtils.now(), 90)
                    .getTime() / 1000L);
            Long beginDay = historicalMap.values()
                    .stream()
                    .map(Tuple2::getT1)
                    .min(Date::compareTo)
                    .map(date -> date.getTime() / 1000L)
                    .map(time -> Math.min(time, daysAgo))
                    .orElse(daysAgo);
            timeCode = portfolioLineChartUtil.timePointByIntervalType(beginDay);
        } else {
            timeCode = portfolioLineChartUtil.timePointByIntervalType(dayEnum.getCode());
        }
        return portfolioLineChartUtil.getHistoryPointsForWallet(timeCode, historicalMap);
    }

    /**
     * 查询wallet所在链上的金额
     *
     * @param walletDetailPO
     * @param cryptoVerificationStatus
     * @return address对应多条链, map中key为每个链上原生代币, value为原生代币金额
     */
    private Mono<Map<Integer, Tuple2<Date, BigDecimal>>> findWalletHistory(PortfolioWalletDetailPO walletDetailPO, Integer chainId, Integer cryptoVerificationStatus) {
        PortfolioCryptoVerificationStatus statusEnum = PortfolioCryptoVerificationStatus.getByCode(cryptoVerificationStatus);
        PortfolioTokenTypeEnum walletTokenType = PortfolioCryptoVerificationStatus.toTokenType(statusEnum, null);

        List<BlockChainPO> finalChains;
        if (chainId != null) {
            finalChains = walletDetailPO.getBlockChains()
                    .stream()
                    .filter(e1 -> e1.getChainId().equals(chainId))
                    .collect(
                            Collectors.toList());
        } else {
            finalChains = walletDetailPO.getBlockChains();
        }
        Map<Integer, Tuple2<Date, BigDecimal>> cryptoMap = new TreeMap<>();
        for (BlockChainPO blockChain : finalChains) {
            BigDecimal totalTokenAmount = walletTokenType == PortfolioTokenTypeEnum.LISTED_TOKEN ?
                    MathUtils.getOrZero(blockChain.getTotalVerifiedTokenAmount()) : blockChain.getTotalTokenAmount();
            if (totalTokenAmount.compareTo(BigDecimal.ZERO) < 0 || blockChain.getNativeTokenId() == null) {
                continue;
            }
            BigDecimal amount = cryptoMap.get(blockChain.getNativeTokenId()) != null ?
                    cryptoMap.get(blockChain.getNativeTokenId())
                            .getT2()
                            .add(totalTokenAmount) :
                    totalTokenAmount;
            cryptoMap.put(blockChain.getNativeTokenId(), Tuples.of(walletDetailPO.getTimeCreated(), amount));
        }
        if (cryptoMap.isEmpty()) {
            return Mono.empty();
        }
        return Mono.just(cryptoMap);
    }

}
