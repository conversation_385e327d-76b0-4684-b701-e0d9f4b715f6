package com.cmc.asset.business.portfolio.manager;

import static com.cmc.asset.model.common.Constant.DEFAULT_SCALE;

import com.cmc.asset.business.portfolio.WalletAssetService;
import com.cmc.asset.business.portfolio.impl.CurrencyPriceService;
import com.cmc.asset.business.portfolio.impl.PortfolioBaseService;
import com.cmc.asset.cache.DexTokenCache;
import com.cmc.asset.cache.DexerPlatformCache;
import com.cmc.asset.config.DynamicApolloRefreshConfig;
import com.cmc.asset.dao.entity.WalletAssetEntity;
import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.BlockChainTokenPO;
import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioWalletDetailPO;
import com.cmc.asset.dao.repository.mongo.PortfolioWalletDetailRepository;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.cmc.asset.model.contract.portfolio.WalletSummaryChainDTO;
import com.cmc.asset.model.contract.portfolio.WalletSummaryDTO;
import com.cmc.asset.model.enums.PortfolioTokenTypeEnum;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.MathUtils;
import com.cmc.framework.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * PortfolioWalletDetailManager
 *
 * <AUTHOR> ricky.x
 * @date : 2023/5/7 下午8:08
 */
@Slf4j
@Service
public class PortfolioWalletDetailManager {

    @Resource
    private PortfolioWalletDetailRepository portfolioWalletDetailRepository;
    @Resource
    private CurrencyPriceService currencyPriceService;
    @Resource
    private DexTokenCache dexTokenCache;
    @Autowired
    private DexerPlatformCache dexerPlatformCache;
    @Resource
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;
    @Autowired
    @Qualifier("assetRedisTemplate")
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;
    @Autowired
    private WalletAssetService walletAssetService;
    @Autowired
    private PortfolioBaseService portfolioBaseService;

    @Value("${com.cmc.asset.portfolio.wallet-token-value-limit:100000}")
    private String walletTokenValueLtLimit;
    @Value("${com.cmc.asset.portfolio.wallet-token-volume-limit:100}")
    private String walletTokenVolume24hGtLimit;
    @Value("${com.cmc.asset.portfolio.dashboard.inactive-coin-name:}")
    private String inactiveCoinName;
    @Value("${com.cmc.asset.portfolio.dashboard.inactive-coin-symbol:}")
    private String inactiveCoinSymbol;
    @Value("${com.cmc.asset.portfolio.dashboard.inactive-coin-slug:}")
    private String inactiveCoinSlug;

    public Mono<Tuple2<PortfolioWalletDetailPO, List<WalletSummaryDTO>>> getOneDetail(PortfolioMultiEntity multiEntity, boolean refresh) {
        ObjectId userId = multiEntity.getUserId();
        String sourceId = multiEntity.getPortfolioSourceId();
        String walletAddress = multiEntity.getWalletAddress();
        return walletAssetService.getWalletDetailPOByAddress(walletAddress, userId, sourceId, refresh)
            .flatMap(walletDetailPO -> {
                //从mongo中获取保存的三方链上数据,进行分组汇总与计算
                List<BlockChainPO> copied = deepCopyList(walletDetailPO);
                return groupTokensByBlockChains(copied)
                    .flatMap(tuple2Map -> {
                        return calculationWalletTokenBalance(tuple2Map)
                            .doOnNext(walletSummaryDTOS -> {
                                if (refresh) {
                                    Mono<PortfolioWalletDetailPO> refreshMono = this.refreshValue(walletDetailPO, walletSummaryDTOS, walletAddress);
                                    portfolioBaseService.refreshValue(userId.toHexString(), sourceId, walletDetailPO, () -> refreshMono, new TypeReference<>() {}).subscribe();
                                }
                            });
                    })
                    .map(walletSummaries -> Tuples.of(walletDetailPO, walletSummaries));
            });
    }

    private Mono<PortfolioWalletDetailPO> refreshValue(PortfolioWalletDetailPO walletDetailPO, List<WalletSummaryDTO> walletSummaries, String walletAddress) {
        if (CollectionUtils.isEmpty(walletDetailPO.getBlockChains())) {
            return Mono.just(walletDetailPO);
        }
        Map<Integer, BigDecimal> chainTokenValues = new HashMap<>();
        Map<Integer, BigDecimal> chainVerifiedTokenValues = new HashMap<>();
        Map<String, BigDecimal> tokenValues = new HashMap<>();
        for (WalletSummaryDTO walletSummaryDTO : walletSummaries) {
            if (walletSummaryDTO.getChainDetails() != null) {
                for (WalletSummaryChainDTO chainDetail : walletSummaryDTO.getChainDetails()) {
                    Integer chainId = chainDetail.getChainId();
                    BigDecimal value = chainDetail.getValue();
                    if (chainId == null || value == null) {
                        continue;
                    }
                    chainTokenValues.merge(chainId, value, BigDecimal::add);
                    if (PortfolioTokenTypeEnum.LISTED_TOKEN.getCode().equals(walletSummaryDTO.getType())) {
                        chainVerifiedTokenValues.merge(chainId, value, BigDecimal::add);
                    }
                    tokenValues.put(buildTokenKey(chainId, walletSummaryDTO.getCryptocurrencyId(), walletSummaryDTO.getContractAddress()), value);
                }
            }
        }
        List<Integer> nativeTokenIds = walletDetailPO.getBlockChains()
            .stream()
            .filter(d -> d.getNativeTokenId() != null)
            .map(BlockChainPO::getNativeTokenId)
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(nativeTokenIds)){
            return Mono.just(walletDetailPO);
        }
        return currencyPriceService.queryCurrentPrices(nativeTokenIds)
            .flatMap(priceMap -> {
                for (BlockChainPO blockChainPO : walletDetailPO.getBlockChains()) {
                    BigDecimal totalValue = chainTokenValues.getOrDefault(blockChainPO.getChainId(), BigDecimal.ZERO);
                    BigDecimal verifiedTotalValue = chainVerifiedTokenValues.getOrDefault(blockChainPO.getChainId(), BigDecimal.ZERO);
                    blockChainPO.setTotalTokenValue(totalValue);
                    blockChainPO.setTotalVerifiedTokenValue(verifiedTotalValue);

                    //除对应的价格获取amount
                    BigDecimal nativeTokenPrice = priceMap.get(blockChainPO.getNativeTokenId()).transferPrice();
                    BigDecimal totalAmount = nativeTokenPrice.compareTo(BigDecimal.ZERO) != 0 ?
                        totalValue.divide(nativeTokenPrice, DEFAULT_SCALE, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;
                    BigDecimal totalVerifiedAmount = nativeTokenPrice.compareTo(BigDecimal.ZERO) != 0 ?
                        verifiedTotalValue.divide(nativeTokenPrice, DEFAULT_SCALE, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;
                    blockChainPO.setTotalTokenAmount(totalAmount);
                    blockChainPO.setTotalVerifiedTokenAmount(totalVerifiedAmount);

                    if (CollectionUtils.isNotEmpty(blockChainPO.getTokens())) {
                        for (BlockChainTokenPO tokenPO : blockChainPO.getTokens()) {
                            BigDecimal value = tokenValues.getOrDefault(buildTokenKey(blockChainPO.getChainId(), tokenPO.getCryptoId(), tokenPO.getContractAddress()), BigDecimal.ZERO);
                            tokenPO.setValue(value);
                        }
                    }
                }

                List<Integer> chainIds = walletDetailPO.getBlockChains().stream()
                    .map(BlockChainPO::getChainId)
                    .collect(Collectors.toList());
                WalletAssetEntity assetEntity = WalletAssetEntity.builder()
                    .walletAddress(walletAddress)
                    .blockChains(walletDetailPO.getBlockChains())
                    .build();
                return walletAssetService.batchInsertOrUpdate(List.of(assetEntity))
                    .publishOn(Schedulers.boundedElastic())
                    .doOnNext(r -> this.cleanCache(walletDetailPO.getUserId().toHexString(), walletDetailPO.getPortfolioSourceId(), chainIds).subscribe());
            }).thenReturn(walletDetailPO);
    }

    @NotNull
    private static List<BlockChainPO> deepCopyList(PortfolioWalletDetailPO wallet) {
        return (CollectionUtils.isEmpty(wallet.getBlockChains()) ? Collections.<BlockChainPO>emptyList() : wallet.getBlockChains())
                .stream()
                .map(chain -> BlockChainPO.builder()
                        .chainId(chain.getChainId())
                        .syncTime(chain.getSyncTime())
                        .nativeTokenId(chain.getNativeTokenId())
                        .totalTokenAmount(chain.getTotalTokenAmount())
                        .totalVerifiedTokenAmount(chain.getTotalVerifiedTokenAmount())
                        .totalTokenValue(chain.getTotalTokenValue())
                        .totalVerifiedTokenValue(chain.getTotalVerifiedTokenValue())
                        .tokens(chain.getTokens() == null ? Collections.emptyList() : chain.getTokens()
                                .stream()
                                .map(token -> BlockChainTokenPO.builder()
                                        .canMap(token.getCanMap())
                                        .type(token.getType())
                                        .cryptoName(token.getCryptoName())
                                        .amount(token.getAmount())
                                        .cryptoId(token.getCryptoId())
                                        .poolId(token.getPoolId())
                                        .platformId(token.getPlatformId())
                                        .pairContractAddress(token.getPairContractAddress())
                                        .contractAddress(token.getContractAddress())
                                        .value(token.getValue())
                                        .changeUsd24h(token.getChangeUsd24h())
                                        .build())
                                .collect(Collectors.toList()))
                        .build())
                .collect(Collectors.toList());
    }

    private static String buildTokenKey(Integer chainId, Integer cryptoId, String contractAddress) {
        String cryptoIdStr = cryptoId != null ? cryptoId.toString() : "";
        String contractAddressStr = contractAddress != null ? contractAddress : "";
        return chainId + "_" + cryptoIdStr + "_" + contractAddressStr;
    }

    private Mono<Long> cleanCache(String userId, String portfolioSourceId, List<Integer> chainIds) {
        Mono<Long> cleanCurrent = assetRedisTemplate.delete(Flux.fromIterable(RedisConstants.getPortfolioCacheKeys(userId, portfolioSourceId)));
        Mono<Long> cleanHistory = assetRedisTemplate.delete(Flux.fromIterable(RedisConstants.getPortfolioHistoryCacheKeys(userId, portfolioSourceId)));
        Mono<Long> cleanChainHistory = assetRedisTemplate.delete(Flux.fromIterable(chainIds)
                .flatMap(chainId -> Flux.fromIterable(RedisConstants.getWalletPortfolioChainHistoryCacheKeys(userId, portfolioSourceId, chainId))));
        return Mono.zip(cleanCurrent, cleanHistory, cleanChainHistory).map(tuples -> tuples.getT1() + tuples.getT2() + tuples.getT3());
    }

    /**
     * 获取用户最新的portfolio wallet记录
     *
     * @param userId            userId
     * @param portfolioSourceId sourceId
     * @return mono
     */
    public Mono<PortfolioWalletDetailPO> getOneDetail(ObjectId userId, String portfolioSourceId) {
        return portfolioWalletDetailRepository.findFirstDetail(userId, portfolioSourceId);
    }

    /**
     * 链上数据按照cryptoName汇总
     *
     * @param blockChains token in okLink
     * @return distinct and increase tokenPO
     */

    private Mono<Tuple2<Map<String, BlockChainTokenPO>, Map<String, List<WalletSummaryChainDTO>>>> groupTokensByBlockChains(List<BlockChainPO> blockChains) {
        //链上可能会出现token与cmc数据映射不上,所以以cryptoName为key
        HashMap<String, BlockChainTokenPO> tokenMap = new HashMap<>();
        HashMap<String, List<WalletSummaryChainDTO>> tokenChainMap = new HashMap<>();
        Map<Integer, PlatformNewDTO> platformMap = dexerPlatformCache.getVisibleOnDexScanPlatforms();

        for (BlockChainPO blockChainPO : blockChains) {
            for (BlockChainTokenPO blockChainTokenPO : blockChainPO.getTokens()) {
                String hashKey;
                if (blockChainTokenPO.getCryptoId() != null) {
                    hashKey = blockChainTokenPO.getCryptoId().toString();
                } else if (blockChainTokenPO.getPlatformId() != null && platformMap.containsKey(blockChainTokenPO.getPlatformId()) && StringUtils.isNotEmpty(blockChainTokenPO.getContractAddress())) {
                    hashKey = platformMap.get(blockChainTokenPO.getPlatformId()).getDn() + "_" + blockChainTokenPO.getContractAddress();
                } else {
                    continue;
                }
                tokenMap.put(hashKey, tokenDistinctAndIncreaseAmount(tokenMap.get(hashKey), blockChainTokenPO));
                tokenChainMap.put(hashKey, chainDistinctAndIncreaseAmount(tokenChainMap.get(hashKey), blockChainPO, blockChainTokenPO));
            }
        }
        return Mono.just(Tuples.of(tokenMap, tokenChainMap));
    }

    private BlockChainTokenPO tokenDistinctAndIncreaseAmount(BlockChainTokenPO hasValue, BlockChainTokenPO blockChainTokenPO) {
        if (hasValue == null) {
            return blockChainTokenPO;
        }
        BigDecimal amount = hasValue.getAmount() != null ? hasValue.getAmount() : BigDecimal.ZERO;
        if(blockChainTokenPO.getAmount() != null && blockChainTokenPO.getAmount().compareTo(BigDecimal.ZERO) > 0){
            amount = amount.add(blockChainTokenPO.getAmount());
        }
        hasValue.setAmount(amount);
        BigDecimal value = hasValue.getValue() != null ? hasValue.getValue() : BigDecimal.ZERO;
        BigDecimal nextValue = blockChainTokenPO.getValue();
        if(Boolean.TRUE.equals(hasValue.getCanMap()) && nextValue != null && nextValue.compareTo(BigDecimal.ZERO) > 0){
            value = value.add(nextValue);
            hasValue.setValue(value);
        }
        return hasValue;
    }

    private List<WalletSummaryChainDTO> chainDistinctAndIncreaseAmount(List<WalletSummaryChainDTO> hasValueList, BlockChainPO blockChain,
                                                                       BlockChainTokenPO blockChainTokenPO) {

        WalletSummaryChainDTO walletChain = WalletSummaryChainDTO.builder().chainId(blockChain.getChainId())
                .chainLogoId(getChainLogoId(blockChain.getChainId())).chainName(getChainName(blockChain.getChainId()))
                .chainShortName(getChainShortName(blockChain.getChainId()))
                .balance(blockChainTokenPO.getAmount()).build();
        if (!blockChainTokenPO.getCanMap()) {
            walletChain.setValue(blockChainTokenPO.getValue());
        }
        if (CollectionUtils.isEmpty(hasValueList)) {
            List<WalletSummaryChainDTO> list = new ArrayList<>();
            list.add(walletChain);
            return list;
        }
        hasValueList.add(walletChain);
        return hasValueList;
    }

    private Mono<List<WalletSummaryDTO>> calculationWalletTokenBalance(Tuple2<Map<String, BlockChainTokenPO>, Map<String, List<WalletSummaryChainDTO>>> tuple2) {
        Map<String, BlockChainTokenPO> tokenMap = tuple2.getT1();
        Map<String, List<WalletSummaryChainDTO>> chainMap = tuple2.getT2();
        List<BlockChainTokenPO> blockChainTokens = new ArrayList<>(tokenMap.values());

        if(CollectionUtils.isEmpty(tokenMap) || CollectionUtils.isEmpty(chainMap)) {
            return Mono.just(List.of());
        }

        List<Integer> idList = blockChainTokens.stream()
                .map(BlockChainTokenPO::getCryptoId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Mono<Map<Integer, PortfolioCryptoInfoDTO>> idPriceMap = currencyPriceService
                .queryCurrentInfos(idList).defaultIfEmpty(Maps.newHashMap());

        Map<Integer, PlatformNewDTO> platformMap = dexerPlatformCache.getVisibleOnDexScanPlatforms();
        Set<String[]> platformAndAddress = blockChainTokens.stream()
            .filter(
                b -> b.getPlatformId() != null && platformMap.containsKey(b.getPlatformId()) && StringUtils.isNotEmpty(
                    b.getContractAddress()))
            .map(b -> new String[] {platformMap.get(b.getPlatformId()).getDn(), b.getContractAddress()})
            .collect(Collectors.toSet());
        Mono<Map<String, TokenPriceDTO>> pairsMono = dexTokenCache.getAll(platformAndAddress);

        Mono<List<WalletSummaryDTO>> calcUnverifiedCrypto = pairsMono.flatMap(
            pairMap -> Flux.fromIterable(platformAndAddress)
                .map(pa -> DexTokenCache.buildKey(pa[0], pa[1]))
                .filter(pa -> tokenMap.containsKey(pa) && pairMap.containsKey(pa))
                .map(pa -> {
                    BlockChainTokenPO blockChainToken = tokenMap.get(pa);
                    TokenPriceDTO tokenPriceDTO = pairMap.get(pa);
                    List<WalletSummaryChainDTO> walletSummaryChainDTOS = chainMap.get(pa);
                    return buildWalletSummary(blockChainToken, tokenPriceDTO, walletSummaryChainDTOS);
                })
                .filter(dto -> dto.getValue() != null && dto.getValue().compareTo(BigDecimal.ONE) >= 0 && dto.getValue()
                                                                                                              .compareTo(
                                                                                                                  new BigDecimal(
                                                                                                                      walletTokenValueLtLimit)) <= 0)
                .collectList()).defaultIfEmpty(List.of());

        Mono<List<WalletSummaryDTO>> calcVerifiedCrypto = idPriceMap.flatMap(idPrice -> Flux.fromIterable(idList)
                .filter(cryptoId -> tokenMap.containsKey(cryptoId.toString()))
                .map(cryptoId -> {
                    BlockChainTokenPO blockChainToken = tokenMap.get(cryptoId.toString());
                    PortfolioCryptoInfoDTO cryptoInfoDTO = idPrice.get(cryptoId);
                    List<WalletSummaryChainDTO> tokenList = chainMap.get(cryptoId.toString());

                    if (cryptoInfoDTO != null && cryptoInfoDTO.isActive()) {
                        return buildWalletSummary(blockChainToken, cryptoInfoDTO, tokenList);
                    } else {
                        WalletSummaryDTO walletSummary = WalletSummaryDTO.builder()
                                .cryptocurrencyId(blockChainToken.getCryptoId())
                                .contractAddress(blockChainToken.getContractAddress())
                                .balance(blockChainToken.getAmount())
                                .build();
                        if (cryptoInfoDTO != null) {
                            walletSummary.setName(cryptoInfoDTO.getName());
                            walletSummary.setSymbol(cryptoInfoDTO.getSymbol());
                            walletSummary.setSlug(cryptoInfoDTO.getSlug());
                        } else {
                            walletSummary.setName(blockChainToken.getCryptoName());
                            walletSummary.setSymbol(inactiveCoinSymbol);
                            walletSummary.setSlug(inactiveCoinSlug);
                        }
                        walletSummary.setType(PortfolioTokenTypeEnum.UNTRACKED_TOKEN.getCode());
                        walletSummary.setValue(null);
                        walletSummary.setCurrentPrice(null);
                        walletSummary.setYesterdayChangePercent(null);
                        walletSummary.setChainDetails(tokenList);
                        return walletSummary;
                    }
                })
                .collectList()).defaultIfEmpty(List.of());

        return Mono.zip(calcVerifiedCrypto, calcUnverifiedCrypto).map(walletSummaryDTOTuple2 -> {
                    List<WalletSummaryDTO> verifiedCrypto = walletSummaryDTOTuple2.getT1();
                    List<WalletSummaryDTO> unverifiedCrypto = walletSummaryDTOTuple2.getT2();
                    return Stream.concat(verifiedCrypto.stream(), unverifiedCrypto.stream())
                            .collect(Collectors.toList());
                })
                .map(list -> {
                    //calculate token value percent
                    BigDecimal totalValue = CollectionUtils.isEmpty(list) ? BigDecimal.ZERO :
                            list.stream()
                                    .map(e -> MathUtils.getOrZero(e.getValue()))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (WalletSummaryDTO walletSummaryDTO : list) {
                        BigDecimal value = walletSummaryDTO.getValue();
                        if (value != null) {
                            walletSummaryDTO.setHoldingsPercent(value.compareTo(BigDecimal.ZERO) == 0
                                    ? BigDecimal.ZERO : value
                                    .divide(totalValue, DEFAULT_SCALE, RoundingMode.HALF_EVEN));
                        }
                    }
                    return list;
                });
    }

    public WalletSummaryDTO buildWalletSummary(BlockChainTokenPO blockChainToken, TokenPriceDTO priceDto,
        List<WalletSummaryChainDTO> tokenList) {
        WalletSummaryDTO walletSummary = WalletSummaryDTO.builder()
            .cryptocurrencyId(blockChainToken.getCryptoId())
            .name(blockChainToken.getCryptoName())
            .symbol(blockChainToken.getCryptoName())
            .contractAddress(blockChainToken.getContractAddress())
            .platformId(blockChainToken.getPlatformId())
            .balance(blockChainToken.getAmount())
            .build();
        BigDecimal price = priceDto.getPrice();
        List<WalletSummaryChainDTO> finalList = tokenList.stream().peek(walletSummaryChain -> {
            if (walletSummaryChain.getValue() == null || walletSummaryChain.getValue().compareTo(BigDecimal.ZERO) == 0) {
                walletSummaryChain.setValue(walletSummaryChain.getBalance().multiply(price));
            }
        }).collect(Collectors.toList());
        walletSummary.setType(PortfolioTokenTypeEnum.DEX_TOKEN.getCode());

        walletSummary.setPlatformId(priceDto.getPlatformId());
        walletSummary.setPlatformName(priceDto.getPlatformDexerName());
        walletSummary.setLogoUrl(priceDto.getLogoUrl());
        walletSummary.setName(priceDto.getName());
        walletSummary.setSymbol(priceDto.getSymbol());
        walletSummary.setSlug(priceDto.getName());

        BigDecimal value =
            finalList.stream().map(WalletSummaryChainDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        walletSummary.setValue(value);
        walletSummary.setCurrentPrice(MathUtils.getOrZero(price));
        if (priceDto.getPriceChange24h() != null) {
            BigDecimal yesterdayChangePercent = priceDto.getPriceChange24h().multiply(new BigDecimal("100"));
            walletSummary.setYesterdayChangePercent(yesterdayChangePercent);
        }
        if (priceDto.getPriceChange1h() != null) {
            walletSummary.setOneHourChangePercent(priceDto.getPriceChange1h().multiply(new BigDecimal("100")));
        }
        if (priceDto.getPriceChange7d() != null) {
            walletSummary.setSevenDaysChangePercent(priceDto.getPriceChange7d().multiply(new BigDecimal("100")));
        }
        walletSummary.setChainDetails(finalList);
        return walletSummary;
    }

    public WalletSummaryDTO buildWalletSummary(BlockChainTokenPO blockChainToken, PortfolioCryptoInfoDTO cryptoInfo, List<WalletSummaryChainDTO> tokenList) {
        WalletSummaryDTO walletSummary = WalletSummaryDTO.builder()
                .cryptocurrencyId(blockChainToken.getCryptoId())
                .name(blockChainToken.getCryptoName())
                .symbol(blockChainToken.getCryptoName())
                .contractAddress(blockChainToken.getContractAddress())
                .balance(blockChainToken.getAmount()).build();
        BigDecimal price = cryptoInfo.transferPrice();
        List<WalletSummaryChainDTO> finalList = tokenList.stream().peek(walletSummaryChain -> {
            if (walletSummaryChain.getValue() == null) {
                walletSummaryChain.setValue(walletSummaryChain.getBalance().multiply(price));
            }
        }).collect(Collectors.toList());
        walletSummary.setType(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode());
        walletSummary.setName(cryptoInfo.getName());
        walletSummary.setSymbol(cryptoInfo.getSymbol());
        walletSummary.setSlug(cryptoInfo.getSlug());
        if (cryptoInfo.isActive()) {
            BigDecimal value = finalList.stream().map(WalletSummaryChainDTO::getValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            walletSummary.setValue(value);
            walletSummary.setLastUpdated(cryptoInfo.getLastUpdated());
            walletSummary.setCurrentPrice(price);
            walletSummary.setYesterdayChangePercent(cryptoInfo.transferChangePerCent());
            walletSummary.setOneHourChangePercent(cryptoInfo.transferOneHourChangePerCent());
            walletSummary.setSevenDaysChangePercent(cryptoInfo.transferSevenDaysChangePerCent());
            walletSummary.setThirtyDaysChangePercent(cryptoInfo.transferThirtyDaysChangePerCent());

            blockChainToken.setValue(value);
        }
        walletSummary.setChainDetails(finalList);
        return walletSummary;
    }

    private String getChainName(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getChainName();
    }

    private String getChainShortName(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getChainShortName();
    }

    private Integer getChainLogoId(Integer chainId) {
        return dynamicApolloRefreshConfig.getChainIdMap().get(chainId).getChainLogoId();
    }

}
