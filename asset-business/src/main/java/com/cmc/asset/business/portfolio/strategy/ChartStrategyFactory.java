package com.cmc.asset.business.portfolio.strategy;

import com.cmc.asset.business.portfolio.IPortfolioChartDataStrategy;
import com.cmc.asset.business.portfolio.IPortfolioChartDataStrategyV2;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/2/25
 */
public class ChartStrategyFactory {
    private static final int ONE_DAY_VALUE = 1;
    private static final int SEVEN_DAY_VALUE = 7;
    private static final int ALL_DAY_VALUE = 0;

    public static IPortfolioChartDataStrategy createStrategy(int days) {
        if (days == ALL_DAY_VALUE) {
            return new AllChartDataStrategy();
        } else if (days == ONE_DAY_VALUE) {
            return new OneDayChartDataStrategy();
        } else if (days == SEVEN_DAY_VALUE) {
            return new SevenDaysChartDataStrategy();
        } else {
            return new OverSevenDaysChartDataStrategy();
        }
    }

    public static IPortfolioChartDataStrategyV2 createStrategyV2(int days) {
        if (days == ALL_DAY_VALUE) {
            return new AllChartDataStrategyV2();
        } else if (days == ONE_DAY_VALUE) {
            return new OneDayChartDataStrategyV2();
        } else if (days == SEVEN_DAY_VALUE) {
            return new SevenDaysChartDataStrategyV2();
        } else {
            return new OverSevenDaysChartDataStrategyV2();
        }
    }
}
