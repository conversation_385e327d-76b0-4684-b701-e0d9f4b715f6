package com.cmc.asset.business.anti;

import com.cmc.framework.captcha.entity.ValidateVO;
import com.cmc.framework.common.model.ApiResponse;
import org.springframework.http.server.reactive.ServerHttpRequest;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/9/14
 */
public interface AntiBotService {

    /**
     * security check
     * @param request real request
     * @return validateVO result
     */
    Mono<ApiResponse<ValidateVO>> securityCheck(ServerHttpRequest request, String validType, String bizId);
}
