package com.cmc.asset.business.portfolio.impl;

import lombok.extern.slf4j.Slf4j;
import reactor.kafka.sender.KafkaSender;

import com.cmc.asset.business.BaseService;
import com.cmc.asset.message.SenderMessage;
import com.cmc.asset.model.contract.portfolio.PortfolioLogDto;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PortfolioLogService extends BaseService {

    @Resource(name = "portfolioProducer")
    private KafkaSender<String,String> kafkaSender;

    @Value("${cmc.asset.kafka.producer.portfolioLog.topic}")
    private String topic;

    public void sendMessage(PortfolioLogDto portfolioLogDto) {
        SenderMessage message =
            SenderMessage.builder()
                .topic(topic)
                .userId(portfolioLogDto.getUserId())
                .data(portfolioLogDto)
                .build();
        sendMQ(message, kafkaSender);
    }
}
