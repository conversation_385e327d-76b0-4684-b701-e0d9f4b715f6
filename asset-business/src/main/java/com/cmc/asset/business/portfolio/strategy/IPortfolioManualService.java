package com.cmc.asset.business.portfolio.strategy;

import com.cmc.asset.model.contract.portfolio.PortfolioCumulativeChangeRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioCumulativeChangeResponse;
import reactor.core.publisher.Mono;

/**
 * cumulativeChange
 * <AUTHOR> ricky.x
 * @date : 2023/4/13 下午4:48
 */
public interface IPortfolioManualService {

    /**
     * 查询manual类型portfolio的change point与btc的kline point
     * @param request request
     * @return result
     */
    Mono<PortfolioCumulativeChangeResponse> cumulativeChange(PortfolioCumulativeChangeRequest request);
}
