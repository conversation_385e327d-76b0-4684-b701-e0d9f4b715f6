package com.cmc.asset.business.usertradealert;

import com.cmc.asset.model.contract.usertradealert.UserTradeAlertParamDTO;
import com.cmc.asset.model.contract.usertradealert.UserTradeAlertResultDTO;

import java.util.List;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/12/29 下午3:10
 * @description
 */
public interface UserTradeAlertService {
    /**
     * add user trade alert
     * @param params
     * @return
     */
    Mono<Boolean> addUserTradeAlert(UserTradeAlertParamDTO params);

    /**
     * delete user trade alert
     * @param params
     * @return
     */
    Mono<Boolean> deleteUserTradeAlert(UserTradeAlertParamDTO params);

    /**
     * query user trade alert list
     * @param params
     * @return
     */
    Mono<List<UserTradeAlertResultDTO>> queryUserTradeAlertList(UserTradeAlertParamDTO params);
}
