package com.cmc.asset.business.portfolio.strategy;

import com.cmc.asset.business.portfolio.IPortfolioChartDataStrategyV2;
import com.cmc.asset.business.portfolio.PortfolioUtils;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.NavigableMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/2/24
 */
public class SevenDaysChartDataStrategyV2 implements IPortfolioChartDataStrategyV2 {
    private static final int ONE_HOUR_MILLISECONDS = 60 * 60 * 1000;

    @Override
    public List<PortfolioTotalDTO> getChartData(int days, NavigableMap<Date, NavigableMap<Date, BigDecimal>> mapByDay) {
        Date today = PortfolioUtils.getStartOfDay(ExtUtils.now());
        Date startDay = PortfolioUtils.getBefore(today, days);
        List<PortfolioTotalDTO> totalList = new ArrayList<>();

        return mapByDay.subMap(startDay, true, today, true).values()
            .stream()
            .flatMap(map -> map.entrySet().stream()
                .filter(e->e.getKey().getTime() % ONE_HOUR_MILLISECONDS == 0)
                .map(e-> PortfolioTotalDTO.builder().timestamp(e.getKey()).value(e.getValue()).build()))
            .collect(Collectors.toList());
    }

    @Override
    public NavigableMap<Date, BigDecimal> subHoldingMap(int days,
        NavigableMap<Date, NavigableMap<Date, BigDecimal>> mapByDay) {
        Date today = PortfolioUtils.getStartOfDay(ExtUtils.now());
        Date startDay = PortfolioUtils.getBefore(today, days);
        NavigableMap<Date, BigDecimal> result = new TreeMap<>();
        for (NavigableMap<Date, BigDecimal> nestedMap : mapByDay.subMap(startDay, true, today, true).values()) {
            nestedMap.forEach((k, v) -> {
                if (k.getTime() % ONE_HOUR_MILLISECONDS == 0) {
                    result.put(k,v);
                }
            });
        }
        return result;
    }
}
