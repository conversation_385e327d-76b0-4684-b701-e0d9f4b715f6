package com.cmc.asset.business.watchlist.manager;

import com.cmc.asset.dao.entity.MarketPairEntity;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.domain.crypto.CryptoCurrencyListResultDTO.CryptoCurrencyDTO;
import com.cmc.asset.integration.BaseDataServiceClient;
import com.cmc.asset.integration.DataApiServiceClient;
import com.cmc.asset.model.contract.crypto.CryptoMarketPairDto;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * CryptoInfoManager
 * <AUTHOR> ricky.x
 * @date : 2023/5/11 上午12:21
 */
@Slf4j
@Service
public class CryptoInfoManager {

    @Autowired
    private DataApiServiceClient dataApiServiceClient;

    @Autowired
    private BaseDataServiceClient baseDataServiceClient;

    /**
     * 查询dataApi并将结果转为Map
     * @param entityList entityList
     * @param convertIds convertIds
     * @param cryptoAux cryptoAux
     * @return map
     */
    public Mono<Map<Integer, CryptoCurrencyDTO>> getCryptoListingMap(List<WatchListEntity> entityList,String convertIds,String cryptoAux){
        if (CollectionUtils.isEmpty(entityList)) {
            return Mono.just(Map.of());
        }
        Set<Integer> cryptos = entityList.stream().filter(entity -> CollectionUtils.isNotEmpty(entity.getCryptos()))
            .flatMap(entity -> entity.getCryptos().stream()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(cryptos)) {
            return Mono.just(Map.of());
        }
        return dataApiServiceClient.getCryptoListingBatch(cryptos, convertIds, cryptoAux)
            .filter(f -> CollectionUtils.isNotEmpty(f.getCryptoCurrencyList())).map(
                dataApiCryptos -> dataApiCryptos.getCryptoCurrencyList().stream()
                    .collect(Collectors.toMap(CryptoCurrencyDTO::getId, v -> v, (k1, k2) -> k1)))
            .onErrorResume(throwable -> Mono.empty()).defaultIfEmpty(Map.of());
    }

    /**
     * 查询baseData并将结果转为Map
     * @param entityList entityList
     * @return map
     */
    public Mono<Map<Integer,MarketPairEntity>> getMarketPairsMap(List<WatchListEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Mono.just(Map.of());
        }
        Set<Integer> exchangeIds =
            entityList.stream().filter(entity -> CollectionUtils.isNotEmpty(entity.getExchanges()))
                .flatMap(entity -> entity.getExchanges().stream()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(exchangeIds)) {
            return Mono.just(Map.of());
        }
        return baseDataServiceClient.getMarketPairsBatch(exchangeIds).flatMapIterable(Function.identity())
            .collectMap(CryptoMarketPairDto::getId,
                it -> MarketPairEntity.builder().id(it.getId()).pairBase(it.getPairBase()).pairQuote(it.getPairQuote())
                    .build()).onErrorResume(throwable -> Mono.just(Map.of())).defaultIfEmpty(Map.of());
    }

}
