package com.cmc.asset.business.dex;

import com.cmc.asset.dao.entity.vo.DexTokenDetailCacheVo;
import java.util.List;
import java.util.Map;
import java.util.Set;
import reactor.core.publisher.Mono;

/**
 * DexDataHubService
 * <AUTHOR> ricky.x
 * @date: 2025/6/4 17:08
 */
public interface DexDataHubService {

    Mono<List<DexTokenDetailCacheVo>>  batchGetDexTokenWithCache(Set<String> chainTokenStrList);


    Mono<Map<String,DexTokenDetailCacheVo>> batchGetDexTokenMapWithCache(Set<String> chainTokenStrList);

}
