package com.cmc.asset.business.gravity;

import com.cmc.asset.business.BaseService;
import com.cmc.asset.domain.gravity.AutoFollowDTO;
import com.cmc.asset.message.SenderMessage;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.kafka.sender.KafkaSender;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AutoFollowService extends BaseService {

    @Resource(name = "autoFollowProducer")
    private KafkaSender<String,String> kafkaSender;

    @Value("${cmc.asset.kafka.producer.autoFollow.topic}")
    private String topic;

    public void sendMessage(AutoFollowDTO autoFollowDTO) {
        SenderMessage message =
            SenderMessage.builder()
                .topic(topic)
                .userId(autoFollowDTO.getUserId())
                .data(autoFollowDTO)
                .build();
        sendMQ(message, kafkaSender);
    }
}
