package com.cmc.asset.business.coins.impl;

import com.cmc.asset.business.coins.IUserCoinService;
import com.cmc.asset.business.portfolio.IPortfolioMultiService;
import com.cmc.asset.business.portfolio.IPortfolioOldService;
import com.cmc.asset.business.watchlist.WatchListService;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.UserTopCoinsEntity;
import com.cmc.asset.dao.repository.mongo.UserTopCoinsRepository;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.news.QueryTopCoinsParamDTO;
import com.cmc.asset.model.contract.news.QueryTopCoinsResultDTO;
import com.cmc.asset.model.contract.news.QueryTopCoinsResultDTO.CoinDTO;
import com.cmc.asset.model.contract.news.QueryUserTopCoinsParamDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioOldMultiQueryDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQueryResultDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsRequestDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsResponseDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.CryptoCurrencyDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.WatchListDTO;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.Header;
import com.cmc.data.common.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cmc.asset.model.enums.TopCoinsSourceEnum.PORTFOLIO;
import static com.cmc.asset.model.enums.TopCoinsSourceEnum.WATCHLIST;

/**
 * <AUTHOR>
 * @date 2022/3/31 上午10:02
 */
@Service
@Slf4j
public class UserCoinService implements IUserCoinService {

    @Value("${cmc.asset.secretKey}")
    private String systemSecretKey;

    private static final QueryTopCoinsResultDTO DEFAULT_EMPTY_RESULT = QueryTopCoinsResultDTO.builder().coins(List.of()).build();

    @Autowired
    private WatchListService watchListService;

    @Autowired
    private IPortfolioMultiService portfolioMultiService;

    @Autowired
    private IPortfolioOldService portfolioOldService;

    @Autowired
    private UserTopCoinsRepository userTopCoinsRepository;

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Autowired
    @Qualifier("assetRedisTemplate")
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;

    private static final Integer MAX_RANK = 9999;

    private static final Integer MIN_RANK = -1;

    @Value("${cmc.asset.user.coins.limit:1000}")
    private Integer coinsLimit;

    @Override
    public Mono<QueryTopCoinsResultDTO> getTopCoins(QueryTopCoinsParamDTO queryTopCoinsParamDTO) {
        String userId = queryTopCoinsParamDTO.getHeader().getUserId();
        String key = String.format(RedisConstants.USER_TOP_COINS, userId);
        return assetRedisTemplate.opsForValue().get(key)
            .map(t -> JacksonUtils.getInstance().deserialize(t, QueryTopCoinsResultDTO.class))
            .switchIfEmpty(Mono.defer(() -> findInTopCoins(userId)
                .switchIfEmpty(Mono.defer(() -> findInMainPortfolio(userId)))
                .filter(result -> CollectionUtils.isNotEmpty(result.getCoins()))
                .switchIfEmpty(Mono.defer(() -> findInMainWatchlist(queryTopCoinsParamDTO)))
                .defaultIfEmpty(DEFAULT_EMPTY_RESULT)
                .map(result -> QueryTopCoinsResultDTO.builder().coins(getTopRank5Coins(result)).build())
                .doOnSuccess(o -> assetRedisTemplate.opsForValue().set(key, JacksonUtils.toJsonString(o),  Duration.ofMinutes(10)).subscribe())));
    }

    @Override
    public Mono<QueryTopCoinsResultDTO> getUserTopCoins(QueryUserTopCoinsParamDTO queryUserTopCoinsParamDTO) {
        String userId = queryUserTopCoinsParamDTO.getHeader().getUserId();
        Integer topN = queryUserTopCoinsParamDTO.getTop();
        String key = String.format(RedisConstants.USER_TOP_N_COINS, topN, userId);
        return assetRedisTemplate.opsForValue().get(key)
            .filter(StringUtils::isNotBlank)
            .map(t -> JacksonUtils.getInstance().deserialize(t, QueryTopCoinsResultDTO.class))
            .switchIfEmpty(Mono.defer(() -> findInTopCoins(userId, topN)
                .filter(result -> CollectionUtils.isNotEmpty(result.getCoins()))
                .switchIfEmpty(Mono.defer(() -> findInMainPortfolioAndWatchList(queryUserTopCoinsParamDTO, topN)))
                .defaultIfEmpty(DEFAULT_EMPTY_RESULT)
                .map(result -> QueryTopCoinsResultDTO.builder().coins(getTopRankNCoins(result, topN)).build())
                .doOnSuccess(o -> assetRedisTemplate.opsForValue().set(key, JacksonUtils.toJsonString(o),  Duration.ofMinutes(10)).subscribe())));

    }

    @Override
    public Mono<Boolean> resetTopCoins(BaseRequest request, String source) {
        String userId = request.getHeader().getUserId();

        if (userId == null) {
            return Mono.just(Boolean.FALSE);
        }

        String key = String.format(RedisConstants.USER_TOP_COINS, userId);
        Mono<QueryTopCoinsResultDTO> mono = Mono.empty();
        if (PORTFOLIO.getSourceId().equals(source)) {
            mono = findInMainPortfolio(userId);
        } else if (WATCHLIST.getSourceId().equals(source)) {
            mono = findInMainWatchlist(request);
        }
        return mono.thenReturn(Boolean.TRUE)
            .defaultIfEmpty(Boolean.FALSE)
            .publishOn(Schedulers.boundedElastic())
            .doOnSuccess(o ->
                Mono.zip(
                    assetRedisTemplate.delete(key),
                    assetRedisTemplate.delete(String.format(RedisConstants.USER_RELEVANT_COINS, userId))
                ).subscribe()
            );
    }

    @Override
    public Mono<UserRelevantCoinsResponseDTO> getUserRelevantCoins(UserRelevantCoinsRequestDTO requestDTO) {
        String userId = requestDTO.getUserId();
        BaseRequest baseRequest = BaseRequest.getInstance();
        baseRequest.getHeader().setUserId(userId);
        baseRequest.getHeader().setVerifiedUserType(1);

        return assetRedisTemplate.opsForValue().get(String.format(RedisConstants.USER_RELEVANT_COINS, userId))
            .filter(StringUtils::isNotBlank)
            .map(s -> JacksonUtils.getInstance().deserialize(s, UserRelevantCoinsResponseDTO.class))
            .switchIfEmpty(Mono.defer(() ->
                    Mono.zip(
                        this.findInMainPortfolioAmountGreaterThanZero(userId).defaultIfEmpty(new QueryTopCoinsResultDTO()),
                            this.findInMainWatchlist(baseRequest).defaultIfEmpty(new QueryTopCoinsResultDTO())
                        )
                        .map(tuple ->
                                UserRelevantCoinsResponseDTO.builder()
                                    .portfolioCoins(limitCoins(tuple.getT1().getCoins()))
                                    .watchListCoins(limitCoins(tuple.getT2().getCoins()))
                                    .build()
                        )
                        .publishOn(Schedulers.boundedElastic())
                        .doOnNext(it ->
                                assetRedisTemplate.opsForValue().set(
                                    String.format(RedisConstants.USER_RELEVANT_COINS, userId),
                                    JacksonUtils.toJsonString(it),
                                    Duration.ofMinutes(10)
                                ).subscribe()
                        )
                )
            )
            .onErrorResume(throwable -> {
                log.error("getUserRelevantCoins error, userId is {}, ex is {}", userId, throwable);
                return Mono.just(new UserRelevantCoinsResponseDTO());
            });

    }

    private List<CoinDTO> limitCoins(List<CoinDTO> coins) {
        if (CollectionUtils.isEmpty(coins)) {
            return coins;
        }
        if (coins.size() > coinsLimit) {
            return new ArrayList<>(coins.subList(0, coinsLimit));
        }
        return coins;
    }


    private Mono<QueryTopCoinsResultDTO> findInTopCoins(String userId) {
        return userTopCoinsRepository.findFirstByUserId(userId)
            .flatMap(entity -> {
                List<Integer> portfolioCoins = entity.getPortfolioCoins();
                List<Integer> watchlistCoins = entity.getWatchlistCoins();
                if (CollectionUtils.isNotEmpty(portfolioCoins)) {
                    return Mono.just(QueryTopCoinsResultDTO.builder().coins(buildCoins(portfolioCoins, PORTFOLIO.getDescription())).build());
                } else if (CollectionUtils.isNotEmpty(watchlistCoins)) {
                    return Mono.just(QueryTopCoinsResultDTO.builder().coins(buildCoins(watchlistCoins, WATCHLIST.getDescription())).build());
                }
                return Mono.empty();
            });
    }

    private Mono<QueryTopCoinsResultDTO> findInMainPortfolio(String userId) {
        PortfolioOldMultiQueryDTO portfolioOldMultiQueryDTO = PortfolioOldMultiQueryDTO.builder().build();
        portfolioOldMultiQueryDTO.setSecretKey(systemSecretKey);
        portfolioOldMultiQueryDTO.setUserId(userId);

        return portfolioMultiService.queryMain(userId)
            .flatMapMany(result -> {
                portfolioOldMultiQueryDTO.setPortfolioSourceId(result.getPortfolioSourceId());
                return portfolioOldService.queryByPortfolioSourceId(portfolioOldMultiQueryDTO);
            })
            .map(PortfolioQueryResultDTO::getCryptocurrencyId)
            .map(cryptoCurrencyCache::getCryptoCurrencyById)
            .collectList()
            .map(list -> list.stream()
                .sorted(Comparator.nullsLast(
                    Comparator.comparing(CryptoCurrencyInfoDTO::getRank, Comparator.nullsLast(Integer::compareTo))))
                .map(CryptoCurrencyInfoDTO::getId)
                .limit(coinsLimit)
                .collect(Collectors.toList()))
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(list -> upsertTopCoins(userId, PORTFOLIO.getSourceId(), list).subscribe())
            .map(coins -> QueryTopCoinsResultDTO.builder().coins(buildCoins(coins, PORTFOLIO.getDescription())).build());
    }

    protected Mono<QueryTopCoinsResultDTO> findInTopCoins(String userId, Integer topN) {
        return userTopCoinsRepository.findFirstByUserId(userId)
            .flatMap(entity -> {
                List<Integer> portfolioCoinIds = entity.getPortfolioCoins();
                List<Integer> watchlistCoinIds = entity.getWatchlistCoins();

                List<CoinDTO> portfolioCoins = CollectionUtils.isEmpty(portfolioCoinIds) ? Collections.emptyList() : buildCoins(portfolioCoinIds, PORTFOLIO.getDescription());
                if (portfolioCoins.size() >= topN) {
                    return Mono.just(portfolioCoins);
                }

                List<CoinDTO> watchlistCoins = CollectionUtils.isEmpty(watchlistCoinIds) ? Collections.emptyList() : buildCoins(watchlistCoinIds, WATCHLIST.getDescription());
                List<CoinDTO> allCoinDTOS = concatAll(portfolioCoins, watchlistCoins);
                return Mono.just(allCoinDTOS);
            })
            .map(coinDTOS -> QueryTopCoinsResultDTO.builder().coins(coinDTOS).build());
    }

    protected Mono<QueryTopCoinsResultDTO> findInMainPortfolioAndWatchList(QueryUserTopCoinsParamDTO queryUserTopCoinsParamDTO, Integer topN) {
        String userId = queryUserTopCoinsParamDTO.getHeader().getUserId();

        // find from portfolio first
        return findInMainPortfolio(userId)
            .map(QueryTopCoinsResultDTO::getCoins)
            .map(portfolioCoins -> portfolioCoins == null ? new ArrayList<CoinDTO>(0) : portfolioCoins)
            .flatMap(portfolioCoins -> {
                if (portfolioCoins.size() >= topN) {
                    return Mono.just(portfolioCoins);
                }

                // find from watchlist after
                return findInMainWatchlist(queryUserTopCoinsParamDTO)
                    .map(QueryTopCoinsResultDTO::getCoins)
                    .map(watchListCoins -> watchListCoins == null ? new ArrayList<CoinDTO>(0) : watchListCoins)
                    .map(watchListCoins -> concatAll(watchListCoins, portfolioCoins));
            })
            .map(coinDTOS -> QueryTopCoinsResultDTO.builder().coins(coinDTOS).build());

    }

    private List<CoinDTO> concatAll(List<CoinDTO> portfolioCoinDTOS, List<CoinDTO> watchlistCoinDTOS) {
        Map<Integer, CoinDTO> rank2CoinDTO = Stream.concat(portfolioCoinDTOS.stream(), watchlistCoinDTOS.stream())
            .filter(Objects::nonNull)
            .sorted((c1, c2) -> {
                if (c1.getRank() == null) {
                    return -1;
                }
                if (c2.getRank() == null) {
                    return 1;
                }
                return c1.getRank().compareTo(c2.getRank());
            })
            .collect(Collectors.toMap(CoinDTO::getId, Function.identity(), (c1, c2) -> c1));

        return new ArrayList<>(rank2CoinDTO.values());
    }

    protected List<CoinDTO> getTopRankNCoins(QueryTopCoinsResultDTO result, Integer topN) {
        return result.getCoins().stream()
            .peek(coinDTO -> {
                CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(coinDTO.getId());
                if (cryptoCurrencyInfoDTO == null || cryptoCurrencyInfoDTO.getRank() == null) {
                    coinDTO.setRank(MAX_RANK);
                } else {
                    coinDTO.setRank(cryptoCurrencyInfoDTO.getRank());
                }
            })
            .filter(coinDTO -> coinDTO.getId() != null && coinDTO.getRank() != null)
            .sorted(Comparator.comparingInt(CoinDTO::getRank))
            .limit(topN)
            .collect(Collectors.toList());
    }

    private Mono<QueryTopCoinsResultDTO> findInMainPortfolioAmountGreaterThanZero(String userId) {
        PortfolioOldMultiQueryDTO portfolioOldMultiQueryDTO = PortfolioOldMultiQueryDTO.builder().build();
        portfolioOldMultiQueryDTO.setSecretKey(systemSecretKey);
        portfolioOldMultiQueryDTO.setUserId(userId);

        return portfolioMultiService.queryMain(userId)
            .flatMapMany(result -> {
                portfolioOldMultiQueryDTO.setPortfolioSourceId(result.getPortfolioSourceId());
                return portfolioOldService.queryByPortfolioSourceId(portfolioOldMultiQueryDTO);
            })
            .filter(it -> it != null && it.getAmount() != null && it.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .map(PortfolioQueryResultDTO::getCryptocurrencyId)
            .map(cryptoCurrencyCache::getCryptoCurrencyById)
            .collectList()
            .map(list -> list.stream().filter(Objects::nonNull).sorted(Comparator.nullsLast(
                    Comparator.comparing(CryptoCurrencyInfoDTO::getRank, Comparator.nullsLast(Integer::compareTo))))
                .map(CryptoCurrencyInfoDTO::getId).limit(coinsLimit).collect(Collectors.toList()))
            .map(coins -> QueryTopCoinsResultDTO.builder().coins(buildCoins(coins, PORTFOLIO.getDescription())).build());
    }

    private Mono<QueryTopCoinsResultDTO> findInMainWatchlist(BaseRequest request) {
        String userId = request.getHeader().getUserId();

        return watchListService.query(buildWatchlistQueryParam(request))
            .map(result -> {
                List<WatchListDTO> watchListDTOS = result.getWatchLists();
                if (CollectionUtils.isNotEmpty(watchListDTOS)) {
                    return watchListDTOS.get(0).getCryptoCurrencies().stream().map(CryptoCurrencyDTO::getId)
                        .collect(Collectors.toList());
                }
                return new ArrayList<Integer>();
            })
            .flatMapIterable(v -> v)
            .map(it -> Optional.ofNullable(cryptoCurrencyCache.getCryptoCurrencyById(it)).orElse(new CryptoCurrencyInfoDTO()))
            .filter(it -> it.getId() != null)
            .collectList()
            .map(list -> list.stream().sorted(Comparator.comparing(CryptoCurrencyInfoDTO::getRank, Comparator.nullsLast(Comparator.naturalOrder()))
                            .reversed())
                .map(CryptoCurrencyInfoDTO::getId).limit(coinsLimit).collect(Collectors.toList()))
            .onErrorResume(e -> {
                log.error("find coins in main watchlist occurs error", e);
                return Mono.just(List.of());
            })
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(list -> upsertTopCoins(userId, WATCHLIST.getSourceId(), list).subscribe())
            .map(coins -> QueryTopCoinsResultDTO.builder().coins(buildCoins(coins, WATCHLIST.getDescription())).build());
    }

    private Mono<Boolean> upsertTopCoins(String userId, String source, List<Integer> coins) {
        Mono<UserTopCoinsEntity> mono = Mono.empty();
        if (PORTFOLIO.getSourceId().equals(source)) {
            mono = userTopCoinsRepository.upsert(userId, coins, null, coinsLimit);
        } else if (WATCHLIST.getSourceId().equals(source)) {
            mono = userTopCoinsRepository.upsert(userId, null, coins, coinsLimit);
        }

        return mono.thenReturn(Boolean.TRUE).defaultIfEmpty(Boolean.FALSE);
    }

    protected QueryWatchListParamDTO buildWatchlistQueryParam(BaseRequest request) {
        Header header = request.getHeader();

        QueryWatchListParamDTO queryWatchListParamDTO = new QueryWatchListParamDTO();
        Header queryHeader = queryWatchListParamDTO.getHeader();
        queryHeader.setUserId(header.getUserId());
        queryHeader.setUserName(header.getUserName());
        queryHeader.setVerifiedUserType(header.getVerifiedUserType());
        queryWatchListParamDTO.setIsMain(Boolean.TRUE);

        return queryWatchListParamDTO;
    }

    private List<CoinDTO> buildCoins(List<Integer> ids, String source) {
        return ids.stream().map(id -> CoinDTO.builder().id(id).source(source).build()).collect(Collectors.toList());
    }

    private List<CoinDTO> getTopRank5Coins(QueryTopCoinsResultDTO result) {
        return getTopRankNCoins(result, 5);
    }
}
