package com.cmc.asset.business.watchlist;

import com.cmc.asset.business.BaseService;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.domain.watchlist.WatchListChangeDTO;
import com.cmc.asset.message.SenderMessage;
import com.cmc.framework.utils.CollectionUtils;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * async with watchListService
 * <AUTHOR>
 * @Description
 * @date 2021/9/2
 */
@Service
@Slf4j
public class AsyncWatchListService extends BaseService {
    @Value("${cmc.asset.kafka.notification.producer.watchlist.topic}")
    private String watchListChangeTopic;

    @Value("${cmc.asset.business.watchlist.enable-notification:false}")
    private Boolean enableFlag;

    /**
     *  when subscribe or unsubscribe main list coin send notification kafka
     * @param entity
     */
    @Async
    public void asyncCalcSubscribeWatchListCoin(SubscribeParamDTO param, boolean isAdded, WatchListEntity entity) {
        if (!Boolean.TRUE.equals(enableFlag)) {
            return;
        }
        try {
            if (Boolean.TRUE.equals(entity.getMain())) {
                List<Integer> resourceIds = Optional.ofNullable(param.getResourceIds())
                    .map(k -> k.stream().map(Math::toIntExact).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
                Set<Integer> sourceIds = Optional.ofNullable(entity.getCryptos()).orElse(new HashSet<>());
                if (isAdded) {
                    sourceIds.addAll(resourceIds);
                } else {
                    sourceIds.removeAll(resourceIds);
                }

                WatchListChangeDTO watchListChangeDTO =
                    WatchListChangeDTO.builder()
                        .userId(entity.getUserId())
                        .timestamp(Instant.now().toEpochMilli())
                        .cryptos(CollectionUtils.isEmpty(resourceIds) ? entity.getCryptos() : resourceIds)
                        .operation(isAdded)
                        .watchlist(sourceIds).build();

                SenderMessage senderMessage =
                    SenderMessage.builder().userId(entity.getUserId()).data(watchListChangeDTO).topic(watchListChangeTopic).build();
                sendNotificationMQ(senderMessage);
            }
        } catch (Exception e) {
            log.warn(" notification  kafka send err : {}", entity.getUserId());
        }

    }
}
