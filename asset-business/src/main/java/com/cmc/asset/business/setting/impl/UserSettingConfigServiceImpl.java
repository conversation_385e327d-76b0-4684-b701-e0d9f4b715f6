package com.cmc.asset.business.setting.impl;

import static com.cmc.asset.model.common.RedisConstants.KEY_USER_SETTING_CONFIG_CACHE;
import com.cmc.asset.business.setting.UserSettingConfigService;
import com.cmc.asset.dao.entity.UserCustomizeSectionEntity;
import com.cmc.asset.dao.repository.mongo.UserCustomizeSectionRepository;
import com.cmc.asset.dao.repository.redis.CacheRepository;
import com.cmc.asset.locale.LocaleConstants;
import com.cmc.asset.model.contract.user.UserSettingConfigDTO;
import com.cmc.asset.model.contract.user.UserSettingConfigParamDTO;
import com.cmc.asset.model.contract.user.UserSettingConfigQueryDTO;
import com.cmc.data.common.utils.JacksonUtils;
import com.cmc.data.common.utils.LocaleMessageSource;
import com.cmc.data.common.utils.ParamConditions;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: User Setting Config Service
 * @Author: Rick Z
 * @CreateTime: 2022-08-05
 */
@Service
@Slf4j
public class UserSettingConfigServiceImpl implements UserSettingConfigService {

    private final static String jsonKey = "%s:%s";// platform:deviceId
    @Value("${com.cmc.asset.setting.storage.platform:ios,android}")
    private Set<String> settingPlatform;

    @Value("${com.cmc.asset.user-setting.cache.expireTime:3600}")
    private Integer expireTime;

    @Autowired
    private UserCustomizeSectionRepository userCustomizeSectionRepository;

    @Autowired
    private CacheRepository cacheRepository;

    @Override
    public Mono<UserSettingConfigDTO> saveSettingConfig(UserSettingConfigParamDTO paramDTO) {

        ParamConditions.checkArgument(paramDTO != null && StringUtils.isNotBlank(paramDTO.getUserId()) &&
                StringUtils.isNotBlank(paramDTO.getConfigJson()),
            LocaleMessageSource.getMessage(LocaleConstants.REQUEST_PARAM_ERROR_NULL, "userId, configJson"));

        return Mono.justOrEmpty(paramDTO)
            .filter(dto -> StringUtils.isNotBlank(dto.getUserId()))
            .flatMap(dto -> userCustomizeSectionRepository.findByUserId(dto.getUserId()))
            .switchIfEmpty(Mono.defer(() -> Mono.just(UserCustomizeSectionEntity.builder().build())))
            .map(entity -> {
                entity.setUserId(paramDTO.getUserId());
                entity.setUId(paramDTO.getUId());
                entity.setConfigJson(convertConfigJson(paramDTO, entity));
                return entity;
            })
            .flatMap(e -> userCustomizeSectionRepository.findAndModify(e))
            .map(entity -> convertToDTO(entity, paramDTO.getPlatform(), paramDTO.getDeviceId()))
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(dto ->  cacheRepository.delete(String.format(KEY_USER_SETTING_CONFIG_CACHE, paramDTO.getUserId()))
                .subscribe());
    }

    @Override
    public Mono<UserSettingConfigDTO> querySettingConfig(UserSettingConfigQueryDTO paramDto) {
        String userId = paramDto.getHeader().getUserId();
        if (StringUtils.isEmpty(userId)) {
            return Mono.error(new IllegalArgumentException("userId is null"));
        }
        String cacheKey = String.format(KEY_USER_SETTING_CONFIG_CACHE, userId);
        return cacheRepository.get(cacheKey)
            .filter(StringUtils::isNotBlank)
            .map(json -> JacksonUtils.getInstance().deserialize(json, UserSettingConfigDTO.class))
            .switchIfEmpty(Mono.defer(() -> userCustomizeSectionRepository.findByUserId(userId)
                .map(entity -> convertToDTO(entity, paramDto.getPlatform(), paramDto.getDeviceId()))
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(entity -> cacheRepository.cacheInString(cacheKey,
                    JacksonUtils.getInstance().serialize(entity), Duration.ofSeconds(expireTime))
                    .subscribe())
            ));

    }

    // Support incremental and full updates
    private String convertConfigJson(UserSettingConfigParamDTO paramDTO, UserCustomizeSectionEntity entity){
        String platform = paramDTO.getPlatform();
        String deviceId = paramDTO.getDeviceId();

        Map<String, String> jsonMap = Maps.newHashMap();
        if(StringUtils.isNotBlank(entity.getConfigJson())){
            jsonMap = JacksonUtils.getInstance().deserialize(entity.getConfigJson(), Map.class);
        }

        String vJson;
        if(CollectionUtils.isNotEmpty(settingPlatform) && StringUtils.isNotBlank(entity.getConfigJson()) &&
            settingPlatform.stream().filter(p -> platform.toLowerCase().contains(p.toLowerCase())).collect(Collectors.toSet()).size() > 0){

            vJson = settingPlatform.stream()
                .filter(p -> platform.toLowerCase().contains(p.toLowerCase()))
                .findFirst()
                .map(p -> Optional.ofNullable(JacksonUtils.getInstance().deserialize(entity.getConfigJson(), Map.class).get(buildConfigJsonKey(platform, deviceId)))
                    .map(j -> {
                        Map nMap = JacksonUtils.getInstance().deserialize((String)j, Map.class);
                        nMap.putAll(JacksonUtils.getInstance().deserialize(paramDTO.getConfigJson(), Map.class));
                        return JacksonUtils.getInstance().serialize(nMap);
                    }).orElse(paramDTO.getConfigJson()))
                .get();
        }else{
            vJson = paramDTO.getConfigJson();
        }

        jsonMap.put(platform, vJson);// platform setting
        jsonMap.put(buildConfigJsonKey(platform, deviceId), vJson);// platform + deviceId setting
        return JacksonUtils.getInstance().serialize(jsonMap);
    }

    private UserSettingConfigDTO convertToDTO(UserCustomizeSectionEntity entity, String platform, String deviceId){
        String jsonConfig = (String)Optional.ofNullable(entity.getConfigJson())
            .map(str -> JacksonUtils.getInstance().deserialize(str, Map.class)).orElse(Map.of())
        .get(buildConfigJsonKey(platform, deviceId));

        return UserSettingConfigDTO.builder()
            .deviceId(deviceId)
            .configJson(jsonConfig)
//            .userId(entity.getUserId())
//            .uId(entity.getUId())
//            .platform(platform)
            .build();
    }

    private String buildConfigJsonKey(String platform, String deviceId){
        deviceId = StringUtils.isNotBlank(deviceId) ? deviceId : "-1";
        return String.format(jsonKey, platform, deviceId);
    }

}
