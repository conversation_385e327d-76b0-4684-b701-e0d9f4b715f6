package com.cmc.asset.business.portfolio;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiAddDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiAddResultDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiCryptoResultDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiDeleteDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiDeleteRequest;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiDuplicateDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiInitDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiListQueryDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiListUpdateDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiQueryByCryptoIdDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiQueryDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiResultDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiUpdateDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioQueryByCryptoIdResponse;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/6/15 20:33:33
 */
public interface IPortfolioMultiService {

    /**
     * @description init Main portfolio
     * <AUTHOR>
     * @date 2021/6/22 15:38:32
     */
    Mono<PortfolioMultiResultDTO> initMain(PortfolioMultiInitDTO portfolioMultiInitDTO);

    /**
     * @description add single portfolio group
     * <AUTHOR>
     * @date 2021/6/17 15:50:56
     */
    Mono<PortfolioMultiAddResultDTO> add(PortfolioMultiAddDTO portfolioMultiAddDTO);

    /**
     * @description delete portfolio group (by id)
     * <AUTHOR>
     * @date 2021/6/17 15:51:44
     */
    Mono<Boolean> delete(PortfolioMultiDeleteDTO portfolioMultiDeleteDTO);

    /**
     *  delete portfolio group (by id)
     * <AUTHOR>
     * @date 2021/6/17 15:51:44
     */
    /**
     * app dashboard版本调用v4接口进行删除,此接口为预留接口,与v3逻辑基本一致,等后续对此接口移除main与manual删除限制
     * @param request request
     * @return boolean
     */
    Mono<Boolean> deleteV4(PortfolioMultiDeleteRequest request);

    /**
     * @description update portfolio group
     * <AUTHOR>
     * @date 2021/6/17 15:51:44
     */
    Mono<PortfolioMultiResultDTO> update(PortfolioMultiUpdateDTO portfolioMultiUpdateDTO);

    /**
     * @description batch update portfolio group
     * <AUTHOR>
     * @date 2021/6/17 15:53:01
     */
    Mono<Boolean> batchUpdate(PortfolioMultiListUpdateDTO portfolioMultiListUpdateDTO);

    /**
     * @description query portfolio group by id
     * <AUTHOR>
     * @date 2021/6/17 15:53:25
     */
    Mono<PortfolioMultiResultDTO> query(PortfolioMultiQueryDTO portfolioMultiQueryDTO);

    /**
     * query main portfolio
     * @return PortfolioMultiResultDTO
     */
    Mono<PortfolioMultiResultDTO> queryMain(String userId);

    /**
     * @description query manual portfolio group by userId
     * <AUTHOR>
     * @date 2021/6/17 15:53:25
     */
    Mono<List<PortfolioMultiResultDTO>> queryAll(PortfolioMultiListQueryDTO portfolioMultiListQueryDTO);

    /**
     * query all portfolio group by userId
     * @param portfolioMultiListQueryDTO userId
     * @return list
     */
    Mono<List<PortfolioMultiResultDTO>> queryAllByUserId(PortfolioMultiListQueryDTO portfolioMultiListQueryDTO);

    /**
     * @description query portfolios by id
     * <AUTHOR>
     * @date 2021/7/15 11:29:58
     */
    Mono<List<PortfolioMultiCryptoResultDTO>> queryByCryptoId(PortfolioMultiQueryByCryptoIdDTO portfolioMultiQueryByCryptoIdDTO);

    /**
     * @description query portfolios by id
     * <AUTHOR>
     * @date 2021/7/15 11:29:58
     */
    Mono<List<PortfolioQueryByCryptoIdResponse>> queryByCryptoIdV4(PortfolioMultiQueryByCryptoIdDTO portfolioMultiQueryByCryptoIdDTO);

    /**
     * get portfolioSourceId by userId
     * <AUTHOR>
     * @date 2021/6/17 15:54:18
     */
    Mono<String> transferPortfolioSourceToMultiId(String userId, String portfolioSourceId);

    /**
     * get portfolioMultiEntity
     * @param userId userId
     * @param portfolioSourceId portfolioSourceId
     * @return portfolioMultiEntity
     */
    Mono<PortfolioMultiEntity> transferPortfolioSourceAdaptForApp(String method, String userId, String portfolioSourceId, String platform,String appVersion) ;

    Flux<PortfolioMultiEntity> findAllByUserId(ObjectId userId);

    Mono<PortfolioMultiEntity> findPortfolioMultiEntity(String userId, String portfolioSourceId);

    Mono<PortfolioMultiAddResultDTO> duplicate(PortfolioMultiDuplicateDTO portfolioMultiDuplicateDTO);

    Mono<PortfolioMultiEntity> getAndRefreshCache(String userId, String sourceId);

    Flux<PortfolioMultiEntity> findByUserIdAndPortfolioSourceIdIn(ObjectId userId, Set<String> sourceId);
}
