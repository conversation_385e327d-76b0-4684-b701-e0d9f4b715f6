package com.cmc.asset.cache;

import com.cmc.asset.business.CryptoInfoServiceImpl;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyTiming;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CryptoTimingsCache extends BaseLocalCache<String, Map<Integer, CryptoCurrencyTiming>> {

    @Resource
    private CryptoInfoServiceImpl cryptoInfoService;

    /**
     * Cache expires in 30 seconds
     */
    public CryptoTimingsCache() {
        super(RandomUtils.nextInt(60, 120), TimeUnit.MINUTES);
    }

    @Override
    protected Map<Integer, CryptoCurrencyTiming> load(String key) {
        return cryptoInfoService.getAllCryptoLatestTimings()
                .block(Duration.ofSeconds(240));
    }

    @Override
    protected void preLoad() {
        log.info("Preload the CryptoTimingsCache cache data.");
        getCryptoCurrencyTimings();
    }

    @SneakyThrows
    public Map<Integer, CryptoCurrencyTiming> getCryptoCurrencyTimings() {
        return loadingCache.get(StringUtils.EMPTY);
    }

    public CryptoCurrencyTiming getById(Integer id) {
        return getCryptoCurrencyTimings().get(id);
    }

    public Set<Integer> getByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        return ids.stream().filter(id -> getById(id) != null).collect(Collectors.toSet());
    }

}
