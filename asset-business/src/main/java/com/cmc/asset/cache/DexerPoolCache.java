package com.cmc.asset.cache;

import com.cmc.asset.business.portfolio.DexerService;
import com.cmc.asset.business.portfolio.impl.PortfolioCryptoRedisService;
import com.cmc.asset.config.CryptoBaseCurrencyConfig;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import org.apache.commons.lang3.RandomUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @since 2024/3/12 12:44
 */
@Component
public class DexerPoolCache extends AbstractCache<Long, PairInfoDTO> {

    @Autowired
    private DexerService dexerService;

    protected DexerPoolCache(@Value("${com.cmc.asset.cache.dex-pool.max-cache-size:10000}") Integer maxCacheSize,
                             @Value("${com.cmc.asset.cache.dex-pool.min-refresh-seconds:60}") Integer minRefreshSeconds) {
        super(maxCacheSize, RandomUtils.nextInt(minRefreshSeconds, minRefreshSeconds * 2), TimeUnit.SECONDS);
    }

    @Override
    public Mono<PairInfoDTO> load(@NonNull Long key) {
        return dexerService.getPairs(List.of(key)).map(pairInfoDTOMap -> pairInfoDTOMap.get(key));
    }

    @Override
    public Mono<Map<Long, PairInfoDTO>> loadAll(@NonNull Iterable<? extends @NonNull Long> keys) {
        List<Long> poolIds = StreamSupport.stream(keys.spliterator(), false)
                .collect(Collectors.toList());
        return dexerService.getPairs(poolIds);
    }

}
