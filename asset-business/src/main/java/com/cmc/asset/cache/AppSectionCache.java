package com.cmc.asset.cache;

import com.cmc.asset.dao.entity.AppSectionEntity;
import com.cmc.asset.dao.repository.mongo.AppSectionRepository;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/4/8 20:59
 * @description
 */
@Component
@Slf4j
public class AppSectionCache extends BaseLocalCache<String, Map<String, AppSectionEntity>> {

    @Autowired
    private AppSectionRepository appSectionRepository;

    /**
     * Cache expires in 30 seconds
     */
    public AppSectionCache() {
        super(RandomUtils.nextInt(300, 600), TimeUnit.SECONDS);
    }


    @Override
    protected Map<String, AppSectionEntity> load(String key) {

        return appSectionRepository.findAll()
                .filter(f -> f.isActived())
                .collectMap(k -> k.getId().toHexString(), v -> v)
                .block(Duration.ofMillis(30000));
    }

    @Override
    protected void preLoad() {

        log.info("Preload the app section cache data.");
        getAppSections();
    }

    @SneakyThrows
    public Map<String, AppSectionEntity> getAppSections() {

        return loadingCache.get(Strings.EMPTY);
    }

    /**
     * get the AppSectionEntity by id.
     * @param id
     * @return
     */
    public AppSectionEntity getAppSection(String id){

        return getAppSections().get(id);
    }
}
