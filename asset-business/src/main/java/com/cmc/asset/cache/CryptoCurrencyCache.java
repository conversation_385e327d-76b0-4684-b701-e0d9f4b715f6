package com.cmc.asset.cache;

import com.cmc.asset.business.CryptoInfoServiceImpl;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @date 2020/10/19 19:42
 * @description
 */
@Component
@Slf4j
public class CryptoCurrencyCache extends BaseLocalCache<String, Map<Integer, CryptoCurrencyInfoDTO>> {

    @Autowired
    private CryptoInfoServiceImpl cryptoInfoService;

    private volatile Map<String, List<CryptoCurrencyInfoDTO>> symbolAndIdCache;

    /**
     * Cache expires in 30 seconds
     */
    public CryptoCurrencyCache() {
        super(RandomUtils.nextInt(120, 200), TimeUnit.SECONDS);
    }

    @Override
    protected Map<Integer, CryptoCurrencyInfoDTO> load(String key) {
        return cryptoInfoService.getAllCryptoCurrencies()
            .flatMapIterable(d -> d)
            .collectList()
            .flatMapMany(Flux::fromIterable)
            .map(m -> CryptoCurrencyInfoDTO.builder()
                .id(m.getId())
                .name(m.getName())
                .slug(m.getSlug())
                .status(m.getStatus())
                .symbol(m.getSymbol())
                .category(m.getCategory())
                .is_active(m.getIs_active())
                .rank(m.getRank())
                .build())
            .collectMap(k -> k.getId(), v -> v)
            .map(cryptos -> {
                symbolAndIdCache = cryptos.values().stream()
                        .collect(Collectors.groupingBy(CryptoCurrencyInfoDTO::getSymbol));
                return cryptos;
            })
            .block(Duration.ofSeconds(2400));
    }

    @Override
    protected void preLoad() {

        log.info("Preload the crypto cache data.");
        getCryptoCurrencies();
    }

    @SneakyThrows
    public Map<Integer, CryptoCurrencyInfoDTO> getCryptoCurrencies() {

        return loadingCache.get(StringUtils.EMPTY);
    }

    public CryptoCurrencyInfoDTO getById(Integer id) {

        return getCryptoCurrencies().get(id);
    }

    public Set<Integer> getByIds(Collection<Integer> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        return ids.stream().filter(id -> getById(id) != null).collect(Collectors.toSet());
    }

    /**
     * get the CryptoCurrencyEntity by id
     *
     * @param id
     * @return
     */
    public CryptoCurrencyInfoDTO getCryptoCurrencyById(Integer id) {

        Map<Integer, CryptoCurrencyInfoDTO> cryptoCurrencyMap = getCryptoCurrencies();
        CryptoCurrencyInfoDTO cryptoCurrency = cryptoCurrencyMap.get(id);
        if (cryptoCurrency == null) {
            log.debug("CryptoCurrencyCache: get the crypto currency return null. id={}", id);
            return null;
        }
        return cryptoCurrency;
    }

    public List<CryptoCurrencyInfoDTO> getIdBySymbol(String symbol) {
        if (StringUtils.isBlank(symbol) ) {
            return null;
        }
        return symbolAndIdCache.get(symbol);
    }
}
