package com.cmc.asset.cache;

import com.cmc.asset.business.CryptoInfoServiceImpl;
import com.cmc.asset.domain.exchange.ExchangeInfoEntity;
import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/10/20 11:44
 * @description
 */
@Component
@Slf4j
public class ExchangeCache extends BaseLocalCache<String, Map<Integer, ExchangeInfoEntity>> {

    @Autowired
    private CryptoInfoServiceImpl cryptoInfoService;

    /**
     * Cache expires in 30 seconds
     */
    public ExchangeCache() {
        super(RandomUtils.nextInt(20, 30), TimeUnit.SECONDS);
    }

    @Override
    protected Map<Integer, ExchangeInfoEntity> load(String key) {

        return cryptoInfoService.getAllExchanges()
                .flatMapIterable(d -> d)
                .map(m -> ExchangeInfoEntity.builder()
                        .id(m.getId())
                        .name(m.getName())
                        .slug(m.getSlug())
                        .status(m.getStatus())
                        .is_active(m.getIs_active())
                        .build())
                .collectMap(k -> k.getId(), v -> v)
                .block(Duration.ofSeconds(120));
    }

    @Override
    protected void preLoad() {

        log.info("Preload the exchange cache data.");
        getExchangeInfos();
    }

    @SneakyThrows
    public Map<Integer, ExchangeInfoEntity> getExchangeInfos() {

        return loadingCache.get(StringUtils.EMPTY);
    }

    public ExchangeInfoEntity getById(Integer id) {

        return getExchangeInfos().get(id);
    }

    public Set<Integer> getByIds(Collection<Integer> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        return ids.stream().filter(id -> getById(id) != null).collect(Collectors.toSet());
    }
}
