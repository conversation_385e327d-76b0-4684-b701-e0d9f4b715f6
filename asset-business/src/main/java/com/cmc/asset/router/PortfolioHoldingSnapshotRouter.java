package com.cmc.asset.router;

import com.cmc.asset.business.portfolio.IPortfolioSnapshotService;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.asset.model.enums.HistoricalChartDayEnum;
import com.cmc.framework.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * PortfolioHoldingSnapshotRouter
 * 查询ddb的到holding快照 或者 查询snapshot表并根据币价来的到holding快照
 * <AUTHOR> ricky.x
 * @date : 2023/6/15 12:53
 */
@Service
@Slf4j
public class PortfolioHoldingSnapshotRouter {

    /**
     * 灰度流量切换万分比(仅用于查询)
     */
    @Value("${com.cmc.asset-service.portfolio-snapshot-holding.gray-percentage:0}")
    private int grayscalePercentage;


    /**
     * 白名单切换
     */
    @Value("${com.cmc.asset-service.portfolio-snapshot-holding.user_ids:}")
    private List<String> userIds;

    /**
     * 处理snapshot表的manager
     */
    @Resource
    private IPortfolioSnapshotService portfolioSnapshotService;

    public Mono<List<PortfolioTotalDTO>> findHoldingChart(String userId, String portfolioSourceId, int days) {
        HistoricalChartDayEnum dayEnum = HistoricalChartDayEnum.getByCode(days);
        if (dayEnum == null) {
            return Mono.empty();
        }
        return portfolioSnapshotService.findHistoricalHoldingChart(userId, portfolioSourceId, dayEnum);
    }

    /**
     * get yesterday holding
     */
    public Mono<BigDecimal> getYesterdayHolding(String userId, String portfolioSourceId, Integer cryptoId, Date currentTime, Integer cryptoUnit){
        return portfolioSnapshotService.getYesterdayHolding(userId,portfolioSourceId, cryptoId, currentTime, cryptoUnit);
    }


    public boolean gray(String userId) {
        if(CollectionUtils.isNotEmpty(userIds)){
            return userIds.contains(userId);
        }
        return grayscalePercentage >= ThreadLocalRandom.current().nextInt(1, 101);
    }

}
