package com.cmc.asset.mapstruct;

import com.cmc.asset.dao.entity.vo.DexTokenDetailCacheVo;
import com.cmc.asset.domain.dex.DataHubTokenDetailDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * DataHubDexTokenMapper
 * <AUTHOR> ricky.x
 * @date: 2025/6/11 18:44
 */

@Mapper
public interface DataHubDexTokenMapper {

    DataHubDexTokenMapper INSTANCE = Mappers.getMapper(DataHubDexTokenMapper.class);


    WatchListResultDTO.DexTokenDTO buildDexTokenByCache(DexTokenDetailCacheVo source);


    DexTokenDetailCacheVo buildDexTokenCacheVo(DataHubTokenDetailDTO source);

}
