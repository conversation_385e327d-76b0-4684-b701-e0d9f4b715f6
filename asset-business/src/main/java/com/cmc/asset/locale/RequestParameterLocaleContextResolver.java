package com.cmc.asset.locale;

import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.exception.BusinessException;
import org.springframework.context.i18n.LocaleContext;
import org.springframework.context.i18n.SimpleLocaleContext;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.i18n.LocaleContextResolver;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2020/7/24 14:18
 * @description
 */
public class RequestParameterLocaleContextResolver implements LocaleContextResolver {

    @Override
    public LocaleContext resolveLocaleContext(ServerWebExchange exchange) {

        Locale target = Locale.getDefault();
        List<String> langs = exchange.getRequest().getQueryParams().getOrDefault("lang", List.of("en"));
        if (langs != null && !langs.isEmpty()) {
            target = Locale.forLanguageTag(langs.get(0));
        }
        return new SimpleLocaleContext(target);
    }

    @Override
    public void setLocaleContext(ServerWebExchange exchange, LocaleContext localeContext) {
        BusinessException.throwIfErrorCode(MessageCode.SYS_NOT_SUPPORT);
    }
}
