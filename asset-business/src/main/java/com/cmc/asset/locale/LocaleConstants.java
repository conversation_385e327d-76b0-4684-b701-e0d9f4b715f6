package com.cmc.asset.locale;

/**
 * <AUTHOR>
 * @date 2020/10/19 17:23
 * @description
 */
public interface LocaleConstants {
    /**
     * common
     */
    String REQUEST_PARAM_ERROR_NULL = "request.param.error.null";
    String REQUEST_PARAM_ERROR_INTERVAL = "request.param.error.interval";
    String REQUEST_PARAM_ERROR_LARGER_THEN = "request.param.error.larger_then";
    String REQUEST_PARAM_ERROR_UNSUPPORTED = "request.param.error.unsupported";
    String RESPONSE_ERROR_NO_FOUND = "response.error.no.found";
    /***
     * watchlist
     */
    String WATCHLIST_SUBSCRIBE_PARAM_SUBSCRIBE_TYPE_ERROR = "watchlist.subscribe.param.subscribe_type.error";
    String WATCHLIST_SUBSCRIBE_PARAM_RESOURCE_TYPE_ERROR = "watchlist.subscribe.param.resource_type.error";
    String WATCHLIST_SUBSCRIBE_PARAM_RESOURCE_SIZE_LIMIT="watchlist.subscribe.param.resource.max";
    String WATCHLIST_FOLLOW_PARAM_FOLLOW_TYPE_ERROR = "watchlist.follow.param.follow_type.error";
    String WATCHLIST_FOLLOW_PARAM_MAX_ERROR="watchlist.follow.param.max.error";
    String WATCHLIST_PARAM_CHARACTERS_MAX="watchlist.param.characters.max";
    /**
     * crypto prediction.
     */
    String CRYPTO_PREDICTION_PARAM_TIME = "crypto.prediction.param.time";
    String CRYPTO_PREDICTION_PARAM_TIME_RANGE = "crypto.prediction.param.time_range";

    /**
     * vote country crypto
     */
    String VOTE_PARAM_COUNTRY_AMOUNT = "vote.predict.country.crypto.amount";

    /**
     * auth
     */
    String REQUEST_PARAM_NO_AUTH = "request.param.no.auth";
    String REQUEST_PARAM_VALIDATE_USERID = "request.param.validate.user_id";
}
