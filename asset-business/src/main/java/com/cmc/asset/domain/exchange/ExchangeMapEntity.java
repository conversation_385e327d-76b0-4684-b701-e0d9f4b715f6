package com.cmc.asset.domain.exchange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/20 11:46
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExchangeMapEntity {

    /**
     * The unique CoinMarketCap ID for this exchange.
     */
    private Integer id;
    /**
     * The name of this exchange.
     */
    private String name;

    /**
     * The web URL friendly shorthand version of this exchange name.
     */
    private String slug;

    /**
     * 1 if this exchange is still being actively tracked and updated, otherwise 0.
     */
    private Integer is_active;

    /**
     * The listing status of the exchange. *This field is only returned if requested through the `aux` request parameter.*
     */
    private String status;

    /**
     * Timestamp (ISO 8601) of the earliest market data record available to query using our historical endpoints. `null` if there is no historical data currently available for this exchange.
     */
    private String first_historical_data;

    /**
     * Timestamp (ISO 8601) of the latest market data record available to query using our historical endpoints. `null` if there is no historical data currently available for this exchange.
     */
    private String last_historical_data;

    private Integer is_redistributable;

    private Integer is_listed;

    private String description;
}
