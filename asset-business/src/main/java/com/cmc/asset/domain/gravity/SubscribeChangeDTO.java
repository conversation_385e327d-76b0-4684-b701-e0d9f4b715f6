package com.cmc.asset.domain.gravity;

import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubscribeChangeDTO {

    private String userId;

    private Long uid;
    /**
     * subscribe | unsubscribe
     */
    private SubscribeTypeEnum subscribeType;

    /**
     * exchange | crypto | marketpair
     */
    private ResourceTypeEnum resourceType;

    private List<Integer> resourceIds;

    private String watchListId;

    private Boolean shared;

    private Date timeCreated ;
}
