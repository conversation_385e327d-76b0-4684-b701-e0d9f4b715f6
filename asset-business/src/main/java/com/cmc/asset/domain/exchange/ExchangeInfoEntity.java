package com.cmc.asset.domain.exchange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/20 11:46
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExchangeInfoEntity {

    /**
     * The unique CoinMarketCap ID for this exchange.
     */
    private Integer id;
    /**
     * The name of this exchange.
     */
    private String name;

    /**
     * The web URL friendly shorthand version of this exchange name.
     */
    private String slug;

    /**
     * 1 if this exchange is still being actively tracked and updated, otherwise 0.
     */
    private Integer is_active;

    /**
     * The listing status of the exchange. *This field is only returned if requested through the `aux` request parameter.*
     */
    private String status;
}
