package com.cmc.asset.domain.dex;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * DataHubTokenDetailDTO
 * <AUTHOR> ricky.x
 * @date: 2025/7/2 00:25
 */
@Data
public class DataHubTokenDetailDTO {
    @JsonProperty("n")
    private String name;

    @JsonProperty("sym")
    private String symbol;

    @JsonProperty("addr")
    private String address;

    @JsonProperty("pdex")
    private String platformDexerName;

    @JsonProperty("pcid")
    private Integer platformCryptoId;

    @JsonProperty("plt")
    private String platform;

    @JsonProperty("pid")
    private Integer platformId;

    @JsonProperty("dec")
    private Integer decimals;

    @JsonProperty("crt")
    private String creator;

    @JsonProperty("own")
    private String owner;

    @JsonProperty("rnc")
    private String renounced;

    @JsonProperty("web")
    private String website;

    @JsonProperty("tw")
    private String twitter;

    @JsonProperty("tg")
    private String telegram;

    @JsonProperty("lg")
    private String logo;

    @JsonProperty("pubAt")
    private Long publishAt;

    @JsonProperty("lchAt")
    private Long launchedAt;

    @JsonProperty("fdv")
    private String fdv;

    @JsonProperty("mcap")
    private String marketcap;

    @JsonProperty("ts")
    private String totalSupply;

    @JsonProperty("bs")
    private String burnSupply;

    @JsonProperty("cs")
    private String circulatingSupply;

    @JsonProperty("liqUsd")
    private String liquidityUsd;

    @JsonProperty("liq")
    private String liquidity;

    @JsonProperty("hld")
    private Long holders;

    @JsonProperty("p")
    private String priceUsd;

    @JsonProperty("bcr")
    private Double bondingCurveRatio;

    @JsonProperty("sts")
    private List<DataHubTokenStatsDTO> stats;

    @JsonProperty("tsrc")
    private String poolSource;

    @JsonProperty("rl")
    private String riskLevel;


    @JsonProperty("pt")
    private Long priceTime;
}