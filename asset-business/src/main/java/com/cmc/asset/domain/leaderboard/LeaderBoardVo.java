package com.cmc.asset.domain.leaderboard;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/7/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeaderBoardVo {
    private Integer rank;
    private Integer accumulatedDiamonds;
    private String userName;
    private String avatarId;
    private Integer index;
    @JsonInclude(Include.NON_NULL)
    private Integer preRank;
    @JsonInclude(Include.NON_NULL)
    private Integer preAccumulatedDiamonds;
    @JsonInclude(Include.NON_NULL)
    private Integer preIndex;
    @JsonInclude(Include.NON_NULL)
    private String preUserName;
    @JsonInclude(Include.NON_NULL)
    private String preAvatarId;
    @JsonInclude(Include.NON_NULL)
    private Integer nextRank;
    @JsonInclude(Include.NON_NULL)
    private Integer nextAccumulatedDiamonds;
    @JsonInclude(Include.NON_NULL)
    private String nextUserName;
    @JsonInclude(Include.NON_NULL)
    private String nextAvatarId;
    @JsonInclude(Include.NON_NULL)
    private Integer nextIndex;
}
