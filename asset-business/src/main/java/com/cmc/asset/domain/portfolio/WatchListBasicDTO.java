package com.cmc.asset.domain.portfolio;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WatchListBasicDTO
 * <AUTHOR> ricky.x
 * @date : 2023/8/15 12:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatchListBasicDTO {

    private String watchListId;

    private List<Integer> cryptoCurrencies;

    private List<Integer> exchanges;

    private List<Integer> marketPairs;

    private List<Long> dexPairIds;
}

