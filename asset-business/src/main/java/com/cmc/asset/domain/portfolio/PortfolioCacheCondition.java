package com.cmc.asset.domain.portfolio;

import com.cmc.asset.model.common.PageDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQueryResultDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2020/12/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PortfolioCacheCondition extends PageDTO {
    private String userId;
    private String redisKey;
    private Duration expire;
}
