package com.cmc.asset.domain.client.content;

import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/9/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetUserInfoParamDTO{

    /**
     * platform
     */
    @Pattern(regexp = "(^web$|^mobile$)", message = "Invalid platform")
    private String platform;
}
