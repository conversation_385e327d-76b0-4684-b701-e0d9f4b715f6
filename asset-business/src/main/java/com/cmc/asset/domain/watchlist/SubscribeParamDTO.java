package com.cmc.asset.domain.watchlist;

import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/19 17:27
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubscribeParamDTO {

    private String userId;

    private Long uid;
    /**
     * subscribe | unsubscribe
     */
    private SubscribeTypeEnum subscribeType;

    /**
     * exchange | crypto | marketpair
     */
    private ResourceTypeEnum resourceType;

    private List<Long> resourceIds;

    private List<String> dexTokenAddress;

    private String watchListId;

    private Boolean shared;

    private Boolean needReplaceAllResourceIds;

    private Long guestId;

}
