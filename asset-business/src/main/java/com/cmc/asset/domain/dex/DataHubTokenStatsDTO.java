package com.cmc.asset.domain.dex;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * DataHubTokenStatsDTO
 * <AUTHOR> ricky.x
 * @date: 2025/7/2 00:25
 */
@Data
public class DataHubTokenStatsDTO {
    @JsonProperty("tp")
    private String type;

    @JsonProperty("vu")
    private String volume;

    @JsonProperty("txs")
    private Long txs;

    @JsonProperty("nb")
    private Long numBuy;
    @JsonProperty("ns")
    private Long numSell;
    @JsonProperty("bvu")
    private String buyVolume;
    @JsonProperty("svu")
    private String sellVolume;
    @JsonProperty("but")
    private Long buyers;
    @JsonProperty("sut")
    private Long sellers;

    @JsonProperty("pc")
    private Float priceChangeRate;

    @JsonProperty("ut")
    private Long uniqueTraders;
}