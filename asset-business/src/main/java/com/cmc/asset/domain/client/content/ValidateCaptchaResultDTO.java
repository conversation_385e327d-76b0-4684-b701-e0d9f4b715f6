package com.cmc.asset.domain.client.content;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/3/19
 * @description VO for validate result
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidateCaptchaResultDTO {
    private Boolean success;
    private String hostname;
    @JsonProperty("challenge_ts")
    private Date challengeTs;
}
