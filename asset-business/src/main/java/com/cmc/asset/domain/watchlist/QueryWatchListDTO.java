package com.cmc.asset.domain.watchlist;

import com.cmc.asset.model.contract.watchlist.WatchListResultDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/10/20 14:04
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryWatchListDTO {

    private String userId;

    private String userName;

    private Boolean verifiedByCmc;

    private String watchListId;

    /**
     *  ORDINARY,FOLLOWED; default ORDINARY
     */
    private WatchListResultDTO.WatchListType watchListType;

    /**
     * 1 crypto 2 exchange, default 1.,4 get all the main watchlist's crypto
     */
    private Integer aux;

    private Integer start;

    private Integer limit;

    private String convertIds;

    private String cryptoAux;

    private Boolean isMain;

    private Long guestId;

    private Boolean containDexToken;
}
