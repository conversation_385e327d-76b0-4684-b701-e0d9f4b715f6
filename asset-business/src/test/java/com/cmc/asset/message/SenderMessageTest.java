package com.cmc.asset.message;

import com.cmc.asset.message.SenderMessage.SenderMessageBuilder;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class SenderMessageTest {

    private SenderMessage<Object> senderMessageUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        senderMessageUnderTest = new SenderMessage<>("topic", "userId", null);
        senderMessageUnderTest.setTopic("");
        senderMessageUnderTest.setUserId("");
        senderMessageUnderTest.setData(new Object());
        senderMessageUnderTest = new SenderMessage<>();
    }

    @Test
    public void testEquals() throws Exception {
        assertTrue(senderMessageUnderTest.equals(new SenderMessage<>()));
    }

    @Test
    public void testCanEqual() throws Exception {
        assertFalse(senderMessageUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() throws Exception {
        assertNotEquals(0, senderMessageUnderTest.hashCode());
    }

    @Test
    public void testToString() throws Exception {
        assertNotEquals("result", senderMessageUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final SenderMessageBuilder result = SenderMessage.builder();
        result.toString();
        SenderMessage senderMessage = SenderMessage.builder().topic("").userId("").data(new Object()).build();
        // Verify the results
    }
}
