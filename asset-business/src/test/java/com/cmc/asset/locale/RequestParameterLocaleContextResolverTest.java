package com.cmc.asset.locale;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import java.net.URI;
import java.security.Principal;
import java.time.Instant;
import java.util.Map;
import java.util.function.Function;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContext;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpHeaders;
import org.springframework.http.codec.multipart.Part;
import org.springframework.http.server.RequestPath;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebSession;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class RequestParameterLocaleContextResolverTest {

    private RequestParameterLocaleContextResolver requestParameterLocaleContextResolverUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        requestParameterLocaleContextResolverUnderTest = new RequestParameterLocaleContextResolver();
    }

    @Test
    public void testResolveLocaleContext() {
        // Setup
        ServerHttpRequest serverHttpRequest = new ServerHttpRequest() {
            @Override
            public String getId() {return null;}
            @Override
            public RequestPath getPath() {return null;}
            @Override
            public MultiValueMap<String, String> getQueryParams() {return new LinkedMultiValueMap();}
            @Override
            public MultiValueMap<String, HttpCookie> getCookies() {return null;}
            @Override
            public String getMethodValue() {return null;}
            @Override
            public URI getURI() {return null;}
            @Override
            public Flux<DataBuffer> getBody() {return null;}
            @Override
            public HttpHeaders getHeaders() {return null;}
        };
        final ServerWebExchange exchange = new ServerWebExchange() {
            @Override
            public ServerHttpRequest getRequest() {return serverHttpRequest;}
            @Override
            public ServerHttpResponse getResponse() {return null;}
            @Override
            public Map<String, Object> getAttributes() {return null;}
            @Override
            public Mono<WebSession> getSession() {return null;}
            @Override
            public <T extends Principal> Mono<T> getPrincipal() {return null;}
            @Override
            public Mono<MultiValueMap<String, String>> getFormData() {return null;}
            @Override
            public Mono<MultiValueMap<String, Part>> getMultipartData() {return null;}
            @Override
            public LocaleContext getLocaleContext() {return null;}
            @Override
            public ApplicationContext getApplicationContext() {return null;}
            @Override
            public boolean isNotModified() {return false;}
            @Override
            public boolean checkNotModified(Instant instant) {return false;}
            @Override
            public boolean checkNotModified(String s) {return false;}
            @Override
            public boolean checkNotModified(String s, Instant instant) {return false;}
            @Override
            public String transformUrl(String s) {return null;}
            @Override
            public void addUrlTransformer(Function<String, String> function) {}
            @Override
            public String getLogPrefix() {return null;}
        };

        // Run the test
        final LocaleContext result = requestParameterLocaleContextResolverUnderTest.resolveLocaleContext(exchange);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testSetLocaleContext() {
        // Setup
        // Run the test
        try {
            requestParameterLocaleContextResolverUnderTest.setLocaleContext(null, null);
        } catch (Exception e) {

        }

    }
}
