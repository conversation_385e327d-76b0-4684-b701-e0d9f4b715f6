package com.cmc.asset.domain.portfolio;

import com.cmc.asset.domain.portfolio.PortfolioCacheCondition.PortfolioCacheConditionBuilder;
import java.time.Duration;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class PortfolioCacheConditionTest {

    private PortfolioCacheCondition portfolioCacheConditionUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        portfolioCacheConditionUnderTest = new PortfolioCacheCondition("userId", "redisKey",
            Duration.ofDays(0L));
        portfolioCacheConditionUnderTest.setUserId("");
        portfolioCacheConditionUnderTest.setRedisKey("");
        portfolioCacheConditionUnderTest.setExpire(Duration.ofDays(0L));
        portfolioCacheConditionUnderTest.setCurrentPage(0);
        portfolioCacheConditionUnderTest.setPageSize(0);
        portfolioCacheConditionUnderTest = new PortfolioCacheCondition();

    }

    @Test
    public void testEquals() throws Exception {
        assertTrue(portfolioCacheConditionUnderTest.equals(new PortfolioCacheCondition()));
    }

    @Test
    public void testCanEqual() throws Exception {
        assertFalse(portfolioCacheConditionUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() throws Exception {
        assertNotEquals(0, portfolioCacheConditionUnderTest.hashCode());
    }

    @Test
    public void testToString() throws Exception {
        assertNotEquals("result", portfolioCacheConditionUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final PortfolioCacheConditionBuilder result = PortfolioCacheCondition.builder();
        result.toString();
        PortfolioCacheCondition portfolioCacheCondition =
            PortfolioCacheCondition.builder().userId("").redisKey("").expire(Duration.ofDays(1))
                .build();
        // Verify the results
    }
}
