package com.cmc.asset.domain.leaderboard;

import com.cmc.asset.domain.leaderboard.LeaderBoardVo.LeaderBoardVoBuilder;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class LeaderBoardVoTest {

    private LeaderBoardVo leaderBoardVoUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        leaderBoardVoUnderTest =
            new LeaderBoardVo(0, 0, "userName", "avatarId", 0, 0, 0, 0, "preUserName", "preAvatarId", 0, 0,
                "nextUserName", "nextAvatarId", 0);
        leaderBoardVoUnderTest.setRank(0);
        leaderBoardVoUnderTest.setAccumulatedDiamonds(0);
        leaderBoardVoUnderTest.setUserName("");
        leaderBoardVoUnderTest.setAvatarId("");
        leaderBoardVoUnderTest.setIndex(0);
        leaderBoardVoUnderTest.setPreRank(0);
        leaderBoardVoUnderTest.setPreAccumulatedDiamonds(0);
        leaderBoardVoUnderTest.setPreIndex(0);
        leaderBoardVoUnderTest.setPreUserName("");
        leaderBoardVoUnderTest.setPreAvatarId("");
        leaderBoardVoUnderTest.setNextRank(0);
        leaderBoardVoUnderTest.setNextAccumulatedDiamonds(0);
        leaderBoardVoUnderTest.setNextUserName("");
        leaderBoardVoUnderTest.setNextAvatarId("");
        leaderBoardVoUnderTest.setNextIndex(0);
        leaderBoardVoUnderTest = new LeaderBoardVo();
    }

    @Test
    public void testEquals() throws Exception {
        assertTrue(leaderBoardVoUnderTest.equals(new LeaderBoardVo()));
    }

    @Test
    public void testCanEqual() throws Exception {
        assertFalse(leaderBoardVoUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() throws Exception {
        assertNotEquals(0, leaderBoardVoUnderTest.hashCode());
    }

    @Test
    public void testToString() throws Exception {
        assertNotEquals("result", leaderBoardVoUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final LeaderBoardVoBuilder result = LeaderBoardVo.builder();
        result.toString();
        LeaderBoardVo leaderBoardVo =
            LeaderBoardVo.builder().rank(0).accumulatedDiamonds(0).userName("").avatarId("").index(0).preRank(0)
                .preAccumulatedDiamonds(0).preIndex(0).preUserName("").preAvatarId("").nextRank(0)
                .nextAccumulatedDiamonds(0).nextUserName("").nextAvatarId("").nextIndex(0).build();
        // Verify the results
    }
}
