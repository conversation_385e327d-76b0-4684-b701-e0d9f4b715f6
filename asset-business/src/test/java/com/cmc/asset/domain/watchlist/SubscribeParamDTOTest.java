package com.cmc.asset.domain.watchlist;
import java.util.ArrayList;

import com.cmc.asset.domain.watchlist.SubscribeParamDTO.SubscribeParamDTOBuilder;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import java.util.List;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class SubscribeParamDTOTest {

    private SubscribeParamDTO subscribeParamDTOUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        subscribeParamDTOUnderTest = new SubscribeParamDTO("userId", 1L, SubscribeTypeEnum.SUBSCRIBE,
            ResourceTypeEnum.EXCHANGE, List.of(0L),List.of(), "watchListId", false, false, 1l);
        subscribeParamDTOUnderTest.setUserId("");
        subscribeParamDTOUnderTest.setSubscribeType(SubscribeTypeEnum.SUBSCRIBE);
        subscribeParamDTOUnderTest.setResourceType(ResourceTypeEnum.EXCHANGE);
        subscribeParamDTOUnderTest.setResourceIds(new ArrayList<>());
        subscribeParamDTOUnderTest.setWatchListId("");
        subscribeParamDTOUnderTest.setShared(false);
        subscribeParamDTOUnderTest.setNeedReplaceAllResourceIds(false);
        subscribeParamDTOUnderTest = new SubscribeParamDTO();

    }

    @Test
    public void testEquals() throws Exception {
        assertTrue(subscribeParamDTOUnderTest.equals(new SubscribeParamDTO()));
    }

    @Test
    public void testCanEqual() throws Exception {
        assertFalse(subscribeParamDTOUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() throws Exception {
        assertNotEquals(0, subscribeParamDTOUnderTest.hashCode());
    }

    @Test
    public void testToString() throws Exception {
        assertNotEquals("result", subscribeParamDTOUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final SubscribeParamDTOBuilder result = SubscribeParamDTO.builder();
        result.toString();
        SubscribeParamDTO subscribeParamDTO =
            SubscribeParamDTO.builder().userId("").subscribeType(SubscribeTypeEnum.UNSUBSCRIBE)
                .resourceType(ResourceTypeEnum.CRYPTO).resourceIds(new ArrayList<>()).watchListId("").shared(false)
                .needReplaceAllResourceIds(false).build();
        // Verify the results
    }
}
