package com.cmc.asset.domain.watchlist;

import com.cmc.asset.domain.watchlist.QueryWatchListDTO.QueryWatchListDTOBuilder;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.WatchListType;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class QueryWatchListDTOTest {

    private QueryWatchListDTO queryWatchListDTOUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        queryWatchListDTOUnderTest = new QueryWatchListDTO("userId", "userName", false, "watchListId",
            WatchListType.NONE, 0, 0, 0, "convertIds", "cryptoAux", false,1l, false);
        queryWatchListDTOUnderTest.setUserId("");
        queryWatchListDTOUnderTest.setUserName("");
        queryWatchListDTOUnderTest.setVerifiedByCmc(false);
        queryWatchListDTOUnderTest.setWatchListId("");
        queryWatchListDTOUnderTest.setWatchListType(WatchListType.NONE);
        queryWatchListDTOUnderTest.setAux(0);
        queryWatchListDTOUnderTest.setStart(0);
        queryWatchListDTOUnderTest.setLimit(0);
        queryWatchListDTOUnderTest.setConvertIds("");
        queryWatchListDTOUnderTest.setCryptoAux("");
        queryWatchListDTOUnderTest.setIsMain(false);
        queryWatchListDTOUnderTest = new QueryWatchListDTO();
    }

    @Test
    public void testEquals() throws Exception {
        assertTrue(queryWatchListDTOUnderTest.equals(new QueryWatchListDTO()));
    }

    @Test
    public void testCanEqual() throws Exception {
        assertFalse(queryWatchListDTOUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() throws Exception {
        assertNotEquals(0, queryWatchListDTOUnderTest.hashCode());
    }

    @Test
    public void testToString() throws Exception {
        assertNotEquals("result", queryWatchListDTOUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final QueryWatchListDTOBuilder result = QueryWatchListDTO.builder();
        result.toString();
        QueryWatchListDTO queryWatchListDTO =
            QueryWatchListDTO.builder().userId("").userName("").verifiedByCmc(false).watchListId("")
                .watchListType(WatchListType.NONE).aux(0).start(0).limit(0).convertIds("").cryptoAux("").isMain(false)
                .build();

        // Verify the results
    }
}
