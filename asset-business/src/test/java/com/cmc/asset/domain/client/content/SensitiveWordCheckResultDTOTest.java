package com.cmc.asset.domain.client.content;

import com.cmc.asset.domain.client.content.SensitiveWordCheckResultDTO.SensitiveWordCheckResultDTOBuilder;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class SensitiveWordCheckResultDTOTest {

    private SensitiveWordCheckResultDTO sensitiveWordCheckResultDTOUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        sensitiveWordCheckResultDTOUnderTest = new SensitiveWordCheckResultDTO(false);
        sensitiveWordCheckResultDTOUnderTest.setState(false);
        sensitiveWordCheckResultDTOUnderTest = new SensitiveWordCheckResultDTO();
    }

    @Test
    public void testEquals() {
        assertTrue(sensitiveWordCheckResultDTOUnderTest.equals(new SensitiveWordCheckResultDTO()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(sensitiveWordCheckResultDTOUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, sensitiveWordCheckResultDTOUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", sensitiveWordCheckResultDTOUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final SensitiveWordCheckResultDTOBuilder result = SensitiveWordCheckResultDTO.builder();
        result.toString();
        SensitiveWordCheckResultDTO sensitiveWordCheckResultDTO =
            SensitiveWordCheckResultDTO.builder().state(false).build();
        // Verify the results
    }
}
