package com.cmc.asset.domain.crypto;

import com.cmc.asset.domain.crypto.CryptoListingsRequestDTO.CryptoListingsRequestDTOBuilder;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class CryptoListingsRequestDTOTest {

    private CryptoListingsRequestDTO cryptoListingsRequestDTOUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        cryptoListingsRequestDTOUnderTest = new CryptoListingsRequestDTO("ids", "aux", "convertIds");
        cryptoListingsRequestDTOUnderTest.setIds("");
        cryptoListingsRequestDTOUnderTest.setAux("");
        cryptoListingsRequestDTOUnderTest.setConvertIds("");
        cryptoListingsRequestDTOUnderTest = new CryptoListingsRequestDTO();
    }

    @Test
    public void testEquals() throws Exception {
        assertTrue(cryptoListingsRequestDTOUnderTest.equals(new CryptoListingsRequestDTO()));
    }

    @Test
    public void testCanEqual() throws Exception {
        assertFalse(cryptoListingsRequestDTOUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() throws Exception {
        assertNotEquals(0, cryptoListingsRequestDTOUnderTest.hashCode());
    }

    @Test
    public void testToString() throws Exception {
        assertNotEquals("result", cryptoListingsRequestDTOUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final CryptoListingsRequestDTOBuilder result = CryptoListingsRequestDTO.builder();
        result.toString();
        CryptoListingsRequestDTO cryptoListingsRequestDTO =
            CryptoListingsRequestDTO.builder().ids("").aux("").convertIds("").build();
        // Verify the results
    }
}
