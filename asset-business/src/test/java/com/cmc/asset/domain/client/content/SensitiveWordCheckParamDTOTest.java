package com.cmc.asset.domain.client.content;
import java.util.ArrayList;

import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class SensitiveWordCheckParamDTOTest {

    private SensitiveWordCheckParamDTO sensitiveWordCheckParamDTOUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        sensitiveWordCheckParamDTOUnderTest = new SensitiveWordCheckParamDTO();
        sensitiveWordCheckParamDTOUnderTest.setTexts(new ArrayList<>());
        sensitiveWordCheckParamDTOUnderTest = new SensitiveWordCheckParamDTO();
    }

    @Test
    public void testEquals() {
        assertTrue(sensitiveWordCheckParamDTOUnderTest.equals(new SensitiveWordCheckParamDTO()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(sensitiveWordCheckParamDTOUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, sensitiveWordCheckParamDTOUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", sensitiveWordCheckParamDTOUnderTest.toString());
    }
}
