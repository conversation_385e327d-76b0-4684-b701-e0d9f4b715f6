package com.cmc.asset.domain.avatar;

import com.cmc.asset.domain.avatar.AvatarVO.AvatarVOBuilder;
import java.util.Date;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class AvatarVOTest {
    private AvatarVO avatarVOUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        avatarVOUnderTest = new AvatarVO("id", "name", 0, false, 0, new Date(), new Date());
        avatarVOUnderTest.setId("");
        avatarVOUnderTest.setName("");
        avatarVOUnderTest.setIsReward(0);
        avatarVOUnderTest.setIsActive(false);
        avatarVOUnderTest.setSort(0);
        avatarVOUnderTest.setCreateTime(new Date());
        avatarVOUnderTest.setUpdateTime(new Date());
        avatarVOUnderTest = new AvatarVO();

    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testEquals() {
        assertTrue(avatarVOUnderTest.equals(new AvatarVO()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(avatarVOUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, avatarVOUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", avatarVOUnderTest.toString());
    }

    @Test
    public void testBuilder() throws Exception {
        // Setup
        // Run the test
        final AvatarVOBuilder result = AvatarVO.builder();
        result.toString();
        AvatarVO avatarVO =
            AvatarVO.builder().id("").name("").isReward(0).isActive(false).sort(0).createTime(new Date())
                .updateTime(new Date()).build();
        // Verify the results
    }
}
