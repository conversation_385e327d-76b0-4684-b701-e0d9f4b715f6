package com.cmc.asset.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class ChainAddressUtilTest {

    @Test
    void testIsEthValidAddress() {
        assertThat(ChainAddressUtil.isEthValidAddress("input")).isFalse();
    }

    @Test
    void testIsValidAddress() {
        assertThat(ChainAddressUtil.isValidAddress("******************************************")).isTrue();
    }

    @Test
    void testGetSupportPlatformIds() {
        List<Integer> supportPlatformIds = ChainAddressUtil.getSupportPlatformIds("******************************************");
        log.info("supportPlatformIds: {}", supportPlatformIds);
        assertThat(supportPlatformIds).isNotEmpty();
    }
}
