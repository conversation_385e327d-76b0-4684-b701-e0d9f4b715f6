package com.cmc.asset.utils;

import java.util.List;
import java.util.Set;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
public class CommonUtilsTest {

    @Test
    public void testToLongList() {
        List<Long> list = CommonUtils.toLongList("", ",");
        Assert.assertNull(list);
        List<Long> list1 = CommonUtils.toLongList("1,2,3", ",");
        Assert.assertEquals(list1.size(), 3);
    }

    @Test
    public void testGetExistAssetSize() {
        Integer existAssetSize = CommonUtils.getExistAssetSize(null, null);
        Assert.assertEquals((int)existAssetSize, 0);
        Integer existAssetSize1 = CommonUtils.getExistAssetSize(List.of(1L, 2L, 3L), null);
        Assert.assertEquals((int)existAssetSize1, 3);
        Integer existAssetSize2 = CommonUtils.getExistAssetSize(null, Set.of(1, 2, 3));
        Assert.assertEquals((int)existAssetSize2, 3);
    }
}
