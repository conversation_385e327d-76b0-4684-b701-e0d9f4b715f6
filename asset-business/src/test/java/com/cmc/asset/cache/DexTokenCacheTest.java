package com.cmc.asset.cache;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.cmc.asset.business.portfolio.DexerService;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

public class DexTokenCacheTest {
    private DexerService dexerService;
    private DexTokenCache dexTokenCache;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        dexerService = mock(DexerService.class);
        dexTokenCache = new DexTokenCache(100, 1);
        // English: Inject mock DexerService by reflection
        ReflectionTestUtils.setField(dexTokenCache, "dexerService", dexerService);
    }

    @Test
    public void testLoad() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xabc");
        dto.setPlatformDexerName("eth");

        // Mock the new interface that accepts List<BatchPlatformTokenRequestDTO.PlatformTokenDTO>
        when(dexerService.getPairsFromDeQuery(argThat(tokens ->
            tokens != null && tokens.size() == 1 &&
            "eth".equals(tokens.get(0).getPlatform()) &&
            "0xabc".equals(tokens.get(0).getAddress())
        ))).thenReturn(Mono.just(List.of(dto)));

        Mono<TokenPriceDTO> result = dexTokenCache.load("eth_0xabc");
        TokenPriceDTO value = result.block();
        Assert.assertNotNull(value);
        Assert.assertEquals(value.getAddress(), "0xabc");
    }

    @Test
    public void testLoadAll() {
        TokenPriceDTO dto1 = new TokenPriceDTO();
        dto1.setAddress("0xabc");
        dto1.setPlatformDexerName("eth");

        TokenPriceDTO dto2 = new TokenPriceDTO();
        dto2.setAddress("0xdef");
        dto2.setPlatformDexerName("eth");

        // Mock the new interface that accepts List<BatchPlatformTokenRequestDTO.PlatformTokenDTO>
        when(dexerService.getPairsFromDeQuery(argThat(tokens ->
            tokens != null && tokens.size() == 2 &&
            tokens.stream().anyMatch(t -> "eth".equals(t.getPlatform()) && "0xabc".equals(t.getAddress())) &&
            tokens.stream().anyMatch(t -> "eth".equals(t.getPlatform()) && "0xdef".equals(t.getAddress()))
        ))).thenReturn(Mono.just(List.of(dto1, dto2)));

        List<String> keys = List.of("eth_0xabc", "eth_0xdef");
        Mono<Map<String, TokenPriceDTO>> result = dexTokenCache.loadAll(keys);
        Map<String, TokenPriceDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertEquals(map.size(), 2);
        Assert.assertTrue(map.containsKey("eth_0xabc"));
        Assert.assertTrue(map.containsKey("eth_0xdef"));
    }

    @Test
    public void testGetAllByPlatformAndAddresses() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xabc");
        dto.setPlatformDexerName("bsc");

        // Mock the new interface that accepts List<BatchPlatformTokenRequestDTO.PlatformTokenDTO>
        when(dexerService.getPairsFromDeQuery(argThat(tokens ->
            tokens != null && tokens.size() == 1 &&
            "bsc".equals(tokens.get(0).getPlatform()) &&
            "0xabc".equals(tokens.get(0).getAddress())
        ))).thenReturn(Mono.just(List.of(dto)));

        Mono<Map<String, TokenPriceDTO>> result = dexTokenCache.getAll("bsc", Set.of("0xabc"));
        Map<String, TokenPriceDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertTrue(map.containsKey("bsc_0xabc"));
    }

    @Test
    public void testGetAllBySetOfStringArray() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xaaa");
        dto.setPlatformDexerName("op");

        // Mock the new interface that accepts List<BatchPlatformTokenRequestDTO.PlatformTokenDTO>
        when(dexerService.getPairsFromDeQuery(argThat(tokens ->
            tokens != null && tokens.size() == 1 &&
            "op".equals(tokens.get(0).getPlatform()) &&
            "0xaaa".equals(tokens.get(0).getAddress())
        ))).thenReturn(Mono.just(List.of(dto)));

        Set<String[]> input = Set.<String[]>of(new String[]{"op", "0xaaa"});
        Mono<Map<String, TokenPriceDTO>> result = dexTokenCache.getAll(input);
        Map<String, TokenPriceDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertTrue(map.containsKey("op_0xaaa"));
    }
}