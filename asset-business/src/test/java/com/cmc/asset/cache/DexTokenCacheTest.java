package com.cmc.asset.cache;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.cmc.asset.business.portfolio.DexerService;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

public class DexTokenCacheTest {
    private DexerService dexerService;
    private DexTokenCache dexTokenCache;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        dexerService = mock(DexerService.class);
        dexTokenCache = new DexTokenCache(100, 1);
        // English: Inject mock DexerService by reflection
        ReflectionTestUtils.setField(dexTokenCache, "dexerService", dexerService);
    }

    @Test
    public void testLoad() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xabc");
        when(dexerService.getPairsFromDeQuery(eq("eth"), eq(List.of("0xabc"))))
                .thenReturn(Flux.just(dto).collectList());
        Mono<TokenPriceDTO> result = dexTokenCache.load("eth_0xabc");
        TokenPriceDTO value = result.block();
        Assert.assertNotNull(value);
        Assert.assertEquals(value.getAddress(), "0xabc");
    }

    @Test
    public void testLoadAll() {
        TokenPriceDTO dto1 = new TokenPriceDTO();
        dto1.setAddress("0xabc");
        TokenPriceDTO dto2 = new TokenPriceDTO();
        dto2.setAddress("0xdef");
        when(dexerService.getPairsFromDeQuery(eq("eth"), eq(List.of("0xabc", "0xdef"))))
                .thenReturn(Flux.just(dto1, dto2).collectList());
        List<String> keys = List.of("eth_0xabc", "eth_0xdef");
        Mono<Map<String, TokenPriceDTO>> result = dexTokenCache.loadAll(keys);
        Map<String, TokenPriceDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertEquals(map.size(), 2);
        Assert.assertTrue(map.containsKey("eth_0xabc"));
        Assert.assertTrue(map.containsKey("eth_0xdef"));
    }

    @Test
    public void testGetAllByPlatformAndAddresses() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xabc");
        when(dexerService.getPairsFromDeQuery(eq("bsc"), eq(List.of("0xabc"))))
                .thenReturn(Flux.just(dto).collectList());
        Mono<Map<String, TokenPriceDTO>> result = dexTokenCache.getAll("bsc", Set.of("0xabc"));
        Map<String, TokenPriceDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertTrue(map.containsKey("bsc_0xabc"));
    }

    @Test
    public void testGetAllBySetOfStringArray() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xaaa");
        when(dexerService.getPairsFromDeQuery(eq("op"), eq(List.of("0xaaa"))))
                .thenReturn(Flux.just(dto).collectList());
        Set<String[]> input = Set.<String[]>of(new String[]{"op", "0xaaa"});
        Mono<Map<String, TokenPriceDTO>> result = dexTokenCache.getAll(input);
        Map<String, TokenPriceDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertTrue(map.containsKey("op_0xaaa"));
    }
}