package com.cmc.asset.cache;

import reactor.core.publisher.Mono;

import com.cmc.asset.business.CryptoInfoServiceImpl;
import com.cmc.asset.domain.exchange.ExchangeInfoEntity;
import com.google.common.cache.LoadingCache;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;

public class StableCoinCacheTest {

    @Mock
    private CryptoInfoServiceImpl mockCryptoInfoService;

    @InjectMocks
    private StableCoinCache stableCoinCacheUnderTest;

    private AutoCloseable mockitoCloseable;

    private LoadingCache<String, Map<Integer, Integer>> loadingCache;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        loadingCache = Mockito.mock(LoadingCache.class);
        ReflectionTestUtils.setField(stableCoinCacheUnderTest, "loadingCache", loadingCache);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testLoad() {
        // Setup
        final Map<Integer, Integer> expectedResult = Map.ofEntries(Map.entry(0, 0));
        when(mockCryptoInfoService.getAllStableCoin()).thenReturn(Mono.just(List.of(0)));

        // Run the test
        final Map<Integer, Integer> result = stableCoinCacheUnderTest.load("key");

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testPreLoad() throws ExecutionException {
        // Setup
        // Run the test
        when(loadingCache.get(any())).thenReturn(new HashMap<>());
        stableCoinCacheUnderTest.preLoad();

        // Verify the results
    }

    @Test
    public void testGetCryptoCurrencies() throws ExecutionException {
        // Setup
        final Map<Integer, Integer> expectedResult = Map.ofEntries(Map.entry(0, 0));

        when(loadingCache.get(any())).thenReturn(expectedResult);

        // Run the test
        final Map<Integer, Integer> result = stableCoinCacheUnderTest.getCryptoCurrencies();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetById() throws ExecutionException {
        // Setup
        final Map<Integer, Integer> expectedResult = Map.ofEntries(Map.entry(0, 0));

        when(loadingCache.get(any())).thenReturn(expectedResult);

        // Run the test
        final Integer result = stableCoinCacheUnderTest.getById(0);

        // Verify the results
        assertEquals(Optional.ofNullable(result), Optional.of(0));
    }
}
