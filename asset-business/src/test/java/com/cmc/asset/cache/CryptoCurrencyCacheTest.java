package com.cmc.asset.cache;

import com.cmc.asset.business.CryptoInfoServiceImpl;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.domain.crypto.CryptoCurrencyMapDTO;
import com.google.common.cache.LoadingCache;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;

public class CryptoCurrencyCacheTest {

    @Mock
    private CryptoInfoServiceImpl mockCryptoInfoService;

    @InjectMocks
    private CryptoCurrencyCache cryptoCurrencyCacheUnderTest;

    private AutoCloseable mockitoCloseable;

    private LoadingCache<String, Map<Integer, CryptoCurrencyInfoDTO>> loadingCache;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        loadingCache = Mockito.mock(LoadingCache.class);
        ReflectionTestUtils.setField(cryptoCurrencyCacheUnderTest, "loadingCache", loadingCache);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testLoad() {
        // Setup
        final Map<Integer, CryptoCurrencyInfoDTO> expectedResult = Map.ofEntries(
            Map.entry(0, new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0)));

        // Configure CryptoInfoServiceImpl.getAllCryptoCurrencies(...).
        final Flux<List<CryptoCurrencyMapDTO>> listFlux = Flux.just(
            List.of(new CryptoCurrencyMapDTO(0, "name", "symbol", "slug", "category", 0, "status", "first_historical_data",
                "last_historical_data", 0 , null, null)));
        when(mockCryptoInfoService.getAllCryptoCurrencies()).thenReturn(listFlux);

        // Run the test
        final Map<Integer, CryptoCurrencyInfoDTO> result = cryptoCurrencyCacheUnderTest.load("key");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPreLoad() throws ExecutionException {
        // Setup
        // Run the test
        when(loadingCache.get(any())).thenReturn(new HashMap<>());
        cryptoCurrencyCacheUnderTest.preLoad();

        // Verify the results
    }

    @Test
    public void testGetCryptoCurrencies() throws ExecutionException {
        // Setup
        final Map<Integer, CryptoCurrencyInfoDTO> expectedResult = Map.ofEntries(
            Map.entry(0, new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0)));

        when(loadingCache.get(any())).thenReturn(expectedResult);

        // Run the test
        final Map<Integer, CryptoCurrencyInfoDTO> result = cryptoCurrencyCacheUnderTest.getCryptoCurrencies();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetById() throws ExecutionException {
        // Setup
        final CryptoCurrencyInfoDTO expectedResult =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);

        when(loadingCache.get(any())).thenReturn(new HashMap<>(){{ put(0, expectedResult); }});

        // Run the test
        final CryptoCurrencyInfoDTO result = cryptoCurrencyCacheUnderTest.getById(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetByIds() throws ExecutionException {
        // Setup
        // Run the test
        final CryptoCurrencyInfoDTO expectedResult =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);

        when(loadingCache.get(any())).thenReturn(new HashMap<>(){{ put(0, expectedResult); }});
        final Set<Integer> result = cryptoCurrencyCacheUnderTest.getByIds(List.of(0));
        // Verify the results

        assertEquals(Set.of(0), result);
    }

    @Test
    public void testGetCryptoCurrencyById() throws ExecutionException {
        // Setup
        final CryptoCurrencyInfoDTO expectedResult =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);

        when(loadingCache.get(any())).thenReturn(new HashMap<>(){{ put(0, expectedResult); }});
        // Run the test
        final CryptoCurrencyInfoDTO result = cryptoCurrencyCacheUnderTest.getCryptoCurrencyById(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
