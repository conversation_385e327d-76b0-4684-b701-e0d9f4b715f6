package com.cmc.asset.config;
import java.util.ArrayList;
import java.util.HashSet;

import com.cmc.asset.config.CryptoDataPointAssetServiceMigrateConfig.CryptoMigrateGrayConfig;
import com.cmc.data.common.utils.JacksonUtils;
import com.ctrip.framework.apollo.enums.PropertyChangeType;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import java.util.List;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertFalse;

public class CryptoDataPointAssetServiceMigrateConfigTest {

    @InjectMocks
    private CryptoDataPointAssetServiceMigrateConfig cryptoDataPointAssetServiceMigrateConfigUnderTest;
    @Mock
    private Environment environment;
    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        CurrencyPopularConfig currencyPopularConfig = new CurrencyPopularConfig();
        currencyPopularConfig.equals(new CurrencyPopularConfig());
        currencyPopularConfig.hashCode();
        currencyPopularConfig.setFiatIds(new HashSet<>());
        currencyPopularConfig.setCryptoIds(new HashSet<>());
        CryptoMigrateGrayConfig.builder().cryptoMigrateEnum("").includeCryptoIds(new ArrayList<>()).startCryptoId(0)
            .endCryptoId(0).excludeCryptoIds(new ArrayList<>()).callBaseData(false).build();


    }

    @Test
    public void testValid() {
        // Setup
        when(environment.getProperty(anyString(), anyString())).thenReturn("");
        // Run the test
        cryptoDataPointAssetServiceMigrateConfigUnderTest.valid();

        CryptoMigrateGrayConfig cryptoMigrateGrayConfig = new CryptoMigrateGrayConfig();
        cryptoMigrateGrayConfig.equals(new CryptoMigrateGrayConfig());
        cryptoMigrateGrayConfig.hashCode();
        cryptoMigrateGrayConfig.canEqual(new CryptoMigrateGrayConfig());
        cryptoMigrateGrayConfig.setCryptoMigrateEnum("redis");
        when(environment.getProperty(anyString(), anyString())).thenReturn(JacksonUtils.toJsonString(cryptoMigrateGrayConfig));
        // Run the test
        cryptoDataPointAssetServiceMigrateConfigUnderTest.valid();

        CryptoMigrateGrayConfig cryptoMigrateGrayConfig2 = new CryptoMigrateGrayConfig();
        cryptoMigrateGrayConfig2.setCryptoMigrateEnum("111");
        when(environment.getProperty(anyString(), anyString())).thenReturn(JacksonUtils.toJsonString(cryptoMigrateGrayConfig2));
        // Run the test
        cryptoDataPointAssetServiceMigrateConfigUnderTest.valid();
    }

    @Test
    public void testChange() {
        // Setup
        final ConfigChangeEvent changeEvent = new ConfigChangeEvent("namespace", Map.ofEntries(
            Map.entry("com.cmc.asset.crypto.datapoint.migrate.strategy",
                new ConfigChange("namespace", "propertyName", "oldValue", "", PropertyChangeType.ADDED))));

        // Run the test
        cryptoDataPointAssetServiceMigrateConfigUnderTest.change(changeEvent);

        // Verify the results
    }

    @Test
    public void testCallBaseData() {
        // Setup
        // Run the test

        Assert.assertTrue(cryptoDataPointAssetServiceMigrateConfigUnderTest.callBaseData());

        CryptoMigrateGrayConfig cryptoMigrateGrayConfig = new CryptoMigrateGrayConfig();
        cryptoMigrateGrayConfig.setCallBaseData(true);
        ReflectionTestUtils.setField(cryptoDataPointAssetServiceMigrateConfigUnderTest, "cryptoMigrateGrayConfig", cryptoMigrateGrayConfig);

        Assert.assertTrue(cryptoDataPointAssetServiceMigrateConfigUnderTest.callBaseData());

    }

    @Test
    public void testAllowRedis() {
        // Setup
        // Run the test
        Assert.assertTrue(cryptoDataPointAssetServiceMigrateConfigUnderTest.allowRedis(null));

        CryptoMigrateGrayConfig cryptoMigrateGrayConfig = new CryptoMigrateGrayConfig();
        cryptoMigrateGrayConfig.setCryptoMigrateEnum("ddb");
        cryptoMigrateGrayConfig.setExcludeCryptoIds(List.of(1));
        ReflectionTestUtils.setField(cryptoDataPointAssetServiceMigrateConfigUnderTest, "cryptoMigrateGrayConfig", cryptoMigrateGrayConfig);
        Assert.assertTrue(cryptoDataPointAssetServiceMigrateConfigUnderTest.allowRedis(1));

        cryptoMigrateGrayConfig = new CryptoMigrateGrayConfig();
        cryptoMigrateGrayConfig.setCryptoMigrateEnum("ddb");
        cryptoMigrateGrayConfig.setIncludeCryptoIds(List.of(1));
        ReflectionTestUtils.setField(cryptoDataPointAssetServiceMigrateConfigUnderTest, "cryptoMigrateGrayConfig", cryptoMigrateGrayConfig);
        Assert.assertFalse(cryptoDataPointAssetServiceMigrateConfigUnderTest.allowRedis(1));

        cryptoMigrateGrayConfig = new CryptoMigrateGrayConfig();
        cryptoMigrateGrayConfig.setCryptoMigrateEnum("ddb");
        cryptoMigrateGrayConfig.setStartCryptoId(0);
        cryptoMigrateGrayConfig.setEndCryptoId(2);
        ReflectionTestUtils.setField(cryptoDataPointAssetServiceMigrateConfigUnderTest, "cryptoMigrateGrayConfig", cryptoMigrateGrayConfig);
        Assert.assertFalse(cryptoDataPointAssetServiceMigrateConfigUnderTest.allowRedis(1));


    }

}
