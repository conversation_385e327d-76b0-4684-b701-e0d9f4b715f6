package com.cmc.asset.integration;

import com.cmc.asset.integration.AsynHttpClient.DefaultWebClient;
import org.testng.annotations.BeforeMethod;
import static org.mockito.Mockito.mock;

public class BaseServiceClientTest {

    private BaseServiceClient baseServiceClientUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        baseServiceClientUnderTest = new BaseServiceClient() {
        };
        baseServiceClientUnderTest.client = mock(DefaultWebClient.class);
    }
}
