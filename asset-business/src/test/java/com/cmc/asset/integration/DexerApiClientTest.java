package com.cmc.asset.integration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.dexer.PlatformDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.data.common.ApiResponse;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.framework.utils.JacksonUtils;
import java.util.List;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class DexerApiClientTest {

    private DexerApiClient dexerApiClientUnderTest;
    private AsynHttpClient.DefaultWebClient mockClient;

    @BeforeMethod
    public void setUp() {
        dexerApiClientUnderTest = new DexerApiClient();
        mockClient = Mockito.mock(AsynHttpClient.DefaultWebClient.class);
        ReflectionTestUtils.setField(dexerApiClientUnderTest, "client", mockClient);
        ReflectionTestUtils.setField(dexerApiClientUnderTest, "dexerBaseUrl", "https://dexer-api.example.com");
        ReflectionTestUtils.setField(dexerApiClientUnderTest, "dqueryServiceBaseUrl", "https://dquery-api.example.com");
    }

    @Test
    public void testGetPairs() {
        // Setup
        PairInfoDTO pairInfoDTO = new PairInfoDTO();
        ApiResponse<List<PairInfoDTO>> response = new ApiResponse<>(MessageCode.SUCCESS, List.of(pairInfoDTO));
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.serialize(response), HttpStatus.OK);
        when(mockClient.post(anyString(), any())).thenReturn(Mono.just(entity));

        // Run the test
        Mono<List<PairInfoDTO>> result = dexerApiClientUnderTest.getPairs(List.of(1L, 2L));

        // Verify the results
        StepVerifier.create(result)
                .assertNext(pairs -> {
                    Assert.assertNotNull(pairs);
                    Assert.assertEquals(pairs.size(), 1);
                })
                .verifyComplete();
    }

    @Test
    public void testGetPairs_ErrorHandling() {
        // Setup
        when(mockClient.post(anyString(), any())).thenReturn(Mono.error(new RuntimeException("API error")));

        // Run the test
        Mono<List<PairInfoDTO>> result = dexerApiClientUnderTest.getPairs(List.of(1L, 2L));

        // Verify the results
        StepVerifier.create(result)
                .assertNext(pairs -> {
                    Assert.assertNotNull(pairs);
                    Assert.assertTrue(pairs.isEmpty());
                })
                .verifyComplete();
    }

    @Test
    public void testGetPlatforms() {
        // Setup
        PlatformDTO platformDTO = new PlatformDTO();
        ApiResponse<List<PlatformDTO>> response = new ApiResponse<>(MessageCode.SUCCESS, List.of(platformDTO));
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.serialize(response), HttpStatus.OK);
        when(mockClient.get(anyString())).thenReturn(Mono.just(entity));

        // Run the test
        Mono<List<PlatformDTO>> result = dexerApiClientUnderTest.getPlatforms();

        // Verify the results
        StepVerifier.create(result)
                .assertNext(platforms -> {
                    Assert.assertNotNull(platforms);
                    Assert.assertEquals(platforms.size(), 1);
                })
                .verifyComplete();
    }

    @Test
    public void testGetPlatforms_ErrorHandling() {
        // Setup
        when(mockClient.get(anyString())).thenReturn(Mono.error(new RuntimeException("API error")));

        // Run the test
        Mono<List<PlatformDTO>> result = dexerApiClientUnderTest.getPlatforms();

        // Verify the results
        StepVerifier.create(result)
                .assertNext(platforms -> {
                    Assert.assertNotNull(platforms);
                    Assert.assertTrue(platforms.isEmpty());
                })
                .verifyComplete();
    }

    @Test
    public void testGetPlatformsFromDeQuery() {
        // Setup
        PlatformNewDTO platformNewDTO = new PlatformNewDTO();
        ApiResponse<List<PlatformNewDTO>> response = new ApiResponse<>(MessageCode.SUCCESS, List.of(platformNewDTO));
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.serialize(response), HttpStatus.OK);
        when(mockClient.get(anyString())).thenReturn(Mono.just(entity));

        // Run the test
        Mono<List<PlatformNewDTO>> result = dexerApiClientUnderTest.getPlatformsFromDeQuery();

        // Verify the results
        StepVerifier.create(result)
                .assertNext(platforms -> {
                    Assert.assertNotNull(platforms);
                    Assert.assertEquals(platforms.size(), 1);
                })
                .verifyComplete();
    }

    @Test
    public void testGetPlatformsFromDeQuery_ErrorHandling() {
        // Setup
        when(mockClient.get(anyString())).thenReturn(Mono.error(new RuntimeException("API error")));

        // Run the test
        Mono<List<PlatformNewDTO>> result = dexerApiClientUnderTest.getPlatformsFromDeQuery();

        // Verify the results
        StepVerifier.create(result)
                .assertNext(platforms -> {
                    Assert.assertNotNull(platforms);
                    Assert.assertTrue(platforms.isEmpty());
                })
                .verifyComplete();
    }

    @Test
    public void testGetPairListByDeQuery() {
        // Setup
        TokenPriceDTO tokenPriceDTO = new TokenPriceDTO();
        ApiResponse<List<TokenPriceDTO>> response = new ApiResponse<>(MessageCode.SUCCESS, List.of(tokenPriceDTO));
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.serialize(response), HttpStatus.OK);
        when(mockClient.post(anyString(), any(BatchPlatformTokenRequestDTO.class))).thenReturn(Mono.just(entity));

        // Create test tokens using the new interface
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token);

        // Run the test
        Mono<List<TokenPriceDTO>> result = dexerApiClientUnderTest.getPairListByDeQuery(tokens);

        // Verify the results
        StepVerifier.create(result)
                .assertNext(tokenList -> {
                    Assert.assertNotNull(tokenList);
                    Assert.assertEquals(tokenList.size(), 1);
                })
                .verifyComplete();
    }

    @Test
    public void testGetPairListByDeQuery_NullResponse() {
        // Setup
        ResponseEntity<String> entity = new ResponseEntity<>(null, HttpStatus.OK);
        when(mockClient.post(anyString(), any(BatchPlatformTokenRequestDTO.class))).thenReturn(Mono.just(entity));

        // Run the test
        Mono<List<TokenPriceDTO>> result = dexerApiClientUnderTest.getPairListByDeQuery("eth", List.of("0xabc"));

        // Verify the results
        StepVerifier.create(result)
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    public void testPartitionList() {
        // Use reflection to test private method
        List<Integer> input = List.of(1, 2, 3, 4, 5, 6, 7);
        List<List<Integer>> result = ReflectionTestUtils.invokeMethod(
                dexerApiClientUnderTest, 
                "partitionList", 
                input, 
                3);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(result.size(), 3);
        Assert.assertEquals(result.get(0), List.of(1, 2, 3));
        Assert.assertEquals(result.get(1), List.of(4, 5, 6));
        Assert.assertEquals(result.get(2), List.of(7));
    }
}