package com.cmc.asset.integration;

import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoAggregationVo;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoDetailCacheV3VO;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyQuotesLatestCacheV3VO;
import com.cmc.asset.model.contract.crypto.CryptoMarketPairDto;
import org.junit.Assert;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class BaseDataServiceClientTest {

    private BaseDataServiceClient baseDataServiceClientUnderTest;

    private AsynHttpClient.DefaultWebClient mockClient;

    private String baseDataService;

    private RestTemplate restTemplate;

    @BeforeTest
    public void setUp() throws Exception {
        baseDataService = "https://api.beta.coinmarketcap.supply/base-data";
        baseDataServiceClientUnderTest = new BaseDataServiceClient();
        mockClient = Mockito.mock(AsynHttpClient.DefaultWebClient.class);
        ReflectionTestUtils.setField(baseDataServiceClientUnderTest, "client", mockClient);
        ReflectionTestUtils.setField(baseDataServiceClientUnderTest, "baseDataService", baseDataService);
        ReflectionTestUtils.setField(baseDataServiceClientUnderTest, "batchSize", 300);
    }

    @Test
    public void testGetCryptoCurrencyDetailAll() {
        // Setup
        CryptoCurrencyInfoDetailCacheV3VO cryptoCurrencyInfoDetailCacheV3VO = new CryptoCurrencyInfoDetailCacheV3VO();
        cryptoCurrencyInfoDetailCacheV3VO.setId(1);
        cryptoCurrencyInfoDetailCacheV3VO.setName("1");
        ResponseEntity<List<CryptoCurrencyInfoDetailCacheV3VO>> entity =
            new ResponseEntity<>(List.of(cryptoCurrencyInfoDetailCacheV3VO), HttpStatus.OK);

        when(mockClient.getList(any(), any(),
            any(), eq(CryptoCurrencyInfoDetailCacheV3VO.class))).thenReturn(Mono.just(entity));
        // Run the test
        final Mono<List<CryptoCurrencyInfoDetailCacheV3VO>> result =
            baseDataServiceClientUnderTest.getCryptoCurrencyDetailAll();

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();

    }

    @Test
    public void testGetCryptoCurrencyQuotesDetailExtra() {
        // Setup
        ResponseEntity<CryptoCurrencyInfoAggregationVo> entity =
            new ResponseEntity<>(new CryptoCurrencyInfoAggregationVo(), HttpStatus.OK);
        when(mockClient.get(any(), any(),
            any(), eq(CryptoCurrencyInfoAggregationVo.class))).thenReturn(Mono.just(entity));
        // Run the test
        final Mono<CryptoCurrencyInfoAggregationVo> result =
            baseDataServiceClientUnderTest.getCryptoCurrencyQuotesDetailExtra(List.of(0));

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();
    }

    @Test
    public void testGetCryptoCurrencyQuotesDetailExtraMoreThan500() {
        // Setup
        ResponseEntity<CryptoCurrencyInfoAggregationVo> entity =
            new ResponseEntity<>(new CryptoCurrencyInfoAggregationVo(), HttpStatus.OK);
        when(mockClient.get(any(), any(),
            any(), eq(CryptoCurrencyInfoAggregationVo.class))).thenReturn(Mono.just(entity));
        // Run the test
        final Mono<CryptoCurrencyInfoAggregationVo> result =
            baseDataServiceClientUnderTest.getCryptoCurrencyQuotesDetailExtra(
                Stream.iterate(1, n -> n + 1).limit(2000).collect(Collectors.toList())
            );

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();
    }

    @Test
    public void testGetMarketPairs() {
        // Setup
        ResponseEntity<List<CryptoMarketPairDto>> entity =
            new ResponseEntity<>(List.of(new CryptoMarketPairDto()), HttpStatus.OK);
        when(mockClient.getList(any(), any(),
            any(), eq(CryptoMarketPairDto.class))).thenReturn(Mono.just(entity));
        // Run the test
        final Mono<List<CryptoMarketPairDto>> result = baseDataServiceClientUnderTest.getMarketPairs(List.of(1, 2, 3));
        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();

    }

    @Test
    public void testQueryLatestQuotesV3() {
        // Setup
        ResponseEntity<List<CryptoCurrencyQuotesLatestCacheV3VO>> entity =
            new ResponseEntity<>(List.of(new CryptoCurrencyQuotesLatestCacheV3VO()), HttpStatus.OK);
        when(mockClient.getList(any(), any(),
            any(), eq(CryptoCurrencyQuotesLatestCacheV3VO.class))).thenReturn(Mono.just(entity));
        // Run the test
        final Mono<List<CryptoCurrencyQuotesLatestCacheV3VO>> result =
            baseDataServiceClientUnderTest.queryLatestQuotesV3(List.of(1, 2, 3));
        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();

    }
}
