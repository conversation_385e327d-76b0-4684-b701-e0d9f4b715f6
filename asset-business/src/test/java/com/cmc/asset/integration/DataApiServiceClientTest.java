package com.cmc.asset.integration;

import com.cmc.asset.domain.crypto.CryptoCurrencyListResultDTO;
import com.cmc.asset.domain.crypto.CryptoCurrencyMapDTO;
import com.cmc.asset.domain.crypto.CryptoListingsRequestDTO;
import com.cmc.asset.domain.exchange.ExchangeMapEntity;
import com.cmc.data.common.ApiResponse;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.utils.JacksonUtils;
import org.junit.Assert;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class DataApiServiceClientTest {

    private DataApiServiceClient dataApiServiceClientUnderTest;

    private AsynHttpClient.DefaultWebClient mockClient;

    @BeforeMethod
    public void setUp() throws Exception {
        dataApiServiceClientUnderTest = new DataApiServiceClient();
        mockClient = Mockito.mock(AsynHttpClient.DefaultWebClient.class);
        ReflectionTestUtils.setField(dataApiServiceClientUnderTest, "client", mockClient);
        ReflectionTestUtils.setField(dataApiServiceClientUnderTest, "defaultTimeoutMills", 500);
        ReflectionTestUtils.setField(dataApiServiceClientUnderTest, "defaultRetries", 2);
    }

    @Test
    public void testGetCryptoMap() {
        // Setup
        ApiResponse<List<CryptoCurrencyMapDTO>> response = new ApiResponse<>(MessageCode.SUCCESS, List.of(new CryptoCurrencyMapDTO()));
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.toJsonString(response), HttpStatus.OK);
        when(mockClient.get(any())).thenReturn(Mono.just(entity));

        // Run the test
        final Flux<CryptoCurrencyMapDTO> result = dataApiServiceClientUnderTest.getCryptoMap(0, 0);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();

    }

    @Test
    public void testGetExchangeMap() {
        // Setup
        ApiResponse<List<ExchangeMapEntity>> response = new ApiResponse<>(MessageCode.SUCCESS, List.of(new ExchangeMapEntity()));
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.toJsonString(response), HttpStatus.OK);
        when(mockClient.get(any())).thenReturn(Mono.just(entity));

        // Run the test
        final Flux<ExchangeMapEntity> result = dataApiServiceClientUnderTest.getExchangeMap(0, 0);
        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();

    }

    @Test
    public void testGetCryptoListings() {
        // Setup
        final CryptoListingsRequestDTO request = new CryptoListingsRequestDTO("ids", "aux", "convertIds");
        ApiResponse<CryptoCurrencyListResultDTO> response = new ApiResponse<>(MessageCode.SUCCESS, new CryptoCurrencyListResultDTO());
        ResponseEntity<String> entity = new ResponseEntity<>(JacksonUtils.toJsonString(response), HttpStatus.OK);
        when(mockClient.post(any(), any())).thenReturn(Mono.just(entity));
        // Run the test
        final Mono<CryptoCurrencyListResultDTO> result = dataApiServiceClientUnderTest.getCryptoListings(request);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotNull(it)).verifyComplete();

    }
}
