package com.cmc.asset;
import com.cmc.asset.ServerContext.Authorization;
import org.springframework.web.server.ServerWebExchange;

import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class ServerContextTest {

    private ServerContext serverContextUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        serverContextUnderTest = new ServerContext();
        serverContextUnderTest.setServerWebExchange(null);
        serverContextUnderTest.setStartTime(0L);
        serverContextUnderTest.setTranId("");
        serverContextUnderTest.setUrl("");
        serverContextUnderTest.setFullActionName("");
        serverContextUnderTest.setAuthorization(new Authorization());
        Authorization authorization = new Authorization();
        authorization.setRequired(false);
        serverContextUnderTest = new ServerContext();
        authorization.equals(new Authorization());
        authorization.canEqual(new Authorization());
        authorization.toString();
        authorization.hashCode();
    }

    @Test
    public void testEquals() {
        assertTrue(serverContextUnderTest.equals(new ServerContext()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(serverContextUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, serverContextUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", serverContextUnderTest.toString());
    }

}
