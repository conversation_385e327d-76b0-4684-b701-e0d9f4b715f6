package com.cmc.asset.business.priceprediction.impl;

import com.cmc.asset.cache.StableCoinCache;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.contract.priceprediction.BasePricePredictionParamDTO;
import com.cmc.asset.model.contract.priceprediction.UpdatePricePredictionParamDTO;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.exception.BusinessException;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class UpdatePricePredictionValidatorTest {

    @Mock
    private StableCoinCache mockStableCoinCache;

    @InjectMocks
    private UpdatePricePredictionValidator updatePricePredictionValidatorUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testValidate() {
        // Setup
        final BasePricePredictionParamDTO dto1 = new BasePricePredictionParamDTO(0, 2020, 1);
        final BasePricePredictionParamDTO dto2 = new UpdatePricePredictionParamDTO("1", BigDecimal.ONE, "1", "2");
        final BasePricePredictionParamDTO dto3 = new UpdatePricePredictionParamDTO("1", BigDecimal.ONE, "1", "1");
        ZonedDateTime time = ExtUtils.getLastTimeEndOfCurrentMonth();
        dto3.setTargetYear(time.getYear());
        dto3.setTargetMonth(time.getMonthValue());

        final BasePricePredictionParamDTO dto4 = new UpdatePricePredictionParamDTO("1", BigDecimal.ONE, "1", "1");
        ZonedDateTime time1 = time.minusYears(1);
        dto4.setTargetYear(time1.getYear());
        dto4.setTargetMonth(time1.getMonthValue());

        final BasePricePredictionParamDTO dto5 = new UpdatePricePredictionParamDTO("1", BigDecimal.ONE, "1", "1");
        ZonedDateTime time2 = time.plusYears(1);
        dto5.setTargetYear(time2.getYear());
        dto5.setTargetMonth(time2.getMonthValue());
        dto5.setCryptoId(0);

        final BasePricePredictionParamDTO dto6 = new UpdatePricePredictionParamDTO("1", BigDecimal.ONE, "1", "1");
        dto6.setTargetYear(time2.getYear());
        dto6.setTargetMonth(time2.getMonthValue());
        dto6.setCryptoId(1);

        when(mockStableCoinCache.getById(0)).thenReturn(0);
        when(mockStableCoinCache.getById(1)).thenReturn(null);


        // Run the test
        try {
            updatePricePredictionValidatorUnderTest.validate(dto1);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Invalid parameter for validation.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            updatePricePredictionValidatorUnderTest.validate(dto2);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Operation forbidden.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            updatePricePredictionValidatorUnderTest.validate(dto3);
        } catch (BusinessException e) {

        } finally {
            Assert.assertTrue(true);
        }

        try {
            updatePricePredictionValidatorUnderTest.validate(dto4);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Updating price prediction is closed for past month")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            updatePricePredictionValidatorUnderTest.validate(dto5);
        } catch (BusinessException e) {
            if (e.getDesc().equals("The crypto is stable coin.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            updatePricePredictionValidatorUnderTest.validate(dto5);
        } catch (BusinessException e) {
            if (e.getDesc().equals("The crypto is stable coin.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            updatePricePredictionValidatorUnderTest.validate(dto6);
        } catch (Exception e) {
            Assert.assertTrue(false);
        }
        Assert.assertTrue(true);



    }
}
