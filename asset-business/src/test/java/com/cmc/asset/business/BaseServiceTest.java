package com.cmc.asset.business;

import com.cmc.asset.message.SenderMessage;
import com.cmc.auth.common.utils.AuthUtils;
import com.cmc.auth.common.utils.AuthUtils.AuthResult;
import com.cmc.data.common.enums.MessageCode;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderResult;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertTrue;

/**
 * <AUTHOR> Yin
 * @Description
 * @date 2021/8/9 下午6:25
 */
public class BaseServiceTest {

    private String shopAuthKey;

    @InjectMocks
    private BaseService baseService;

    @Mock(name = "portfolioProducer")
    private KafkaSender<String,String> portfolioProducer;
    @Mock(name = "watchlistProducer")
    private KafkaSender<String,String> watchlistProducer;

    private AutoCloseable mockitoCloseable;
    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        // set repository
        ReflectionTestUtils.setField(baseService, "shopAuthKey", "5296b3b6-c683-44a1-9439-8ed27bc486b0");
    }

    @Test
    public void testCheckAuth() {
        String secretKey = "5296b3b6-c683-44a1-9439-8ed27bc486b0";
        String adminToken = "Bearer eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJyb2xlcyI6IndlYixhZG1pbiIsImlzcyI6ImNtYy1hdXRoIiwiaWF0IjoxNjIzODk3MzYxLCJzdWIiOiI1ZmEyMjAxMzQwYmQ1NzZiMWE2NmZjNzEiLCJleHAiOjE2Mzk0NDkzNjF9.8VVESROoybjCUAyqfd7t9BTbiXOAaWEuzxRiUP0BpSA";
        Boolean rst = ReflectionTestUtils.invokeMethod(baseService, "checkAuth", secretKey, adminToken);
        Assert.assertNotNull(rst);
        Assert.assertTrue(rst);
    }

    @Test
    public void testValidateParam() {
        String messageCode = MessageCode.SUCCESS.getCode();
        String message = MessageCode.SUCCESS.getDesc();
        ReflectionTestUtils.invokeMethod(baseService, "validateParam", true, messageCode, message);
    }

    @Test
    public void testSendMQ1() {
        // Setup
        final SenderMessage messageSenderEntity = new SenderMessage<>("topic", "userId", "data");

        when(portfolioProducer.send(any())).thenReturn(Flux.just(new SenderResult<Object>() {
            @Override
            public RecordMetadata recordMetadata() {
                return new RecordMetadata(null, 1, 1, 1, 1L, 1, 1);
            }
            @Override
            public Exception exception() {
                return null;
            }
            @Override
            public Object correlationMetadata() {
                return new Object();
            }
        }));
        // Run the test
        baseService.sendMQ(messageSenderEntity);

        // Verify the results
    }

    @Test
    public void testSendMQ2() {
        // Setup
        final SenderMessage messageSenderEntity = new SenderMessage<>("topic", "userId", "data");

        when(portfolioProducer.send(any())).thenReturn(Flux.just(new SenderResult<Object>() {
            @Override
            public RecordMetadata recordMetadata() {
                return new RecordMetadata(null, 1, 1, 1, 1L, 1, 1);
            }
            @Override
            public Exception exception() {
                return new Exception();
            }
            @Override
            public Object correlationMetadata() {
                return new Object();
            }
        }));
        // Run the test
        baseService.sendMQ(messageSenderEntity, portfolioProducer);

        // Verify the results
    }

    @Test
    public void testValidateUserId() {
        AuthResult authResult = new AuthResult();
        authResult.setUserId("11");
        authResult.setUid(0L);
        authResult.setUsername("");
        authResult.setIsValid(true);
        authResult.setVerifiedByCMC(true);
        authResult.setAvatarId("");
        authResult.setReferralCode("");
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            when(AuthUtils.getUserInfo(any())).thenReturn(authResult);
            assertTrue(baseService.validateUserId("userId"));
        }
    }

    @Test
    public void testSendNotificationMQ() {
        // Setup
        final SenderMessage messageSenderEntity = new SenderMessage<>("topic", "userId", "data");

        when(watchlistProducer.send(any())).thenReturn(Flux.error(new Exception("error")));
        // Run the test
        baseService.sendNotificationMQ(messageSenderEntity);

        // Verify the results
    }


}
