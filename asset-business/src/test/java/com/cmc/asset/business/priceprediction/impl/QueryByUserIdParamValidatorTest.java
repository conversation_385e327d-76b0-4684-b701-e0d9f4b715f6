package com.cmc.asset.business.priceprediction.impl;

import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.model.contract.priceprediction.BasePricePredictionParamDTO;
import com.cmc.data.common.exception.BusinessException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class QueryByUserIdParamValidatorTest {

    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;

    @InjectMocks
    private QueryByUserIdParamValidator queryByUserIdParamValidatorUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testValidate() {
        // Setup
        final BasePricePredictionParamDTO request1 = new BasePricePredictionParamDTO(0, 2020, 1);

        // Configure CryptoCurrencyCache.getCryptoCurrencyById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(0)).thenReturn(cryptoCurrencyInfoDTO);
        // Run the test

        queryByUserIdParamValidatorUnderTest.validate(request1);


    }

    @Test
    public void testValidate2() {
        final BasePricePredictionParamDTO request = new BasePricePredictionParamDTO();


        try {
            queryByUserIdParamValidatorUnderTest.validate(request);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Invalid parameters")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }
    }

    @Test
    public void testValidate3() {
        final BasePricePredictionParamDTO request = new BasePricePredictionParamDTO(1, 2020, 1);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(1)).thenReturn(null);
        try {
            queryByUserIdParamValidatorUnderTest.validate(request);
        } catch (BusinessException e) {
            if (e.getDesc().equals("The crypto is not found.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }
    }

}