package com.cmc.asset.business.portfolio.impl;

import com.cmc.asset.business.oauth.impl.BinanceOAuth2Client;
import com.cmc.asset.business.oauth.impl.OAuth2ClientFactory;
import com.cmc.asset.business.portfolio.IPortfolioAppAdapterService;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.portfolio.*;
import com.cmc.asset.dao.repository.mongo.*;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.model.contract.BaseResultResponse;
import com.cmc.asset.model.contract.portfolio.*;
import com.cmc.asset.model.contract.portfolio.thirdparty.*;
import com.cmc.asset.model.enums.OAuth2ProviderType;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.domain.Range;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;

public class PortfolioThirdPartyServiceImplTest {

    @Mock
    private OAuth2ClientFactory mockOAuth2ClientFactory;
    @Mock
    private PortfolioMultiRepository mockPortfolioMultiRepository;
    @Mock
    private PortfolioThirdPartyRepository mockPortfolioThirdPartyRepository;
    @Mock
    private PortfolioThirdPartyAssetRepository mockPortfolioThirdPartyAssetRepository;
    @Mock
    private PortfolioThirdPartyAssetHistoryRepository mockPortfolioThirdPartyAssetHistoryRepository;
    @Mock
    private PortfolioThirdPartyAssetHistory5mRepository mockPortfolioThirdPartyAssetHistory5mRepository;
    @Mock
    private PortfolioThirdPartyAssetHistory1dRepository mockPortfolioThirdPartyAssetHistory1dRepository;
    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;
    @Mock
    private PortfolioCryptoRedisService mockPortfolioCryptoRedisService;
    @Mock
    private CurrencyPriceService mockCurrencyPriceService;
    @Mock
    private ReactiveRedisTemplate<String, String> mockAssetRedisTemplate;
    @Mock
    private BinanceOAuth2Client mockBinanceOAuth2Client;
    @Mock
    private IPortfolioAppAdapterService iPortfolioAppAdapterService;
    @Mock
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;

    @InjectMocks
    private PortfolioThirdPartyServiceImpl portfolioThirdPartyServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    private final PortfolioMultiEntity portfolioMultiEntity = PortfolioMultiEntity.builder()
            .id(new ObjectId("64788ca0be06321f6c0833c3"))
            .userId(new ObjectId("64788ca0be06321f6c0833c3"))
            .portfolioSourceId("portfolioSourceId")
            .portfolioType("portfolioType")
            .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
            .state("state")
            .build();
    private final PortfolioThirdPartyEntity thirdPartyEntity = PortfolioThirdPartyEntity.builder()
            .id(new ObjectId("64788ca0be06321f6c0833c3"))
            .openId("openId")
            .source(0)
            .token(AccessToken.builder()
                    .accessToken("accessToken")
                    .refreshToken("refreshToken")
                    .scope("scope")
                    .tokenType("tokenType")
                    .expiresIn(0L)
                    .expiredTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                    .build())
            .openUser(OpenUser.builder()
                    .openId("openId")
                    .email("email")
                    .build())
            .valid(false)
            .lastSyncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
            .syncStatus(0)
            .build();
    private final PortfolioThirdPartyAssetEntity portfolioThirdPartyAssetEntity = PortfolioThirdPartyAssetEntity.builder()
            .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
            .openId("openId")
            .source(0)
            .cryptoId(0)
            .cryptoSymbol("cryptoSymbol")
            .cryptoName("cryptoName")
            .balance(new BigDecimal("0.00"))
            .value(new BigDecimal("0.00"))
            .build();
    private final PortfolioThirdPartyAssetHistoryAggregatedEntity portfolioThirdPartyAssetHistoryAggregatedEntity = new PortfolioThirdPartyAssetHistoryAggregatedEntity(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 1, new BigDecimal("0.00"),
            new BigDecimal("0.00"));

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);

        ReflectionTestUtils.setField(portfolioThirdPartyServiceImplUnderTest, "secondsBeforeExpiration", 300L);
        ReflectionTestUtils.setField(portfolioThirdPartyServiceImplUnderTest, "enableMockData", false);
        ReflectionTestUtils.setField(portfolioThirdPartyServiceImplUnderTest, "oauthStateLength", 16);
        ReflectionTestUtils.setField(portfolioThirdPartyServiceImplUnderTest, "defaultQueryAllDays", 180);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testOnCallback() throws Exception {
        // Setup
        final OauthCallbackRequestDTO requestDTO = new OauthCallbackRequestDTO("code", "64788ca0be06321f6c0833c3");
        requestDTO.getHeader().setUserId("64788ca0be06321f6c0833c3");

        when(iPortfolioAppAdapterService.isFromApp(any())).thenReturn(false);

        // Configure PortfolioMultiRepository.findFirstByIdAndUserId(...).
        when(mockPortfolioMultiRepository.findFirstByUserIdAndState(
                any(ObjectId.class),
                anyString()))
                .thenReturn(Mono.just(portfolioMultiEntity));

        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioType(...).
        final Flux<PortfolioMultiEntity> portfolioMultiEntityMono1 = Flux.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findByUserIdAndPortfolioTypeIn(any(ObjectId.class), anyList()))
                .thenReturn(portfolioMultiEntityMono1);

        when(mockOAuth2ClientFactory.getClient(OAuth2ProviderType.BINANCE)).thenReturn(null);

        // Configure PortfolioThirdPartyRepository.findAndModify(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findAndModify(thirdPartyEntity)).thenReturn(portfolioThirdPartyEntityMono);

        // Configure PortfolioThirdPartyRepository.insert(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono1 =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.insert(thirdPartyEntity)).thenReturn(portfolioThirdPartyEntityMono1);

        // Configure PortfolioMultiRepository.findFirstByThirdPartyId(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono2 = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findFirstByThirdPartyId(
                any(ObjectId.class)))
                .thenReturn(portfolioMultiEntityMono2);

        when(mockCryptoCurrencyCache.getIdBySymbol("cryptoSymbol")).thenReturn(List.of(CryptoCurrencyInfoDTO.builder().id(0).build()));
        when(mockPortfolioThirdPartyAssetRepository.deleteAllByThirdPartyId(
                any(ObjectId.class)))
                .thenReturn(Mono.just(DeleteResult.acknowledged(0L)));

        // Configure PortfolioThirdPartyAssetRepository.insert(...).
        final Flux<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityFlux =
                Flux.just(PortfolioThirdPartyAssetEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .build());
        when(mockPortfolioThirdPartyAssetRepository.insert(anyList())).thenReturn(portfolioThirdPartyAssetEntityFlux);

        // Configure PortfolioThirdPartyAssetHistoryRepository.insert(...).
        final Flux<PortfolioThirdPartyAssetHistoryEntity> portfolioThirdPartyAssetHistoryEntityFlux =
                Flux.just(PortfolioThirdPartyAssetHistoryEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .syncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(
                mockPortfolioThirdPartyAssetHistoryRepository.insert(anyList())).thenReturn(portfolioThirdPartyAssetHistoryEntityFlux);

        when(mockAssetRedisTemplate.unlink(any(Flux.class))).thenReturn(Mono.just(0L));
        when(mockPortfolioMultiRepository.updateThirdPartyId(
                any(ObjectId.class),
                any(ObjectId.class)))
                .thenReturn(Mono.just(UpdateResult.unacknowledged()));
        when(mockPortfolioMultiRepository.deleteById(
                any(ObjectId.class))).thenReturn(Mono.empty());

        // Run the test
        final Mono<OauthCallbackResponseDTO> result = portfolioThirdPartyServiceImplUnderTest.onCallback(requestDTO);

        // Verify the results
    }

    @Test
    public void testExchangeCodeAndSave() throws Exception {
        // Setup
        final PortfolioMultiEntity multiEntity = portfolioMultiEntity;

        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioType(...).
        final Flux<PortfolioMultiEntity> portfolioMultiEntityMono = Flux.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findByUserIdAndPortfolioTypeIn(any(ObjectId.class), anyList()))
                .thenReturn(portfolioMultiEntityMono);

        when(mockOAuth2ClientFactory.getClient(OAuth2ProviderType.BINANCE)).thenReturn(null);

        // Configure PortfolioThirdPartyRepository.findAndModify(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findAndModify(PortfolioThirdPartyEntity.builder()
                .id(new ObjectId("64788ca0be06321f6c0833c3"))
                .openId("openId")
                .source(0)
                .token(AccessToken.builder()
                        .accessToken("accessToken")
                        .refreshToken("refreshToken")
                        .scope("scope")
                        .tokenType("tokenType")
                        .expiresIn(0L)
                        .expiredTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build())
                .openUser(OpenUser.builder()
                        .openId("openId")
                        .email("email")
                        .build())
                .valid(false)
                .lastSyncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .syncStatus(0)
                .build())).thenReturn(portfolioThirdPartyEntityMono);

        // Configure PortfolioThirdPartyRepository.insert(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono1 =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.insert(PortfolioThirdPartyEntity.builder()
                .id(new ObjectId("64788ca0be06321f6c0833c3"))
                .openId("openId")
                .source(0)
                .token(AccessToken.builder()
                        .accessToken("accessToken")
                        .refreshToken("refreshToken")
                        .scope("scope")
                        .tokenType("tokenType")
                        .expiresIn(0L)
                        .expiredTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build())
                .openUser(OpenUser.builder()
                        .openId("openId")
                        .email("email")
                        .build())
                .valid(false)
                .lastSyncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .syncStatus(0)
                .build())).thenReturn(portfolioThirdPartyEntityMono1);

        // Configure PortfolioMultiRepository.findFirstByThirdPartyId(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono1 = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findFirstByThirdPartyId(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(portfolioMultiEntityMono1);

        when(mockCryptoCurrencyCache.getIdBySymbol("cryptoSymbol")).thenReturn(List.of(CryptoCurrencyInfoDTO.builder().id(0).build()));
        when(mockPortfolioThirdPartyAssetRepository.deleteAllByThirdPartyId(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(Mono.just(DeleteResult.acknowledged(0L)));

        // Configure PortfolioThirdPartyAssetRepository.insert(...).
        final Flux<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityFlux =
                Flux.just(PortfolioThirdPartyAssetEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .build());
        when(mockPortfolioThirdPartyAssetRepository.insert(List.of(PortfolioThirdPartyAssetEntity.builder()
                .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                .openId("openId")
                .source(0)
                .cryptoId(0)
                .cryptoSymbol("cryptoSymbol")
                .cryptoName("cryptoName")
                .balance(new BigDecimal("0.00"))
                .value(new BigDecimal("0.00"))
                .build()))).thenReturn(portfolioThirdPartyAssetEntityFlux);

        // Configure PortfolioThirdPartyAssetHistoryRepository.insert(...).
        final Flux<PortfolioThirdPartyAssetHistoryEntity> portfolioThirdPartyAssetHistoryEntityFlux =
                Flux.just(PortfolioThirdPartyAssetHistoryEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .syncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(
                mockPortfolioThirdPartyAssetHistoryRepository.insert(List.of(PortfolioThirdPartyAssetHistoryEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .syncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build()))).thenReturn(portfolioThirdPartyAssetHistoryEntityFlux);

        when(mockAssetRedisTemplate.unlink(any(Flux.class))).thenReturn(Mono.just(0L));

        // Run the test
        final Mono<OauthCallbackResponseDTO> result =
                portfolioThirdPartyServiceImplUnderTest.exchangeCodeAndSave("code", multiEntity, false);

        // Verify the results
    }

    @Test
    public void testDisconnect() throws Exception {
        // Setup
        final DisconnectPortfolioRequestDTO requestDTO = new DisconnectPortfolioRequestDTO("portfolioSourceId");
        requestDTO.getHeader().setUserId("64788ca0be06321f6c0833c3");
        when(mockPortfolioMultiRepository.deleteFirstByUserIdAndPortfolioSourceId(any(ObjectId.class), anyString()))
                .thenReturn(Mono.just(DeleteResult.acknowledged(0L)));
        when(assetRedisTemplate.delete(anyString())).thenReturn(Mono.just(1L));

        // Run the test
        final Mono<BaseResultResponse> result = portfolioThirdPartyServiceImplUnderTest.disconnect(requestDTO);

        // Verify the results
    }

    @Test
    public void testRefreshData() throws Exception {
        // Setup
        final RefreshDataRequestDTO requestDTO = new RefreshDataRequestDTO();
        final RefreshDataRequestDTO.ThirdPartyAssetDTO thirdPartyAssetDTO =
                new RefreshDataRequestDTO.ThirdPartyAssetDTO();
        thirdPartyAssetDTO.setThirdPartyId("portfolioSourceId");
        thirdPartyAssetDTO.setCryptoSymbol("cryptoSymbol");
        thirdPartyAssetDTO.setCryptoName("cryptoName");
        thirdPartyAssetDTO.setBalance(new BigDecimal("0.00"));
        thirdPartyAssetDTO.setValue(new BigDecimal("0.00"));
        requestDTO.setData(List.of(thirdPartyAssetDTO));
        requestDTO.setSyncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure PortfolioThirdPartyRepository.findById(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findById(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(portfolioThirdPartyEntityMono);

        when(mockCryptoCurrencyCache.getIdBySymbol("cryptoSymbol")).thenReturn(List.of(CryptoCurrencyInfoDTO.builder().id(0).build()));
        when(mockPortfolioThirdPartyAssetRepository.deleteAllByThirdPartyId(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(Mono.just(DeleteResult.acknowledged(0L)));

        // Configure PortfolioThirdPartyAssetRepository.insert(...).
        final Flux<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityFlux =
                Flux.just(PortfolioThirdPartyAssetEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .build());
        when(mockPortfolioThirdPartyAssetRepository.insert(List.of(PortfolioThirdPartyAssetEntity.builder()
                .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                .openId("openId")
                .source(0)
                .cryptoId(0)
                .cryptoSymbol("cryptoSymbol")
                .cryptoName("cryptoName")
                .balance(new BigDecimal("0.00"))
                .value(new BigDecimal("0.00"))
                .build()))).thenReturn(portfolioThirdPartyAssetEntityFlux);

        // Configure PortfolioThirdPartyAssetHistoryRepository.insert(...).
        final Flux<PortfolioThirdPartyAssetHistoryEntity> portfolioThirdPartyAssetHistoryEntityFlux =
                Flux.just(PortfolioThirdPartyAssetHistoryEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .syncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(
                mockPortfolioThirdPartyAssetHistoryRepository.insert(List.of(PortfolioThirdPartyAssetHistoryEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .syncTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build()))).thenReturn(portfolioThirdPartyAssetHistoryEntityFlux);

        // Run the test
        final Mono<BaseResultResponse> result = portfolioThirdPartyServiceImplUnderTest.refreshData(requestDTO);

        // Verify the results
    }

    @Test
    public void testQueryThirdPartyAssetInfo() throws Exception {
        // Setup
        final ObjectId thirdPartyId = new ObjectId("64788ca0be06321f6c0833c3");

        when(mockPortfolioThirdPartyRepository.findById(any(ObjectId.class))).thenReturn(Mono.just(thirdPartyEntity));
        // Configure PortfolioThirdPartyAssetRepository.findByThirdPartyId(...).
        final Flux<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityFlux =
                Flux.just(PortfolioThirdPartyAssetEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .build());
        when(mockPortfolioThirdPartyAssetRepository.findByThirdPartyId(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(portfolioThirdPartyAssetEntityFlux);

        // Configure PortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(...).
        final Mono<Map<Integer, PortfolioCryptoInfoDTO>> mapMono =
                Mono.just(Map.ofEntries(Map.entry(0, PortfolioCryptoInfoDTO.builder().build())));
        when(mockPortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(List.of(0))).thenReturn(mapMono);

        // Run the test
        final Mono<QueryThirdPartyDTO> result =
                portfolioThirdPartyServiceImplUnderTest.queryThirdPartyAssetInfo(thirdPartyId, "userId", "portfolioSourceId", true);

        // Verify the results
    }

    @Test
    public void testPortfolioStatistics() throws Exception {
        // Setup
        final PortfolioMultiEntity requestEntity = portfolioMultiEntity;
        final PortfolioCryptoDTO portfolioUserIdDTO = new PortfolioCryptoDTO();
        portfolioUserIdDTO.setCryptoUnit(0);
        portfolioUserIdDTO.setFiatUnit(0);
        portfolioUserIdDTO.setCryptocurrencyId(0);
        portfolioUserIdDTO.setPortfolioSourceId("64788ca0be06321f6c0833c3");
        portfolioUserIdDTO.setWithPortfolioDetail(false);

        // Configure PortfolioThirdPartyAssetRepository.findByThirdPartyId(...).
        final Flux<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityFlux =
                Flux.just(PortfolioThirdPartyAssetEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .build());
        when(mockPortfolioThirdPartyAssetRepository.findByThirdPartyId(
                any(ObjectId.class)))
                .thenReturn(portfolioThirdPartyAssetEntityFlux);

        // Configure PortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(...).
        final Mono<Map<Integer, PortfolioCryptoInfoDTO>> mapMono =
                Mono.just(Map.ofEntries(Map.entry(0, PortfolioCryptoInfoDTO.builder().build())));
        when(mockPortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(List.of(0))).thenReturn(mapMono);

        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(
                any(ObjectId.class),
                anyString())).thenReturn(portfolioMultiEntityMono);

        when(mockPortfolioCryptoRedisService.queryCryptoInfo(anyInt(), any(Date.class), anyBoolean())).thenReturn(Mono.just(PortfolioCryptoInfoDTO.builder()
                .price("1")
                .build()));

        // Configure PortfolioThirdPartyAssetHistoryRepository.sumByTime(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux = Flux.just(
                new PortfolioThirdPartyAssetHistoryAggregatedEntity(
                        new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 1, new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockPortfolioThirdPartyAssetHistoryRepository.aggregationByMinTime(
                any(ObjectId.class),
                any(Date.class),
                anyBoolean(),
                anyInt()))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);

        // Configure PortfolioThirdPartyRepository.findById(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findById(
                any(ObjectId.class)))
                .thenReturn(portfolioThirdPartyEntityMono);

        when(mockOAuth2ClientFactory.getClient(OAuth2ProviderType.BINANCE)).thenReturn(null);

        // Configure PortfolioCryptoRedisService.queryCryptoInfo(...).
        final Mono<PortfolioCryptoInfoDTO> portfolioCryptoInfoDTOMono =
                Mono.just(PortfolioCryptoInfoDTO.builder().build());
        when(mockPortfolioCryptoRedisService.queryCryptoInfo(anyInt(),
                any(Date.class), anyBoolean())).thenReturn(portfolioCryptoInfoDTOMono);

        // Run the test
        final Mono<PortfolioStatisticsDTO> result =
                portfolioThirdPartyServiceImplUnderTest.portfolioStatistics(requestEntity, portfolioUserIdDTO);

        // Verify the results
    }

    @Test
    public void testHistoricalChart() throws Exception {
        // Setup
        final ObjectId thirdPartyId = new ObjectId("64788ca0be06321f6c0833c3");
        final PortfolioHistoricalChartQueryDTO queryDTO = new PortfolioHistoricalChartQueryDTO();
        queryDTO.setCryptoIds(List.of(0));
        queryDTO.setDays(7);
        queryDTO.setPortfolioSourceId("portfolioSourceId");
        queryDTO.setChainId(0);

        // Configure PortfolioThirdPartyAssetHistoryRepository.aggregationByTime(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux = Flux.just(
                portfolioThirdPartyAssetHistoryAggregatedEntity);
        when(mockPortfolioThirdPartyAssetHistoryRepository.aggregationByTime(
                any(ObjectId.class),
                any(Range.class)))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);
        when(mockPortfolioThirdPartyAssetHistory5mRepository.aggregationByTime(
                any(ObjectId.class),
                any(Range.class)))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);
        when(mockPortfolioThirdPartyAssetHistory1dRepository.aggregationByTime(
                any(ObjectId.class),
                any(Range.class)))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);

        // Configure PortfolioThirdPartyAssetRepository.aggregation(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux1 = Flux.just(
                portfolioThirdPartyAssetHistoryAggregatedEntity);
        when(mockPortfolioThirdPartyAssetRepository.aggregation(
                any(ObjectId.class), anyInt()))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux1);

        when(mockPortfolioThirdPartyAssetRepository.findByThirdPartyId(any())).thenReturn(Flux.just(portfolioThirdPartyAssetEntity));

        // Configure CurrencyPriceService.loadPrices(...).
        final Mono<NavigableMap<Long, BigDecimal>> navigableMapMono =
                Mono.just(new TreeMap<>(Map.ofEntries(Map.entry(0L, new BigDecimal("0.00")))));
        when(mockCurrencyPriceService.loadPrices(0, 0L)).thenReturn(navigableMapMono);

        when(mockCurrencyPriceService.getPrice(new TreeMap<>(Map.ofEntries(Map.entry(0L, new BigDecimal("0.00")))),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final Mono<List<PortfolioTotalDTO>> result =
                portfolioThirdPartyServiceImplUnderTest.historicalChart(thirdPartyId, queryDTO);

        // Verify the results
    }

    @Test
    public void testPortfolioTotalValue() throws Exception {
        // Setup
        final ObjectId userId = new ObjectId("64788ca0be06321f6c0833c3");

        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(
                new ObjectId("64788ca0be06321f6c0833c3"), "sourceId"))
                .thenReturn(portfolioMultiEntityMono);

        // Configure PortfolioThirdPartyAssetRepository.aggregation(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux = Flux.just(portfolioThirdPartyAssetHistoryAggregatedEntity);
        when(mockPortfolioThirdPartyAssetRepository.aggregation(
                new ObjectId("64788ca0be06321f6c0833c3"), 0))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);

        // Run the test
        final Mono<BigDecimal> result = portfolioThirdPartyServiceImplUnderTest.portfolioTotalValue(userId, "sourceId");

        // Verify the results
    }

    @Test
    public void testGetAuthorizationUrl() throws Exception {
        String expectValue = "http://mock.com";
        // Setup
        when(mockPortfolioMultiRepository.findById(any(ObjectId.class))).thenReturn(Mono.just(portfolioMultiEntity));
        when(mockPortfolioMultiRepository.updateById(any(ObjectId.class), anyString())).thenReturn(Mono.just(UpdateResult.acknowledged(1L, 1L, null)));
        when(mockOAuth2ClientFactory.getClient(OAuth2ProviderType.BINANCE)).thenReturn(mockBinanceOAuth2Client);
        when(mockBinanceOAuth2Client.getAuthorizationUrl(anyString())).thenReturn(expectValue);

        // Run the test
        final Mono<String> result = portfolioThirdPartyServiceImplUnderTest.getAuthorizationUrl("64788ca0be06321f6c0833c3",
                OAuth2ProviderType.BINANCE);

        // Verify the results
        assertEquals(expectValue, expectValue);
    }

    @Test
    public void testQueryNeedReAuthBySourceId() throws Exception {
        // Setup
        // Configure PortfolioMultiRepository.findById(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findById(
                any(ObjectId.class)))
                .thenReturn(portfolioMultiEntityMono);

        // Configure PortfolioThirdPartyRepository.findById(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findById(
                any(ObjectId.class)))
                .thenReturn(portfolioThirdPartyEntityMono);

        // Run the test
        final Mono<Boolean> result =
                portfolioThirdPartyServiceImplUnderTest.queryNeedReAuthBySourceId("64788ca0be06321f6c0833c3");

        // Verify the results
    }

    @Test
    public void testQueryNeedReAuth1() throws Exception {
        // Setup
        // Configure PortfolioThirdPartyRepository.findById(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findById(
                any(ObjectId.class)))
                .thenReturn(portfolioThirdPartyEntityMono);

        // Run the test
        final Mono<Boolean> result = portfolioThirdPartyServiceImplUnderTest.queryNeedReAuth("64788ca0be06321f6c0833c3");

        // Verify the results
    }

    @Test
    public void testQueryNeedReAuth2() throws Exception {
        // Setup
        final ObjectId thirdPartyId = new ObjectId("64788ca0be06321f6c0833c3");

        // Configure PortfolioThirdPartyRepository.findById(...).
        final Mono<PortfolioThirdPartyEntity> portfolioThirdPartyEntityMono =
                Mono.just(thirdPartyEntity);
        when(mockPortfolioThirdPartyRepository.findById(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(portfolioThirdPartyEntityMono);

        // Run the test
        final Mono<Boolean> result = portfolioThirdPartyServiceImplUnderTest.queryNeedReAuth(thirdPartyId);

        // Verify the results
    }

    @Test
    public void testPortfolioYesterdayValue() throws Exception {
        // Setup
        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(
                any(ObjectId.class),
                anyString())).thenReturn(portfolioMultiEntityMono);
        when(mockPortfolioCryptoRedisService.queryCryptoInfo(anyInt(), any(Date.class), anyBoolean())).thenReturn(Mono.just(PortfolioCryptoInfoDTO.builder()
                .price("1")
                .build()));
        // Configure PortfolioThirdPartyAssetHistoryRepository.sumByTime(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux = Flux.just(
                new PortfolioThirdPartyAssetHistoryAggregatedEntity(
                        new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 1, new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockPortfolioThirdPartyAssetHistoryRepository.aggregationByMinTime(
                any(ObjectId.class),
                any(Date.class),
                anyBoolean(),
                anyInt()))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);

        // Run the test
        final Mono<BigDecimal> result =
                portfolioThirdPartyServiceImplUnderTest.portfolioYesterdayValue("64788ca0be06321f6c0833c3", "64788ca0be06321f6c0833c3", 1);

        // Verify the results
    }

    @Test
    public void testHasAsset() {
        // Setup
        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(...).
        final Mono<PortfolioMultiEntity> portfolioMultiEntityMono = Mono.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(
                any(ObjectId.class),
                anyString())).thenReturn(portfolioMultiEntityMono);

        // Configure PortfolioThirdPartyAssetRepository.findFirstByThirdPartyId(...).
        final Mono<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityMono =
                Mono.just(portfolioThirdPartyAssetEntity);
        when(mockPortfolioThirdPartyAssetRepository.findFirstByThirdPartyId(any(ObjectId.class)))
                .thenReturn(portfolioThirdPartyAssetEntityMono);

        // Run the test
        final Mono<Boolean> result =
                portfolioThirdPartyServiceImplUnderTest.hasAsset("64788ca0be06321f6c0833c3", "64788ca0be06321f6c0833c3");

        // Verify the results
    }

    @Test
    public void testFindAssetByUserId() throws Exception {
        // Setup
        final ObjectId userId = new ObjectId("64788ca0be06321f6c0833c3");

        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioType(...).
        final Flux<PortfolioMultiEntity> portfolioMultiEntityMono = Flux.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findByUserIdAndPortfolioTypeIn(any(ObjectId.class), anyList()))
                .thenReturn(portfolioMultiEntityMono);

        // Configure PortfolioThirdPartyAssetRepository.findByThirdPartyId(...).
        final Flux<PortfolioThirdPartyAssetEntity> portfolioThirdPartyAssetEntityFlux =
                Flux.just(PortfolioThirdPartyAssetEntity.builder()
                        .thirdPartyId(new ObjectId("64788ca0be06321f6c0833c3"))
                        .openId("openId")
                        .source(0)
                        .cryptoId(0)
                        .cryptoSymbol("cryptoSymbol")
                        .cryptoName("cryptoName")
                        .balance(new BigDecimal("0.00"))
                        .value(new BigDecimal("0.00"))
                        .build());
        when(mockPortfolioThirdPartyAssetRepository.findByThirdPartyId(
                new ObjectId("64788ca0be06321f6c0833c3")))
                .thenReturn(portfolioThirdPartyAssetEntityFlux);

        // Run the test
        final Flux<PortfolioThirdPartyAssetDTO> result =
                portfolioThirdPartyServiceImplUnderTest.findAssetByUserId(userId);

        // Verify the results
    }

    @Test
    public void testFindAssetByUserIdAndCryptoId() throws Exception {
        // Setup
        final ObjectId userId = new ObjectId("64788ca0be06321f6c0833c3");

        // Configure PortfolioMultiRepository.findFirstByUserIdAndPortfolioType(...).
        final Flux<PortfolioMultiEntity> portfolioMultiEntityMono = Flux.just(portfolioMultiEntity);
        when(mockPortfolioMultiRepository.findByUserIdAndPortfolioTypeIn(any(ObjectId.class), anyList()))
                .thenReturn(portfolioMultiEntityMono);

        // Configure PortfolioThirdPartyAssetRepository.aggregation(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux = Flux.just();
        when(mockPortfolioThirdPartyAssetRepository.aggregation(
                new ObjectId("64788ca0be06321f6c0833c3"), 0))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);

        // Run the test
        final Flux<Tuple3<PortfolioMultiEntity, BigDecimal, BigDecimal>> result =
                portfolioThirdPartyServiceImplUnderTest.findAssetByUserIdAndCryptoId(userId, 0);

        // Verify the results
    }

    @Test
    public void testHistoricalChartData() throws Exception {
        // Setup
        final ObjectId thirdPartyId = new ObjectId("64788ca0be06321f6c0833c3");
        final PortfolioHistoricalChartQueryDTO queryDTO = new PortfolioHistoricalChartQueryDTO();
        queryDTO.setCryptoIds(List.of(0));
        queryDTO.setDays(7);
        queryDTO.setPortfolioSourceId("portfolioSourceId");
        queryDTO.setChainId(0);

        // Configure PortfolioThirdPartyAssetHistoryRepository.aggregationByTime(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux = Flux.just(portfolioThirdPartyAssetHistoryAggregatedEntity);
        when(mockPortfolioThirdPartyAssetHistoryRepository.aggregationByTime(
                any(ObjectId.class),
                any(Range.class)))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);
        when(mockPortfolioThirdPartyAssetHistory5mRepository.aggregationByTime(
                any(ObjectId.class),
                any(Range.class)))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);
        when(mockPortfolioThirdPartyAssetHistory1dRepository.aggregationByTime(
                any(ObjectId.class),
                any(Range.class)))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux);

        // Configure PortfolioThirdPartyAssetRepository.aggregation(...).
        final Flux<PortfolioThirdPartyAssetHistoryAggregatedEntity>
                portfolioThirdPartyAssetHistoryAggregatedEntityFlux1 = Flux.just(portfolioThirdPartyAssetHistoryAggregatedEntity);
        when(mockPortfolioThirdPartyAssetRepository.aggregation(
                any(ObjectId.class), anyInt()))
                .thenReturn(portfolioThirdPartyAssetHistoryAggregatedEntityFlux1);

        when(mockPortfolioThirdPartyAssetRepository.findByThirdPartyId(any())).thenReturn(Flux.just(portfolioThirdPartyAssetEntity));

        // Run the test
        final Mono<List<PortfolioTotalDTO>> result =
                portfolioThirdPartyServiceImplUnderTest.historicalChartData(thirdPartyId, queryDTO);

        // Verify the results
    }

}
