package com.cmc.asset.business.exchangelisting.impl;


import com.cmc.asset.dao.entity.NewCoinSubscribeEntity;
import com.cmc.asset.dao.repository.mongo.NewCoinSubscribeRepository;
import com.cmc.asset.model.contract.exchangelisting.NewCoinListingQueryRequest;
import com.cmc.asset.model.contract.exchangelisting.NewCoinListingQueryResponse;
import com.cmc.asset.model.contract.exchangelisting.NewCoinListingSubscribeRequest;
import com.cmc.asset.model.contract.exchangelisting.NewCoinListingSubscribeResponse;
import com.cmc.data.common.BaseRequest;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class NewCoinListingServiceImplTest {

    @Mock
    private NewCoinSubscribeRepository mockNewCoinSubscribeRepository;

    @InjectMocks
    private NewCoinListingServiceImpl newCoinListingServiceImpl;

    private AutoCloseable closeable;

    @BeforeMethod
    public void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        closeable.close();
    }

    @Test(expectedExceptions = IllegalArgumentException.class)
    public void testQuerySubscribe_uidNull() {
        NewCoinListingQueryRequest req = NewCoinListingQueryRequest.builder().uid(null).build();
        newCoinListingServiceImpl.querySubscribe(req).block();
    }

    @Test
    public void testQuerySubscribe_success() {
        NewCoinListingQueryRequest req = NewCoinListingQueryRequest.builder().uid(123L).build();
        when(mockNewCoinSubscribeRepository.findByUid(123L))
                .thenReturn(Flux.just(
                        NewCoinSubscribeEntity.builder().newCoinDataId("id1").build(),
                        NewCoinSubscribeEntity.builder().newCoinDataId("id2").build()
                ));

        StepVerifier.create(newCoinListingServiceImpl.querySubscribe(req))
                .expectSubscription()
                .assertNext(resp -> {
                    Assert.assertEquals(resp.getSubscribedIds(), List.of("id1", "id2"));
                })
                .verifyComplete();
    }

    @Test(expectedExceptions = IllegalArgumentException.class)
    public void testSubscribe_paramNull() {
        NewCoinListingSubscribeRequest req = NewCoinListingSubscribeRequest.builder()
                .id(null)
                .subscribe(true)
                .build();
        req.getHeader().setUid(null);
        newCoinListingServiceImpl.subscribe(req).block();
    }

    @Test
    public void testSubscribe_subscribeTrue_success() {
        NewCoinListingSubscribeRequest req = NewCoinListingSubscribeRequest.builder()
                .id("id1")
                .subscribe(true)
                .build();
        req.getHeader().setUid(123L);
        req.getHeader().setUserId("u1");

        when(mockNewCoinSubscribeRepository.upsert(any(NewCoinSubscribeEntity.class)))
                .thenReturn(Mono.just(NewCoinSubscribeEntity.builder().newCoinDataId("id1").build()));

        StepVerifier.create(newCoinListingServiceImpl.subscribe(req))
                .expectSubscription()
                .assertNext(resp -> {
                    Assert.assertEquals(resp.getId(), "id1");
                    Assert.assertTrue(resp.getSubscribed());
                })
                .verifyComplete();
    }

    @Test
    public void testSubscribe_subscribeFalse_success() {
        NewCoinListingSubscribeRequest req = NewCoinListingSubscribeRequest.builder()
                .id("id2")
                .subscribe(false)
                .build();
        req.getHeader().setUid(123L);
        req.getHeader().setUserId("u2");

        when(mockNewCoinSubscribeRepository.removeByUid(123L, "id2"))
                .thenReturn(Mono.just(Boolean.TRUE));

        StepVerifier.create(newCoinListingServiceImpl.subscribe(req))
                .expectSubscription()
                .assertNext(resp -> {
                    Assert.assertEquals(resp.getId(), "id2");
                    Assert.assertFalse(resp.getSubscribed());
                })
                .verifyComplete();
    }

    @Test
    public void testSubscribe_onErrorResume() {
        NewCoinListingSubscribeRequest req = NewCoinListingSubscribeRequest.builder()
                .id("id3")
                .subscribe(true)
                .build();
        req.getHeader().setUid(123L);
        req.getHeader().setUserId("u3");

        when(mockNewCoinSubscribeRepository.upsert(any(NewCoinSubscribeEntity.class)))
                .thenReturn(Mono.error(new RuntimeException("db error")));

        StepVerifier.create(newCoinListingServiceImpl.subscribe(req))
                .expectSubscription()
                .assertNext(resp -> {
                    Assert.assertEquals(resp.getId(), "id3");
                    Assert.assertFalse(resp.getSubscribed());
                })
                .verifyComplete();
    }
}