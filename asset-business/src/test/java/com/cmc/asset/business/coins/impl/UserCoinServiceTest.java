package com.cmc.asset.business.coins.impl;

import com.cmc.asset.business.portfolio.IPortfolioMultiService;
import com.cmc.asset.business.portfolio.IPortfolioOldService;
import com.cmc.asset.business.watchlist.WatchListService;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.UserTopCoinsEntity;
import com.cmc.asset.dao.repository.mongo.UserTopCoinsRepository;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.model.contract.news.QueryTopCoinsParamDTO;
import com.cmc.asset.model.contract.news.QueryTopCoinsResultDTO;
import com.cmc.asset.model.contract.news.QueryUserTopCoinsParamDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioOldMultiQueryDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQueryResultDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiResultDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsRequestDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsResponseDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO.*;
import com.cmc.asset.model.enums.FollowTypeEnum;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.utils.JacksonUtils;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class UserCoinServiceTest {

    @Mock
    private WatchListService mockWatchListService;
    @Mock
    private IPortfolioMultiService mockPortfolioMultiService;
    @Mock
    private IPortfolioOldService mockPortfolioOldService;
    @Mock
    private UserTopCoinsRepository mockUserTopCoinsRepository;
    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;

    private ReactiveRedisTemplate<String, String> assetRedisTemplate;

    @InjectMocks
    private UserCoinService userCoinServiceUnderTest;

    private AutoCloseable mockitoCloseable;

    private final PortfolioQueryResultDTO queryResultDTO = PortfolioQueryResultDTO.builder()
            .cryptocurrencyId(0)
            .cryptoUnitPrice(new BigDecimal("0.00"))
            .fiatUnitPrice(new BigDecimal("0.00"))
            .amount(new BigDecimal("0.00"))
            .cryptoHoldings(new BigDecimal("0.00"))
            .plPercentValue(new BigDecimal("0.00"))
            .plValue(new BigDecimal("0.00"))
            .currentPrice(new BigDecimal("0.00"))
            .yesterdayChange(new BigDecimal("0.00"))
            .yesterdayChangePercent(new BigDecimal("0.00"))
            .oneHourChangePercent(new BigDecimal("0.00"))
            .sevenDaysChangePercent(new BigDecimal("0.00"))
            .holdingsPercent(new BigDecimal("0.00"))
            .name("name")
            .symbol("symbol")
            .slug("slug")
            .lastUpdated("lastUpdated")
            .totalFee(new BigDecimal("0.00"))
            .buyAvgPrice(new BigDecimal("0.00"))
            .totalBuySpent(new BigDecimal("0.00"))
            .jobFlag(false)
            .build();

    @BeforeMethod
    public void setUp() {
        assetRedisTemplate = Mockito.mock(ReactiveRedisTemplate.class, Mockito.RETURNS_DEEP_STUBS);
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(userCoinServiceUnderTest, "coinsLimit", 10);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testGetTopCoinsFromCache() {
        // Setup
        final QueryTopCoinsParamDTO queryTopCoinsParamDTO = new QueryTopCoinsParamDTO();

        // Configure cacheRepository.
        final UserTopCoinsEntity userTopCoinsEntityMono =
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10);
        final Mono<String> s = Mono.just(JacksonUtils.toJsonString(userTopCoinsEntityMono));
        when(assetRedisTemplate.opsForValue().get(any())).thenReturn(s);

        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.getTopCoins(queryTopCoinsParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                System.out.println(JacksonUtils.toJsonString(res));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testGetTopCoinsFromDb() {
        // Setup
        final QueryTopCoinsParamDTO queryTopCoinsParamDTO = new QueryTopCoinsParamDTO();

        // Configure cacheRepository.
        when(assetRedisTemplate.opsForValue().get(any())).thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(any(), any(), any())).thenReturn(Mono.just(true));

        // Configure UserTopCoinsRepository.findByUserId(...).
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.findFirstByUserId(any())).thenReturn(userTopCoinsEntityMono);

        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.getTopCoins(queryTopCoinsParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                System.out.println(JacksonUtils.toJsonString(res));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testGetTopCoinsFromPortfolio() {
        // Setup
        final QueryTopCoinsParamDTO queryTopCoinsParamDTO = new QueryTopCoinsParamDTO();

        // Configure cacheRepository.
        when(assetRedisTemplate.opsForValue().get(any())).thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(any(), any(), any())).thenReturn(Mono.just(true));

        // Configure UserTopCoinsRepository.findByUserId(...).
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.findFirstByUserId(any())).thenReturn(Mono.empty());

        // Configure IPortfolioMultiService.queryMain(...).
        Date time = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        PortfolioMultiResultDTO portfolioMultiResultDTO = PortfolioMultiResultDTO.builder()
            .portfolioSourceId("portfolioSourceId")
            .portfolioName("portfolioName")
            .totalAmount(new BigDecimal("0.00"))
            .bgColor("bgColor")
            .sortIndex(0)
            .ownWallet(false)
            .isMain(false)
            .timeCreated(time)
            .timeUpdated(time)
            .portfolioType("manual")
            .portfolioAvatar("portfolioAvatar")
            .walletAddress("walletAddress")
            .build();
        final Mono<PortfolioMultiResultDTO> portfolioMultiResultDTOMono = Mono.just(portfolioMultiResultDTO);
        when(mockPortfolioMultiService.queryMain(any())).thenReturn(portfolioMultiResultDTOMono);

        // Configure IPortfolioOldService.queryByPortfolioSourceId(...).
        final Flux<PortfolioQueryResultDTO> portfolioQueryResultDTOFlux = Flux.just(queryResultDTO);
        when(mockPortfolioOldService.queryByPortfolioSourceId(any(PortfolioOldMultiQueryDTO.class)))
            .thenReturn(portfolioQueryResultDTOFlux);

        // Configure CryptoCurrencyCache.getCryptoCurrencyById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(anyInt())).thenReturn(cryptoCurrencyInfoDTO);

        // Configure UserTopCoinsRepository.upsert(...).
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono1 = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.upsert(any(), anyList(), any(), any())).thenReturn(userTopCoinsEntityMono1);

        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.getTopCoins(queryTopCoinsParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                System.out.println(JacksonUtils.toJsonString(res));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testGetTopCoinsFromWatchlist() {
        // Setup
        final QueryTopCoinsParamDTO queryTopCoinsParamDTO = new QueryTopCoinsParamDTO();

        // Configure cacheRepository.
        when(assetRedisTemplate.opsForValue().get(any())).thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(any(), any(), any())).thenReturn(Mono.just(true));

        // Configure UserTopCoinsRepository.findByUserId(...).
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.findFirstByUserId(any())).thenReturn(Mono.empty());

        // Configure IPortfolioMultiService.queryMain(...).
        Date time = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        PortfolioMultiResultDTO portfolioMultiResultDTO = PortfolioMultiResultDTO.builder()
            .portfolioSourceId("portfolioSourceId")
            .portfolioName("portfolioName")
            .totalAmount(new BigDecimal("0.00"))
            .bgColor("bgColor")
            .sortIndex(0)
            .ownWallet(false)
            .isMain(false)
            .timeCreated(time)
            .timeUpdated(time)
            .portfolioType("manual")
            .portfolioAvatar("portfolioAvatar")
            .walletAddress("walletAddress")
            .build();
        final Mono<PortfolioMultiResultDTO> portfolioMultiResultDTOMono = Mono.just(portfolioMultiResultDTO);
        when(mockPortfolioMultiService.queryMain(any())).thenReturn(portfolioMultiResultDTOMono);

        // Configure IPortfolioOldService.queryByPortfolioSourceId(...).
        final Flux<PortfolioQueryResultDTO> portfolioQueryResultDTOFlux = Flux.just(queryResultDTO);
        when(mockPortfolioOldService.queryByPortfolioSourceId(any(PortfolioOldMultiQueryDTO.class)))
            .thenReturn(Flux.empty());

        // Configure CryptoCurrencyCache.getCryptoCurrencyById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(anyInt())).thenReturn(cryptoCurrencyInfoDTO);

        // Configure UserTopCoinsRepository.upsert(...).
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono1 = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.upsert(any(), any(), any(), any())).thenReturn(userTopCoinsEntityMono1);
        CryptoCurrencyDTO cryptoCurrencyDTO = CryptoCurrencyDTO.builder()
            .rank(1)
            .name("bitcoin")
            .symbol("BTC")
            .category("AI")
            .status("1")
            .slug("bitcoin")
            .tags("ai")
            .wrappedStakedMcRank(1)
            .marketPairCount(1000)
            .circulatingSupply(new BigDecimal("21000000"))
            .selfReportedCirculatingSupply(new BigDecimal("21000000"))
            .totalSupply(new BigDecimal("21000000"))
            .maxSupply(new BigDecimal("21000000"))
            .build();
        // Configure WatchListService.query(...).
        final Mono<WatchListResultDTO> watchListResultDTOMono = Mono.just(new WatchListResultDTO(List.of(
            new WatchListDTO("userId", "creator", false, "watchListId", 0, "name", "description", 0L, 0, 0, 0, 0, false, false,
                WatchListType.NONE, List.of(cryptoCurrencyDTO), List.of(new ExchangeDTO(0, "name", "slug", "status")),
                List.of(new MarketPairDTO(0, false)),null,null, null, "createdTime", "updatedTime", FollowTypeEnum.NONE,
                "sharedImageUrl", null)), WatchListType.NONE));
        when(mockWatchListService.query(any(QueryWatchListParamDTO.class)))
            .thenReturn(watchListResultDTOMono);

        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.getTopCoins(queryTopCoinsParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                System.out.println(JacksonUtils.toJsonString(res));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testResetTopCoins() {
        // Setup
        final BaseRequest request = BaseRequest.getInstance();

        // Configure IPortfolioMultiService.queryMain(...).
        Date time = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        PortfolioMultiResultDTO portfolioMultiResultDTO = PortfolioMultiResultDTO.builder()
            .portfolioSourceId("portfolioSourceId")
            .portfolioName("portfolioName")
            .totalAmount(new BigDecimal("0.00"))
            .bgColor("bgColor")
            .sortIndex(0)
            .ownWallet(false)
            .isMain(false)
            .timeCreated(time)
            .timeUpdated(time)
            .portfolioType("manual")
            .portfolioAvatar("portfolioAvatar")
            .walletAddress("walletAddress")
            .build();
        final Mono<PortfolioMultiResultDTO> portfolioMultiResultDTOMono = Mono.just(portfolioMultiResultDTO);
        when(mockPortfolioMultiService.queryMain(any())).thenReturn(portfolioMultiResultDTOMono);

        // Configure IPortfolioOldService.queryByPortfolioSourceId(...).
        final Flux<PortfolioQueryResultDTO> portfolioQueryResultDTOFlux = Flux.just(queryResultDTO);
        when(mockPortfolioOldService.queryByPortfolioSourceId(PortfolioOldMultiQueryDTO.builder().build()))
            .thenReturn(portfolioQueryResultDTOFlux);

        // Configure CryptoCurrencyCache.getCryptoCurrencyById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(anyInt())).thenReturn(cryptoCurrencyInfoDTO);

        // Configure UserTopCoinsRepository.upsert(...).
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.upsert(any(), any(), any(), any())).thenReturn(userTopCoinsEntityMono);
        CryptoCurrencyDTO cryptoCurrencyDTO = CryptoCurrencyDTO.builder()
            .rank(1)
            .name("bitcoin")
            .symbol("BTC")
            .category("AI")
            .status("1")
            .slug("bitcoin")
            .tags("ai")
            .wrappedStakedMcRank(1)
            .marketPairCount(1000)
            .circulatingSupply(new BigDecimal("21000000"))
            .selfReportedCirculatingSupply(new BigDecimal("21000000"))
            .totalSupply(new BigDecimal("21000000"))
            .maxSupply(new BigDecimal("21000000"))
            .build();
        // Configure WatchListService.query(...).
        final Mono<WatchListResultDTO> watchListResultDTOMono = Mono.just(new WatchListResultDTO(List.of(
            new WatchListDTO("userId", "creator", false, "watchListId", 0, "name", "description", 0L, 0, 0, 0, 0, false, false,
                WatchListType.NONE, List.of(cryptoCurrencyDTO), List.of(new ExchangeDTO(0, "name", "slug", "status")),
                List.of(new MarketPairDTO(0, false)),null,null, null, "createdTime", "updatedTime", FollowTypeEnum.NONE,
                "sharedImageUrl", null)), WatchListType.NONE));
        when(mockWatchListService.query(any(QueryWatchListParamDTO.class)))
            .thenReturn(watchListResultDTOMono);

        // Run the test
        final Mono<Boolean> result = userCoinServiceUnderTest.resetTopCoins(request, "source");

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                System.out.println(JacksonUtils.toJsonString(res));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testGetUserRelevantCoins() {
        // Setup
        UserRelevantCoinsResponseDTO userRelevantCoinsResponseDTO =
            UserRelevantCoinsResponseDTO.builder().watchListCoins(new ArrayList<>()).portfolioCoins(new ArrayList<>())
                .build();
        when(assetRedisTemplate.opsForValue().get(any()))
            .thenReturn(Mono.just(JacksonUtils.toJsonString(userRelevantCoinsResponseDTO)));

        final UserRelevantCoinsRequestDTO requestDTO = new UserRelevantCoinsRequestDTO("userId");

        // Configure IPortfolioMultiService.queryMain(...).
        PortfolioMultiResultDTO portfolioMultiResultDTO =
            PortfolioMultiResultDTO.builder().portfolioSourceId("").portfolioName("").totalAmount(new BigDecimal(0))
                .bgColor("").sortIndex(0).isMain(false).timeUpdated(new Date()).timeCreated(new Date()).build();

        final Mono<PortfolioMultiResultDTO> portfolioMultiResultDTOMono = Mono.just(portfolioMultiResultDTO);

        when(mockPortfolioMultiService.queryMain(any())).thenReturn(portfolioMultiResultDTOMono);

        // Configure IPortfolioOldService.queryByPortfolioSourceId(...).
        PortfolioQueryResultDTO portfolioQueryResultDTO =
            PortfolioQueryResultDTO.builder().cryptocurrencyId(0).cryptoUnitPrice(new BigDecimal(0))
                .fiatUnitPrice(new BigDecimal(0)).amount(new BigDecimal(0)).cryptoHoldings(new BigDecimal(0))
                .plPercentValue(new BigDecimal(0)).plValue(new BigDecimal(0)).currentPrice(new BigDecimal(0))
                .yesterdayChangePercent(new BigDecimal(0)).holdingsPercent(new BigDecimal(0)).name("").symbol("")
                .slug("").lastUpdated("").totalFee(new BigDecimal(0)).buyAvgPrice(new BigDecimal(0)).jobFlag(false)
                .build();
        final Flux<PortfolioQueryResultDTO> portfolioQueryResultDTOFlux = Flux.just(portfolioQueryResultDTO);

        when(mockPortfolioOldService.queryByPortfolioSourceId(any())).thenReturn(portfolioQueryResultDTOFlux);

        // Configure CryptoCurrencyCache.getCryptoCurrencyById(...).
        CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            CryptoCurrencyInfoDTO.builder().id(0).name("").symbol("").slug("").status("").is_active(0).rank(0).build();
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(0)).thenReturn(cryptoCurrencyInfoDTO);

        // Configure UserTopCoinsRepository.upsert(...).
        UserTopCoinsEntity userTopCoinsEntity =
            UserTopCoinsEntity.builder().id(new ObjectId()).userId("").portfolioCoins(new ArrayList<>())
                .watchlistCoins(new ArrayList<>()).coinsLimit(0).build();
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono = Mono.just(userTopCoinsEntity);
        when(mockUserTopCoinsRepository.upsert(any(), any(), any(), any())).thenReturn(userTopCoinsEntityMono);

        // Configure WatchListService.query(...).
        final Mono<WatchListResultDTO> watchListResultDTOMono = Mono.just(WatchListResultDTO.builder()
            .watchLists(List.of(WatchListDTO.builder()
                .cryptoCurrencies(List.of(CryptoCurrencyDTO.builder()
                    .id(0)
                    .build()))
                .build()))
            .build());

        when(mockWatchListService.query(any())).thenReturn(watchListResultDTOMono);

        // Configure UserTopCoinsRepository.findByUserId(...).
        UserTopCoinsEntity userTopCoinsEntity2 =
            UserTopCoinsEntity.builder().id(new ObjectId()).userId("").portfolioCoins(new ArrayList<>())
                .watchlistCoins(new ArrayList<>()).coinsLimit(0).build();
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono1 = Mono.just(userTopCoinsEntity2);

        when(mockUserTopCoinsRepository.findFirstByUserId(any())).thenReturn(userTopCoinsEntityMono1);

        // Run the test
        final Mono<UserRelevantCoinsResponseDTO> result = userCoinServiceUnderTest.getUserRelevantCoins(requestDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @DataProvider(name = "CoinIdParameterProvider")
    public static Object[][] CoinIdParameterProvider() {
        return new Object[][] {
            {
                5,  // topN
                List.of(1, 1027, 2, 28, 99, 222),   // portfolioCoinIds
                List.of(3, 5, 6),                   //  watchlistCoinIds
                6,   // expected coin size
                Set.of(1, 1027, 2, 28, 99, 222)   // expected coinIds
            },
            {
                5,  // topN
                List.of(1,1027,2),   // portfolioCoinIds
                List.of(),          // watchlistCoinIds
                3,   // expected coin size
                Set.of(1,1027,2)   // expected coinIds
            },
            {
                5,  // topN
                List.of(1, 1027, 2),    // portfolioCoinIds
                List.of(3, 25, 6, 288), // watchlistCoinIds
                7,   // expected coin size
                Set.of(1, 1027, 2, 3, 25, 6, 288)  // expected coinIds
            },
            {
                5,  // topN
                List.of(1, 1027, 2),                   // portfolioCoinIds
                List.of(1, 1027, 2, 6, 288, 556, 999), // watchlistCoinIds
                7,   // expected coin size
                Set.of(1, 1027, 2, 6, 288, 556, 999)  // expected coinIds
            },
            {
                5,  // topN
                List.of(),          // portfolioCoinIds
                List.of(3, 5, 6),   //  watchlistCoinIds
                3,   // expected coin size
                Set.of(3,5,6)   // expected coinIds
            },
            {
                5,  // topN
                List.of(),  // portfolioCoinIds
                List.of(),  //  watchlistCoinIds
                0,          // expected coin size
                Set.of()    // expected coinIds
            }
        };
    }

    // This test will run 6 times since we have 5 parameters defined
    @Test(dataProvider = "CoinIdParameterProvider")
    public void testFindInTopCoins(int topN, List<Integer> portfolioCoinIds, List<Integer> watchlistCoinIds, int expectedCoinSize, Set<Integer> expectedCoinIds) {
        // Setup
        final String userId = "userId";
        UserTopCoinsEntity userTopCoinsEntity =
            UserTopCoinsEntity.builder()
                .id(new ObjectId())
                .userId(userId)
                .portfolioCoins(portfolioCoinIds)
                .watchlistCoins(watchlistCoinIds)
                .coinsLimit(10)
                .build();
        when(mockUserTopCoinsRepository.findFirstByUserId(any()))
            .thenReturn(Mono.just(userTopCoinsEntity));

        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.findInTopCoins(userId, topN);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertEquals(res.getCoins().size(), expectedCoinSize);
                res.getCoins().forEach(coinDTO -> {
                    Assert.assertTrue(expectedCoinIds.contains(coinDTO.getId()));
                });
            })
            .verifyComplete();
    }

    // This test will run 6 times since we have 5 parameters defined
    @Test(dataProvider = "CoinIdParameterProvider")
    public void testFindInMainPortfolioAndWatchList(int topN, List<Integer> portfolioCoinIds, List<Integer> watchlistCoinIds, int expectedCoinSize, Set<Integer> expectedCoinIds) {
        // Setup
        String userId = "userId";
        QueryUserTopCoinsParamDTO queryUserTopCoinsParamDTO = new QueryUserTopCoinsParamDTO();
        queryUserTopCoinsParamDTO.getHeader().setUserId(userId);
        queryUserTopCoinsParamDTO.setTop(topN);

        mockFindInMainPortfolio(userId, portfolioCoinIds);
        mockFindInMainWatchlist(queryUserTopCoinsParamDTO, watchlistCoinIds);

        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.findInMainPortfolioAndWatchList(queryUserTopCoinsParamDTO, topN);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertEquals(res.getCoins().size(), expectedCoinSize);
                res.getCoins().forEach(coinDTO -> {
                    Assert.assertTrue(expectedCoinIds.contains(coinDTO.getId()));
                });
            })
            .verifyComplete();
    }

    private void mockFindInMainPortfolio(String userId, List<Integer> coinIds) {
        when(mockPortfolioMultiService.queryMain(userId))
            .thenReturn(Mono.just(
                PortfolioMultiResultDTO.builder()
                    .portfolioSourceId("portfolioSourceId")
                    .build())
            );

        PortfolioOldMultiQueryDTO portfolioOldMultiQueryDTO = PortfolioOldMultiQueryDTO.builder().build();
        portfolioOldMultiQueryDTO.setSecretKey("systemSecretKey");
        portfolioOldMultiQueryDTO.setPortfolioSourceId("portfolioSourceId");
        portfolioOldMultiQueryDTO.setUserId(userId);

        final Flux<PortfolioQueryResultDTO> portfolioQueryResultDTOFlux = Flux.fromIterable(coinIds)
            .map(this::mockPortfolioQueryResultDTO);
        when(mockPortfolioOldService.queryByPortfolioSourceId(any(PortfolioOldMultiQueryDTO.class)))
            .thenReturn(portfolioQueryResultDTOFlux);

        for (Integer coinId : coinIds) {
            when(mockCryptoCurrencyCache.getCryptoCurrencyById(coinId))
                .thenReturn(mockCryptoCurrencyInfoDTO(coinId));
        }

        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono1 = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.upsert(any(), any(), any(), any())).thenReturn(userTopCoinsEntityMono1);
    }

    private PortfolioQueryResultDTO mockPortfolioQueryResultDTO(Integer cryptocurrencyId) {
        return PortfolioQueryResultDTO.builder()
            .cryptocurrencyId(cryptocurrencyId)
            .name("name")
            .symbol("symbol")
            .slug("slug")
            .build();
    }

    private CryptoCurrencyInfoDTO mockCryptoCurrencyInfoDTO(Integer cryptocurrencyId) {
        return CryptoCurrencyInfoDTO.builder()
            .id(cryptocurrencyId)
            .rank(cryptocurrencyId)
            .name("name")
            .symbol("symbol")
            .slug("slug")
            .build();
    }

    private void mockFindInMainWatchlist(BaseRequest request, List<Integer> coinIds) {
        when(mockWatchListService.query(userCoinServiceUnderTest.buildWatchlistQueryParam(request)))
            .thenReturn(Mono.just(
                WatchListResultDTO.builder()
                    .watchLists(List.of(mockWatchListDTO(coinIds)))
                    .build())
            );

        for (Integer coinId : coinIds) {
            when(mockCryptoCurrencyCache.getCryptoCurrencyById(coinId))
                .thenReturn(mockCryptoCurrencyInfoDTO(coinId));
        }

        String userId = request.getHeader().getUserId();
        final Mono<UserTopCoinsEntity> userTopCoinsEntityMono1 = Mono.just(
            new UserTopCoinsEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", List.of(0), List.of(0), 10));
        when(mockUserTopCoinsRepository.upsert(any(), any(), any(), any())).thenReturn(userTopCoinsEntityMono1);
    }

    private WatchListDTO mockWatchListDTO(List<Integer> coinIds) {
        return WatchListDTO.builder()
            .cryptoCurrencies(mockCryptoCurrencyDTO(coinIds))
            .build();
    }

    private List<CryptoCurrencyDTO> mockCryptoCurrencyDTO(List<Integer> coinIds) {
        return coinIds.stream()
            .map(coinId -> CryptoCurrencyDTO.builder()
                .id(coinId)
                .rank(coinId)
                .name("name")
                .symbol("symbol")
                .slug("slug")
                .build())
            .collect(Collectors.toList());
    }

    @DataProvider(name = "GetUserTopCoinsParameterProvider")
    public static Object[][] GetUserTopCoinsParameterProvider() {
        return new Object[][] {
            {
                5,  // topN
                List.of(1, 1027, 2, 28, 99, 222),   // portfolioCoinIds
                List.of(3, 5, 6),                   //  watchlistCoinIds
                5,   // expected coin size
                Set.of(1, 2, 28, 99, 222)   // expected coinIds
            },
            {
                5,  // topN
                List.of(1, 1027, 2),   // portfolioCoinIds
                List.of(),          // watchlistCoinIds
                3,   // expected coin size
                Set.of(1, 2, 1027)   // expected coinIds
            },
            {
                5,  // topN
                List.of(1, 1027, 2),    // portfolioCoinIds
                List.of(3, 25, 6, 288), // watchlistCoinIds
                5,   // expected coin size
                Set.of(1, 2, 3, 6, 25)  // expected coinIds
            },
            {
                5,  // topN
                List.of(1, 1027, 2),                   // portfolioCoinIds
                List.of(1, 1027, 2, 6, 288, 556, 999), // watchlistCoinIds
                5,   // expected coin size
                Set.of(1, 2, 6, 288, 556)  // expected coinIds
            },
            {
                5,  // topN
                List.of(),          // portfolioCoinIds
                List.of(3, 5, 6),   //  watchlistCoinIds
                3,   // expected coin size
                Set.of(3, 5, 6)   // expected coinIds
            },
            {
                5,  // topN
                List.of(),  // portfolioCoinIds
                List.of(),  //  watchlistCoinIds
                0,          // expected coin size
                Set.of()    // expected coinIds
            }
        };
    }

    // This test will run 6 times since we have 5 parameters defined
    @Test(dataProvider = "GetUserTopCoinsParameterProvider")
    public void testGetUserTopCoins_shouldFindInTopCoins(int topN, List<Integer> portfolioCoinIds, List<Integer> watchlistCoinIds, int expectedCoinSize, Set<Integer> expectedCoinIds) {
        // Setup
        String userId = "userId";
        QueryUserTopCoinsParamDTO queryUserTopCoinsParamDTO = new QueryUserTopCoinsParamDTO();
        queryUserTopCoinsParamDTO.getHeader().setUserId(userId);
        queryUserTopCoinsParamDTO.setTop(topN);
        when(assetRedisTemplate.opsForValue().get(any())).thenReturn(Mono.empty());
        mockFindInMainPortfolio(userId, Collections.EMPTY_LIST);
        mockFindInMainWatchlist(queryUserTopCoinsParamDTO, Collections.EMPTY_LIST);


        // the results are found in method: "findInTopCoins"
        mockFindInTopCoins(userId, portfolioCoinIds, watchlistCoinIds);


        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.getUserTopCoins(queryUserTopCoinsParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertEquals(res.getCoins().size(), expectedCoinSize);
                res.getCoins().forEach(coinDTO -> {
                    Assert.assertTrue(expectedCoinIds.contains(coinDTO.getId()));
                });
            })
            .verifyComplete();
    }

    // This test will run 6 times since we have 5 parameters defined
    @Test(dataProvider = "GetUserTopCoinsParameterProvider")
    public void testGetUserTopCoins_shouldFindInMainPortfolioAndWatchList(int topN, List<Integer> portfolioCoinIds, List<Integer> watchlistCoinIds, int expectedCoinSize, Set<Integer> expectedCoinIds) {
        // Setup
        String userId = "userId";
        QueryUserTopCoinsParamDTO queryUserTopCoinsParamDTO = new QueryUserTopCoinsParamDTO();
        queryUserTopCoinsParamDTO.getHeader().setUserId(userId);
        queryUserTopCoinsParamDTO.setTop(topN);
        when(assetRedisTemplate.opsForValue().get(any())).thenReturn(Mono.empty());
        mockFindInTopCoins(userId, Collections.EMPTY_LIST, Collections.EMPTY_LIST);


        // the results are found in method: "findInMainPortfolioAndWatchList"
        mockFindInMainPortfolio(userId, portfolioCoinIds);
        mockFindInMainWatchlist(queryUserTopCoinsParamDTO, watchlistCoinIds);


        // Run the test
        final Mono<QueryTopCoinsResultDTO> result = userCoinServiceUnderTest.getUserTopCoins(queryUserTopCoinsParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertEquals(res.getCoins().size(), expectedCoinSize);
                res.getCoins().forEach(coinDTO -> {
                    Assert.assertTrue(expectedCoinIds.contains(coinDTO.getId()));
                });
            })
            .verifyComplete();
    }

    private void mockFindInTopCoins(String userId, List<Integer> portfolioCoinIds, List<Integer> watchlistCoinIds) {
        UserTopCoinsEntity userTopCoinsEntity =
            UserTopCoinsEntity.builder()
                .id(new ObjectId())
                .userId(userId)
                .portfolioCoins(portfolioCoinIds)
                .watchlistCoins(watchlistCoinIds)
                .coinsLimit(10)
                .build();

        when(mockUserTopCoinsRepository.findFirstByUserId(any()))
            .thenReturn(Mono.just(userTopCoinsEntity));

        // mock by portfolioCoinIds
        for (Integer coinId : portfolioCoinIds) {
            when(mockCryptoCurrencyCache.getCryptoCurrencyById(coinId))
                .thenReturn(mockCryptoCurrencyInfoDTO(coinId));
        }

        // mock by watchlistCoinIds
        for (Integer coinId : watchlistCoinIds) {
            when(mockCryptoCurrencyCache.getCryptoCurrencyById(coinId))
                .thenReturn(mockCryptoCurrencyInfoDTO(coinId));
        }
    }
}
