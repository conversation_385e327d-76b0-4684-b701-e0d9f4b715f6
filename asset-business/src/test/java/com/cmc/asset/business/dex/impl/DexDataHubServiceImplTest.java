package com.cmc.asset.business.dex.impl;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.cmc.asset.cache.DexerPlatformCache;
import com.cmc.asset.dao.entity.vo.DexTokenDetailCacheVo;
import com.cmc.asset.dao.repository.redis.CryptoCacheRepository;
import com.cmc.asset.domain.dex.DataHubTokenDetailDTO;
import com.cmc.asset.integration.DexDataHubClient;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.data.common.utils.JacksonUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class DexDataHubServiceImplTest {

    @InjectMocks
    private DexDataHubServiceImpl dexDataHubServiceImpl;

    @Mock
    private DexDataHubClient dexDataHubClient;

    @Mock
    private CryptoCacheRepository cryptoCacheRepository;

    @Mock
    private DexerPlatformCache dexerPlatformCache;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(dexDataHubServiceImpl, "cacheTimeSec", 120L);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testBatchGetDexTokenWithCache_EmptyInput() {
        // Given
        Set<String> emptySet = Set.of();

        // When
        Mono<List<DexTokenDetailCacheVo>> resultMono = dexDataHubServiceImpl.batchGetDexTokenWithCache(emptySet);

        // Then
        StepVerifier.create(resultMono)
            .expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                org.testng.Assert.assertNotNull(result);
                org.testng.Assert.assertTrue(result.isEmpty());
            })
            .verifyComplete();
    }

    @Test
    void testBatchGetDexTokenWithCache_AllCached() {
        // Given
        Set<String> uniKeySet = Set.of("1_0x123", "2_0x456");
        DexTokenDetailCacheVo cachedToken1 = DexTokenDetailCacheVo.builder()
            .uniKey("1_0x123")
            .name("Token1")
            .symbol("TKN1")
            .address("0x123")
            .platformId(1)
            .build();
        DexTokenDetailCacheVo cachedToken2 = DexTokenDetailCacheVo.builder()
            .uniKey("2_0x456")
            .name("Token2")
            .symbol("TKN2")
            .address("0x456")
            .platformId(2)
            .build();

        when(cryptoCacheRepository.getDexTokens(anySet()))
            .thenReturn(Flux.just(
                JacksonUtils.getInstance().serialize(cachedToken1),
                JacksonUtils.getInstance().serialize(cachedToken2)
            ));

        // When
        Mono<List<DexTokenDetailCacheVo>> resultMono = dexDataHubServiceImpl.batchGetDexTokenWithCache(uniKeySet);

        // Then
        StepVerifier.create(resultMono)
            .expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                org.testng.Assert.assertNotNull(result);
                org.testng.Assert.assertEquals(result.size(), 2);
                org.testng.Assert.assertEquals(result.get(0).getUniKey(), "1_0x123");
                org.testng.Assert.assertEquals(result.get(1).getUniKey(), "2_0x456");
            })
            .verifyComplete();
    }

    @Test
    void testBatchGetDexTokenWithCache_PartialCached() {
        // Given
        Set<String> uniKeySet = Set.of("1_0x123", "2_0x456");
        DexTokenDetailCacheVo cachedToken = DexTokenDetailCacheVo.builder()
            .uniKey("1_0x123")
            .name("Token1")
            .symbol("TKN1")
            .address("0x123")
            .platformId(1)
            .build();

        PlatformNewDTO platformDTO = new PlatformNewDTO();
        platformDTO.setId(2);
        platformDTO.setDn("ethereum");

        DataHubTokenDetailDTO fetchedToken = new DataHubTokenDetailDTO();
        fetchedToken.setName("Token2");
        fetchedToken.setSymbol("TKN2");
        fetchedToken.setAddress("0x456");
        fetchedToken.setPlatformId(2);

        when(cryptoCacheRepository.getDexTokens(anySet()))
            .thenReturn(Flux.just(JacksonUtils.getInstance().serialize(cachedToken)));
        when(dexerPlatformCache.getPlatformsById(eq(2)))
            .thenReturn(platformDTO);
        when(dexDataHubClient.getTokenDetail(eq("ethereum"), anyList()))
            .thenReturn(Mono.just(List.of(fetchedToken)));
        when(cryptoCacheRepository.cacheDexTokens(anyList(), anyLong()))
            .thenReturn(Mono.empty());

        // When
        Mono<List<DexTokenDetailCacheVo>> resultMono = dexDataHubServiceImpl.batchGetDexTokenWithCache(uniKeySet);

        // Then
        StepVerifier.create(resultMono)
            .expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                org.testng.Assert.assertNotNull(result);
                org.testng.Assert.assertEquals(result.size(), 2);
            })
            .verifyComplete();
    }

    @Test
    void testBatchGetDexTokenWithCache_NoneCached() {
        // Given
        Set<String> uniKeySet = Set.of("1_0x123", "2_0x456");

        PlatformNewDTO platformDTO1 = new PlatformNewDTO();
        platformDTO1.setId(1);
        platformDTO1.setDn("ethereum");

        PlatformNewDTO platformDTO2 = new PlatformNewDTO();
        platformDTO2.setId(2);
        platformDTO2.setDn("bsc");

        DataHubTokenDetailDTO fetchedToken1 = new DataHubTokenDetailDTO();
        fetchedToken1.setName("Token1");
        fetchedToken1.setSymbol("TKN1");
        fetchedToken1.setAddress("0x123");
        fetchedToken1.setPlatformId(1);

        DataHubTokenDetailDTO fetchedToken2 = new DataHubTokenDetailDTO();
        fetchedToken2.setName("Token2");
        fetchedToken2.setSymbol("TKN2");
        fetchedToken2.setAddress("0x456");
        fetchedToken2.setPlatformId(2);

        when(cryptoCacheRepository.getDexTokens(anySet()))
            .thenReturn(Flux.empty());
        when(dexerPlatformCache.getPlatformsById(eq(1)))
            .thenReturn(platformDTO1);
        when(dexerPlatformCache.getPlatformsById(eq(2)))
            .thenReturn(platformDTO2);
        when(dexDataHubClient.getTokenDetail(eq("ethereum"), anyList()))
            .thenReturn(Mono.just(List.of(fetchedToken1)));
        when(dexDataHubClient.getTokenDetail(eq("bsc"), anyList()))
            .thenReturn(Mono.just(List.of(fetchedToken2)));
        when(cryptoCacheRepository.cacheDexTokens(anyList(), anyLong()))
            .thenReturn(Mono.empty());

        // When
        Mono<List<DexTokenDetailCacheVo>> resultMono = dexDataHubServiceImpl.batchGetDexTokenWithCache(uniKeySet);

        // Then
        StepVerifier.create(resultMono)
            .expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                org.testng.Assert.assertNotNull(result);
                org.testng.Assert.assertEquals(result.size(), 2);
            })
            .verifyComplete();
    }

    @Test
    void testBatchGetDexTokenMapWithCache() {
        // Given
        Set<String> uniKeySet = Set.of("1_0x123", "2_0x456");
        DexTokenDetailCacheVo cachedToken1 = DexTokenDetailCacheVo.builder()
            .uniKey("1_0x123")
            .name("Token1")
            .symbol("TKN1")
            .address("0x123")
            .platformId(1)
            .build();
        DexTokenDetailCacheVo cachedToken2 = DexTokenDetailCacheVo.builder()
            .uniKey("2_0x456")
            .name("Token2")
            .symbol("TKN2")
            .address("0x456")
            .platformId(2)
            .build();

        when(cryptoCacheRepository.getDexTokens(anySet()))
            .thenReturn(Flux.just(
                JacksonUtils.getInstance().serialize(cachedToken1),
                JacksonUtils.getInstance().serialize(cachedToken2)
            ));

        // When
        Mono<Map<String, DexTokenDetailCacheVo>> resultMono = dexDataHubServiceImpl.batchGetDexTokenMapWithCache(uniKeySet);

        // Then
        StepVerifier.create(resultMono)
            .expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                org.testng.Assert.assertNotNull(result);
                org.testng.Assert.assertEquals(result.size(), 2);
                org.testng.Assert.assertNotNull(result.get("1_0x123"));
                org.testng.Assert.assertNotNull(result.get("2_0x456"));
                org.testng.Assert.assertEquals(result.get("1_0x123").getName(), "Token1");
                org.testng.Assert.assertEquals(result.get("2_0x456").getName(), "Token2");
            })
            .verifyComplete();
    }



} 