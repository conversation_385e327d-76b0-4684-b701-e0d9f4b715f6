package com.cmc.asset.business.portfolio.manager;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.business.portfolio.WalletAssetService;
import com.cmc.asset.business.portfolio.impl.CurrencyPriceService;
import com.cmc.asset.business.portfolio.impl.PortfolioBaseService;
import com.cmc.asset.cache.DexerPlatformCache;
import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioWalletDetailPO;
import com.cmc.asset.dao.repository.mongo.PortfolioWalletDetailRepository;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.portfolio.WalletSummaryDTO;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.function.Tuple2;

public class PortfolioWalletDetailManagerTest {

    @Mock
    private PortfolioWalletDetailRepository mockPortfolioWalletDetailRepository;
    @Mock
    private CurrencyPriceService currencyPriceService;
    @Mock
    private WalletAssetService walletAssetService;
    @Mock
    private PortfolioBaseService portfolioBaseService;
    @Mock
    private DexerPlatformCache dexerPlatformCache;

    @InjectMocks
    private PortfolioWalletDetailManager portfolioWalletDetailManagerUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(portfolioWalletDetailManagerUnderTest, "walletTokenValueLtLimit", "100");
        ReflectionTestUtils.setField(portfolioWalletDetailManagerUnderTest, "walletTokenVolume24hGtLimit", "100");
    }

    @AfterMethod
    public void tearDown() throws Exception {

        mockitoCloseable.close();
    }

    @Test
    public void testGetOneDetailStr() {

        PortfolioWalletDetailPO walletDetailPO = PortfolioWalletDetailPO.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).blockChains(List.of()).build();
        when(mockPortfolioWalletDetailRepository
            .findFirstDetail(any(ObjectId.class),
                Mockito.anyString())).thenReturn(Mono.just(
                walletDetailPO));

        when(portfolioBaseService.refreshValue(any(), any(), any(), any(), any())).thenReturn(Mono.just(walletDetailPO));
        when(currencyPriceService.queryCurrentPrices(anyList())).thenReturn(Mono.just(Map.of()));
        when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, new PlatformNewDTO()));
        when(walletAssetService.getWalletDetailPOByAddress("5c9c0ec4bb6e9d3ead204db1",
            new ObjectId("5c9c0ec4bb6e9d3ead204db1"), "5c9c0ec4bb6e9d3ead204db1", true))
            .thenReturn(Mono.just(PortfolioWalletDetailPO.builder()
                .blockChains(List.of())
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .portfolioSourceId("5c9c0ec4bb6e9d3ead204db1")
                .build()));

        PortfolioMultiEntity multiEntity = PortfolioMultiEntity.builder()
            .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
            .portfolioSourceId("5c9c0ec4bb6e9d3ead204db1")
            .walletAddress("5c9c0ec4bb6e9d3ead204db1")
            .build();

        Mono<Tuple2<PortfolioWalletDetailPO, List<WalletSummaryDTO>>> result =
            portfolioWalletDetailManagerUnderTest.getOneDetail(multiEntity, true);
        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            Assert.assertNotNull(result);
        }).verifyComplete();
    }

    @Test
    public void testGetOneDetailObjectId() {

        when(mockPortfolioWalletDetailRepository
            .findFirstDetail(any(ObjectId.class),
                Mockito.anyString())).thenReturn(Mono.just(
            PortfolioWalletDetailPO.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).build()));

        Mono<PortfolioWalletDetailPO> result = portfolioWalletDetailManagerUnderTest
            .getOneDetail(new ObjectId("5c9c0ec4bb6e9d3ead204db1"), "5c9c0ec4bb6e9d3ead204db1");
        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            Assert.assertNotNull(result);
        }).verifyComplete();
    }

    @Test
    public void testGetOneDetailObjectIdFalse() {
        when(mockPortfolioWalletDetailRepository.findFirstDetail(any(), any())).thenReturn(Mono.just(
            PortfolioWalletDetailPO.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).build()));

        Mono<PortfolioWalletDetailPO> result = portfolioWalletDetailManagerUnderTest
            .getOneDetail(new ObjectId("5c9c0ec4bb6e9d3ead204db1"), "5c9c0ec4bb6e9d3ead204db1");
        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            Assert.assertNotNull(result);
        }).verifyComplete();
    }
}
