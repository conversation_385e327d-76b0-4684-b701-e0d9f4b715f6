package com.cmc.asset.business.watchlist.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.UserSubscribeEntity;
import com.cmc.asset.dao.repository.mongo.UserSubscribeRepository;
import com.cmc.asset.domain.watchlist.UserSubscribeVO;
import com.cmc.asset.model.contract.user.UserSubscribeAdminListDTO;
import java.util.List;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * @ClassName UserSubscribeServiceImplTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/12/29 上午9:32
 **/
public class UserSubscribeServiceImplTest {

    @Mock
    private UserSubscribeRepository mockUserSubscribeRepository;

    @InjectMocks
    private UserSubscribeServiceImpl userSubscribeServiceImplTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @BeforeClass
    public static void init() {
        ReflectionTestUtils.setField(UserSubscribeServiceImpl.class, "subscribeQueryTime", 7);
    }

    @Test
    private void testList() {
        UserSubscribeAdminListDTO build = UserSubscribeAdminListDTO.builder().build();
        final Mono<List<UserSubscribeEntity>> listMono = Mono.just(Lists.newArrayList(
            UserSubscribeEntity.builder().subscribeType("SUBSCRIBE").resourceId(1).build(),
            UserSubscribeEntity.builder().subscribeType("UNSUBSCRIBE").resourceId(1).build(),
            UserSubscribeEntity.builder().subscribeType("SUBSCRIBE").resourceId(2).build()
        ));
        when(mockUserSubscribeRepository.queryByUidAndTime(any(), any())).thenReturn(listMono);
        // Run the test
        Mono<List<UserSubscribeVO>> result = userSubscribeServiceImplTest.list(build);
        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }
}
