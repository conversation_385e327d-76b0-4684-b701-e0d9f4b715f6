package com.cmc.asset.business.portfolio.impl;

import com.cmc.asset.business.coins.IUserCoinService;
import com.cmc.asset.business.coins.impl.UserCoinService;
import com.cmc.asset.business.portfolio.IPortfolioAppAdapterService;
import com.cmc.asset.business.portfolio.IPortfolioTradeStrategy;
import com.cmc.asset.business.portfolio.PortfolioThirdPartyService;
import com.cmc.asset.business.portfolio.manager.PortfolioPlManager;
import com.cmc.asset.business.portfolio.manager.PortfolioValueManager;
import com.cmc.asset.business.portfolio.strategy.PortfolioManualStrategyImpl;
import com.cmc.asset.business.portfolio.strategy.PortfolioStrategyBeanFactory;
import com.cmc.asset.business.portfolio.strategy.PortfolioWalletStrategyImpl;
import com.cmc.asset.dao.entity.portfolio.PortfolioEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioHoldingEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioPLEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioPlChangeRecordPO;
import com.cmc.asset.dao.entity.portfolio.PortfolioSnapshot1dPO;
import com.cmc.asset.dao.entity.portfolio.PortfolioSnapshot5mPO;
import com.cmc.asset.dao.entity.portfolio.PortfolioWalletDetailPO;
import com.cmc.asset.dao.repository.mongo.PortfolioMultiRepository;
import com.cmc.asset.dao.repository.mongo.PortfolioPLRepository;
import com.cmc.asset.dao.repository.mongo.PortfolioWalletDetailRepository;
import com.cmc.asset.mapstruct.ValueConverter;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiAddDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiDeleteDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiInitDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiListQueryDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiListUpdateDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiQueryByCryptoIdDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiQueryDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiResultDTO;
import com.cmc.asset.model.contract.portfolio.multi.PortfolioMultiUpdateDTO;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.Header;
import com.cmc.data.common.utils.JacksonUtils;
import com.mongodb.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.mongodb.core.ReactiveCollectionCallback;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import org.testng.collections.Lists;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class PortfolioMultiServiceImplTest {

    private PortfolioMultiServiceImpl portfolioMultiService;

    private PortfolioMultiRepository portfolioMultiRepository;

    private ReactiveMongoTemplate reactiveMongoTemplate;

    private PortfolioPlManager portfolioPlManager;

    private ReactiveRedisTemplate<String, String> assetRedisTemplate;

    private IUserCoinService userCoinService;

    private PortfolioStrategyBeanFactory portfolioStrategyBeanFactory;

    private PortfolioManualStrategyImpl portfolioManualStrategy;

    private PortfolioWalletStrategyImpl portfolioWalletStrategy;

    private PortfolioWalletDetailRepository portfolioWalletDetailRepository;

    private PortfolioValueManager portfolioValueManager;

    private PortfolioThirdPartyService portfolioThirdPartyService;

    private IPortfolioAppAdapterService portfolioAppAdapterService;
    private PortfolioBaseService portfolioBaseService;

    private static final String MOCK_ID = "60dd3a4d99ad671cabb95d6e";

    private static final String MOCK_USER_ID = "60b850e8dae0a17606cd1cf2";

    private static final Long maxCount = 20L;

    @BeforeTest
    public void setup() {
        portfolioMultiService = new PortfolioMultiServiceImpl();
        portfolioMultiRepository = Mockito.mock(PortfolioMultiRepository.class);
        reactiveMongoTemplate = Mockito.mock(ReactiveMongoTemplate.class);
        portfolioPlManager = Mockito.mock(PortfolioPlManager.class);
        assetRedisTemplate = Mockito.mock(ReactiveRedisTemplate.class, Mockito.RETURNS_DEEP_STUBS);
        userCoinService = Mockito.mock(UserCoinService.class);
        portfolioStrategyBeanFactory = Mockito.mock(PortfolioStrategyBeanFactory.class);
        portfolioManualStrategy = Mockito.mock(PortfolioManualStrategyImpl.class);
        portfolioWalletStrategy = Mockito.mock(PortfolioWalletStrategyImpl.class);
        portfolioWalletDetailRepository = Mockito.mock(PortfolioWalletDetailRepository.class);
        portfolioValueManager = Mockito.mock(PortfolioValueManager.class);
        portfolioThirdPartyService = Mockito.mock(PortfolioThirdPartyServiceImpl.class);
        portfolioAppAdapterService = Mockito.mock(PortfolioAppAdapterServiceImpl.class);
        portfolioBaseService = Mockito.mock(PortfolioBaseService.class);

        ReflectionTestUtils.setField(portfolioMultiService, "portfolioMultiRepository", portfolioMultiRepository);
        ReflectionTestUtils.setField(portfolioMultiService, "reactiveMongoTemplate", reactiveMongoTemplate);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioPlManager", portfolioPlManager);
        ReflectionTestUtils.setField(portfolioMultiService, "assetRedisTemplate", assetRedisTemplate);
        ReflectionTestUtils.setField(portfolioMultiService, "maxCount", maxCount);
        ReflectionTestUtils.setField(portfolioMultiService, "userCoinService", userCoinService);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioStrategyBeanFactory", portfolioStrategyBeanFactory);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioWalletDetailRepository", portfolioWalletDetailRepository);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioThirdPartyService", portfolioThirdPartyService);
        ReflectionTestUtils.setField(portfolioMultiService, "checkEmoji", true);
        ReflectionTestUtils.setField(portfolioMultiService, "nameLengthLimit", 32);
        ReflectionTestUtils.setField(portfolioMultiService, "queryMainSwitch", true);
        ReflectionTestUtils.setField(portfolioMultiService, "allowNoMainSwitch", false);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioValueManager", portfolioValueManager);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioAppAdapterService", portfolioAppAdapterService);
        ReflectionTestUtils.setField(portfolioMultiService, "portfolioBaseService", portfolioBaseService);
        ReflectionTestUtils.setField(portfolioMultiService, "enableWebOverview", true);


    }

    @Test
    public void testQueryMain() {
        List<PortfolioMultiEntity> list=new ArrayList<>();
        list.add(PortfolioMultiEntity.builder().id(new ObjectId(MOCK_ID)).portfolioSourceId("p1").portfolioType("manual").isMain(false).build());
        list.add(PortfolioMultiEntity.builder().id(new ObjectId(MOCK_ID)).portfolioSourceId("p2").portfolioType("").isMain(true).build());
        list.add(PortfolioMultiEntity.builder().id(new ObjectId(MOCK_ID)).portfolioSourceId("p3").portfolioType("manual").isMain(true).build());
        when(portfolioMultiRepository.findAll(Mockito.any(Example.class))
        ).thenReturn(Flux.fromIterable(list));
        StepVerifier.create(portfolioMultiService.queryMain(MOCK_USER_ID)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }
    @Test
    public void testInitMain() {
        PortfolioMultiInitDTO portfolioMultiInitDTO = new PortfolioMultiInitDTO();
        portfolioMultiInitDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.insert(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());

        when(userCoinService.resetTopCoins(any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        StepVerifier.create(portfolioMultiService.initMain(portfolioMultiInitDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testAdd() {
        PortfolioMultiAddDTO portfolioMultiAddDTO = new PortfolioMultiAddDTO();
        portfolioMultiAddDTO.setPortfolioName("Main");
        portfolioMultiAddDTO.setPortfolioType("manual");
        portfolioMultiAddDTO.setBgColor("#808080");
        portfolioMultiAddDTO.setIsMain(false);
        portfolioMultiAddDTO.setSortIndex(0);
        portfolioMultiAddDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.insert(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());
        when(portfolioMultiRepository.save(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());
        when(userCoinService.resetTopCoins(any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        List<PortfolioMultiEntity> list=new ArrayList<>();
        list.add(PortfolioMultiEntity.builder().isMain(false).build());
        when(portfolioMultiRepository.findAllByUserId(new ObjectId(portfolioMultiAddDTO.getHeader().getUserId()))
        ).thenReturn(Flux.fromIterable(list));
        StepVerifier.create(portfolioMultiService.add(portfolioMultiAddDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }). verifyComplete();
    }

    @Test
    public void testAddExceedLimit() {
        PortfolioMultiAddDTO portfolioMultiAddDTO = new PortfolioMultiAddDTO();
        portfolioMultiAddDTO.setPortfolioName("Main");
        portfolioMultiAddDTO.setPortfolioType("manual");
        portfolioMultiAddDTO.setBgColor("#808080");
        portfolioMultiAddDTO.setIsMain(false);
        portfolioMultiAddDTO.setSortIndex(0);
        portfolioMultiAddDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.insert(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());
        when(portfolioMultiRepository.save(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());

        when(portfolioMultiRepository.count(Example.of(PortfolioMultiEntity.builder()
                .userId(new ObjectId(portfolioMultiAddDTO.getHeader().getUserId())).build(),
            ExampleMatcher.matching().withIgnoreNullValues()))).thenReturn(Mono.just(20L));

        when(portfolioMultiRepository.count(Example.of(PortfolioMultiEntity.builder()
                .userId(new ObjectId(portfolioMultiAddDTO.getHeader().getUserId())).isMain(true).build(),
            ExampleMatcher.matching().withIgnoreNullValues()))).thenReturn(Mono.just(0L));
        List<PortfolioMultiEntity> list=new ArrayList<>();
        for (int i=0;i<25;i++) {
            list.add(PortfolioMultiEntity.builder().isMain(false).build());
        }
        when(portfolioMultiRepository.findAllByUserId(new ObjectId(portfolioMultiAddDTO.getHeader().getUserId()))
        ).thenReturn(Flux.fromIterable(list));

        StepVerifier.create(portfolioMultiService.add(portfolioMultiAddDTO)).expectErrorMessage("Exceeded the maximum number of portfolios!").verify();
    }

    @Test
    public void testAddMultiMain() {
        PortfolioMultiAddDTO portfolioMultiAddDTO = new PortfolioMultiAddDTO();
        portfolioMultiAddDTO.setPortfolioName("Main");
        portfolioMultiAddDTO.setPortfolioType("manual");
        portfolioMultiAddDTO.setBgColor("#808080");
        portfolioMultiAddDTO.setIsMain(true);
        portfolioMultiAddDTO.setSortIndex(0);
        portfolioMultiAddDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.insert(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());
        when(portfolioMultiRepository.save(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());

        List<PortfolioMultiEntity> list=new ArrayList<>();
        list.add(PortfolioMultiEntity.builder().isMain(true).build());
        when(portfolioMultiRepository.findAllByUserId(new ObjectId(portfolioMultiAddDTO.getHeader().getUserId()))
        ).thenReturn(Flux.fromIterable(list));

        StepVerifier.create(portfolioMultiService.add(portfolioMultiAddDTO)).expectErrorMessage("Main portfolio already exists!").verify();
    }

    @Test
    public void testDelete() {
        PortfolioMultiDeleteDTO portfolioMultiDeleteDTO = new PortfolioMultiDeleteDTO();
        portfolioMultiDeleteDTO.setPortfolioSourceId(MOCK_ID);
        portfolioMultiDeleteDTO.getHeader().setUserId(MOCK_USER_ID);

        Mono<PortfolioMultiEntity> portfolioMultiEntityMono = getMockEntity().map(entity -> {
            entity.setIsMain(false);
            return entity;
        });

        List<PortfolioMultiEntity>  list=new ArrayList<>();
        list.add(PortfolioMultiEntity.builder().id(new ObjectId("63f71593ffcb045d6c8f7f3e")).portfolioType("manual")
            .portfolioSourceId("1111").build());

        when(portfolioMultiRepository.findById(any(ObjectId.class))).thenReturn(portfolioMultiEntityMono);
        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(Flux.fromIterable(list));

        when(portfolioMultiRepository.deleteById(any(ObjectId.class))).thenReturn(Mono.empty());
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioEntity.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioPLEntity.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioPlChangeRecordPO.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioHoldingEntity.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioSnapshot1dPO.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioSnapshot5mPO.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        StepVerifier.create(portfolioMultiService.delete(portfolioMultiDeleteDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }). verifyComplete();
    }

    @Test
    public void testDeleteMain() {
        PortfolioMultiDeleteDTO portfolioMultiDeleteDTO = new PortfolioMultiDeleteDTO();
        portfolioMultiDeleteDTO.setPortfolioSourceId(MOCK_ID);
        portfolioMultiDeleteDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.findById(any(ObjectId.class))).thenReturn(getMockEntity());
        when(portfolioMultiRepository.deleteById(any(ObjectId.class))).thenReturn(Mono.empty());
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioEntity.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioPLEntity.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioPlChangeRecordPO.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));
        when(reactiveMongoTemplate.remove(any(Query.class), Mockito.eq(PortfolioHoldingEntity.class)))
            .thenReturn(Mono.just(Mockito.mock(DeleteResult.class)));

        StepVerifier.create(portfolioMultiService.delete(portfolioMultiDeleteDTO))
            .expectErrorMessage("Main portfolio cannot be deleted!").verify();
    }

    @Test
    public void testUpdate() {
        PortfolioMultiUpdateDTO portfolioMultiUpdateDTO = new PortfolioMultiUpdateDTO();
        portfolioMultiUpdateDTO.setPortfolioName("Main");
        portfolioMultiUpdateDTO.setBgColor("#808081");
        portfolioMultiUpdateDTO.setPortfolioSourceId(MOCK_ID);
        portfolioMultiUpdateDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.findById(new ObjectId(MOCK_ID))).thenReturn(getMockEntity());
        when(portfolioMultiRepository.save(any(PortfolioMultiEntity.class))).thenReturn(getMockEntity());

        StepVerifier.create(portfolioMultiService.update(portfolioMultiUpdateDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testBatchUpdate() {
        PortfolioMultiListUpdateDTO portfolioMultiListUpdateDTO = new PortfolioMultiListUpdateDTO();
        PortfolioMultiUpdateDTO portfolioMultiUpdateDTO = new PortfolioMultiUpdateDTO();
        portfolioMultiUpdateDTO.setPortfolioName("Main");
        portfolioMultiUpdateDTO.setBgColor("#808081");
        portfolioMultiUpdateDTO.setPortfolioSourceId(MOCK_ID);
        portfolioMultiUpdateDTO.setIsMain(true);
        portfolioMultiUpdateDTO.getHeader().setUserId(MOCK_USER_ID);
        portfolioMultiListUpdateDTO.setPortfolioList(Lists.newArrayList(portfolioMultiUpdateDTO));
        portfolioMultiListUpdateDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(getMockEntity().flux());
        when(portfolioPlManager.findByUserIdAndPortfolioSourceId(any(ObjectId.class), Mockito.anyString(), anyInt())).thenReturn(
            Mono.just(Mockito.mock(PortfolioPLEntity.class)));
        when(assetRedisTemplate.opsForValue().get(any(Object.class))).thenReturn(Mono.just("234.13"));
        when(reactiveMongoTemplate.execute(Mockito.eq(PortfolioMultiEntity.class), any(
            ReactiveCollectionCallback.class))).thenReturn(Flux.just(Mockito.mock(BulkWriteResult.class)));
        when(userCoinService.resetTopCoins(any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        StepVerifier.create(portfolioMultiService.batchUpdate(portfolioMultiListUpdateDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testBatchUpdateWithMultiMain() {
        PortfolioMultiListUpdateDTO portfolioMultiListUpdateDTO = new PortfolioMultiListUpdateDTO();
        portfolioMultiListUpdateDTO.getHeader().setUserId(MOCK_USER_ID);
        PortfolioMultiUpdateDTO portfolioMultiUpdateDTO = new PortfolioMultiUpdateDTO();
        portfolioMultiUpdateDTO.setPortfolioName("Main");
        portfolioMultiUpdateDTO.setBgColor("#808081");
        portfolioMultiUpdateDTO.setPortfolioSourceId(MOCK_ID);
        portfolioMultiUpdateDTO.setIsMain(true);
        portfolioMultiUpdateDTO.getHeader().setUserId(MOCK_USER_ID);

        PortfolioMultiUpdateDTO portfolioMultiUpdateDTO1 = new PortfolioMultiUpdateDTO();
        portfolioMultiUpdateDTO1.setPortfolioName("Test");
        portfolioMultiUpdateDTO1.setBgColor("#808081");
        portfolioMultiUpdateDTO1.setPortfolioSourceId(MOCK_ID);
        portfolioMultiUpdateDTO1.setIsMain(true);
        portfolioMultiUpdateDTO1.getHeader().setUserId(MOCK_USER_ID);
        List<PortfolioMultiEntity> waitUpdateList=new ArrayList<>();

//        for (PortfolioMultiUpdateDTO update: portfolioList) {
            waitUpdateList.add(PortfolioMultiEntity.builder()
                .userId(new ObjectId(portfolioMultiUpdateDTO.getHeader().getUserId()))
                .id(new ObjectId(portfolioMultiUpdateDTO.getPortfolioSourceId()))
                .portfolioName(portfolioMultiUpdateDTO.getPortfolioName())
                .bgColor(portfolioMultiUpdateDTO.getBgColor())
                .sortIndex(portfolioMultiUpdateDTO.getSortIndex())
                .isMain(portfolioMultiUpdateDTO.getIsMain())
                .timeUpdated(ExtUtils.now())
                .build());

        waitUpdateList.add(PortfolioMultiEntity.builder()
            .userId(new ObjectId(portfolioMultiUpdateDTO1.getHeader().getUserId()))
            .id(new ObjectId(portfolioMultiUpdateDTO1.getPortfolioSourceId()))
            .portfolioName(portfolioMultiUpdateDTO1.getPortfolioName())
            .bgColor(portfolioMultiUpdateDTO1.getBgColor())
            .sortIndex(portfolioMultiUpdateDTO1.getSortIndex())
            .isMain(portfolioMultiUpdateDTO1.getIsMain())
            .timeUpdated(ExtUtils.now())
            .build());
//        }
        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(Flux.fromIterable(waitUpdateList));
        portfolioMultiListUpdateDTO.setPortfolioList(Lists.newArrayList(portfolioMultiUpdateDTO, portfolioMultiUpdateDTO1));

        when(reactiveMongoTemplate.execute(Mockito.eq(PortfolioMultiEntity.class), any(
            ReactiveCollectionCallback.class))).thenReturn(Flux.just(Mockito.mock(BulkWriteResult.class)));

        StepVerifier.create(portfolioMultiService.batchUpdate(portfolioMultiListUpdateDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testQuery() {
        PortfolioMultiQueryDTO portfolioMultiQueryDTO = new PortfolioMultiQueryDTO();
        portfolioMultiQueryDTO.setPortfolioSourceId(MOCK_ID);
        portfolioMultiQueryDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.findOne(any(Example.class))).thenReturn(getMockEntity());

        StepVerifier.create(portfolioMultiService.query(portfolioMultiQueryDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testQueryAll() {
        PortfolioMultiListQueryDTO portfolioMultiListQueryDTO = new PortfolioMultiListQueryDTO();
        portfolioMultiListQueryDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(getMockEntity().flux());
        when(portfolioPlManager.findByUserIdAndPortfolioSourceId(any(ObjectId.class), Mockito.anyString(), anyInt())).thenReturn(
            Mono.just(Mockito.mock(PortfolioPLEntity.class)));
        when(assetRedisTemplate.opsForValue().get(any(Object.class))).thenReturn(Mono.just("234.13"));
        StepVerifier.create(portfolioMultiService.queryAll(portfolioMultiListQueryDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testQueryAllByUserId() {
        PortfolioMultiListQueryDTO portfolioMultiListQueryDTO = new PortfolioMultiListQueryDTO();
        portfolioMultiListQueryDTO.getHeader().setUserId(MOCK_USER_ID);

        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(getMockEntity().flux());
        when(portfolioPlManager.findByUserIdAndPortfolioSourceId(any(ObjectId.class), Mockito.anyString(), anyInt())).thenReturn(
            Mono.just(Mockito.mock(PortfolioPLEntity.class)));
        when(assetRedisTemplate.opsForValue().get(any(Object.class))).thenReturn(Mono.just("234.13"));

        when(portfolioValueManager.getCurrentValue(new ObjectId("60b850e8dae0a17606cd1cf2"),"60dd3a4d99ad671cabb95d6e", PortfolioTypeEnum.MANUAL, null)).thenReturn(Mono.just(new BigDecimal(1)));
        when(portfolioValueManager.getYesterdayValue(new ObjectId("60b850e8dae0a17606cd1cf2"),"60dd3a4d99ad671cabb95d6e", PortfolioTypeEnum.MANUAL, null)).thenReturn(Mono.just(new BigDecimal(1)));
        when(portfolioValueManager.hasBuySpent(any(ObjectId.class),Mockito.anyString(), any(
            PortfolioTypeEnum.class))).thenReturn(Mono.just(Boolean.TRUE));

        IPortfolioTradeStrategy mockPortfolioTradeStrategy = Mockito.mock(IPortfolioTradeStrategy.class);
        when(portfolioStrategyBeanFactory.getTradeService(any(PortfolioTypeEnum.class))).thenReturn(mockPortfolioTradeStrategy);
        when(mockPortfolioTradeStrategy.hasAsset(Mockito.anyString(),Mockito.anyString()))
            .thenReturn(Mono.just(Boolean.TRUE));
        when(portfolioAppAdapterService.checkNeedAddDashboard(any(Header.class), anyBoolean()))
            .thenReturn(true);
        when(portfolioBaseService.requiresUnitCalculation(anyInt()))
            .thenReturn(true);
        when(portfolioBaseService.requiresUnitCalculation(null))
            .thenReturn(true);
        when(portfolioBaseService.asynQueryCryptoInfo(any(PortfolioMultiListQueryDTO.class), any(PortfolioMultiResultDTO.class))).thenReturn(Mono.just(PortfolioMultiResultDTO.builder()
                .totalAmount(BigDecimal.ONE)
                .yesterdayTotalAmount(BigDecimal.ONE)
                .hasBuySpent(true)
                .hasAsset(true)
                .cryptoUnitPrice(BigDecimal.ONE)
                .fiatUnitPrice(BigDecimal.ONE)
                .build()));
        StepVerifier.create(portfolioMultiService.queryAllByUserId(portfolioMultiListQueryDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testQueryByCryptoId() {
        PortfolioMultiQueryByCryptoIdDTO portfolioMultiQueryByCryptoIdDTO = new PortfolioMultiQueryByCryptoIdDTO();
        portfolioMultiQueryByCryptoIdDTO.getHeader().setUserId(MOCK_USER_ID);
        portfolioMultiQueryByCryptoIdDTO.setCryptocurrencyId(1);
        portfolioMultiQueryByCryptoIdDTO.setCryptoUnit(2781);

        PortfolioPLEntity portfolioPLEntity = PortfolioPLEntity.builder().portfolioSourceId(MOCK_ID)
            .portfolios(Collections.emptyList())
            .build();

        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(getMockEntity().flux());
        when(portfolioPlManager.findByUserId(any(ObjectId.class))).thenReturn(
            Flux.just(portfolioPLEntity));
        when(assetRedisTemplate.opsForValue().get(any(Object.class))).thenReturn(Mono.just("234.13"));

        PortfolioWalletDetailPO portfolioWalletDetailPO = PortfolioWalletDetailPO.builder().portfolioSourceId(MOCK_ID)
            .blockChains(Collections.emptyList())
            .build();
        when(portfolioWalletDetailRepository.findByUserId(any(ObjectId.class))).thenReturn(
            Flux.just(portfolioWalletDetailPO)
        );

        StepVerifier.create(portfolioMultiService.queryByCryptoId(portfolioMultiQueryByCryptoIdDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void testQueryByCryptoIdV4() {
        PortfolioMultiQueryByCryptoIdDTO portfolioMultiQueryByCryptoIdDTO = new PortfolioMultiQueryByCryptoIdDTO();
        portfolioMultiQueryByCryptoIdDTO.getHeader().setUserId(MOCK_USER_ID);
        portfolioMultiQueryByCryptoIdDTO.setCryptocurrencyId(1);
        portfolioMultiQueryByCryptoIdDTO.setCryptoUnit(2781);

        PortfolioPLEntity portfolioPLEntity = PortfolioPLEntity.builder().portfolioSourceId(MOCK_ID)
            .portfolios(Collections.emptyList())
            .build();

        when(portfolioMultiRepository.findAll(any(Example.class))).thenReturn(getMockEntity().flux());
        when(portfolioPlManager.findByUserId(any(ObjectId.class))).thenReturn(
            Flux.just(portfolioPLEntity));
        when(portfolioThirdPartyService.findAssetByUserIdAndCryptoId(any(ObjectId.class), anyInt()))
            .thenReturn(Flux.empty());
        when(assetRedisTemplate.opsForValue().get(any(Object.class))).thenReturn(Mono.just("234.13"));

        PortfolioWalletDetailPO portfolioWalletDetailPO = PortfolioWalletDetailPO.builder().portfolioSourceId(MOCK_ID)
            .blockChains(Collections.emptyList())
            .build();
        when(portfolioWalletDetailRepository.findByUserId(any(ObjectId.class))).thenReturn(
            Flux.just(portfolioWalletDetailPO)
        );

        StepVerifier.create(portfolioMultiService.queryByCryptoIdV4(portfolioMultiQueryByCryptoIdDTO)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }


    @Test
    public void testTransferPortfolioSourceToMultiId() {

        when(portfolioBaseService.getAndRefreshCache(anyString(), anyString()))
            .thenReturn(Mono.just(PortfolioMultiEntity.builder().portfolioSourceId("por").build()));
        when(assetRedisTemplate.opsForValue().get(anyString())).thenReturn(Mono.empty());
        when(portfolioMultiRepository.findFirstByIdAndUserId(any(ObjectId.class), any(ObjectId.class)))
            .thenReturn(Mono.just(PortfolioMultiEntity.builder().id(new ObjectId(MOCK_ID))
                .userId(new ObjectId(MOCK_USER_ID))
                .portfolioSourceId(MOCK_ID).build()));
        when(portfolioMultiRepository.findOne(any(Example.class))).thenReturn(getMockEntity());
        when(portfolioMultiRepository.findById(any(ObjectId.class))).thenReturn(getMockEntity());

        StepVerifier.create(portfolioMultiService.transferPortfolioSourceToMultiId(MOCK_USER_ID, MOCK_ID)).expectSubscription()
            .assertNext(result -> {
                System.out.println(JacksonUtils.getInstance().serialize(result));
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    private Mono<PortfolioMultiEntity> getMockEntity() {
        return Mono.just(PortfolioMultiEntity.builder()
            .id(new ObjectId(MOCK_ID))
            .userId(new ObjectId(MOCK_USER_ID))
            .portfolioName("Test")
            .portfolioSourceId(MOCK_ID)
            .bgColor("#808080")
            .sortIndex(0)
            .isMain(true)
            .timeCreated(ExtUtils.now())
            .timeUpdated(ExtUtils.now())
            .build());
    }
}