package com.cmc.asset.business.portfolio.strategy;

import com.cmc.asset.business.portfolio.PortfolioUtils;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;

public class SevenDaysChartDataStrategyV2Test {

    private SevenDaysChartDataStrategyV2 sevenDaysChartDataStrategyV2UnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        sevenDaysChartDataStrategyV2UnderTest = new SevenDaysChartDataStrategyV2();
    }

    @Test
    public void testGetChartData() {
        // Setup
        Date date = PortfolioUtils.getStartOfDay(new Date());
        final NavigableMap<Date, NavigableMap<Date, BigDecimal>> mapByDay = new TreeMap<>(Map.ofEntries(
            Map.entry(date, new TreeMap<>(Map.ofEntries(Map.entry(date, new BigDecimal("0.00"))))))
        );
        final List<PortfolioTotalDTO> expectedResult = List.of(new PortfolioTotalDTO(new BigDecimal("0.00"), date));

        // Run the test
        final List<PortfolioTotalDTO> result = sevenDaysChartDataStrategyV2UnderTest.getChartData(30000, mapByDay);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
