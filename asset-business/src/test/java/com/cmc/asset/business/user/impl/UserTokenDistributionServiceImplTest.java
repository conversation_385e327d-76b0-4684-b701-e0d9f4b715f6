package com.cmc.asset.business.user.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.UserTokenDistributionEntity;
import com.cmc.asset.dao.repository.mongo.UserTokenDistributionRepository;
import com.cmc.asset.dao.repository.redis.CacheRepository;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.user.UserTokenDistributionListParamDTO;
import com.cmc.asset.model.contract.user.UserTokenDistributionListParamDTO.UserTokenDistributionDTO;
import com.cmc.asset.model.contract.user.UserTokenDistributionParamRequest;
import com.cmc.data.common.exception.BusinessException;
import java.time.Duration;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class UserTokenDistributionServiceImplTest {

    @Mock
    private CacheRepository cacheRepository;

    @Mock
    private UserTokenDistributionRepository userTokenDistributionRepository;

    @InjectMocks
    private UserTokenDistributionServiceImpl service;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        service = new UserTokenDistributionServiceImpl();
        ReflectionTestUtils.setField(service, "submitIntervalSecs", 30);
        ReflectionTestUtils.setField(service, "cacheRepository", cacheRepository);
        ReflectionTestUtils.setField(service, "userTokenDistributionRepository", userTokenDistributionRepository);

        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testFindResponse_whenKeyExists_thenError() {
        // Setup
        UserTokenDistributionParamRequest paramDTO = UserTokenDistributionParamRequest.builder()
            .event("event1")
            .email("email1")
            .binanceId("binanceId1")
            .build();
        String cacheKey = String.format(RedisConstants.USER_TOKEN_DISTRIBUTION_CHECKING_STATUS, paramDTO.getEvent(), paramDTO.getEmail(), paramDTO.getBinanceId());

        when(cacheRepository.getCachedValueInString(cacheKey)).thenReturn(Mono.just("1"));

        // Run the test
        Mono<String> result = service.findResponse(paramDTO);
        StepVerifier.create(result)
            .expectError(BusinessException.class)
            .verify();

        // Verify the results
        verify(cacheRepository, times(1)).getCachedValueInString(cacheKey);
        verify(cacheRepository, never()).cacheInString(any(), any(), any());
        verify(userTokenDistributionRepository, never()).findOne(any(), any(), any());
    }

    @Test
    public void testFindResponse_whenKeyNotExists_thenReadFromDb() {
        // Setup
        UserTokenDistributionParamRequest paramDTO = UserTokenDistributionParamRequest.builder()
            .event("event1")
            .email("email1")
            .binanceId("binanceId1")
            .build();
        String cacheKey = String.format(RedisConstants.USER_TOKEN_DISTRIBUTION_CHECKING_STATUS, paramDTO.getEvent(), paramDTO.getEmail(), paramDTO.getBinanceId());

        UserTokenDistributionEntity entity = UserTokenDistributionEntity.builder()
            .event("event1")
            .email("email1")
            .binanceId("binanceId1")
            .response("response1")
            .build();
        when(cacheRepository.getCachedValueInString(cacheKey)).thenReturn(Mono.empty());
        when(userTokenDistributionRepository.findOne(paramDTO.getEvent(), paramDTO.getEmail(), paramDTO.getBinanceId())).thenReturn(Mono.just(entity));

        // Run the test
        Mono<String> result = service.findResponse(paramDTO);
        StepVerifier.create(result)
            .expectNext("response1")
            .verifyComplete();

        // Verify the results
        verify(cacheRepository, times(1)).getCachedValueInString(cacheKey);
        verify(cacheRepository, times(1)).cacheInString(cacheKey, "1", Duration.ofSeconds(30));
        verify(userTokenDistributionRepository, times(1)).findOne(paramDTO.getEvent(), paramDTO.getEmail(), paramDTO.getBinanceId());
    }

    @Test
    public void testBatchUpsert_whenRowDataNotEmpty_thenUpsertAll() {
        // Setup
        UserTokenDistributionDTO dto1 = UserTokenDistributionDTO.builder()
            .event("event1")
            .email("email1")
            .binanceId("binanceId1")
            .response("response1")
            .build();
        UserTokenDistributionDTO dto2 = UserTokenDistributionDTO.builder()
            .event("event2")
            .email("email2")
            .binanceId("binanceId2")
            .response("response2")
            .build();
        UserTokenDistributionListParamDTO userTokenDistributionListParamDTO = UserTokenDistributionListParamDTO.builder()
            .rowData(List.of(dto1, dto2))
            .build();

        UserTokenDistributionEntity entity1 = UserTokenDistributionEntity.builder()
            .event("event1")
            .email("email1")
            .binanceId("binanceId1")
            .response("response1")
            .build();
        UserTokenDistributionEntity entity2 = UserTokenDistributionEntity.builder()
            .event("event2")
            .email("email2")
            .binanceId("binanceId2")
            .response("response2")
            .build();
        when(userTokenDistributionRepository.upsertAll(anyList())).thenReturn(Mono.just(
            List.of(entity1, entity2)));

        // Run the test
        Mono<String> result = service.batchUpsert(userTokenDistributionListParamDTO);
        StepVerifier.create(result)
            .expectNext("Successfully upsert 2 items")
            .verifyComplete();

        // Verify the results
        verify(userTokenDistributionRepository, times(1)).upsertAll(List.of(entity1, entity2));
    }

    @Test
    public void testReadRecently_whenGetCachedValueInStringReturnsValue_thenReturnTrue() {
        // Setup
        String cacheKey = "testCacheKey";

        when(cacheRepository.getCachedValueInString(cacheKey)).thenReturn(Mono.just("1"));

        // Run the test
        Mono<Boolean> result = service.readRecently(cacheKey);
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        // Verify the results
        verify(cacheRepository, times(1)).getCachedValueInString(cacheKey);
        verify(cacheRepository, never()).cacheInString(anyString(), anyString(), any(Duration.class));
    }

    @Test
    public void testReadRecently_whenGetCachedValueInStringReturnsEmpty_thenCacheAndReturnFalse() {
        // Setup
        String cacheKey = "testCacheKey";
        when(cacheRepository.getCachedValueInString(cacheKey)).thenReturn(Mono.empty());
        when(cacheRepository.cacheInString(cacheKey, "1", Duration.ofSeconds(30))).thenReturn(Mono.just(true));

        // Run the test
        Mono<Boolean> result = service.readRecently(cacheKey);
        StepVerifier.create(result)
            .expectNext(false)
            .verifyComplete();

        // Verify the results
        verify(cacheRepository, times(1)).getCachedValueInString(cacheKey);
        verify(cacheRepository, times(1)).cacheInString(cacheKey, "1", Duration.ofSeconds(30));
    }

    @Test
    public void testReadRecently_whenErrorOccurs_thenReturnFalse() {
        // Setup
        String cacheKey = "testCacheKey";
        RuntimeException runtimeException = new RuntimeException("cache error");

        when(cacheRepository.getCachedValueInString(cacheKey)).thenReturn(Mono.error(runtimeException));

        // Run the test
        Mono<Boolean> result = service.readRecently(cacheKey);
        StepVerifier.create(result)
            .expectNext(false)
            .verifyComplete();

        // Verify the results
        verify(cacheRepository, times(1)).getCachedValueInString(cacheKey);
        verify(cacheRepository, never()).cacheInString(anyString(), anyString(), any(Duration.class));
    }

    @Test
    public void testReadFromDb_whenFindOneReturnsValue_thenReturnValue() {
        // Setup
        String event = "event1";
        String email = "email1";
        String binanceId = "binanceId1";
        UserTokenDistributionEntity entity = UserTokenDistributionEntity.builder()
            .event(event)
            .email(email)
            .binanceId(binanceId)
            .response("response1")
            .build();
        when(userTokenDistributionRepository.findOne(event, email, binanceId)).thenReturn(Mono.just(entity));

        // Run the test
        Mono<String> result = service.readFromDb(event, email, binanceId);
        StepVerifier.create(result)
            .expectNext("response1")
            .verifyComplete();

        // Verify the results
        verify(userTokenDistributionRepository, times(1)).findOne(event, email, binanceId);
    }

    @Test
    public void testReadFromDb_whenFindOneReturnsEmpty_thenReturnNotFound() {
        // Setup
        String event = "event1";
        String email = "email1";
        String binanceId = "binanceId1";
        when(userTokenDistributionRepository.findOne(event, email, binanceId)).thenReturn(Mono.empty());

        // Run the test
        Mono<String> result = service.readFromDb(event, email, binanceId);
        StepVerifier.create(result)
            .expectNext("Not found")
            .verifyComplete();

        // Verify the results
        verify(userTokenDistributionRepository, times(1)).findOne(event, email, binanceId);
    }

    @Test
    public void testReadFromDb_whenErrorOccurs_thenThrowException() {
        // Setup
        String event = "event1";
        String email = "email1";
        String binanceId = "binanceId1";
        RuntimeException runtimeException = new RuntimeException("db error");
        when(userTokenDistributionRepository.findOne(event, email, binanceId)).thenReturn(Mono.error(runtimeException));

        // Run the test
        Mono<String> result = service.readFromDb(event, email, binanceId);
        StepVerifier.create(result)
            .expectError(BusinessException.class)
            .verify();

        // Verify the results
        verify(userTokenDistributionRepository, times(1)).findOne(event, email, binanceId);
    }
}
