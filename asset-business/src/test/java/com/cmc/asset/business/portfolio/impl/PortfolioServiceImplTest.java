package com.cmc.asset.business.portfolio.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.cmc.asset.business.coins.impl.UserCoinService;
import com.cmc.asset.business.gravity.AutoFollowService;
import com.cmc.asset.business.portfolio.manager.PortfolioPlManager;
import com.cmc.asset.business.portfolio.strategy.IPortfolioManualService;
import com.cmc.asset.business.portfolio.strategy.PortfolioManualStrategyImpl;
import com.cmc.asset.business.portfolio.strategy.PortfolioStrategyBeanFactory;
import com.cmc.asset.business.portfolio.strategy.PortfolioWalletStrategyImpl;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.config.DynamicApolloRefreshConfig;
import com.cmc.asset.dao.entity.portfolio.PortfolioEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioHoldingCalculationPo;
import com.cmc.asset.dao.entity.portfolio.PortfolioHoldingEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioPLEntity;
import com.cmc.asset.dao.entity.portfolio.PortfolioPLPo;
import com.cmc.asset.dao.repository.mongo.PortfolioMultiRepository;
import com.cmc.asset.dao.repository.mongo.PortfolioRepository;
import com.cmc.asset.dao.repository.redis.AssetCacheRepository;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.domain.portfolio.PortfolioCacheCondition;
import com.cmc.asset.model.common.ExtUtils;
import com.cmc.asset.model.common.PortfolioPageVo;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.portfolio.PortfolioAddDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCalculatingStatusDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioChangePointDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCheckStatusDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCumulativeChangeRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioCumulativeChangeResponse;
import com.cmc.asset.model.contract.portfolio.PortfolioDeleteDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioHistoricalChartQueryDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQueryResultDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioResultDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioUpdateDTO;
import com.cmc.framework.utils.JacksonUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import org.bson.types.ObjectId;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.ReactiveMongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@Slf4j
public class PortfolioServiceImplTest {
    private PortfolioRepository portfolioRepository;
    private PortfolioPlManager portfolioPlManager;
    private ReactiveMongoOperations mongoOperations;
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;
    private PortfolioMultiServiceImpl portfolioMultiService;
    private PortfolioServiceImpl portfolioService;
    private AutoFollowService autoFollowService;
    private PortfolioStrategyBeanFactory portfolioStrategyBeanFactory;
    private PortfolioManualStrategyImpl portfolioManualStrategy;
    private PortfolioWalletStrategyImpl portfolioWalletStrategy;

    private AssetCacheRepository assetCacheRepository;

    private PortfolioCryptoRedisService portfolioCryptoRedisService;
    private UserCoinService userCoinService;
    private IPortfolioManualService portfolioManualService;
    private CurrencyPriceService currencyPriceService;
    private CryptoCurrencyCache cryptoCurrencyCache;
    private PortfolioBaseService portfolioBaseService;
    private PortfolioMultiRepository portfolioMultiRepository;
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;


    @BeforeTest
    public void setUp() {
        portfolioRepository = mock(PortfolioRepository.class);
        assetCacheRepository = mock(AssetCacheRepository.class);
        portfolioPlManager = mock(PortfolioPlManager.class);
        mongoOperations = mock(ReactiveMongoOperations.class);
        assetRedisTemplate = mock(ReactiveRedisTemplate.class, Mockito.RETURNS_DEEP_STUBS);
        portfolioManualService = mock(PortfolioManualStrategyImpl.class);
        portfolioService = spy(PortfolioServiceImpl.class);
        portfolioMultiService = mock(PortfolioMultiServiceImpl.class);
        autoFollowService = mock(AutoFollowService.class);
        portfolioStrategyBeanFactory = mock(PortfolioStrategyBeanFactory.class);
        portfolioManualStrategy = mock(PortfolioManualStrategyImpl.class);
        portfolioWalletStrategy = mock(PortfolioWalletStrategyImpl.class);
        portfolioCryptoRedisService = mock(PortfolioCryptoRedisService.class);
        userCoinService = mock(UserCoinService.class);
        currencyPriceService = mock(CurrencyPriceService.class);
        cryptoCurrencyCache = mock(CryptoCurrencyCache.class);
        portfolioBaseService = mock(PortfolioBaseService.class);
        portfolioMultiRepository = mock(PortfolioMultiRepository.class);
        dynamicApolloRefreshConfig = mock(DynamicApolloRefreshConfig.class);

        ReflectionTestUtils.setField(portfolioService, "assetCacheRepository", assetCacheRepository);
        ReflectionTestUtils.setField(portfolioService, "portfolioManualService", portfolioManualService);
        ReflectionTestUtils.setField(portfolioService, "portfolioRepository", portfolioRepository);
        ReflectionTestUtils.setField(portfolioService, "userCoinService", userCoinService);
        ReflectionTestUtils.setField(portfolioService, "portfolioPlManager", portfolioPlManager);
        ReflectionTestUtils.setField(portfolioService, "mongoOperations", mongoOperations);
        ReflectionTestUtils.setField(portfolioService, "assetRedisTemplate", assetRedisTemplate);
        ReflectionTestUtils.setField(portfolioService, "portfolioMultiService", portfolioMultiService);
        ReflectionTestUtils.setField(portfolioService, "portfolioStrategyBeanFactory", portfolioStrategyBeanFactory);
        ReflectionTestUtils.setField(portfolioService, "currencyPriceService", currencyPriceService);
        ReflectionTestUtils.setField(portfolioService, "cryptoCurrencyCache", cryptoCurrencyCache);
        ReflectionTestUtils.setField(portfolioService, "portfolioBaseService", portfolioBaseService);
        ReflectionTestUtils.setField(portfolioService, "portfolioMultiRepository", portfolioMultiRepository);
        ReflectionTestUtils.setField(portfolioService, "dynamicApolloRefreshConfig", dynamicApolloRefreshConfig);
        ReflectionTestUtils.setField(portfolioService, "cacheTimeMills", 30000);
        ReflectionTestUtils.setField(portfolioService, "disableCache", false);
        ReflectionTestUtils.setField(portfolioService, "disableJobCheck", true);
        ReflectionTestUtils.setField(portfolioService, "disableJobFlag", true);
        ReflectionTestUtils.setField(portfolioService, "CHART_CACHE_SECONDS", 1800);
        ReflectionTestUtils.setField(portfolioService, "refreshValueMills", 1800);
        when(portfolioMultiService.transferPortfolioSourceToMultiId(anyString(), anyString()))
            .thenReturn(Mono.just("default"));
        Mockito.when(portfolioMultiService
            .transferPortfolioSourceAdaptForApp(anyString(), anyString(), anyString(), any(), any())).thenReturn(
            Mono.just(PortfolioMultiEntity.builder().userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .portfolioSourceId("default").build()));

        Mockito.when(portfolioCryptoRedisService.queryCryptoInfo(anyInt(), any(), anyBoolean()))
            .thenReturn(Mono.just(PortfolioCryptoInfoDTO.builder().price("0.0").build()));

        Mockito.when(userCoinService.resetTopCoins(any(), anyString())).thenReturn(Mono.empty());
    }

    @Test
    public void testAddV2() {
        when(portfolioRepository.insert(Mockito.any(PortfolioEntity.class))).thenReturn(Mono.just(
            PortfolioEntity.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).amount(new BigDecimal(1)).build()));

        PortfolioAddDTO portfolioAddDTO = new PortfolioAddDTO();
        portfolioAddDTO.getHeader().setUserId("5c9c0ec4bb6e9d3ead204db1");
        portfolioAddDTO.setUserId("5c9c0ec4bb6e9d3ead204db1");
        portfolioAddDTO.setCryptocurrencyId(1);
        portfolioAddDTO.setCryptoUnit(2781);
        portfolioAddDTO.setFiatUnit(2781);
        portfolioAddDTO.setAmount(new BigDecimal("1"));
        portfolioAddDTO.setPrice(new BigDecimal("0"));
        portfolioAddDTO.setFee(new BigDecimal("0"));
        portfolioAddDTO.setTransactionType("buy");
        portfolioAddDTO.setTransactionTime(new Date());
        portfolioAddDTO.setPortfolioSourceId("default");

        Mono<PortfolioResultDTO> result = portfolioService.addV2(portfolioAddDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            Assert.assertNotNull(result);
        }).verifyComplete();
    }


    @Test
    public void testCumulativeChange() {
        when(assetCacheRepository.get(anyString())).thenReturn(Mono.empty());
        when(assetCacheRepository.cacheInString(any(), any(), any())).thenReturn(Mono.just(true));
        when(portfolioBaseService.requiresUnitCalculation(anyInt())).thenReturn(true);
        when(currencyPriceService.queryCurrentPrice(anyInt())).thenReturn(Mono.just(PortfolioCryptoInfoDTO.builder().price("1").build()));
        when(portfolioMultiService.getAndRefreshCache(anyString(), anyString()))
            .thenReturn(Mono.just(PortfolioMultiEntity.builder().id(new ObjectId("60dd3a4d99ad671cabb95d6e"))
                .userId(new ObjectId("60b850e8dae0a17606cd1cf2"))
                .portfolioSourceId("60dd3a4d99ad671cabb95d6e").build()));
        when(portfolioMultiRepository.findFirstByUserIdAndPortfolioSourceId(any(ObjectId.class), anyString())).thenReturn(Mono.just(PortfolioMultiEntity.builder().build()));

        PortfolioChangePointDTO portfolioChangePointDTO = new PortfolioChangePointDTO();
        portfolioChangePointDTO.setTimestamp(new Date());
        portfolioChangePointDTO.setPlPercentage(new BigDecimal("100"));
        portfolioChangePointDTO.setPlValue(new BigDecimal("1"));
        portfolioChangePointDTO.setKLinePercentage(new BigDecimal("1"));
        portfolioChangePointDTO.setHasBuySpent(false);
        PortfolioCumulativeChangeResponse cumulativeChangeResponse = PortfolioCumulativeChangeResponse.builder()
                .cumulativeChangeList(List.of(portfolioChangePointDTO))
                .build();
        when(assetCacheRepository.wrapperCache(any(), any(), any())).thenReturn(Mono.just(JacksonUtils.toJson(cumulativeChangeResponse)));
        when(portfolioManualService.cumulativeChange(any(PortfolioCumulativeChangeRequest.class)))
            .thenReturn(Mono.just(cumulativeChangeResponse));

        Map<Integer, PortfolioCryptoInfoDTO> map = new HashMap<>();
        map.put(2781,PortfolioCryptoInfoDTO.builder().cryptoId(2781).price("1.11").build());
        when(currencyPriceService.queryCurrentPrices(anyList()))
            .thenReturn(Mono.just(map));
        PortfolioCumulativeChangeRequest request = new PortfolioCumulativeChangeRequest();
        request.setDays(30);
        request.setCryptoIds(List.of(2781));
        request.setPortfolioSourceId("sourceId");
        request.getHeader().setUserId(new ObjectId().toHexString());
        Mono<PortfolioCumulativeChangeResponse> result = portfolioService.cumulativeChange(request);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            Assert.assertNotNull(result);
        }).verifyComplete();
    }

    @Test
    public void testQueryV2() {
    }

    @Test
    public void testUpdateV2() {
        when(portfolioRepository.save(Mockito.any(PortfolioEntity.class))).thenReturn(Mono.just(
            PortfolioEntity.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).amount(new BigDecimal(1)).build()));

        when(portfolioRepository.findById(Mockito.any(ObjectId.class))).thenReturn(Mono.just(
            PortfolioEntity.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).transactionTime(new Date())
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).amount(new BigDecimal(1)).build()));

        PortfolioUpdateDTO portfolioUpdateDTO = new PortfolioUpdateDTO();
        portfolioUpdateDTO.getHeader().setUserId("5c9c0ec4bb6e9d3ead204db1");
        portfolioUpdateDTO.setUserId("5c9c0ec4bb6e9d3ead204db1");
        portfolioUpdateDTO.setCryptocurrencyId(1);
        portfolioUpdateDTO.setCryptoUnit(2781);
        portfolioUpdateDTO.setFiatUnit(2781);
        portfolioUpdateDTO.setAmount(new BigDecimal("1"));
        portfolioUpdateDTO.setPrice(new BigDecimal("0"));
        portfolioUpdateDTO.setTransactionType("buy");
        portfolioUpdateDTO.setTransactionTime(new Date());
        portfolioUpdateDTO.setPortfolioSourceId("default");
        portfolioUpdateDTO.setId("5c9c0ec4bb6e9d3ead204db1");

        Mono<PortfolioResultDTO> result = portfolioService.updateV2(portfolioUpdateDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            Assert.assertNotNull(result);
        }).verifyComplete();

    }

    @Test
    public void testBatchAdd() {
    }

    @Test
    public void testDeleteV2() {

        when(portfolioRepository
            .findById(Mockito.any(ObjectId.class)))
            .thenReturn(Mono.just(PortfolioEntity.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).portfolioSourceId("5c9c0ec4bb6e9d3ead204db1").amount(new BigDecimal(1)).build()));

        when(portfolioRepository
            .deleteById(Mockito.any(ObjectId.class)))
            .thenReturn(Mono.empty().then());


        List<PortfolioPLPo> portfoliosList = new ArrayList<>();
        portfoliosList.add(PortfolioPLPo.builder().cryptocurrencyId(1).build());

        when(portfolioPlManager
            .findByUserIdAndPortfolioSourceId(new ObjectId("5c9c0ec4bb6e9d3ead204db1"), "default", null))
            .thenReturn(Mono.just(PortfolioPLEntity.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).portfolios(portfoliosList).build()));

        when(portfolioPlManager
            .save(Mockito.any(PortfolioPLEntity.class)))
            .thenReturn(Mono.just(PortfolioPLEntity.builder().id(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1")).portfolioSourceId("5c9c0ec4bb6e9d3ead204db1").portfolios(portfoliosList).build()));

        PortfolioDeleteDTO deleteDTO = new PortfolioDeleteDTO();
        deleteDTO.getHeader().setUserId("5c9c0ec4bb6e9d3ead204db1");

        deleteDTO.setCryptocurrencyId(1);
        deleteDTO.setId("5c9c0ec4bb6e9d3ead204db1");
        deleteDTO.setPortfolioSourceId("5c9c0ec4bb6e9d3ead204db1");

        Mono<Boolean> result =portfolioService
            .deleteV2(deleteDTO);
        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();

    }

    @Test
    public void testQueryDataByList() {
        when(assetRedisTemplate.opsForValue().get(anyString())).thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(anyString(),anyString())).thenReturn(Mono.empty());
        ArrayList<PortfolioQueryResultDTO> data = Lists.newArrayList(PortfolioQueryResultDTO.builder()
                .cryptocurrencyId(1)
                .cryptoHoldings(BigDecimal.TEN)
                .amount(BigDecimal.ONE)
                .build());
        Mono<List<PortfolioQueryResultDTO>> listMono = Mono.just(data);
        when(assetCacheRepository.wrapperCache(any(), any(), any())).thenReturn(Mono.just(JacksonUtils.toJson(data)));
        PortfolioCacheCondition portfolioCacheCondition = new PortfolioCacheCondition();
        portfolioCacheCondition.setCurrentPage(1);
        portfolioCacheCondition.setPageSize(10);
        portfolioCacheCondition.setRedisKey(String.format(RedisConstants.KEY_NEW_PORTFOLIO_USER_QUERY_DATA, "5c9c0ec4bb6e9d3ead204db1","default"));
        portfolioCacheCondition.setExpire(Duration.ofMinutes(5));
        portfolioCacheCondition.setUserId("5c9c0ec4bb6e9d3ead204db1");

        when(portfolioRepository.existsByUserIdAndPortfolioSourceId(new ObjectId("5c9c0ec4bb6e9d3ead204db1"), "default"))
            .thenReturn(Mono.just(true));

        Mono<PortfolioPageVo<PortfolioQueryResultDTO>> result =portfolioService
            .queryDataByList(false, listMono, portfolioCacheCondition, "default", "5c9c0ec4bb6e9d3ead204db1");

        PortfolioPageVo<PortfolioQueryResultDTO> pageVo = new PortfolioPageVo<>();
        pageVo.setTotalNum(1);
        pageVo.setList(data);
        pageVo.setCurrentPage(1);
        pageVo.setJobFlag(false);
        pageVo.setPortfolioSourceId("default");

        StepVerifier.create(result)
            .expectNext(pageVo)
            .verifyComplete();
    }

    @Test
    public void testQueryStatistics() {

    }

    @Test
    public void testQueryTransactionsByCrypto() {
    }

    @Test
    public void testCovertResult() {
    }

    @Test
    public void testQueryForHistoricalChart() {
        //prepare cache data
        var cacheDTOList = new ArrayList<PortfolioHistoricalChartDTO>();
        PortfolioHistoricalChartDTO cacheChartDTO =
            PortfolioHistoricalChartDTO.builder().cryptoId(1).unitPrice(BigDecimal.valueOf(30000))
                .totalList(Lists.newArrayList(PortfolioTotalDTO.builder().build())).build();
        cacheDTOList.add(cacheChartDTO);

        when(assetRedisTemplate.opsForValue().get(anyString())).thenReturn(Mono.just(JSON.toJSONString(cacheDTOList)))
            .thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(anyString(), anyString())).thenReturn(Mono.just(true));

        //prepare portfolioHoldingEntities
        var entities = new ArrayList<PortfolioHoldingEntity>();
        Date now =  ExtUtils.getNear5minTime(new Date());
        String sourceId = "default";
        long fiveMinutes = 5 * 60 * 1000L;
        long oneHourMilliseconds = 60 * 60 * 1000L;
        long oneDayMilliseconds = 24 * oneHourMilliseconds;
        Date nearOneHour = new Date(now.getTime() - now.getTime() % oneHourMilliseconds);
        Date date = new Date(now.getTime() - now.getTime() % oneDayMilliseconds);

        //prepare portfolioHoldingEntities >7 day
        for (int i = 7; i < 205; i++) {
            entities.add(PortfolioHoldingEntity.builder().portfolioSourceId(sourceId)
                .date(new Date(date.getTime() - i * oneDayMilliseconds)).currentValue(Lists.newArrayList(
                    PortfolioHoldingCalculationPo.builder()
                        .calculationTime(new Date(nearOneHour.getTime() - i * oneDayMilliseconds - fiveMinutes * 2))
                        .calculationValue(BigDecimal.valueOf(30000)).build())).build());
        }
        //prepare portfolioHoldingEntities 7 day
        for (int i = 1; i < 7; i++) {
            entities.add(PortfolioHoldingEntity.builder().portfolioSourceId(sourceId)
                .date(new Date(date.getTime() - i * oneDayMilliseconds)).currentValue(Lists.newArrayList(
                    PortfolioHoldingCalculationPo.builder()
                        .calculationTime(new Date(nearOneHour.getTime() - i * oneDayMilliseconds))
                        .calculationValue(BigDecimal.valueOf(20000)).build())).build());
        }
        //prepare portfolioHoldingEntities 1 day
        entities.add(PortfolioHoldingEntity.builder().portfolioSourceId(sourceId).date(date).currentValue(Lists
            .newArrayList(PortfolioHoldingCalculationPo.builder()
                    .calculationTime(new Date(nearOneHour.getTime() - 2 * fiveMinutes))
                    .calculationValue(BigDecimal.valueOf(1000)).build(),
                PortfolioHoldingCalculationPo.builder().calculationTime(new Date(nearOneHour.getTime() - fiveMinutes))
                    .calculationValue(BigDecimal.valueOf(10000)).build())).build());
        //prepare portfolioHoldingEntities end
        when(mongoOperations.find(any(Query.class), eq(PortfolioHoldingEntity.class)))
            .thenReturn(Flux.fromIterable(entities));

        var queryDTO = new PortfolioHistoricalChartQueryDTO();
        queryDTO.setCryptoIds(Lists.newArrayList(1));
        queryDTO.getHeader().setUserId("5c9c0ec4bb6e9d3ead204db1");
        queryDTO.setDays(0);
        queryDTO.setPortfolioSourceId("default");
    }

    @Test
    public void testQueryDetailByCryptoId() {
    }

    @Test
    public void testCheckCalculatingStatus() {
        when(assetRedisTemplate.opsForValue().get(anyString())).thenReturn(Mono.just(JSON.toJSONString(
            PortfolioCalculatingStatusDTO.builder().isCalculating(false).lastCalculatedTime(new Date()).build())))
            .thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(anyString(), anyString())).thenReturn(Mono.just(true));

        Date now = new Date();
        Date fiveMinAgo = new Date(now.getTime() - (10 * 60 * 1000));
        String userId = "5c9c0ec4bb6e9d3ead204db1";
        String logEntityId = "6006b90ea5fdbd0e85dbca36";
        String sourceId = "default";

        PortfolioCheckStatusDTO portfolioCheckStatusDTO = new PortfolioCheckStatusDTO();
        portfolioCheckStatusDTO.setPortfolioSourceId(sourceId);
        portfolioCheckStatusDTO.getHeader().setUserId(userId);

        //cache exist
        StepVerifier.create(portfolioService.checkCalculatingStatus(portfolioCheckStatusDTO)).assertNext(
            portfolioCalculatingStatusDTO -> Assert.assertFalse(portfolioCalculatingStatusDTO.getIsCalculating()))
            .verifyComplete();

        //portfolioCalculationLogEntity not exist
        StepVerifier.create(portfolioService.checkCalculatingStatus(portfolioCheckStatusDTO)).assertNext(
            portfolioCalculatingStatusDTO -> Assert.assertFalse(portfolioCalculatingStatusDTO.getIsCalculating()))
            .verifyComplete();

        when(portfolioRepository
            .countAllByUserIdAndPortfolioSourceId(new ObjectId(userId), sourceId))
            .thenReturn(Mono.just(0L)).thenReturn(Mono.just(1L)).thenReturn(Mono.just(0L));

        //portfolioCalculationLogEntity exist portfolioCount=0,statusCount=0,time<5min
        StepVerifier.create(portfolioService.checkCalculatingStatus(portfolioCheckStatusDTO)).assertNext(
            portfolioCalculatingStatusDTO -> Assert.assertFalse(portfolioCalculatingStatusDTO.getIsCalculating()))
            .verifyComplete();
        //portfolioCalculationLogEntity exist portfolioCount=1,statusCount=0, time>5min
        StepVerifier.create(portfolioService.checkCalculatingStatus(portfolioCheckStatusDTO)).assertNext(
            portfolioCalculatingStatusDTO -> Assert.assertFalse(portfolioCalculatingStatusDTO.getIsCalculating()))
            .verifyComplete();

    }

    @Test
    public void buildBaseCryptoInfo() {
        PortfolioCryptoInfoDTO portfolioCryptoInfoDTO = new PortfolioCryptoInfoDTO();
        portfolioCryptoInfoDTO.setName("test");
        portfolioCryptoInfoDTO.setPrice("1.1");
        portfolioCryptoInfoDTO.setSlug("test-slug");
        portfolioCryptoInfoDTO.setSymbol("test-symbol");
        CryptoCurrencyInfoDTO btc = CryptoCurrencyInfoDTO.builder()
                .id(1)
                .name("btc")
                .symbol("btc-symbol")
                .slug("btc-slug")
                .build();
        when(cryptoCurrencyCache.getCryptoCurrencyById(anyInt())).thenReturn(btc);
        when(currencyPriceService.queryCurrentPrice(anyInt())).thenReturn(Mono.just(portfolioCryptoInfoDTO));
        when(portfolioBaseService.requiresUnitCalculation(anyInt())).thenReturn(true);
        Map<Integer, Integer> map = new HashMap<>();
        when(dynamicApolloRefreshConfig.getTokenMigrationMap()).thenReturn(map);
        PortfolioQueryResultDTO.PortfolioQueryResultDTOBuilder portfolioQueryResultDTOBuilder =
            portfolioService.buildBaseCryptoInfo(1, 1).block();
        PortfolioQueryResultDTO build = portfolioQueryResultDTOBuilder.build();
        System.out.println(build.getName() + build.getSlug() + build.getSymbol());
        org.junit.Assert.assertNotNull(build);
    }

    @Test
    public void testQueryForHistoricalChartV2() {
        //prepare cache data
        var cacheDTOList = new ArrayList<PortfolioHistoricalChartDTO>();
        ArrayList<PortfolioTotalDTO> totalList = Lists.newArrayList(PortfolioTotalDTO.builder()
                .timestamp(new Date())
                .value(BigDecimal.ONE)
                .build());
        PortfolioHistoricalChartDTO cacheChartDTO =
                PortfolioHistoricalChartDTO.builder().cryptoId(1).unitPrice(BigDecimal.valueOf(30000))
                        .totalList(totalList).build();
        cacheDTOList.add(cacheChartDTO);

        when(currencyPriceService.loadPrices(anyInt(), anyList(), anyString())).thenReturn(Mono.just(new TreeMap<>()));
        when(currencyPriceService.getPrice(any(), any(Date.class))).thenReturn(BigDecimal.ONE);
        when(currencyPriceService.queryCurrentPrice(anyInt())).thenReturn(Mono.just(PortfolioCryptoInfoDTO.builder().price("1").build()));

        when(assetRedisTemplate.opsForValue().get(anyString())).thenReturn(Mono.just(JSON.toJSONString(totalList)))
                .thenReturn(Mono.empty());
        when(assetRedisTemplate.opsForValue().set(anyString(), anyString())).thenReturn(Mono.just(true));

        var queryDTO = new PortfolioHistoricalChartQueryDTO();
        queryDTO.setCryptoIds(Lists.newArrayList(1));
        queryDTO.getHeader().setUserId("5c9c0ec4bb6e9d3ead204db1");
        queryDTO.setDays(0);
        queryDTO.setPortfolioSourceId("default");

        when(portfolioMultiService.transferPortfolioSourceAdaptForApp(anyString(), anyString(), anyString(), any(), any())).thenReturn(Mono.just(PortfolioMultiEntity.builder()
                .userId(new ObjectId("5c9c0ec4bb6e9d3ead204db1"))
                .portfolioSourceId("default")
                .portfolioType("manual")
                .build()));

        //cache exist
        StepVerifier.create(portfolioService.queryForHistoricalChartV2(queryDTO))
                .assertNext(portfolioHistoricalChartDTOS -> Assert.assertEquals(1, portfolioHistoricalChartDTOS.size()))
                .verifyComplete();
        when(portfolioStrategyBeanFactory.getTradeService(anyString())).thenReturn(portfolioManualStrategy);
        when(portfolioManualStrategy.historicalChart(any(), any())).thenReturn(Mono.just(totalList));
        //day 0
        queryDTO.setDays(0);
        StepVerifier.create(portfolioService.queryForHistoricalChartV2(queryDTO))
                .assertNext(portfolioHistoricalChartDTOS -> {
                    Assert.assertEquals(portfolioHistoricalChartDTOS.size(), 1);
                }).verifyComplete();
        //day 1
        queryDTO.setDays(1);
        StepVerifier.create(portfolioService.queryForHistoricalChartV2(queryDTO))
                .assertNext(portfolioHistoricalChartDTOS -> {
                    Assert.assertEquals(1, portfolioHistoricalChartDTOS.size());
                }).verifyComplete();
        //day 7
        queryDTO.setDays(7);
        StepVerifier.create(portfolioService.queryForHistoricalChartV2(queryDTO))
                .assertNext(portfolioHistoricalChartDTOS -> {
                    Assert.assertEquals(portfolioHistoricalChartDTOS.size(), 1);
                }).verifyComplete();
        //day >7
        queryDTO.setDays(30);
        StepVerifier.create(portfolioService.queryForHistoricalChartV2(queryDTO))
                .assertNext(portfolioHistoricalChartDTOS -> {
                    Assert.assertEquals(portfolioHistoricalChartDTOS.size(), 1);
                }).verifyComplete();
        StepVerifier.create(portfolioService.queryForHistoricalChartV2(queryDTO))
                .assertNext(portfolioHistoricalChartDTOS -> {
                    Assert.assertEquals(portfolioHistoricalChartDTOS.size(), 1);
                }).verifyComplete();

    }

}
