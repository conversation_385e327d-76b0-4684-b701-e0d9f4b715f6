package com.cmc.asset.business.watchlist.impl;

import com.cmc.asset.business.coins.impl.UserCoinService;
import com.cmc.asset.business.watchlist.manager.CryptoInfoManager;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.FollowWatchListEntity;
import com.cmc.asset.dao.entity.MarketPairEntity;
import com.cmc.asset.dao.entity.WatchListCacheEntity;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.dao.repository.mongo.FollowWatchListRepositoryImpl;
import com.cmc.asset.dao.repository.mongo.WatchListRepository;
import com.cmc.asset.dao.repository.mongo.WatchListRespositoryImpl;
import com.cmc.asset.dao.repository.redis.AssetCacheRepository;
import com.cmc.asset.dao.repository.redis.WatchListCacheRepository;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.domain.crypto.CryptoCurrencyListResultDTO;
import com.cmc.asset.domain.crypto.CryptoCurrencyListResultDTO.CryptoCurrencyDTO;
import com.cmc.asset.integration.BaseDataServiceClient;
import com.cmc.asset.integration.ContentServiceClient;
import com.cmc.asset.integration.DataApiServiceClient;
import com.cmc.asset.model.contract.crypto.CryptoMarketPairDto;
import com.cmc.asset.model.contract.watchlist.*;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.utils.LocaleMessageSource;
import com.google.common.collect.Lists;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.support.StaticMessageSource;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class WatchListServiceImplTest {

    @Mock
    private WatchListRespositoryImpl mockWatchListRespositoryImpl;
    @Mock
    private FollowWatchListRepositoryImpl mockFollowWatchListRepository;
    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;
    @Mock
    private DataApiServiceClient mockDataApiServiceClient;
    @Mock
    private ContentServiceClient mockContentServiceClient;
    @Mock
    private WatchListCacheRepository mockWatchListCacheRepository;

    @Mock
    private AssetCacheRepository mockAssetCacheRepository;

    @Mock
    private WatchListRepository mockWatchListRepository;

    @Mock
    private UserCoinService mockUserCoinService;

    @Mock
    private BaseDataServiceClient mockBaseDataServiceClient;

    @Mock
    private CryptoInfoManager mockCryptoInfoManager;

    @InjectMocks
    private WatchListServiceImpl watchListServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "watchCountEnable", true);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "watchlistMaxSize", 3000);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "cacheTimeMinutes", 1L);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "fixSecurityQuestion", true);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "autoFillFollow", false);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "autoAddFollow", false);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "pinChange", false);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "dataHubBatchSize", 20);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "topCryptoSize", 3);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "shareImageUrl", "https://example.com");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @BeforeClass
    public static void init() {
        ReflectionTestUtils.setField(LocaleMessageSource.class, "messageSource", new StaticMessageSource());
    }

    @Test
    public void testSubscribe() {
        List<String> keyString = new ArrayList<>();
        keyString.add("cache1");
        when(mockAssetCacheRepository.getOpsSet(anyString())).thenReturn(Mono.just(keyString));
        when(mockAssetCacheRepository.delete(anyString())).thenReturn(Mono.just(1L));

        DexPairWatchlistHandleStrategyImpl strategyMock = Mockito.mock(DexPairWatchlistHandleStrategyImpl.class);
        ReflectionTestUtils.setField(watchListServiceImplUnderTest, "watchListHandleStrategies", List.of(strategyMock));
        Mockito.when(strategyMock.isMatched(Mockito.any())).thenReturn(true);
        Mockito.when(strategyMock.subscribe(Mockito.any(), Mockito.any()))
            .thenReturn(Mono.just(WatchListSubscribeResultDTO.builder().build()));

        WatchListSubscribeParamDTO param =
            WatchListSubscribeParamDTO.builder().subscribeType("SUBSCRIBE").resourceType("DEX_PAIR")
                .resourceId("248954").build();
        param.getHeader().setUserId("6399375f501618395d24c38c");
        // Run the test
        final Mono<WatchListSubscribeResultDTO> result = watchListServiceImplUnderTest.subscribe(param);
        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testQuery() {
        // Setup
        ObjectId watchId = ObjectId.get();
        QueryWatchListParamDTO param =
            new QueryWatchListParamDTO(watchId.toHexString(), 0, 0, "FOLLOWED", 8, "convertIds", "1", true,false, null, null);
        param.getHeader().setUserId("111");
        when(mockWatchListRespositoryImpl.getWatchListRepository()).thenReturn(mockWatchListRepository);
        lenient().when(mockFollowWatchListRepository.existsByUserIdAndWatchListId(any(), any()))
            .thenReturn(Mono.just(false));
        when(mockWatchListRepository.findByIdEqualsAndActivedTrue(watchId)).thenReturn(
            Flux.fromIterable(List.of(
                WatchListEntity.builder()
                    .id(watchId).marketPairs(Set.of(1, 2, 3)).main(true).shared(true)
                    .build()))
        );

        List<String> keyString = new ArrayList<>();
        keyString.add("cache1");
        when(mockAssetCacheRepository.addOpsSet(anyString(),anyString())).thenReturn(Mono.just(1L));
        Map<Integer, CryptoCurrencyDTO> listingMap = new HashMap<>();
        listingMap.put(1,CryptoCurrencyDTO.builder().build());

        when(mockCryptoInfoManager.getCryptoListingMap(anyList(),anyString(),anyString())).thenReturn(Mono.just(listingMap));
        Map<Integer, MarketPairEntity> pairMap = new HashMap<>();
        pairMap.put(1,MarketPairEntity.builder().build());
        when(mockCryptoInfoManager.getMarketPairsMap(anyList())).thenReturn(Mono.just(pairMap));

        when(mockAssetCacheRepository.get(any())).thenReturn(Mono.empty());
        when(mockAssetCacheRepository.cacheInString(anyString(),anyString(),any())).thenReturn(Mono.just(Boolean.TRUE));
        when(mockAssetCacheRepository.expire(anyString(),any())).thenReturn(Mono.just(Boolean.TRUE));
        CryptoCurrencyDTO cryptoCurrencyDTO = CryptoCurrencyDTO.builder()
            .cmcRank(1)
            .name("bitcoin")
            .symbol("BTC")
            .category("AI")
            .status("1")
            .slug("bitcoin")
            .tags("ai")
            .wrappedStakedMcRank(1)
            .marketPairCount(1000)
            .circulatingSupply(new BigDecimal("21000000"))
            .selfReportedCirculatingSupply(new BigDecimal("21000000"))
            .totalSupply(new BigDecimal("21000000"))
            .maxSupply(new BigDecimal("21000000"))
            .build();
        // Configure DataApiServiceClient.getCryptoListings(...).
        final Mono<CryptoCurrencyListResultDTO> cryptoCurrencyListResultDTOMono = Mono.just(
            new CryptoCurrencyListResultDTO(List.of(cryptoCurrencyDTO), 0L));
        lenient().when(mockDataApiServiceClient.getCryptoListings(any()))
            .thenReturn(cryptoCurrencyListResultDTOMono);

        // Configure CryptoCurrencyCache.getById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        lenient().when(mockCryptoCurrencyCache.getById(anyInt())).thenReturn(cryptoCurrencyInfoDTO);

        // Configure CryptoRepository.getMarketPairs(...).
        final Mono<List<CryptoMarketPairDto>> pair = Mono.just(
            List.of(CryptoMarketPairDto.builder().id(0).pairBase("pairBase").pairQuote("pairQuote").build())
        );
        lenient().when(mockBaseDataServiceClient.getMarketPairs(anyCollection())).thenReturn(pair);

        // Configure FollowWatchListRepositoryImpl.findByUserId(...).
        final Mono<FollowWatchListEntity> followWatchListEntityMono = Mono.just(
            new FollowWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", Set.of("value")));
        lenient().when(mockFollowWatchListRepository.findByUserId("userId")).thenReturn(followWatchListEntityMono);

        lenient().when(mockWatchListCacheRepository.getPopularWatchSize()).thenReturn(Mono.just(0L));

        // Configure WatchListCacheRepository.getPopularWatchLists(...).
        final Mono<List<WatchListCacheEntity>> listMono = Mono.just(List.of(
            new WatchListCacheEntity("id", "userId", "name", "description", 0L, 0, 0, Set.of(0), false, false, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false)));
        lenient().when(mockWatchListCacheRepository.getPopularWatchLists(0, 0)).thenReturn(listMono);

        lenient().when(mockWatchListRespositoryImpl.getSharedWatchListSize()).thenReturn(Mono.just(0L));

        // Configure WatchListRespositoryImpl.getSharedWatchList(...).
        final Mono<List<WatchListCacheEntity>> listMono1 = Mono.just(List.of(
            new WatchListCacheEntity("id", "userId", "name", "description", 0L, 0, 0, Set.of(0), false, false, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false)));
        lenient().when(mockWatchListRespositoryImpl.getSharedWatchList(anyInt(), anyInt())).thenReturn(listMono1);

        lenient().when(mockWatchListCacheRepository.getPopularWatchListsPage(anyInt(), anyInt())).thenReturn(Mono.empty());
        lenient().when(mockWatchListRespositoryImpl.querySharedWatchList(anyInt(), anyInt())).thenReturn(Mono.empty());

        // Run the test
        final Mono<WatchListResultDTO> result = watchListServiceImplUnderTest.query(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testSave() {
        List<String> keyString = new ArrayList<>();
        keyString.add("cache1");
        when(mockAssetCacheRepository.getOpsSet(anyString())).thenReturn(Mono.just(keyString));
        when(mockAssetCacheRepository.delete(anyString())).thenReturn(Mono.just(1L));
        // Setup
        WatchListParamDTO param = new WatchListParamDTO(null, "name", "description", false, false, false, null);

        when(mockUserCoinService.resetTopCoins(Mockito.any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        param.getHeader().setUserId("111");
        lenient().when(mockWatchListRespositoryImpl.getWatchListRepository()).thenReturn(mockWatchListRepository);
        // Configure WatchListRespositoryImpl.save(...).
        final Mono<WatchListEntity> watchListEntityMono = Mono.just(
            new WatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "userId",
                1L, "name", "description", 0L, Set.of(0), Set.of(0), Set.of(0), List.of(), false, false, 0, null, null));
        lenient().when(mockWatchListRespositoryImpl.save(any()))
            .thenReturn(watchListEntityMono);

        lenient().when(mockContentServiceClient.checkSenitiveWord(anyList())).thenReturn(Mono.just(false));

        when(mockWatchListRespositoryImpl.countWatchList(Mockito.any(String.class))).thenReturn(Mono.just(10L));
        when(mockWatchListRespositoryImpl.existsMainWatchlist(Mockito.any(), Mockito.anyString())).thenReturn(
            Mono.just(true));

        // Run the test
        final Mono<String> result = watchListServiceImplUnderTest.save(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testSave_duplicate() {
        // Setup
        WatchListParamDTO param = new WatchListParamDTO("6127523b86a9443a989cfd8d", "name", "description", false, false, true, null);
        param.getHeader().setUserId("111");
        lenient().when(mockWatchListRespositoryImpl.getWatchListRepository()).thenReturn(mockWatchListRepository);
        when(mockUserCoinService.resetTopCoins(Mockito.any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        List<String> keyString = new ArrayList<>();
        keyString.add("cache1");
        when(mockAssetCacheRepository.getOpsSet(anyString())).thenReturn(Mono.just(keyString));
        when(mockAssetCacheRepository.delete(anyString())).thenReturn(Mono.just(1L));
        // Configure WatchListRespositoryImpl.save(...).
        Mono<WatchListEntity> watchListEntityMono = Mono.just(
            WatchListEntity.builder().userId("111").id(new ObjectId("6127523b86a9443a989cfd8d")).build());
        when(mockWatchListRepository.findById(new ObjectId("6127523b86a9443a989cfd8d")))
            .thenReturn(Mono.just(WatchListEntity.builder().userId("111").id(new ObjectId("6127523b86a9443a989cfd8d")).build()));
        when(mockWatchListRespositoryImpl.save(any()))
            .thenReturn(watchListEntityMono);

        when(mockWatchListRespositoryImpl.countWatchList(Mockito.any(String.class))).thenReturn(Mono.just(10L));
        when(mockWatchListRespositoryImpl.existsMainWatchlist(Mockito.any(), Mockito.anyString())).thenReturn(
            Mono.just(true));
        // Run the test
        final Mono<String> result = watchListServiceImplUnderTest.save(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testDelete() {
        // Setup
        DeleteWatchListParamDTO param = new DeleteWatchListParamDTO("6127523b86a9443a989cfd8d");
        param.getHeader().setUserId("111");
        when(mockWatchListRespositoryImpl
            .delete(any(), any()))
            .thenReturn(Mono.just("6127523b86a9443a989cfd8d"));
        List<String> keyString = new ArrayList<>();
        keyString.add("cache1");
        when(mockAssetCacheRepository.getOpsSet(anyString())).thenReturn(Mono.just(keyString));
        when(mockAssetCacheRepository.delete(anyString())).thenReturn(Mono.just(1L));

        when(mockUserCoinService.resetTopCoins(Mockito.any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        // Run the test
        final Mono<String> result = watchListServiceImplUnderTest.delete(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testFollowWatchList1() {
        // Setup
        FollowWatchListParamDTO param = new FollowWatchListParamDTO("FOLLOW", "6127523b86a9443a989cfd8d");
        param.getHeader().setUserId("111");
        when(mockWatchListRespositoryImpl.getWatchListRepository()).thenReturn(mockWatchListRepository);
        when(mockWatchListRepository.findById(new ObjectId("6127523b86a9443a989cfd8d")))
            .thenReturn(Mono.just(WatchListEntity.builder()
                .shared(true)
                .userId("222")
                .build()));

        // Configure FollowWatchListRepositoryImpl.findAndModify(...).
        final Mono<FollowWatchListEntity> followWatchListEntityMono = Mono.just(
            new FollowWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", Set.of("value")));
        when(mockFollowWatchListRepository.findAndModify(any(), anyString(), anyInt(), anyBoolean())).thenReturn(followWatchListEntityMono);

        // Configure WatchListRespositoryImpl.incrByFollows(...).
        final Mono<WatchListEntity> watchListEntityMono = Mono.just(
            new WatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "userId",
                1L, "name", "description", 0L, Set.of(0), Set.of(0), Set.of(0), List.of(),false, false, 0, null, null));
        when(mockWatchListRespositoryImpl.incrByFollows(any(), anyInt())).thenReturn(watchListEntityMono);

        // Run the test
        final Mono<String> result = watchListServiceImplUnderTest.followWatchList(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testQuerySharedWatchLists() {
        // Setup
        QuerySharedWatchListParamDTO param = new QuerySharedWatchListParamDTO(0, 0);
        param.getHeader().setUserId("111");

        // Configure FollowWatchListRepositoryImpl.findByUserId(...).
        final Mono<FollowWatchListEntity> followWatchListEntityMono = Mono.just(
            new FollowWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", Set.of("value")));
        when(mockFollowWatchListRepository.findByUserId(any())).thenReturn(followWatchListEntityMono);

        lenient().when(mockWatchListCacheRepository.getPopularWatchSize()).thenReturn(Mono.just(0L));

        // Configure WatchListCacheRepository.getPopularWatchLists(...).
        final Mono<List<WatchListCacheEntity>> listMono = Mono.just(List.of(
            new WatchListCacheEntity("id", "userId", "name", "description", 0L, 0, 0, Set.of(0), false, false, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false)));
        lenient().when(mockWatchListCacheRepository.getPopularWatchLists(anyInt(), anyInt())).thenReturn(listMono);

        lenient().when(mockWatchListRespositoryImpl.getSharedWatchListSize()).thenReturn(Mono.just(0L));

        // Configure WatchListRespositoryImpl.getSharedWatchList(...).
        final Mono<List<WatchListCacheEntity>> listMono1 = Mono.just(List.of(
            new WatchListCacheEntity("id", "userId", "name", "description", 0L, 0, 0, Set.of(0), false, false, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false)));
        lenient().when(mockWatchListRespositoryImpl.getSharedWatchList(anyInt(), anyInt())).thenReturn(listMono1);

        lenient().when(mockWatchListCacheRepository.getPopularWatchListsPage(anyInt(), anyInt())).thenReturn(Mono.empty());

        Tuple2<List<WatchListCacheEntity>, Long> tuple2 = Tuples.of(List.of(WatchListCacheEntity.builder()
            .id("111")
            .build()), 1l);
        lenient().when(mockWatchListRespositoryImpl.querySharedWatchList(anyInt(), anyInt())).thenReturn(Mono.just(tuple2));

        // Configure CryptoCurrencyCache.getById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        lenient().when(mockCryptoCurrencyCache.getById(any())).thenReturn(cryptoCurrencyInfoDTO);

        // Run the test
        final Mono<WatchListSharedResultDTO> result = watchListServiceImplUnderTest.querySharedWatchLists(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testCreateMainWatchList() {
        // Setup
        BaseRequest param = BaseRequest.getInstance();
        param.getHeader().setUserId("111");
        // Configure WatchListRespositoryImpl.doInitMainWatchlist(...).
        final Mono<WatchListEntity> watchListEntityMono = Mono.just(
            new WatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "userId",
                1L, "name", "description", 0L, Set.of(0), Set.of(0), Set.of(0),List.of(), false, false, 0, null, null));
        when(mockWatchListRespositoryImpl.doInitMainWatchlist(any()))
            .thenReturn(watchListEntityMono);
        when(mockUserCoinService.resetTopCoins(Mockito.any(BaseRequest.class), Mockito.anyString())).thenReturn(Mono.just(true));

        // Run the test
        final Mono<String> result = watchListServiceImplUnderTest.createMainWatchList(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void fillFollow() {
        // Setup
        ObjectId watchId = ObjectId.get();
        AutoFillFollowParamDTO autoFillFollowParamDTO = AutoFillFollowParamDTO.builder().userId("111").build();
        QueryWatchListParamDTO param =
            new QueryWatchListParamDTO(watchId.toHexString(), 0, 0, "FOLLOWED", 8, "convertIds", "1", true,false, null, null);
        param.getHeader().setUserId("111");
        lenient().when(mockWatchListRespositoryImpl.getWatchListRepository()).thenReturn(mockWatchListRepository);
        lenient().when(mockFollowWatchListRepository.existsByUserIdAndWatchListId(any(), any()))
            .thenReturn(Mono.just(false));
        lenient().when(mockWatchListRepository.findByIdEqualsAndActivedTrue(watchId)).thenReturn(
            Flux.fromIterable(List.of(
                WatchListEntity.builder()
                    .id(watchId).marketPairs(Set.of(1, 2, 3)).main(true).shared(true)
                    .build()))
        );
        CryptoCurrencyDTO cryptoCurrencyDTO = CryptoCurrencyDTO.builder()
            .cmcRank(1)
            .name("bitcoin")
            .symbol("BTC")
            .category("AI")
            .status("1")
            .slug("bitcoin")
            .tags("ai")
            .wrappedStakedMcRank(1)
            .marketPairCount(1000)
            .circulatingSupply(new BigDecimal("21000000"))
            .selfReportedCirculatingSupply(new BigDecimal("21000000"))
            .totalSupply(new BigDecimal("21000000"))
            .maxSupply(new BigDecimal("21000000"))
            .build();
        // Configure DataApiServiceClient.getCryptoListings(...).
        final Mono<CryptoCurrencyListResultDTO> cryptoCurrencyListResultDTOMono = Mono.just(
            new CryptoCurrencyListResultDTO(List.of(cryptoCurrencyDTO), 0L));
        lenient().when(mockDataApiServiceClient.getCryptoListings(any()))
            .thenReturn(cryptoCurrencyListResultDTOMono);

        // Configure CryptoCurrencyCache.getById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        lenient().when(mockCryptoCurrencyCache.getById(anyInt())).thenReturn(cryptoCurrencyInfoDTO);

        // Configure CryptoRepository.getMarketPairs(...).
        final Mono<List<CryptoMarketPairDto>> pair = Mono.just(
            List.of(CryptoMarketPairDto.builder().id(0).pairBase("pairBase").pairQuote("pairQuote").build())
        );
        lenient().when(mockBaseDataServiceClient.getMarketPairs(anyCollection())).thenReturn(pair);

        // Configure FollowWatchListRepositoryImpl.findByUserId(...).
        final Mono<FollowWatchListEntity> followWatchListEntityMono = Mono.just(
            new FollowWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", Set.of("value")));
        lenient().when(mockFollowWatchListRepository.findByUserId("userId")).thenReturn(followWatchListEntityMono);

        lenient().when(mockWatchListCacheRepository.getPopularWatchSize()).thenReturn(Mono.just(0L));

        // Configure WatchListCacheRepository.getPopularWatchLists(...).
        final Mono<List<WatchListCacheEntity>> listMono = Mono.just(List.of(
            new WatchListCacheEntity("id", "userId", "name", "description", 0L, 0, 0, Set.of(0), false, false, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false)));
        lenient().when(mockWatchListCacheRepository.getPopularWatchLists(0, 0)).thenReturn(listMono);

        lenient().when(mockWatchListRespositoryImpl.getSharedWatchListSize()).thenReturn(Mono.just(0L));

        // Configure WatchListRespositoryImpl.getSharedWatchList(...).
        final Mono<List<WatchListCacheEntity>> listMono1 = Mono.just(List.of(
            new WatchListCacheEntity("id", "userId", "name", "description", 0L, 0, 0, Set.of(0), false, false, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false)));
        lenient().when(mockWatchListRespositoryImpl.getSharedWatchList(anyInt(), anyInt())).thenReturn(listMono1);

        lenient().when(mockWatchListCacheRepository.getPopularWatchListsPage(anyInt(), anyInt())).thenReturn(Mono.empty());
        lenient().when(mockWatchListRespositoryImpl.querySharedWatchList(anyInt(), anyInt())).thenReturn(Mono.empty());

        // Run the test
        final Mono<WatchListResultDTO> result = watchListServiceImplUnderTest.fillFollow(autoFillFollowParamDTO);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res ->
                Assert.assertNotNull(res))
            .verifyComplete();
    }

    @Test
    public void testQueryResource() {
        QueryWatchListParamDTO queryParam =
            QueryWatchListParamDTO.builder().resourceType("DEX_PAIR").resourceId("248954").build();
        queryParam.getHeader().setUserId("63aad7eb1699420e8b95f4cb");
        Mockito.when(mockWatchListRespositoryImpl.findByResourceId(Mockito.any(), Mockito.any(), Mockito.anyString()))
            .thenReturn(Mono.just(
                Lists.newArrayList(WatchListEntity.builder().id(new ObjectId("63aad7ebbe6a33afdbf027b6")).build())));
        final Mono<WatchListResourceResultDTO> result = watchListServiceImplUnderTest.queryByResourceId(queryParam);
        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(Assert::assertNotNull)
            .verifyComplete();
    }
}
