package com.cmc.asset.business.avatar.impl;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import com.cmc.asset.dao.entity.avatar.SystemAvatarsEntity;
import com.cmc.asset.dao.entity.avatar.UserAvatarsEntity;
import com.cmc.asset.dao.entity.avatar.UserAvatarsEntity.AvatarInfoDetailEntity;
import com.cmc.asset.dao.repository.mongo.SystemAvatarRepository;
import com.cmc.asset.dao.repository.mongo.UserAvatarRepository;
import com.cmc.asset.dao.repository.redis.UserAvatarRedisRepository;
import com.cmc.asset.domain.avatar.AvatarVO;
import com.cmc.asset.integration.UserApiClient;
import com.cmc.asset.model.common.CommonPageVO;
import com.cmc.asset.model.contract.user.AvatarDTO;
import com.cmc.asset.model.contract.user.BatchUpdateUserAvatarSortDTO;
import com.cmc.asset.model.contract.user.OldUserDTO;
import com.cmc.asset.model.contract.user.UpdateUserAvatarSortDTO;
import com.cmc.asset.model.contract.user.UserAvatarAdminListDTO;
import com.cmc.asset.model.contract.user.UserAvatarAdminSaveDTO;
import com.cmc.asset.model.contract.user.UserAvatarResultDTO;
import com.cmc.asset.utils.UserInfoUtils;
import com.cmc.auth.common.utils.AuthUtils.AuthResult;
import com.cmc.data.common.BaseRequest;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.reactivestreams.Publisher;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class UserAvatarServiceImplTest {

    @Mock
    private SystemAvatarRepository mockSystemAvatarRepository;
    @Mock
    private UserAvatarRepository mockUserAvatarRepository;
    @Mock
    private UserAvatarRedisRepository mockUserAvatarRedisRepository;

    @Mock
    private UserApiClient mockUserApiClient;

    @InjectMocks
    private UserAvatarServiceImpl userAvatarServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    private static MockedStatic<UserInfoUtils> userInfoUtilsMockedStatic;

    @BeforeClass
    public static void init() {
        userInfoUtilsMockedStatic = mockStatic(UserInfoUtils.class);
    }

    @AfterClass
    public static void close() {
        userInfoUtilsMockedStatic.close();
    }

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testQueryUserAvatarList() {
        // Setup
        final BaseRequest baseRequest = BaseRequest.getInstance();
        baseRequest.getHeader().setUserId("userId");
        AuthResult authResult = new AuthResult();
        authResult.setAvatarId("612855fa483c0f232244146c");

        // Configure SystemAvatarRepository.findByTypeAndActive(...).
        final Flux<SystemAvatarsEntity> systemAvatarsEntityFlux = Flux.just(
            new SystemAvatarsEntity(new ObjectId("612855ee483c0f232244146b"), "name",
                0, true, 0, new Date(),
                new Date()));
        when(mockSystemAvatarRepository.findByTypeAndActiveOrderBySortAsc(0, true)).thenReturn(systemAvatarsEntityFlux);

        // Configure UserAvatarRepository.findByUserId(...).
        final Mono<UserAvatarsEntity> userAvatarsEntityMono = Mono.just(
            new UserAvatarsEntity(new ObjectId("612855fa483c0f232244146c"), "userId",
                Set.of(AvatarInfoDetailEntity.builder().createTime(new Date()).avatarId("val").build()), new Date(),
                new Date()));
        when(mockUserAvatarRepository.findByUserId("userId")).thenReturn(userAvatarsEntityMono);

        when(mockUserAvatarRedisRepository.get(Mockito.any())).thenReturn(Mono.empty());

        userInfoUtilsMockedStatic.when(()->UserInfoUtils.getUserInfo("userId")).thenReturn(Mono.just(authResult));
        when(mockUserApiClient.getUserInfo("userId")).thenReturn(Mono.just(new OldUserDTO()));

        // Run the test
        final Mono<List<UserAvatarResultDTO>> result = userAvatarServiceImplUnderTest.queryUserAvatarList(baseRequest);

        List<UserAvatarResultDTO> list = Lists.newArrayList(UserAvatarResultDTO.builder().avatarId("612855ee483c0f232244146b").newest(true).selected(false).build(),UserAvatarResultDTO.builder().avatarId("612855fa483c0f232244146c").newest(true).selected(true).build());

        // Verify the results
        StepVerifier.create(result).expectNext(list);
    }

    @Test
    public void listTest() {
        Mono<Long> countMonoMock = Mono.just(1L);
        SystemAvatarsEntity entityMock =
            SystemAvatarsEntity.builder().id(new ObjectId("612855fa483c0f232244146c")).build();
        Flux<SystemAvatarsEntity> avatarsEntityFlux = Flux.just(entityMock);
        when(mockSystemAvatarRepository.countByCriteria(Mockito.any())).thenReturn(countMonoMock);
        when(mockSystemAvatarRepository.findByCriteria(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
            .thenReturn(avatarsEntityFlux);
        UserAvatarAdminListDTO request = new UserAvatarAdminListDTO(1, true, "1");
        request.setCurrentPage(1);
        request.setPageSize(10);
        Mono<CommonPageVO<AvatarVO>> result = userAvatarServiceImplUnderTest.list(request);
        CommonPageVO<AvatarVO> pageVO = result.block();
        Assert.assertTrue(pageVO != null && pageVO.getList().size() > 0);
    }

    @Test
    public void saveSystemAvatarTest() {

        SystemAvatarsEntity entityMock =
            SystemAvatarsEntity.builder().id(new ObjectId("612855fa483c0f232244146c")).build();
        Mono<SystemAvatarsEntity> avatarsEntityMono = Mono.just(entityMock);
        when(mockSystemAvatarRepository.findById(new ObjectId("612855fa483c0f232244146c"))).thenReturn(avatarsEntityMono);
        when(mockUserAvatarRedisRepository.setIfAbsent(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(Mono.just(true));
        when(mockSystemAvatarRepository.findMaxSort()).thenReturn(Mono.just(1));
        when(mockSystemAvatarRepository.save(entityMock)).thenReturn(Mono.just(entityMock));
        when(mockUserAvatarRedisRepository.delete(Mockito.anyString())).thenReturn(Mono.just(1L));

        UserAvatarAdminSaveDTO request = UserAvatarAdminSaveDTO.builder()
            .id("612855fa483c0f232244146c")
            .isActive(true)
            .isReward(0)
            .name("test")
            .build();

        Mono<AvatarVO> avatarVOMono = userAvatarServiceImplUnderTest.saveSystemAvatar(request);

        AvatarVO avatarVO = avatarVOMono.block();
        Assert.assertNotNull(avatarVO);

    }

    @Test
    public void batchUpdateSortsTest() {
        SystemAvatarsEntity entityMock =
            SystemAvatarsEntity.builder().id(new ObjectId("612855fa483c0f232244146c")).build();
        Flux<SystemAvatarsEntity> avatarsEntityFlux = Flux.just(entityMock);

        when(mockSystemAvatarRepository.findAllById(Mockito.anyCollection())).thenReturn(avatarsEntityFlux);
        when(mockSystemAvatarRepository.saveAll(Mockito.any(Publisher.class))).thenReturn(avatarsEntityFlux);

        UpdateUserAvatarSortDTO sortDTO = new UpdateUserAvatarSortDTO("612855fa483c0f232244146c", 1);
        BatchUpdateUserAvatarSortDTO request = new BatchUpdateUserAvatarSortDTO(Collections.singletonList(sortDTO));

        Mono<List<AvatarVO>> listMono = userAvatarServiceImplUnderTest.batchUpdateSorts(request);

        Assert.assertTrue(listMono.block() != null && listMono.block().size() > 0);

    }



}
