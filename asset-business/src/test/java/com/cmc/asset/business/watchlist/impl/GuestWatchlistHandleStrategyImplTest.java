package com.cmc.asset.business.watchlist.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

import com.cmc.asset.business.portfolio.DexerService;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.GuestWatchListEntity;
import com.cmc.asset.dao.repository.mongo.guest.GuestWatchListRepository;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.model.contract.dexer.DexTokenDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.exception.ArgumentException;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.data.common.utils.LocaleMessageSource;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.context.support.StaticMessageSource;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class GuestWatchlistHandleStrategyImplTest {

    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;
    @Mock
    private GuestWatchListRepository mockGuestWatchListRepository;
    @Mock
    private DexerService mockDexerService;

    @InjectMocks
    private GuestWatchlistHandleStrategyImpl guestWatchlistHandleStrategyImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(guestWatchlistHandleStrategyImplUnderTest, "size", 2);
        ReflectionTestUtils.setField(LocaleMessageSource.class, "messageSource", new StaticMessageSource());
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testIsResourceTypeMatched() {
        // 测试所有资源类型都返回 false
        assertFalse(guestWatchlistHandleStrategyImplUnderTest.isResourceTypeMatched(ResourceTypeEnum.EXCHANGE));
        assertFalse(guestWatchlistHandleStrategyImplUnderTest.isResourceTypeMatched(ResourceTypeEnum.CRYPTO));
        assertFalse(guestWatchlistHandleStrategyImplUnderTest.isResourceTypeMatched(ResourceTypeEnum.MARKETPAIR));
        assertFalse(guestWatchlistHandleStrategyImplUnderTest.isResourceTypeMatched(ResourceTypeEnum.DEX_PAIR));
        assertFalse(guestWatchlistHandleStrategyImplUnderTest.isResourceTypeMatched(ResourceTypeEnum.DEX_TOKEN));
        assertFalse(guestWatchlistHandleStrategyImplUnderTest.isResourceTypeMatched(null));
    }

    @Test
    public void testIsMatched_WithGuestId() {
        // Setup - 有 guestId 的情况
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", 0L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.EXCHANGE, List.of(0L), List.of(),
                "watchListId", false, false, 123L);

        // Run the test
        final boolean result = guestWatchlistHandleStrategyImplUnderTest.isMatched(param);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testIsMatched_WithoutGuestId() {
        // Setup - 没有 guestId 的情况
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", 0L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.EXCHANGE, List.of(0L), List.of(),
                "watchListId", false, false, null);

        // Run the test
        final boolean result = guestWatchlistHandleStrategyImplUnderTest.isMatched(param);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSubscribeCrypto_Success() {
        // Setup
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.CRYPTO, List.of(1L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();
        when(mockCryptoCurrencyCache.getByIds(any())).thenReturn(Set.of(1));

        // Configure GuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        // Configure GuestWatchListRepository.updateCryptosByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateCryptosByGuestId(eq(123L), eq(true), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }



    @Test
    public void testSubscribeCrypto_RepositoryError() {
        // Setup
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.CRYPTO, List.of(1L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();
        when(mockCryptoCurrencyCache.getByIds(any())).thenReturn(Set.of(1));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(Mono.error(new RuntimeException("Database error")));

        // Configure GuestWatchListRepository.updateCryptosByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateCryptosByGuestId(eq(123L), eq(true), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results - 数据库错误被处理，应该正常返回
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }

    @Test
    public void testSubscribeCrypto_Unsubscribe() {
        // Setup - 取消订阅
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.UNSUBSCRIBE, ResourceTypeEnum.CRYPTO, List.of(1L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();
        when(mockCryptoCurrencyCache.getByIds(any())).thenReturn(Set.of(1));

        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateCryptosByGuestId(eq(123L), eq(false), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }

    @Test
    public void testSubscribeDexPair_Success() {
        // Setup
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.DEX_PAIR, List.of(1L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();

        // Mock DexerService
        PairInfoDTO pairInfo = PairInfoDTO.builder()
            .poolId(1L)
            .platformId(1)
            .baseToken(DexTokenDTO.builder()
                .address("0x123")
                .build())
            .build();
        when(mockDexerService.getPairs(anyList())).thenReturn(Mono.just(Map.of(1L, pairInfo)));

        // Configure GuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        // Configure GuestWatchListRepository.updateDexPairsByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateDexPairsByGuestId(eq(123L), eq(true), anySet(), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }



    @Test
    public void testSubscribeDexToken_Success() {
        // Setup
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.DEX_TOKEN, List.of(), List.of("1:0x123"),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();

        // Configure GuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        // Configure GuestWatchListRepository.updateDexTokenByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateDexTokenByGuestId(eq(123L), eq(true), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }


    @Test
    public void testSubscribeCrypto_LimitExceeded() {
        // Setup - 超过限制的情况
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.CRYPTO, List.of(1L, 2L, 3L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();
        when(mockCryptoCurrencyCache.getByIds(any())).thenReturn(Set.of(1, 2, 3));

        // 模拟已有2个加密货币，再添加3个会超过限制
        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(4, 5), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        // Configure GuestWatchListRepository.updateCryptosByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateCryptosByGuestId(eq(123L), eq(true), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results - 应该抛出参数异常
        StepVerifier.create(result)
            .expectError(ArgumentException.class)
            .verify();
    }

    @Test
    public void testSubscribeDexPair_DexerServiceError() {
        // Setup
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.DEX_PAIR, List.of(1L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();

        // Mock DexerService 返回错误
        when(mockDexerService.getPairs(anyList())).thenReturn(Mono.error(new RuntimeException("Dexer service error")));

        // Configure GuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        // Configure GuestWatchListRepository.updateDexPairsByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateDexPairsByGuestId(eq(123L), eq(true), anySet(), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results - 应该正常返回，因为错误被处理了
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }

    @Test
    public void testSubscribe_DefaultCase() {
        // Setup - 默认情况（非CRYPTO、DEX_TOKEN、DEX_PAIR）
        final SubscribeParamDTO param =
            new SubscribeParamDTO("userId", -1L, SubscribeTypeEnum.SUBSCRIBE, ResourceTypeEnum.EXCHANGE, List.of(1L), List.of(),
                "", false, false, 123L);
        final BaseRequest request = BaseRequest.getInstance();

        // Mock DexerService
        PairInfoDTO pairInfo = PairInfoDTO.builder()
            .poolId(1L)
            .platformId(1)
            .baseToken(DexTokenDTO.builder()
                .address("0x123")
                .build())
            .build();
        when(mockDexerService.getPairs(anyList())).thenReturn(Mono.just(Map.of(1L, pairInfo)));

        // Configure GuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L),List.of(), "type"));
        when(mockGuestWatchListRepository.queryFirstGuestWatchListEntityByGuestId(eq(123L)))
            .thenReturn(guestWatchListEntityMono);

        // Configure GuestWatchListRepository.updateDexPairsByGuestId(...).
        final Mono<GuestWatchListEntity> guestWatchListEntityMono1 = Mono.just(
            new GuestWatchListEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), 123L,
                "name", Set.of(0), Set.of(0), Set.of(0), List.of(0L), List.of(),"type"));
        when(mockGuestWatchListRepository.updateDexPairsByGuestId(eq(123L), eq(true), anySet(), anySet())).thenReturn(guestWatchListEntityMono1);

        // Run the test
        final Mono<WatchListSubscribeResultDTO> result =
            guestWatchlistHandleStrategyImplUnderTest.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertTrue(watchListResult.getMain());
            })
            .verifyComplete();
    }
}
