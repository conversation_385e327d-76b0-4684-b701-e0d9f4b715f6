package com.cmc.asset.business.watchlist.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.dao.repository.mongo.WatchListRespositoryImpl;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.exception.BusinessException;
import java.util.List;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
public class DexPairWatchlistHandleStrategyImplTest {

    @Mock
    private WatchListRespositoryImpl mockWatchListRespository;

    @InjectMocks
    private DexPairWatchlistHandleStrategyImpl dexPairWatchlistHandleStrategy;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testIsResourceTypeMatched_DexPair() {
        // 测试 DEX_PAIR 资源类型匹配
        assertTrue(dexPairWatchlistHandleStrategy.isResourceTypeMatched(ResourceTypeEnum.DEX_PAIR));
    }

    @Test
    public void testIsResourceTypeMatched_OtherTypes() {
        // 测试其他资源类型不匹配
        assertFalse(dexPairWatchlistHandleStrategy.isResourceTypeMatched(ResourceTypeEnum.CRYPTO));
        assertFalse(dexPairWatchlistHandleStrategy.isResourceTypeMatched(ResourceTypeEnum.EXCHANGE));
        assertFalse(dexPairWatchlistHandleStrategy.isResourceTypeMatched(ResourceTypeEnum.MARKETPAIR));
        assertFalse(dexPairWatchlistHandleStrategy.isResourceTypeMatched(ResourceTypeEnum.DEX_TOKEN));
        assertFalse(dexPairWatchlistHandleStrategy.isResourceTypeMatched(null));
    }

    @Test
    public void testIsMatched() {
        // 测试 isMatched 方法总是返回 false
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .userId("userId")
            .uid(123L)
            .resourceType(ResourceTypeEnum.DEX_PAIR)
            .resourceIds(List.of(1L))
            .build();

        assertFalse(dexPairWatchlistHandleStrategy.isMatched(param));
    }

    @Test
    public void testSubscribe_Success() {
        // Setup - 成功订阅
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .userId("userId")
            .uid(123L)
            .subscribeType(SubscribeTypeEnum.SUBSCRIBE)
            .resourceType(ResourceTypeEnum.DEX_PAIR)
            .resourceIds(List.of(1L, 2L))
            .needReplaceAllResourceIds(false)
            .build();

        BaseRequest request = BaseRequest.getInstance();

        // Mock WatchListEntity
        WatchListEntity watchListEntity = WatchListEntity.builder()
            .id(new ObjectId())
            .userId("userId")
            .uid(123L)
            .build();

        // Configure WatchListRespositoryImpl.upsertWatchlistWithType(...)
        when(mockWatchListRespository.upsertWatchlistWithType(any(WatchListEntity.class), eq(ResourceTypeEnum.DEX_PAIR)))
            .thenReturn(Mono.just(watchListEntity));

        // Configure WatchListRespositoryImpl.followResourcesByType(...)
        when(mockWatchListRespository.followResourcesByType(eq(watchListEntity), eq(ResourceTypeEnum.DEX_PAIR), 
            eq(new Long[]{1L, 2L}), eq(true), eq(false)))
            .thenReturn(Mono.just(watchListEntity));

        // Run the test
        Mono<WatchListSubscribeResultDTO> result = dexPairWatchlistHandleStrategy.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertEquals(watchListResult.getWatchListId(), watchListEntity.getId().toHexString());
            })
            .verifyComplete();
    }

    @Test
    public void testSubscribe_Unsubscribe() {
        // Setup - 取消订阅
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .userId("userId")
            .uid(123L)
            .subscribeType(SubscribeTypeEnum.UNSUBSCRIBE)
            .resourceType(ResourceTypeEnum.DEX_PAIR)
            .resourceIds(List.of(1L))
            .needReplaceAllResourceIds(false)
            .build();

        BaseRequest request = BaseRequest.getInstance();

        // Mock WatchListEntity
        WatchListEntity watchListEntity = WatchListEntity.builder()
            .id(new ObjectId())
            .userId("userId")
            .uid(123L)
            .build();

        // Configure WatchListRespositoryImpl.upsertWatchlistWithType(...)
        when(mockWatchListRespository.upsertWatchlistWithType(any(WatchListEntity.class), eq(ResourceTypeEnum.DEX_PAIR)))
            .thenReturn(Mono.just(watchListEntity));

        // Configure WatchListRespositoryImpl.followResourcesByType(...)
        when(mockWatchListRespository.followResourcesByType(eq(watchListEntity), eq(ResourceTypeEnum.DEX_PAIR), 
            eq(new Long[]{1L}), eq(false), eq(false)))
            .thenReturn(Mono.just(watchListEntity));

        // Run the test
        Mono<WatchListSubscribeResultDTO> result = dexPairWatchlistHandleStrategy.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
            })
            .verifyComplete();
    }

    @Test
    public void testSubscribe_WithWatchListId() {
        // Setup - 使用现有的 watchListId
        String watchListId = new ObjectId().toHexString();
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .userId("userId")
            .uid(123L)
            .subscribeType(SubscribeTypeEnum.SUBSCRIBE)
            .resourceType(ResourceTypeEnum.DEX_PAIR)
            .resourceIds(List.of(1L))
            .watchListId(watchListId)
            .needReplaceAllResourceIds(false)
            .build();

        BaseRequest request = BaseRequest.getInstance();

        // Mock WatchListEntity
        WatchListEntity watchListEntity = WatchListEntity.builder()
            .id(new ObjectId(watchListId))
            .userId("userId")
            .uid(123L)
            .build();

        // Configure WatchListRespositoryImpl.upsertWatchlistWithType(...)
        when(mockWatchListRespository.upsertWatchlistWithType(any(WatchListEntity.class), eq(ResourceTypeEnum.DEX_PAIR)))
            .thenReturn(Mono.just(watchListEntity));

        // Configure WatchListRespositoryImpl.followResourcesByType(...)
        when(mockWatchListRespository.followResourcesByType(eq(watchListEntity), eq(ResourceTypeEnum.DEX_PAIR), 
            eq(new Long[]{1L}), eq(true), eq(false)))
            .thenReturn(Mono.just(watchListEntity));

        // Run the test
        Mono<WatchListSubscribeResultDTO> result = dexPairWatchlistHandleStrategy.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
                Assert.assertEquals(watchListResult.getWatchListId(), watchListId);
            })
            .verifyComplete();
    }


    @Test
    public void testSubscribe_RepositoryError() {
        // Setup - 数据库错误
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .userId("userId")
            .uid(123L)
            .subscribeType(SubscribeTypeEnum.SUBSCRIBE)
            .resourceType(ResourceTypeEnum.DEX_PAIR)
            .resourceIds(List.of(1L))
            .needReplaceAllResourceIds(false)
            .build();

        BaseRequest request = BaseRequest.getInstance();

        // Mock WatchListEntity
        WatchListEntity watchListEntity = WatchListEntity.builder()
            .id(new ObjectId())
            .userId("userId")
            .uid(123L)
            .build();

        // Configure WatchListRespositoryImpl.upsertWatchlistWithType(...) 返回错误
        when(mockWatchListRespository.upsertWatchlistWithType(any(WatchListEntity.class), eq(ResourceTypeEnum.DEX_PAIR)))
            .thenReturn(Mono.error(new RuntimeException("Database error")));

        // Run the test
        Mono<WatchListSubscribeResultDTO> result = dexPairWatchlistHandleStrategy.subscribe(param, request);

        // Verify the results - 错误被处理，返回空结果
        StepVerifier.create(result)
            .expectSubscription()
            .verifyComplete();
    }

    @Test
    public void testSubscribe_ReplaceAllResourceIds() {
        // Setup - 替换所有资源ID
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .userId("userId")
            .uid(123L)
            .subscribeType(SubscribeTypeEnum.SUBSCRIBE)
            .resourceType(ResourceTypeEnum.DEX_PAIR)
            .resourceIds(List.of(1L, 2L, 3L))
            .needReplaceAllResourceIds(true)
            .build();

        BaseRequest request = BaseRequest.getInstance();

        // Mock WatchListEntity
        WatchListEntity watchListEntity = WatchListEntity.builder()
            .id(new ObjectId())
            .userId("userId")
            .uid(123L)
            .build();

        // Configure WatchListRespositoryImpl.upsertWatchlistWithType(...)
        when(mockWatchListRespository.upsertWatchlistWithType(any(WatchListEntity.class), eq(ResourceTypeEnum.DEX_PAIR)))
            .thenReturn(Mono.just(watchListEntity));

        // Configure WatchListRespositoryImpl.followResourcesByType(...)
        when(mockWatchListRespository.followResourcesByType(eq(watchListEntity), eq(ResourceTypeEnum.DEX_PAIR), 
            eq(new Long[]{1L, 2L, 3L}), eq(true), eq(true)))
            .thenReturn(Mono.just(watchListEntity));

        // Run the test
        Mono<WatchListSubscribeResultDTO> result = dexPairWatchlistHandleStrategy.subscribe(param, request);

        // Verify the results
        StepVerifier.create(result)
            .expectSubscription()
            .assertNext(watchListResult -> {
                Assert.assertNotNull(watchListResult);
                Assert.assertNotNull(watchListResult.getWatchListId());
            })
            .verifyComplete();
    }
}
