package com.cmc.asset.business.priceprediction.impl;

import com.cmc.asset.business.priceprediction.PricePredictionParamValidator;
import com.cmc.asset.dao.entity.PricePredictionEntity;
import com.cmc.asset.dao.repository.mongo.PricePredictionRepository;
import com.cmc.asset.dao.repository.redis.CacheRepository;
import com.cmc.asset.model.contract.priceprediction.*;
import com.cmc.data.common.utils.JacksonUtils;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class PricePredictionServiceImplTest {

    @Mock
    private PricePredictionRepository mockPricePredictionRepository;
    @Mock(name = "newPredictionValidator")
    private PricePredictionParamValidator mockNewPredictionValidator;
    @Mock(name = "queryPredictionValidator")
    private PricePredictionParamValidator mockQueryPredictionValidator;
    @Mock
    private QueryByUserIdParamValidator mockQueryByUserIdParamValidator;
    @Mock
    private UpdatePricePredictionValidator mockUpdatePricePredictionValidator;
    @Mock
    private CacheRepository mockCacheRepository;

    @InjectMocks
    private PricePredictionServiceImpl pricePredictionServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testSave() {
        // Setup
        final CreatePricePredictionParamDTO param =
            new CreatePricePredictionParamDTO(1, 2020, 1, new BigDecimal("0.00"));
        param.getHeader().setUserId("1");

        doNothing().when(mockNewPredictionValidator).validate(any());

        // Configure PricePredictionRepository.save(...).
        //doNothing().when(mockNewPredictionValidator).validate(any());

        when(mockPricePredictionRepository.countByUserIdAndCryptoIdAndCreateDateBetween(any(), any(), any(), any())).thenReturn(Mono.just(1L));

        final Mono<PricePredictionEntity> pricePredictionEntityMono = Mono.just(
            new PricePredictionEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "1", 1, new BigDecimal("1.00"), 2020, 1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("1.00"),
                new BigDecimal("1.00"), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockPricePredictionRepository.save(any()))
            .thenReturn(pricePredictionEntityMono);

        when(mockCacheRepository.delete(any())).thenReturn(Mono.just(1L));

        // Run the test
        final Mono<PricePredictionDTO> result = pricePredictionServiceImplUnderTest.save(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotEquals(it, null)).verifyComplete();
    }

    @Test
    public void testUpdate() {
        // Setup
        final UpdatePricePredictionParamDTO param =
            new UpdatePricePredictionParamDTO("1", new BigDecimal("1.00"), "1", "1");

        // Configure PricePredictionRepository.updatePricePrediction(...).
        final Mono<PricePredictionEntity> pricePredictionEntityMono = Mono.just(
            new PricePredictionEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "targetUserId", 0, new BigDecimal("0.00"), 2020, 1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockPricePredictionRepository.updatePricePrediction("1", new BigDecimal("1.00")))
            .thenReturn(pricePredictionEntityMono);

        // Configure PricePredictionRepository.queryById(...).
        final Mono<PricePredictionEntity> pricePredictionEntityMono1 = Mono.just(
            new PricePredictionEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "1", 1, new BigDecimal("1.00"), 2020, 1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockPricePredictionRepository.queryById("1")).thenReturn(pricePredictionEntityMono1);

        when(mockCacheRepository.delete(any())).thenReturn(Mono.just(0L));
        doNothing().when(mockUpdatePricePredictionValidator).validate(any());
        // Run the test
        final Mono<PricePredictionDTO> result = pricePredictionServiceImplUnderTest.update(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotEquals(null, it)).verifyComplete();
    }


    @Test
    public void testQuery() {
        // Setup
        final QueryPricePredictionParamDTO param = new QueryPricePredictionParamDTO();
        param.setCryptoId(0);
        param.setTargetYear(2020);
        param.setTargetMonth(1);

        // Configure PricePredictionRepository.queryPricePrediction(...).
        final Mono<PricePredictionEntity> pricePredictionEntityMono = Mono.just(
            new PricePredictionEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "targetUserId", 0, new BigDecimal("0.00"), 2020, 1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockPricePredictionRepository.queryPricePrediction(any()))
            .thenReturn(pricePredictionEntityMono);

        doNothing().when(mockQueryPredictionValidator).validate(any());

        // Run the test
        final Mono<PricePredictionDTO> result = pricePredictionServiceImplUnderTest.query(param);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotEquals(null, it)).verifyComplete();
    }


    @Test
    public void testQueryHalfYear() {
        // Setup
        final QueryPricePredictionParamDTO request = new QueryPricePredictionParamDTO();
        request.setCryptoId(0);
        request.setTargetYear(2020);
        request.setTargetMonth(1);
        request.getHeader().setUserId("1");

        // Configure PricePredictionRepository.queryPricePredictionsInRecentHalfYearByUserId(...).
        final Flux<PricePredictionEntity> pricePredictionEntityFlux = Flux.just(
            new PricePredictionEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "targetUserId", 0, new BigDecimal("0.00"), 2020, 1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockPricePredictionRepository
            .queryPricePredictionsInRecentHalfYearByUserId(request))
            .thenReturn(pricePredictionEntityFlux);

        when(mockCacheRepository.cacheInString("key", "value", Duration.ofDays(0L))).thenReturn(Mono.just(false));
        when(mockCacheRepository.getCachedValueInString(any()))
            .thenReturn(Mono.just(JacksonUtils.toJsonString(new PricePredictionCombinedResultDTO(1, null))));
        doNothing().when(mockQueryByUserIdParamValidator).validate(any());
        // Run the test
        final Mono<PricePredictionCombinedResultDTO> result = pricePredictionServiceImplUnderTest.queryHalfYear(request);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(it -> Assert.assertNotEquals(null, it)).verifyComplete();

    }

    @Test
    public void testConvertToViewObject() {
        // Setup
        final PricePredictionEntity entity =
            PricePredictionEntity.builder()
                .id(new ObjectId())
                .userId("1")
                .cryptoId(1)
                .predictedPrice(new BigDecimal(1))
                .targetYear(1)
                .targetMonth(1)
                .build();

        final PricePredictionDTO expectedResult =
            PricePredictionDTO.builder()
                .id(entity.getId().toString())
                .userId(entity.getUserId())
                .cryptoId(entity.getCryptoId())
                .predictedPrice(entity.getPredictedPrice())
                .targetMonth(entity.getTargetMonth())
                .targetYear(entity.getTargetYear())
                .build();

        // Run the test
        final PricePredictionDTO result = pricePredictionServiceImplUnderTest.convertToViewObject(entity);

        Assert.assertEquals(expectedResult.toString(), result.toString());
    }
}
