package com.cmc.asset.business.priceprediction.impl;

import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.cache.StableCoinCache;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.model.contract.priceprediction.BasePricePredictionParamDTO;
import com.cmc.data.common.exception.BusinessException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class BasePricePredictionValidatorTest {

    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;
    @Mock
    private StableCoinCache mockStableCoinCache;

    @InjectMocks
    private BasePricePredictionValidator basePricePredictionValidatorUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testValidate() {
        // Setup
        final BasePricePredictionParamDTO request1 = new BasePricePredictionParamDTO(0, 2022, 1);
        final BasePricePredictionParamDTO request2 = new BasePricePredictionParamDTO();
        final BasePricePredictionParamDTO request3 = new BasePricePredictionParamDTO(0, 2020, 1);
        final BasePricePredictionParamDTO request4 = new BasePricePredictionParamDTO(0, 2022, 13);
        final BasePricePredictionParamDTO request5 = new BasePricePredictionParamDTO(1, 2022, 1);
        final BasePricePredictionParamDTO request6 = new BasePricePredictionParamDTO(2, 2022, 1);


        // Configure CryptoCurrencyCache.getCryptoCurrencyById(...).
        final CryptoCurrencyInfoDTO cryptoCurrencyInfoDTO =
            new CryptoCurrencyInfoDTO(0, "name", "symbol", "slug", "status", "category", 0, 0);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(0)).thenReturn(cryptoCurrencyInfoDTO);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(1)).thenReturn(null);
        when(mockCryptoCurrencyCache.getCryptoCurrencyById(2)).thenReturn(cryptoCurrencyInfoDTO);
        when(mockStableCoinCache.getById(0)).thenReturn(0);
        when(mockStableCoinCache.getById(0)).thenReturn(null);

        // Run the test

        basePricePredictionValidatorUnderTest.validate(request1);
        try {
            basePricePredictionValidatorUnderTest.validate(request2);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Invalid parameters")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            basePricePredictionValidatorUnderTest.validate(request3);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Invalid target year")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            basePricePredictionValidatorUnderTest.validate(request4);
        } catch (BusinessException e) {
            if (e.getDesc().equals("Invalid target month")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            basePricePredictionValidatorUnderTest.validate(request5);
        } catch (BusinessException e) {
            if (e.getDesc().equals("The crypto is not found.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

        try {
            basePricePredictionValidatorUnderTest.validate(request6);
        } catch (BusinessException e) {
            if (e.getDesc().equals("The crypto is stable coin.")) {
                Assert.assertTrue(true);
            } else {
                Assert.assertTrue(false);
            }
        }

    }

}
