package com.cmc.asset.business.watchlist.impl;

import com.cmc.asset.business.coins.IUserCoinService;
import com.cmc.asset.business.gravity.AutoFollowService;
import com.cmc.asset.business.watchlist.AsyncWatchListService;
import com.cmc.asset.cache.CryptoCurrencyCache;
import com.cmc.asset.dao.entity.CryptoWatchCountEntity;
import com.cmc.asset.dao.entity.FollowWatchListEntity;
import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.dao.repository.mongo.CryptoWatchCountRepository;
import com.cmc.asset.dao.repository.mongo.FollowWatchListRepositoryImpl;
import com.cmc.asset.dao.repository.mongo.WatchListRespositoryImpl;
import com.cmc.asset.domain.gravity.AutoFollowDTO;
import com.cmc.asset.domain.watchlist.SubscribeParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeParamDTO;
import com.cmc.asset.model.enums.ResourceTypeEnum;
import com.cmc.asset.model.enums.SubscribeTypeEnum;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.function.Tuples;

import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import static org.mockito.MockitoAnnotations.openMocks;

/**
 * <AUTHOR>
 * @date 2022/12/26
 */
public class DefaultWatchListHandleStrategyImplTest {


    @Mock
    private WatchListRespositoryImpl watchListRespository;
    @Mock
    private FollowWatchListRepositoryImpl followWatchListRepository;
    @Mock
    private CryptoCurrencyCache cryptoCurrencyCache;
    @Mock
    private IUserCoinService userCoinService;
    @Mock
    private CryptoWatchCountRepository cryptoWatchCountRepository;
    @Mock
    private AsyncWatchListService asyncWatchListService;
    @Mock
    private AutoFollowService autoFollowService;
    @InjectMocks
    private DefaultWatchListHandleStrategyImpl defaultWatchListHandleStrategy;
    

    private AutoCloseable mockitoCloseable;
    
    
    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        // set repository
    }

    @Test
    public void testIsResourceTypeMatchedSuccess() {
        boolean resourceTypeMatched = defaultWatchListHandleStrategy.isResourceTypeMatched(null);
        boolean resourceTypeMatched2 = defaultWatchListHandleStrategy.isResourceTypeMatched(ResourceTypeEnum.MARKETPAIR);
        Assert.assertTrue(resourceTypeMatched && resourceTypeMatched2);
    }

    @Test
    public void testSubscribeExchange() {
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .needReplaceAllResourceIds(false)
            .subscribeType(SubscribeTypeEnum.SUBSCRIBE)
            .resourceIds(List.of(1L))
            .resourceType(ResourceTypeEnum.CRYPTO).build();

        WatchListEntity entity = WatchListEntity.builder().id(new ObjectId("63a1d74bbe6a33afdb74f776")).build();

        WatchListSubscribeParamDTO watchListSubscribeParamDTO = new WatchListSubscribeParamDTO();
        Mockito.when(cryptoCurrencyCache.getByIds(Mockito.anyCollection())).thenReturn(Set.of(1));
        Mockito.when(watchListRespository.findWatchList(Mockito.any()))
            .thenReturn(Mono.just(entity));
        AtomicReference<Boolean> atomicReference = new AtomicReference<>(false);
        Mockito.when(watchListRespository.findAndCombineMainWatchList(Mockito.any()))
            .thenReturn(Mono.just(Tuples.of(atomicReference,WatchListEntity.builder().build())));

        Mockito.when(watchListRespository.followResources(Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(),
            Mockito.anyBoolean())).thenReturn(Mono.just(entity));
        Mockito.when(userCoinService.resetTopCoins(Mockito.any(), Mockito.any())).thenReturn(Mono.just(true));
        Mockito.when(cryptoWatchCountRepository.incrCryptoWatchCount(Mockito.anyInt(), Mockito.anyInt())).thenReturn(Mono.just(CryptoWatchCountEntity.builder().build()));
        StepVerifier.create(defaultWatchListHandleStrategy.subscribe(param, watchListSubscribeParamDTO))
            .expectSubscription()
            .assertNext(Assert::assertNotNull).verifyComplete();

    }

    @Test
    public void testSubScribeCrypto() {
        ReflectionTestUtils.setField(defaultWatchListHandleStrategy, "autoAddFollow", true);
        SubscribeParamDTO param = SubscribeParamDTO.builder()
            .needReplaceAllResourceIds(false)
            .subscribeType(SubscribeTypeEnum.SUBSCRIBE)
            .resourceIds(List.of(1L))
            .resourceType(ResourceTypeEnum.CRYPTO).build();

        WatchListEntity entity = WatchListEntity.builder().id(new ObjectId("63a1d74bbe6a33afdb74f776")).build();

        WatchListSubscribeParamDTO watchListSubscribeParamDTO = new WatchListSubscribeParamDTO();
        Mockito.when(cryptoCurrencyCache.getByIds(Mockito.anyCollection())).thenReturn(Set.of(1));
        Mockito.when(watchListRespository.findWatchList(Mockito.any()))
            .thenReturn(Mono.just(WatchListEntity.builder().build()));

        AtomicReference<Boolean> atomicReference = new AtomicReference<>(false);
        Mockito.when(watchListRespository.findAndCombineMainWatchList(Mockito.any()))
            .thenReturn(Mono.just(Tuples.of(atomicReference,WatchListEntity.builder().build())));


        FollowWatchListEntity followWatchListEntity = FollowWatchListEntity.builder().build();
        followWatchListEntity.setVersion(1L);
        Mockito.when(followWatchListRepository.findAndModify(Mockito.any(), Mockito.isNull(), Mockito.anyInt(),
            Mockito.anyBoolean())).thenReturn(Mono.just(followWatchListEntity));

        Mockito.when(watchListRespository.followResources(Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(),
            Mockito.anyBoolean())).thenReturn(Mono.just(entity));
        Mockito.when(userCoinService.resetTopCoins(Mockito.any(), Mockito.any())).thenReturn(Mono.just(true));
        Mockito.when(cryptoWatchCountRepository.incrCryptoWatchCount(Mockito.anyInt(), Mockito.anyInt())).thenReturn(Mono.just(CryptoWatchCountEntity.builder().build()));
        Mockito.doNothing().when(asyncWatchListService).asyncCalcSubscribeWatchListCoin(Mockito.any(SubscribeParamDTO.class), Mockito.anyBoolean(), Mockito.any(WatchListEntity.class));
        Mockito.doNothing().when(autoFollowService).sendMessage(Mockito.any(AutoFollowDTO.class));
        StepVerifier.create(defaultWatchListHandleStrategy.subscribe(param, watchListSubscribeParamDTO))
            .expectSubscription()
            .assertNext(Assert::assertNotNull).verifyComplete();
    }
}
