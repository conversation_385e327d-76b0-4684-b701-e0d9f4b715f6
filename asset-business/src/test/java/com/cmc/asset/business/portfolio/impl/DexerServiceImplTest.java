package com.cmc.asset.business.portfolio.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.cmc.asset.integration.DexerApiClient;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.dexer.PlatformDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public class DexerServiceImplTest {
    private DexerApiClient dexerApiClient;
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;
    private ReactiveValueOperations<String, String> valueOperations;
    private DexerServiceImpl dexerServiceImpl;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        dexerApiClient = mock(DexerApiClient.class);
        assetRedisTemplate = mock(ReactiveRedisTemplate.class);
        valueOperations = mock(ReactiveValueOperations.class);
        dexerServiceImpl = new DexerServiceImpl();
        ReflectionTestUtils.setField(dexerServiceImpl, "dexerApiClient", dexerApiClient);
        ReflectionTestUtils.setField(dexerServiceImpl, "assetRedisTemplate", assetRedisTemplate);
        ReflectionTestUtils.setField(dexerServiceImpl, "cacheExpirationSeconds", 60);
        when(assetRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    public void testGetPlatforms() {
        PlatformDTO dto = new PlatformDTO();
        when(dexerApiClient.getPlatforms()).thenReturn(Mono.just(List.of(dto)));
        Mono<List<PlatformDTO>> result = dexerServiceImpl.getPlatforms();
        List<PlatformDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
    }

    @Test
    public void testGetPlatformsFromDeQuery() {
        PlatformNewDTO dto = new PlatformNewDTO();
        when(dexerApiClient.getPlatformsFromDeQuery()).thenReturn(Mono.just(List.of(dto)));
        Mono<List<PlatformNewDTO>> result = dexerServiceImpl.getPlatformsFromDeQuery();
        List<PlatformNewDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
    }

    @Test
    public void testGetPairsFromDeQuery_allCache() {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress("0xabc");
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of("{\"address\":\"0xabc\"}")));
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery("eth", List.of("0xabc"));
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
    }

    @Test
    public void testGetPairsFromDeQuery_partialCache() {
        // one cache miss, one hit
        when(valueOperations.multiGet(anyList())).thenReturn(
            Mono.just(List.of("{\"address\":\"0xabc\"}", "{\"address\":\"0xdef\"}")));
        TokenPriceDTO apiDto = new TokenPriceDTO();
        apiDto.setAddress("0xabc");
        when(dexerApiClient.getPairListByDeQuery(eq("eth"), anyList())).thenReturn(Mono.just(List.of(apiDto)));
        when(valueOperations.set(anyString(), anyString(), any())).thenReturn(Mono.just(true));
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery("eth", List.of("0xabc", "0xdef"));
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 2);
    }
}