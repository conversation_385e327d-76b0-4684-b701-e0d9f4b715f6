package com.cmc.asset.business.portfolio.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.cmc.asset.integration.DexerApiClient;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.asset.model.contract.dexer.PlatformDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

public class DexerServiceImplTest {
    private DexerApiClient dexerApiClient;
    private ReactiveRedisTemplate<String, String> assetRedisTemplate;
    private ReactiveValueOperations<String, String> valueOperations;
    private DexerServiceImpl dexerServiceImpl;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        dexerApiClient = mock(DexerApiClient.class);
        assetRedisTemplate = mock(ReactiveRedisTemplate.class);
        valueOperations = mock(ReactiveValueOperations.class);
        dexerServiceImpl = new DexerServiceImpl();
        ReflectionTestUtils.setField(dexerServiceImpl, "dexerApiClient", dexerApiClient);
        ReflectionTestUtils.setField(dexerServiceImpl, "assetRedisTemplate", assetRedisTemplate);
        ReflectionTestUtils.setField(dexerServiceImpl, "cacheExpirationSeconds", 60);
        ReflectionTestUtils.setField(dexerServiceImpl, "batchSize", 50);
        ReflectionTestUtils.setField(dexerServiceImpl, "maxBatchConcurrency", 3);
        when(assetRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(assetRedisTemplate.expire(anyString(), any(Duration.class))).thenReturn(Mono.just(true));
    }

    @Test
    public void testGetPlatforms() {
        PlatformDTO dto = new PlatformDTO();
        when(dexerApiClient.getPlatforms()).thenReturn(Mono.just(List.of(dto)));
        Mono<List<PlatformDTO>> result = dexerServiceImpl.getPlatforms();
        List<PlatformDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
    }

    @Test
    public void testGetPlatformsFromDeQuery() {
        PlatformNewDTO dto = new PlatformNewDTO();
        when(dexerApiClient.getPlatformsFromDeQuery()).thenReturn(Mono.just(List.of(dto)));
        Mono<List<PlatformNewDTO>> result = dexerServiceImpl.getPlatformsFromDeQuery();
        List<PlatformNewDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
    }

    @Test
    public void testGetPairsFromDeQuery_allCache() {
        // Create test tokens
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token1 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token1);

        // Create cached TokenPriceDTO
        TokenPriceDTO cachedDto = new TokenPriceDTO();
        cachedDto.setAddress("0xabc");
        cachedDto.setPlatformDexerName("eth");
        String cachedJson = JacksonUtils.serialize(cachedDto);

        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of(cachedJson)));

        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
        Assert.assertEquals(list.get(0).getAddress(), "0xabc");
    }

    @Test
    public void testGetPairsFromDeQuery_partialCache() {
        // Create test tokens
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token1 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token2 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xdef")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token1, token2);

        // Create cached TokenPriceDTO for first token
        TokenPriceDTO cachedDto = new TokenPriceDTO();
        cachedDto.setAddress("0xabc");
        cachedDto.setPlatformDexerName("eth");
        String cachedJson = JacksonUtils.serialize(cachedDto);

        // Mock cache response - first token cached, second not cached (empty string)
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of(cachedJson, "")));

        // Mock API response for missing token
        TokenPriceDTO apiDto = new TokenPriceDTO();
        apiDto.setAddress("0xdef");
        apiDto.setPlatformDexerName("eth");
        when(dexerApiClient.getPairListByDeQuery(anyList())).thenReturn(Mono.just(List.of(apiDto)));
        when(valueOperations.set(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 2);
    }

    @Test
    public void testGetPairsFromDeQuery_noCacheHit() {
        // Create test tokens
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token1 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token1);

        // Mock empty cache response
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of("")));

        // Mock API response
        TokenPriceDTO apiDto = new TokenPriceDTO();
        apiDto.setAddress("0xabc");
        apiDto.setPlatformDexerName("eth");
        when(dexerApiClient.getPairListByDeQuery(anyList())).thenReturn(Mono.just(List.of(apiDto)));
        when(valueOperations.set(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
        Assert.assertEquals(list.get(0).getAddress(), "0xabc");
    }

    @Test
    public void testGetPairs_allCache() {
        List<Long> poolIds = List.of(1L, 2L);

        // Create cached PairInfoDTO
        PairInfoDTO cachedPair1 = new PairInfoDTO();
        cachedPair1.setPoolId(1L);
        cachedPair1.setAddress("0xpair1");

        PairInfoDTO cachedPair2 = new PairInfoDTO();
        cachedPair2.setPoolId(2L);
        cachedPair2.setAddress("0xpair2");

        String cachedJson1 = JacksonUtils.serialize(cachedPair1);
        String cachedJson2 = JacksonUtils.serialize(cachedPair2);

        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of(cachedJson1, cachedJson2)));

        Mono<Map<Long, PairInfoDTO>> result = dexerServiceImpl.getPairs(poolIds);
        Map<Long, PairInfoDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertEquals(map.size(), 2);
        Assert.assertTrue(map.containsKey(1L));
        Assert.assertTrue(map.containsKey(2L));
        Assert.assertEquals(map.get(1L).getAddress(), "0xpair1");
        Assert.assertEquals(map.get(2L).getAddress(), "0xpair2");
    }

    @Test
    public void testGetPairs_noCache() {
        List<Long> poolIds = List.of(1L, 2L);

        // Mock empty cache
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of("")));

        // Create API response
        PairInfoDTO apiPair1 = new PairInfoDTO();
        apiPair1.setPoolId(1L);
        apiPair1.setAddress("0xpair1");

        PairInfoDTO apiPair2 = new PairInfoDTO();
        apiPair2.setPoolId(2L);
        apiPair2.setAddress("0xpair2");

        when(dexerApiClient.getPairs(poolIds)).thenReturn(Mono.just(List.of(apiPair1, apiPair2)));
        when(valueOperations.multiSet(any(Map.class))).thenReturn(Mono.just(true));

        Mono<Map<Long, PairInfoDTO>> result = dexerServiceImpl.getPairs(poolIds);
        Map<Long, PairInfoDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertEquals(map.size(), 2);
        Assert.assertTrue(map.containsKey(1L));
        Assert.assertTrue(map.containsKey(2L));
    }

    @Test
    public void testGetPairs_partialCache() {
        List<Long> poolIds = List.of(1L, 2L);

        // Create cached PairInfoDTO for first pool only
        PairInfoDTO cachedPair1 = new PairInfoDTO();
        cachedPair1.setPoolId(1L);
        cachedPair1.setAddress("0xpair1");
        String cachedJson1 = JacksonUtils.serialize(cachedPair1);

        // Mock partial cache hit
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of(cachedJson1, "")));

        // Create API response for missing pool
        PairInfoDTO apiPair2 = new PairInfoDTO();
        apiPair2.setPoolId(2L);
        apiPair2.setAddress("0xpair2");

        when(dexerApiClient.getPairs(List.of(2L))).thenReturn(Mono.just(List.of(apiPair2)));
        when(valueOperations.multiSet(any(Map.class))).thenReturn(Mono.just(true));

        Mono<Map<Long, PairInfoDTO>> result = dexerServiceImpl.getPairs(poolIds);
        Map<Long, PairInfoDTO> map = result.block();
        Assert.assertNotNull(map);
        Assert.assertEquals(map.size(), 2);
        Assert.assertTrue(map.containsKey(1L));
        Assert.assertTrue(map.containsKey(2L));
        Assert.assertEquals(map.get(1L).getAddress(), "0xpair1");
        Assert.assertEquals(map.get(2L).getAddress(), "0xpair2");
    }

    @Test
    public void testGetPairsFromDeQuery_emptyInput() {
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(null);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertTrue(list.isEmpty());

        result = dexerServiceImpl.getPairsFromDeQuery(List.of());
        list = result.block();
        Assert.assertNotNull(list);
        Assert.assertTrue(list.isEmpty());
    }
}