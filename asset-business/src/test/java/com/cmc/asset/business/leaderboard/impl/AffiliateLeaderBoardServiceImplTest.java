package com.cmc.asset.business.leaderboard.impl;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.referral.UserReferralSummaryEntity;
import com.cmc.asset.dao.repository.mongo.UserReferralSummaryRepository;
import com.cmc.asset.model.contract.referral.AffiliateLeaderBoardDTO;
import com.cmc.auth.common.utils.AuthUtils;
import com.cmc.data.common.BaseRequest;
import com.cmc.data.common.utils.JacksonUtils;
import java.util.Date;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * @ClassName AffiliateServiceImplTest.java
 * <AUTHOR>
 * @Date 2021/10/12 11:03
 */
public class AffiliateLeaderBoardServiceImplTest {

    @Mock
    private UserReferralSummaryRepository userReferralSummaryRepository;

    private ReactiveRedisTemplate<String, String> assetRedisTemplate;

    private AutoCloseable mockitoCloseable;

    @InjectMocks
    private AffiliateLeaderBoardServiceImpl affiliateLeaderBoardService;

    @BeforeMethod
    public void setUp() {
        assetRedisTemplate = mock(ReactiveRedisTemplate.class, Mockito.RETURNS_DEEP_STUBS);
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testGetAffiliateRankInfo() {
        BaseRequest baseRequest = BaseRequest.getInstance();
        baseRequest.getHeader().setUserId("test");
        ReflectionTestUtils.setField(affiliateLeaderBoardService, "assetRedisTemplate", assetRedisTemplate);

        UserReferralSummaryEntity entity = UserReferralSummaryEntity.builder()
            .referralAward(10)
            .createTime(new Date())
            .id(new ObjectId())
            .userId("test")
            .build();

        AffiliateLeaderBoardDTO leaderBoardVo = AffiliateLeaderBoardDTO.builder()
            .rank(1)
            .build();
        try(MockedStatic<AuthUtils> mockedStatic = Mockito.mockStatic(AuthUtils.class)) {
            when(userReferralSummaryRepository.findByUserId("test")).thenReturn(Mono.just(entity));
            when(assetRedisTemplate.opsForHash().get(anyString(),anyString())).thenReturn(Mono.empty());
            when(AuthUtils.getUserInfo("test")).thenReturn(new AuthUtils.AuthResult());
            Mono<AffiliateLeaderBoardDTO> affiliateRankInfo = affiliateLeaderBoardService.getAffiliateRankInfo(baseRequest);

            StepVerifier.create(affiliateRankInfo).expectNext(
                AffiliateLeaderBoardDTO.builder().accumulatedDiamonds(10).rank(-1).index(-1).build()).verifyComplete();
        }
    }

    @Test
    public void testGetAffiliateRankOnly() {
        BaseRequest baseRequest = BaseRequest.getInstance();
        baseRequest.getHeader().setUserId("test");
        ReflectionTestUtils.setField(affiliateLeaderBoardService, "assetRedisTemplate", assetRedisTemplate);
        AffiliateLeaderBoardDTO leaderBoardVo = AffiliateLeaderBoardDTO.builder()
            .rank(1)
            .build();

        when(assetRedisTemplate.opsForHash().get(anyString(),anyString())).thenReturn(Mono.just(JacksonUtils.toJsonString(leaderBoardVo)));
        Mono<AffiliateLeaderBoardDTO> affiliateRankOnly = affiliateLeaderBoardService.getAffiliateRankOnly(baseRequest);

        StepVerifier.create(affiliateRankOnly).expectNext(
            AffiliateLeaderBoardDTO.builder().rank(1).build()).verifyComplete();
    }


}
