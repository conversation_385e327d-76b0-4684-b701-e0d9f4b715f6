package com.cmc.asset.business.portfolio.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.business.portfolio.IPortfolioDashboardService;
import com.cmc.asset.business.portfolio.IPortfolioService;
import com.cmc.asset.business.portfolio.manager.PortfolioValueManager;
import com.cmc.asset.model.common.PortfolioPageVo;
import com.cmc.asset.model.contract.portfolio.PortfolioAggrAllocationRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioAggrAllocationResponse;
import com.cmc.asset.model.contract.portfolio.PortfolioAssetAllocationRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioAssetAllocationResponse;
import com.cmc.asset.model.contract.portfolio.PortfolioChainAllocationDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioQuerySummaryRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioQuerySummaryResponse;
import com.cmc.asset.model.contract.portfolio.PortfolioStatisticsDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioTokenAllocationDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioLineChartQueryDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioLineHistoricalChartResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsRequest;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsResponse;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryLineChartRequestDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryLineChartResponseDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryPieChartRequestDTO;
import com.cmc.asset.model.contract.portfolio.dashboard.QueryPieChartResponseDTO;
import org.junit.Assert;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class PortfolioAggregationServiceImplTest {

    @Mock
    private IPortfolioDashboardService mockDashboardService;
    @Mock
    private IPortfolioService mockPortfolioService;
    @Mock
    private PortfolioCryptoRedisService mockPortfolioCryptoRedisService;
    @Mock
    private PortfolioValueManager mockPortfolioValueManager;

    @InjectMocks
    private PortfolioAggregationServiceImpl portfolioAggregationServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(portfolioAggregationServiceImplUnderTest, "allocationLimit", 4);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testAggrAssetAllocationDashboard() {
        // Setup
        final PortfolioAggrAllocationRequest request = new PortfolioAggrAllocationRequest();
        request.setModuleType("dashboard");
        request.setPortfolioSourceId("5c9c0ec4bb6e9d3ead204db1");

        // Configure IPortfolioDashboardService.queryPieChart(...).
        final Mono<QueryPieChartResponseDTO> queryPieChartResponseDTOMono = Mono.just(QueryPieChartResponseDTO.builder()
            .token(List.of(QueryPieChartResponseDTO.PieChartDTO.builder()
                .name("cryptoSymbol")
                .percent(new BigDecimal("0.00"))
                .value(new BigDecimal("0.00"))
                .build()))
            .build());
        final QueryPieChartRequestDTO requestDTO = new QueryPieChartRequestDTO();
        requestDTO.setCryptoUnit(0);
        requestDTO.setFiatUnit(0);
        when(mockDashboardService.queryPieChart(any(QueryPieChartRequestDTO.class))).thenReturn(queryPieChartResponseDTOMono);

        // Configure IPortfolioService.assetAllocation(...).
        final Mono<PortfolioAssetAllocationResponse> portfolioAssetAllocationResponseMono =
            Mono.just(PortfolioAssetAllocationResponse.builder()
                .tokenAllocationList(List.of(PortfolioTokenAllocationDTO.builder()
                    .cryptoSymbol("cryptoSymbol")
                    .percentage(new BigDecimal("0.00"))
                    .value(new BigDecimal("0.00"))
                    .build()))
                .chainAllocationList(List.of(PortfolioChainAllocationDTO.builder()
                    .chainFullName("chainFullName")
                    .percentage(new BigDecimal("0.00"))
                    .value(new BigDecimal("0.00"))
                    .build()))
                .build());
        final PortfolioAssetAllocationRequest request1 = new PortfolioAssetAllocationRequest();
        request1.setPortfolioSourceId("portfolioSourceId");
        when(mockPortfolioService.assetAllocation(any(PortfolioAssetAllocationRequest.class))).thenReturn(portfolioAssetAllocationResponseMono);

        // Run the test
        final Mono<PortfolioAggrAllocationResponse> result =
            portfolioAggregationServiceImplUnderTest.aggrAssetAllocation(request);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }


    @Test
    void testAggrAssetAllocationPortfolioSourceId() {
        // Setup
        final PortfolioAggrAllocationRequest request = new PortfolioAggrAllocationRequest();
        request.setModuleType("portfolio");
        request.setPortfolioSourceId("5c9c0ec4bb6e9d3ead204db1");

        // Configure IPortfolioDashboardService.queryPieChart(...).
        final Mono<QueryPieChartResponseDTO> queryPieChartResponseDTOMono = Mono.just(QueryPieChartResponseDTO.builder()
            .token(List.of(QueryPieChartResponseDTO.PieChartDTO.builder()
                .name("cryptoSymbol")
                .percent(new BigDecimal("0.00"))
                .value(new BigDecimal("0.00"))
                .build()))
            .build());
        when(mockDashboardService.queryPieChart(any(QueryPieChartRequestDTO.class))).thenReturn(queryPieChartResponseDTOMono);

        // Configure IPortfolioService.assetAllocation(...).
        final Mono<PortfolioAssetAllocationResponse> portfolioAssetAllocationResponseMono =
            Mono.just(PortfolioAssetAllocationResponse.builder()
                .tokenAllocationList(List.of(PortfolioTokenAllocationDTO.builder()
                    .cryptoSymbol("cryptoSymbol")
                    .percentage(new BigDecimal("0.00"))
                    .value(new BigDecimal("0.00"))
                    .build()))
                .chainAllocationList(List.of(PortfolioChainAllocationDTO.builder()
                    .chainFullName("chainFullName")
                    .percentage(new BigDecimal("0.00"))
                    .value(new BigDecimal("0.00"))
                    .build()))
                .build());
        final PortfolioAssetAllocationRequest request1 = new PortfolioAssetAllocationRequest();
        request1.setPortfolioSourceId("portfolioSourceId");
        when(mockPortfolioService.assetAllocation(any(PortfolioAssetAllocationRequest.class))).thenReturn(portfolioAssetAllocationResponseMono);

        // Run the test
        final Mono<PortfolioAggrAllocationResponse> result =
            portfolioAggregationServiceImplUnderTest.aggrAssetAllocation(request);

        // Verify the results
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> Assert.assertNotNull(res))
            .verifyComplete();
    }


    @Test
    void testQuerySummaryStatistics() {
        // Setup
        final PortfolioSummaryStatisticsRequest querySummaryRequest = new PortfolioSummaryStatisticsRequest();
        querySummaryRequest.setConvertFiatId(0);
        querySummaryRequest.setConvertCryptoId(0);
        querySummaryRequest.setPortfolioSourceId("portfolioSourceId");
        querySummaryRequest.setChainId(0);
        querySummaryRequest.setChangePeriod("changePeriod");

        // Configure IPortfolioService.querySummaryForApp(...).
        final Mono<PortfolioQuerySummaryResponse> portfolioQuerySummaryResponseMono =
            Mono.just(PortfolioQuerySummaryResponse.builder()
                .portfolioType("portfolioType")
                .manualSummary(List.of(new PortfolioPageVo<>()))
                .walletSummary(List.of(new PortfolioPageVo<>()))
                .build());
        final PortfolioQuerySummaryRequest querySummaryRequest1 = new PortfolioQuerySummaryRequest();
        querySummaryRequest1.setCurrentPage(0);
        querySummaryRequest1.setPageSize(0);
        querySummaryRequest1.setCryptoUnit(0);
        querySummaryRequest1.setFiatUnit(0);
        querySummaryRequest1.setPortfolioSourceId("portfolioSourceId");
        querySummaryRequest1.setChainId(0);
        when(mockPortfolioService.querySummaryForApp(any()))
            .thenReturn(portfolioQuerySummaryResponseMono);

        // Configure IPortfolioService.queryStatistics(...).
        final Mono<PortfolioStatisticsDTO> portfolioStatisticsDTOMono = Mono.just(PortfolioStatisticsDTO.builder()
            .cryptoUnitPrice(new BigDecimal("0.00"))
            .fiatUnitPrice(new BigDecimal("0.00"))
            .yesterdayBalancePercent(new BigDecimal("0.00"))
            .yesterdayChangeBalance(new BigDecimal("0.00"))
            .currentTotalHoldings(new BigDecimal("0.00"))
            .lastUpdated("lastUpdated")
            .build());
        final PortfolioCryptoDTO portfolioUserIdDTO = new PortfolioCryptoDTO();
        portfolioUserIdDTO.setCurrentPage(0);
        portfolioUserIdDTO.setPageSize(0);
        portfolioUserIdDTO.setCryptoUnit(0);
        portfolioUserIdDTO.setFiatUnit(0);
        portfolioUserIdDTO.setPortfolioSourceId("portfolioSourceId");
        portfolioUserIdDTO.setChainId(0);
        when(mockPortfolioService.queryStatistics(portfolioUserIdDTO)).thenReturn(portfolioStatisticsDTOMono);

        // Configure PortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(...).
        final Mono<Map<Integer, PortfolioCryptoInfoDTO>> mapMono =
            Mono.just(Map.ofEntries(Map.entry(0, PortfolioCryptoInfoDTO.builder().cryptoId(0).build())));
        when(mockPortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(anyList())).thenReturn(mapMono);

        // Run the test
        final Mono<PortfolioSummaryStatisticsResponse> result =
            portfolioAggregationServiceImplUnderTest.querySummaryStatistics(querySummaryRequest);

        // Verify the results
    }



    @Test
    void testQueryForLineHistoricalChart() {
        // Setup
        final PortfolioLineChartQueryDTO portfolioLineChartQueryDTO = new PortfolioLineChartQueryDTO();
        portfolioLineChartQueryDTO.setCryptoIds(List.of(0));
        portfolioLineChartQueryDTO.setDays(0);
        portfolioLineChartQueryDTO.setPortfolioSourceId("portfolioSourceId");
        portfolioLineChartQueryDTO.setChainId(0);
        portfolioLineChartQueryDTO.setModuleType("moduleType");

        // Configure IPortfolioDashboardService.queryLineChart(...).
        final Mono<List<QueryLineChartResponseDTO>> listMono = Mono.just(List.of(
            new QueryLineChartResponseDTO(0, new BigDecimal("0.00"), List.of(
                new PortfolioTotalDTO(new BigDecimal("0.00"),
                    new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())))));
        final QueryLineChartRequestDTO requestDTO = new QueryLineChartRequestDTO();
        requestDTO.setCryptoIds(List.of(0));
        requestDTO.setDays(0);
        when(mockDashboardService.queryLineChart(any())).thenReturn(listMono);

        // Run the test
        final Mono<PortfolioLineHistoricalChartResponseDTO> result =
            portfolioAggregationServiceImplUnderTest.queryForLineHistoricalChart(portfolioLineChartQueryDTO);

        // Verify the results
    }

}
