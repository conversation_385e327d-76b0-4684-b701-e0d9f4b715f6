package com.cmc.asset.business.portfolio.strategy;

import com.cmc.asset.model.contract.portfolio.PortfolioTotalDTO;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;

public class AllChartDataStrategyV2Test {

    private AllChartDataStrategyV2 allChartDataStrategyV2UnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        allChartDataStrategyV2UnderTest = new AllChartDataStrategyV2();
    }

    @Test
    public void testGetChartData() {
        // Setup
        final NavigableMap<Date, NavigableMap<Date, BigDecimal>> mapByDay = new TreeMap<>(Map.ofEntries(
            Map.entry(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new TreeMap<>(
                Map.ofEntries(
                    Map.entry(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("0.00")))))));
        final List<PortfolioTotalDTO> expectedResult = List.of(
            new PortfolioTotalDTO(new BigDecimal("0.00"), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));

        // Run the test
        final List<PortfolioTotalDTO> result = allChartDataStrategyV2UnderTest.getChartData(30000, mapByDay);

        // Verify the results
        assertEquals(expectedResult, result);
    }

}
