package com.cmc.asset.job.handler;

import com.cmc.asset.dao.entity.portfolio.AccessToken;
import com.cmc.asset.dao.entity.portfolio.PortfolioThirdPartyEntity;
import com.cmc.asset.job.data.BaseJobData;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.service.portfolio.oauth.impl.OAuth2ClientFactory;
import com.cmc.asset.model.enums.OAuth2ProviderType;
import java.time.Duration;
import java.util.Date;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonDateTime;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PortfolioRefreshThirdPartyTokenHandler implements JobHandler<BaseJobData, Boolean> {

    @Resource
    protected ReactiveMongoTemplate reactiveMongoTemplate;
    @Resource
    private OAuth2ClientFactory oAuth2ClientFactory;

    @Value("${com.asset.portfolio.thirdparty.token.refresh-batch-limit:1000}")
    private Integer limit;
    @Value("${com.asset.portfolio.thirdparty.token.refresh-batch-time:3}")
    private Integer minutesLimit;
    @Value("${com.asset.portfolio.thirdparty.token.expiration:300}")
    private Long secondsBeforeExpiration;

    @Override
    public JobHandlingResult<Boolean> handle(BaseJobData jobData) {
        return this.findExpiredToken(limit)
            .flatMap(entity -> this.refreshToken(entity)
                .flatMap(accessToken -> {
                    if(StringUtils.isNotBlank(accessToken.getAccessToken())) {
                        return this.updateToken(entity.getId(), accessToken);
                    } else {
                        return this.updateToken(entity.getId(), null);
                    }
                })
                .map(res -> new JobHandlingResult<>(res, res)))
            .last(new JobHandlingResult<>(true, true))
            .block(Duration.ofMinutes(minutesLimit));
    }

    private Flux<PortfolioThirdPartyEntity> findExpiredToken(int limit) {
        Query query = Query.query(
                Criteria.where("token.expiredTime").lte(new BsonDateTime(System.currentTimeMillis())).and("valid").is(true))
            .limit(limit);
        return reactiveMongoTemplate.find(query, PortfolioThirdPartyEntity.class);
    }

    private Mono<Boolean> updateToken(ObjectId id, AccessToken newToken) {
        Query query = Query.query(Criteria.where("_id").is(id)).limit(1);
        Update update = new Update();
        if (newToken != null) {
            update.set("token", newToken);
            update.set("valid", true);
        } else {
            update.set("valid", false);
        }
        update.set("updatedTime", new Date());

        return reactiveMongoTemplate.updateFirst(query, update, PortfolioThirdPartyEntity.class)
            .map(result -> result.getModifiedCount() > 0);
    }

    private Mono<AccessToken> refreshToken(PortfolioThirdPartyEntity entity) {
        OAuth2ProviderType source = OAuth2ProviderType.findByCode(entity.getSource());
        return oAuth2ClientFactory.getClient(source)
            .refreshAccessToken(entity.getToken())
            .map(getAccessTokenResponseDTO -> {
                Date expiredTime = new Date(System.currentTimeMillis()
                    + (getAccessTokenResponseDTO.getExpiresIn() - secondsBeforeExpiration) * 1000L);
                return AccessToken.builder()
                    .accessToken(getAccessTokenResponseDTO.getAccessToken())
                    .refreshToken(getAccessTokenResponseDTO.getRefreshToken())
                    .expiresIn(getAccessTokenResponseDTO.getExpiresIn())
                    .scope(getAccessTokenResponseDTO.getScope())
                    .tokenType(getAccessTokenResponseDTO.getTokenType())
                    .expiredTime(expiredTime)
                    .build();
            })
            .defaultIfEmpty(AccessToken.builder().build())
            .onErrorResume(e -> {
                log.error("PortfolioRefreshThirdPartyTokenHandler refresh token error, id: {}", entity.getId(), e);
                return Mono.just(AccessToken.builder().build());
            });

    }

}
