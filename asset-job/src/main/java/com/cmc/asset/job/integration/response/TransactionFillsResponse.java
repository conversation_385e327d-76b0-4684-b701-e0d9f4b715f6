package com.cmc.asset.job.integration.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TransactionFillsResponse
 * <AUTHOR> ricky.x
 * @date : 2023/2/2 下午5:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionFillsResponse {
    private String code;
    private String msg;
    private List<TransactionFillsDTO> data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionFillsDTO{
        private String txid;
        private String amount;
        private String transactionSymbol;
        private String txfee;
        private List<TokenTransferDetail> tokenTransferDetails;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
     public static class TokenTransferDetail{
        private String from;
        private String to;
        private String amount;
        private String tokenContractAddress;
        private String token;
        private String symbol;
    }
}
