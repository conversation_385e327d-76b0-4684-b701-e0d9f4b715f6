package com.cmc.asset.job.cache;

import com.cmc.asset.job.service.dex.DexerService;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.RandomUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2024/3/12 12:44
 */
@Component
public class DexTokenCache extends AbstractCache<String, TokenPriceDTO> {

    @Autowired
    private DexerService dexerService;

    protected DexTokenCache(@Value("${com.cmc.asset.cache.dex-token.max-cache-size:10000}") Integer maxCacheSize,
        @Value("${com.cmc.asset.cache.dex-token.min-refresh-seconds:60}") Integer minRefreshSeconds) {
        super(maxCacheSize, RandomUtils.nextInt(minRefreshSeconds, minRefreshSeconds * 2), TimeUnit.SECONDS);
    }

    @Override
    public Mono<TokenPriceDTO> load(@NonNull String key) {
        String[] strings = parseKey(key);
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token =
            BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder().platform(strings[0]).address(strings[1]).build();
        return dexerService.getPairsFromDeQuery(List.of(token))
            .flatMap(dtos -> Mono.justOrEmpty(dtos.stream().findAny()));
    }

    @Override
    public Mono<Map<String, TokenPriceDTO>> loadAll(@NonNull Iterable<? extends @NonNull String> keys) {
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = new ArrayList<>();
        for (String key : keys) {
            String[] parts = parseKey(key);
            if (parts.length != 2) {
                continue;
            }
            tokens.add(
                BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder().platform(parts[0]).address(parts[1]).build());
        }
        return dexerService.getPairsFromDeQuery(tokens).map(dtos -> {
            Map<String, TokenPriceDTO> result = new HashMap<>();
            for (TokenPriceDTO dto : dtos) {
                if (dto != null) {
                    result.put(buildKey(dto.getPlatformDexerName(), dto.getAddress()), dto);
                }
            }
            return result;
        });
    }

    public Mono<Map<String, TokenPriceDTO>> getAll(@NonNull String platform, Set<String> addresses) {
        if (StringUtils.isEmpty(platform) || CollectionUtils.isEmpty(addresses)) {
            return Mono.just(Map.of());
        }
        Set<String> cacheKeys =
            addresses.stream().map(address -> buildKey(platform, address)).collect(Collectors.toSet());
        return this.getAll(cacheKeys);
    }

    public Mono<TokenPriceDTO> get(@NonNull String platform, String address) {
        if (StringUtils.isEmpty(platform) || StringUtils.isEmpty(address)) {
            return Mono.empty();
        }
        String cacheKey = buildKey(platform, address);
        return this.get(cacheKey);
    }

    public Mono<Map<String, TokenPriceDTO>> getAll(@NonNull Set<String[]> platformAndAddress) {
        if (CollectionUtils.isEmpty(platformAndAddress)) {
            return Mono.just(Map.of());
        }
        Set<String> cacheKeys =
            platformAndAddress.stream().map(strings -> buildKey(strings[0], strings[1])).collect(Collectors.toSet());
        return this.getAll(cacheKeys);
    }

    @NotNull
    private static String[] parseKey(String key) {
        return key.split("_");
    }

    @NotNull
    public static String buildKey(String platform, String address) {
        return platform + "_" + address;
    }

}
