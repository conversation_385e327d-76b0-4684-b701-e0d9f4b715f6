package com.cmc.asset.job.handler;

import com.cmc.asset.dao.entity.NewCoinSubscribeEntity;
import com.cmc.asset.domain.newlisting.NewCoinListingDTO;
import com.cmc.asset.job.dto.AbstractMessage;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.dto.MessageRecord;
import com.cmc.asset.job.dto.PushMessageDTO;
import com.cmc.asset.job.enums.ChannelEnum;
import com.cmc.asset.job.enums.PushTypeEnum;
import com.cmc.asset.job.integration.DataApiServiceClient;
import com.cmc.asset.job.repository.mongo.asset.NewCoinSubscribeRepository;
import com.cmc.asset.job.service.kafka.KafkaProducerSenderService;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.UnicastProcessor;

import java.util.*;

import static com.cmc.asset.job.constants.Constants.*;

/**
 * new coin listing push handler
 *
 * <AUTHOR>
 * @date 2024/4/18 21:01
 */
@Component
@Slf4j
public class NewCoinListingPushHandler implements JobHandler<String, Boolean> {

    @Value("${com.cmc.asset.cdp-app-link-prefix:cmc://coinDetail?slug=}")
    private String cdpAppLinkPrefix;

    @Value("${com.cmc.asset.new-coin-listing.start-push-time-gap:5000}")
    private long startPushTimeGap;

    @Value("${com.cmc.asset.new-coin-listing.end-push-time-gap:60000}")
    private long endPushTimeGap;

    @Value("${com.cmc.asset.new-coin-listing.page-size:1000}")
    private int pageSize;

    @Autowired
    private NewCoinSubscribeRepository newCoinSubscribeRepository;

    @Autowired
    private DataApiServiceClient dataApiServiceClient;

    @Autowired
    private KafkaProducerSenderService kafkaProducerSenderService;

    @Override
    public JobHandlingResult<Boolean> handle(String jobData) {
        long start = System.currentTimeMillis() - startPushTimeGap;
        long end = System.currentTimeMillis() + endPushTimeGap;

        log.info("Start to push new coin listing from {} to {}", start, end);
        UnicastProcessor<String> newCoinSubscribeProcessor = UnicastProcessor.create();
        Mono<Void> mono = dataApiServiceClient.getNewCoinListings(start, end)
            .flatMapMany(Flux::fromIterable)
            .filter(newCoinListing -> !Boolean.TRUE.equals(newCoinListing.getSubscribePushStatus()) &&
                newCoinListing.getExchangeName() != null && newCoinListing.getCoinSymbol() != null &&
                newCoinListing.getCoinSlug() != null && newCoinListing.getCoinId() != null &&
                !Boolean.FALSE.equals(newCoinListing.getIsAccurateTime()))
            // update push status to true
            .flatMap(newCoinListing -> dataApiServiceClient.updateNewCoinListingPushStatus(newCoinListing.getId())
                .onErrorResume(throwable -> {
                    log.error("Failed to update new coin listing push status: {}", newCoinListing.getId(), throwable);
                    return Mono.empty();
                })
                .thenReturn(newCoinListing))
            .flatMap(newCoinListing -> {
                log.info("Start to push new coin listing: {}", newCoinListing.getId());
                return newCoinSubscribeProcessor
                    .flatMap(id -> newCoinSubscribeRepository.findByDataId(newCoinListing.getId(), id, pageSize)
                        .onErrorResume(throwable -> {
                            log.error("Failed to find new coin subscribe by data id: {}", newCoinListing.getId(), throwable);
                            return Flux.empty();
                        })
                        .collectList()
                        .doOnNext(list -> {
                            sendPush(list, newCoinListing);
                            String maxId;
                            NewCoinSubscribeEntity nextEntity = null;
                            boolean hasNext = CollectionUtils.isNotEmpty(list) && (nextEntity = list.get(list.size() - 1)).getId() != null;
                            if (hasNext) {
                                maxId = nextEntity.getId().toHexString();
                                log.debug("[NEW_COIN_SUBSCRIBE_PUSH_PROCESSOR] maxId: {}, size: {}", maxId, list.size());
                                newCoinSubscribeProcessor.sink().next(maxId);
                            } else {
                                newCoinSubscribeProcessor.sink().complete();
                            }
                        }))
                    .doFinally(signalType -> updatePushTimestamp(newCoinListing));
            })
            .then();
        newCoinSubscribeProcessor.sink().next("");
        mono.block();
        return new JobHandlingResult<>(true);
    }

    private void sendPush(List<NewCoinSubscribeEntity> list, NewCoinListingDTO newCoinListingDTO) {
        Flux<String> messageFlux = Flux.fromIterable(list)
            .filter(entity -> entity.getUid() != null)
            .map(NewCoinSubscribeEntity::getUid)
            .buffer(100)
            .map(uidList -> {
                List<String> loginUidList = new ArrayList<>();
                List<String> nonLoginUidList = new ArrayList<>();

                uidList.forEach(uid -> {
                    if (uid > 0) {
                        loginUidList.add(uid + "");
                    } else if (uid < 0) {
                        nonLoginUidList.add(uid + "");
                    }
                });

                PushMessageDTO loginPushMessageDTO = buildPushMessageDTO(newCoinListingDTO, loginUidList, true);
                PushMessageDTO nonLoginPushMessageDTO = buildPushMessageDTO(newCoinListingDTO, nonLoginUidList, false);
                List<AbstractMessage> pushMessageDTOList = new ArrayList<>(2);
                addIfNotEmpty(pushMessageDTOList, loginPushMessageDTO);
                addIfNotEmpty(pushMessageDTOList, nonLoginPushMessageDTO);

                MessageRecord<AbstractMessage> messageRecord = MessageRecord.builder()
                    .channel(ChannelEnum.JPUSH)
                    .bodies(pushMessageDTOList).build();
                return JacksonUtils.toJson(messageRecord);
            });

        kafkaProducerSenderService.bulkSend(WATCHLIST_KAFKA_TOPIC, messageFlux).subscribe();
    }

    // update push timestamp, with ttl index
    private void updatePushTimestamp(NewCoinListingDTO newCoinListingDTO) {
        log.info("Mark delete subscriptions for new coin listing: {}", newCoinListingDTO.getId());
        newCoinSubscribeRepository.updatePushTimeByDataId(newCoinListingDTO.getId(), new Date()).subscribe();
    }

    private PushMessageDTO buildPushMessageDTO(NewCoinListingDTO newCoinListingDTO, List<String> uidList, boolean isLoginUser) {

        if (CollectionUtils.isEmpty(uidList)) {
            return null;
        }

        Set<String> andTags = new HashSet<>();
        andTags.add("tp_" + NEW_COIN_ALERT_TOPIC_ID);

        if (isLoginUser) {
            andTags.add("s_1");
        } else {
            andTags.add("s_0");
        }

        PushMessageDTO pushMessageDTO = PushMessageDTO.builder()
            .alias(new HashSet<>(uidList))
            .title(newCoinListingDTO.getCoinSymbol() + " is now listed on " + newCoinListingDTO.getExchangeName())
            .content("Follow the price action!")
            .lang(LANGUAGE_EN)
            .pushType(PushTypeEnum.PUSH)
            .andTags(andTags)
            .appLink(cdpAppLinkPrefix + newCoinListingDTO.getCoinSlug())
            .options(Map.of("traceId", newCoinListingDTO.getId()))
            .cryptoId(newCoinListingDTO.getCoinId())
            .build();
        pushMessageDTO.setTopicId(NEW_COIN_ALERT_TOPIC_ID);

        return pushMessageDTO;
    }

    private void addIfNotEmpty(List<AbstractMessage> list, PushMessageDTO pushMessageDTO) {
        if (pushMessageDTO != null) {
            list.add(pushMessageDTO);
        }
    }
}
