package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.userpoint.UserPointEntity;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description user point repository
 * @date 2021/6/16 下午4:05
 */
public interface UserPointRepository extends ReactiveMongoRepository<UserPointEntity, ObjectId>,
    ReactiveCrudRepository<UserPointEntity, ObjectId>, UserPointRepositoryCustom {

    Flux<UserPointEntity> findByUserId(String userId, Pageable pageable);

    Mono<UserPointEntity> findByResourceId(String resourceId);
}
