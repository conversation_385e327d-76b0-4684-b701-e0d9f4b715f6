package com.cmc.asset.job.dto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchFetchStrategyConfig {
    private List<Integer> batchFetchChainIds;

    private Map<String, List<Integer>> assetBatchFetchStrategies;

    private Map<String, List<Integer>> transactionBatchFetchStrategies;

    public Map<Integer, String> getAssetStrategyMap() {
        Map<Integer, String> strategyMap = new HashMap<>();
        assetBatchFetchStrategies.forEach((strategyName, chainIds) -> {
            chainIds.forEach(chainId -> {
                strategyMap.put(chainId, strategyName);
            });
        });
        return strategyMap;
    }
}
