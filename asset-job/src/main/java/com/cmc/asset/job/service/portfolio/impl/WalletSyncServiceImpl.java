package com.cmc.asset.job.service.portfolio.impl;

import static com.cmc.asset.model.common.MetricConstant.ASSET_WALLET_SYNC_METER;
import static com.cmc.asset.model.common.MetricConstant.ASSET_WALLET_SYNC_TAG;

import com.cmc.asset.dao.entity.PortfolioWalletEntity;
import com.cmc.asset.dao.entity.WalletAssetEntity;
import com.cmc.asset.dao.entity.WalletTransactionEntity;
import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionTokenPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.dto.BatchFetchStrategyConfig;
import com.cmc.asset.job.dto.PortfolioOkLinkMapVO;
import com.cmc.asset.job.integration.WebSocketClient;
import com.cmc.asset.job.integration.response.AddressTransactionResponse;
import com.cmc.asset.job.service.portfolio.PortfolioWalletService;
import com.cmc.asset.job.service.portfolio.WalletAssetService;
import com.cmc.asset.job.service.portfolio.WalletSyncService;
import com.cmc.asset.job.service.portfolio.WalletTransactionService;
import com.cmc.asset.job.service.portfolio.convert.PortfolioWalletConvert;
import com.cmc.asset.job.service.portfolio.manager.BlockBookBalanceManager;
import com.cmc.asset.job.service.portfolio.manager.OkLinkManager;
import com.cmc.asset.job.service.portfolio.manager.OkxManager;
import com.cmc.asset.job.service.router.BatchFetchManager;
import com.cmc.asset.job.service.router.strategy.BlockBookFetchStrategy;
import com.cmc.asset.job.service.router.strategy.StrategyFactory;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.asset.model.common.Constant;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.asset.model.contract.kafka.PortfolioDataPushMessage;
import com.cmc.asset.model.contract.kafka.WebsocketCustomEventDTO;
import com.cmc.asset.model.contract.task.WalletSyncJobParamDTO;
import com.cmc.asset.model.enums.PortfolioDataEnum;
import com.cmc.asset.model.enums.PortfolioMessageOperateEnum;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import com.cmc.asset.model.enums.PortfolioWalletDataFlagEnum;
import com.cmc.asset.model.enums.PortfolioWalletFetchStatusEnum;
import com.cmc.asset.model.enums.WebsocketBusinessTopicEnum;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.util.ShardingUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * @Description
 * <AUTHOR> Z
 * @CreateTime 2024-07-23
 */
@Service
@Slf4j
public class WalletSyncServiceImpl implements WalletSyncService {

    @Value("${com.cmc.asset-service.portfolio-wallet.sync-transaction-times:3}")
    private Integer transactionTimes;
    @Value("${com.cmc.asset.wallet.page.size:10}")
    private Integer walletPageSize;
    @Value("${com.cmc.asset.wallet.sync.concurrency:1}")
    private Integer concurrency;
    @Value("${com.cmc.asset.wallet.sync.cost-log.threshold:30000}")
    private int costLogThreshold;
    @Value("${com.cmc.asset.wallet.sync.failed.delete-locke:false}")
    private Boolean deleteLocked;

    @Value("${com.cmc.asset.wallet.sync.use-okx-transaction-api:true}")
    private Boolean useOkxTransactionApi;

    @Value("${com.cmc.asset.wallet.sync.max-wallet-sync-concurrency}")
    private Integer maxWalletSyncConcurrency;

    @Autowired
    private StrategyFactory strategyFactory;
    @Autowired
    private WalletAssetService walletAssetService;
    @Autowired
    private WalletTransactionService walletTransactionService;
    @Autowired
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;
    @Autowired
    private OkLinkManager okLinkManager;
    @Autowired
    private BlockBookBalanceManager blockBookBalanceManager;
    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;
    @Autowired
    @Qualifier("assetAsyncRedisTemplate")
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;
    @Autowired
    private PortfolioWalletService portfolioWalletService;
    @Autowired
    private BlockBookFetchStrategy blockBookFetchStrategy;
    @Autowired
    private CmcMeterRegistry cmcMeterRegistry;
    @Autowired
    private WebSocketClient webSocketClient;

    @Resource
    private OkxManager okxManager;

    @Resource
    private BatchFetchManager batchFetchManager;

    @Value("${com.cmc.asset-service.portfolio-manual.enable-ws-push-wallet:true}")
    private Boolean enableWsPushWallet;

    @Override
    public Mono<Boolean> batchSyncWallet(String jobData, ShardingUtil.ShardingVO shardingVO) {
        int shardingVoIndex = 0;
        int shardingVoTotal = 1;
        if (shardingVO != null) {
            shardingVoIndex = shardingVO.getIndex();
            shardingVoTotal = shardingVO.getTotal();
        }

        WalletSyncJobParamDTO paramDTO = WalletSyncJobParamDTO.builder().build();
        if(StringUtils.isNotBlank(jobData)){
            paramDTO = JacksonUtils.deserialize(jobData, new TypeReference<>() {});
        }
        Long assetFetchTime = null;
        if(paramDTO.getSyncInterval() != null && paramDTO.getSyncInterval() > 0){
            assetFetchTime = DatetimeUtils.getMinutesAgo(new Date(), paramDTO.getSyncInterval()).getTime();
        }
        int finalShardingVoTotal = shardingVoTotal;
        int finalShardingVoIndex = shardingVoIndex;
        Long finalAssetFetchTime = assetFetchTime;
        WalletSyncJobParamDTO finalParamDTO = paramDTO;
        return portfolioWalletService.calcTotalPage(assetFetchTime, walletPageSize)
            .filter(totalPage -> totalPage > 0)
            .flatMapMany(totalPage -> Flux.range(1, totalPage))
            .filter(page -> page % finalShardingVoTotal == finalShardingVoIndex)
            .flatMap(page -> portfolioWalletService.queryPage(finalAssetFetchTime, page, walletPageSize)
                .filter(multiEntity -> filterWallet(multiEntity, finalParamDTO))
                .map(PortfolioWalletEntity::getWalletAddress)
                .collectList()
                .flatMap(walletAddressList -> syncWalletAssetAndTransaction(walletAddressList)), concurrency)
            .collectList()
            .map(list -> list.stream().anyMatch(Boolean::booleanValue));
    }

    @Override
    public Mono<Boolean> syncWalletAssetAndTransaction(List<String> walletAddressList) {
        if(CollectionUtils.isEmpty(walletAddressList)){
            return Mono.just(false);
        }
        log.debug("Portfolio sync wallet walletAddress {}", JacksonUtils.toJson(walletAddressList));
        return Flux.fromIterable(walletAddressList)
            .concatMap(walletAddress -> syncWalletAsset(walletAddress)
                .flatMap(r -> syncWalletTransaction(walletAddress)))
            .collectList()
            .thenReturn(true);
    }

    @Override
    public Mono<Boolean> syncWalletAsset(String walletAddress) {
        // todo 加锁
        List<Integer> supportChains = portfolioWalletService.getSupportChainIds(walletAddress);
        if(CollectionUtils.isEmpty(supportChains)){
            return Mono.just(false);
        }
        Tuple2<List<Integer>, List<Integer>> partition = chainIdPartition(supportChains, walletAddress);
        List<Integer> fetchChainIdsList = partition.getT1();
        List<Integer> batchFetchChainIds = partition.getT2();

        Mono<List<BlockChainPO>> fetchMono = Flux.fromIterable(fetchChainIdsList).flatMap(chainId -> {
            if (Constant.CHAIN_ID_BTC.equals(chainId) && walletAddress.toLowerCase()
                .startsWith(Constant.WALLET_ADDRESS_XPUB)) {
                return blockBookFetchStrategy.fetch(chainId, walletAddress, null);
            }
            return strategyFactory.createStrategy(chainId).fetch(chainId, walletAddress, null);
        }, maxWalletSyncConcurrency).collectList();
        Mono<List<BlockChainPO>> batchFetchMono = batchFetchManager.batchFetchAsset(batchFetchChainIds, walletAddress, null)
            .defaultIfEmpty(new ArrayList<>());

        return Mono.zip(fetchMono, batchFetchMono).flatMap(tuple -> {
                List<BlockChainPO> fetchRes = tuple.getT1();
                List<BlockChainPO> batchFetchRes = tuple.getT2();
                fetchRes.addAll(batchFetchRes);
                return Mono.just(fetchRes);
            }).flatMap(blockChainPOS -> {
                WalletAssetEntity assetEntity = WalletAssetEntity.builder()
                    .walletAddress(walletAddress)
                    .blockChains(blockChainPOS)
                    .dataTime(System.currentTimeMillis())
                    .fetchStatus(PortfolioWalletFetchStatusEnum.FETCH.getStatus())
                    .build();
                return walletAssetService.batchInsertOrUpdate(List.of(assetEntity));
            })
            .flatMap(r -> updatePortfolioWallet(walletAddress))
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(r -> {
                log.debug("syncWalletAsset success walletAddress {}", walletAddress);
                PortfolioDataPushMessage message = PortfolioDataPushMessage.builder()
                    .pt(PortfolioTypeEnum.WALLET.getCode())
                    .dt(PortfolioDataEnum.ASSET.getCode())
                    .status(PortfolioWalletDataFlagEnum.SYNC_SUCCESS.getStatus())
                    .updated(System.currentTimeMillis())
                    .build();
                sendWsPushMessage(walletAddress, message).subscribe();
            })
            .elapsed()
            .map(t -> {
                recordTime(t.getT1(), "walletAsset");
                return t.getT2();
            })
            .onErrorResume(e -> {
                log.error("refreshWalletAsset error walletAddress {}", walletAddress, e);
                WalletAssetEntity assetEntity = WalletAssetEntity.builder()
                    .walletAddress(walletAddress)
                    .fetchStatus(PortfolioWalletFetchStatusEnum.FETCH_ERROR.getStatus())
                    .build();
                if(deleteLocked){
                    String redisKey = String.format(RedisConstants.KEY_PORTFOLIO_WALLET_MESSAGE_LOCK, walletAddress, PortfolioMessageOperateEnum.WALLET.getCode());
                    return reactiveRedisTemplate.hasKey(redisKey)
                        .flatMap(exists -> {
                            if(exists){
                                return reactiveRedisTemplate.delete(redisKey);
                            }
                            return Mono.just(0L);
                        })
                        .flatMap(r -> walletAssetService.batchInsertOrUpdate(List.of(assetEntity)));
                }else {
                    return walletAssetService.batchInsertOrUpdate(List.of(assetEntity));
                }
            })
            .defaultIfEmpty(true);
    }

    private Tuple2<List<Integer>, List<Integer>> chainIdPartition(List<Integer> chainIds, String walletAddress) {
        BatchFetchStrategyConfig batchFetchStrategyConfig = dynamicApolloRefreshConfig.getBatchFetchStrategyConfig();
        Set<Integer> batchFetchChainIds = batchFetchStrategyConfig.getBatchFetchChainIds()
            .stream()
            .collect(Collectors.toSet());
        List<Integer> fetchList = new ArrayList<>();
        List<Integer> batchFetchList = new ArrayList<>();
        chainIds.forEach(chainId -> {
            if (Constant.CHAIN_ID_BTC.equals(chainId) && walletAddress.toLowerCase()
                .startsWith(Constant.WALLET_ADDRESS_XPUB)) {
                fetchList.add(chainId);
                return;
            }

            if (batchFetchChainIds.contains(chainId)) {
                batchFetchList.add(chainId);
            } else {
                fetchList.add(chainId);
            }
        });
        return Tuples.of(fetchList, batchFetchList);
    }

    private Mono<Boolean> sendWsPushMessage(String walletAddress, PortfolioDataPushMessage message) {
        if (BooleanUtils.isNotTrue(enableWsPushWallet)) {
            return Mono.just(true);
        }
        String tags = String.join(",", PortfolioTypeEnum.WALLET.getCode(), walletAddress);
        WebsocketCustomEventDTO<PortfolioDataPushMessage> websocketCustomEvent = WebsocketCustomEventDTO.<PortfolioDataPushMessage>builder()
                .businessTopic(WebsocketBusinessTopicEnum.PORTFOLIO)
                .tags(tags)
                .metaData(() -> message)
                .build();
        return webSocketClient.send(websocketCustomEvent);
    }

    @Override
    public Mono<Boolean> syncWalletTransaction(String walletAddress) {
        List<Integer> supportChains = portfolioWalletService.getSupportChainIds(walletAddress);
        if(CollectionUtils.isEmpty(supportChains)){
            return Mono.just(false);
        }

        return Flux.fromIterable(supportChains)
            .limitRate(concurrency)
            .flatMap(chainId -> getMonoChainTransaction(chainId, walletAddress)
                .onErrorResume(e -> {
                    log.error("getMonoChainTransaction error, chainId {}, walletAddress {}", chainId, walletAddress, e);
                    return Mono.empty();
                }))
            .collectList()
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(transactions -> {
                WalletTransactionEntity transactionEntity = WalletTransactionEntity.builder()
                    .walletAddress(walletAddress)
                    .chainTransactions(transactions)
                    .dataTime(System.currentTimeMillis())
                    .fetchStatus(PortfolioWalletFetchStatusEnum.FETCH.getStatus())
                    .build();
                return walletTransactionService.batchInsertOrUpdate(List.of(transactionEntity));
            })
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(r -> {
                log.debug("refreshWalletTransaction success walletAddress {}", walletAddress);
                PortfolioDataPushMessage message = PortfolioDataPushMessage.builder()
                        .pt(PortfolioTypeEnum.WALLET.getCode())
                        .dt(PortfolioDataEnum.TRANSACTION.getCode())
                        .status(PortfolioWalletDataFlagEnum.SYNC_SUCCESS.getStatus())
                        .updated(System.currentTimeMillis())
                        .build();
                sendWsPushMessage(walletAddress, message).subscribe();
            })
            .elapsed()
            .map(t -> {
                recordTime(t.getT1(), "walletTransaction");
                return t.getT2();
            })
            .onErrorResume(e -> {
                log.error("refreshWalletTransaction error walletAddress {}", walletAddress, e);
                WalletTransactionEntity transactionEntity = WalletTransactionEntity.builder()
                    .walletAddress(walletAddress)
                    .fetchStatus(PortfolioWalletFetchStatusEnum.FETCH_ERROR.getStatus())
                    .build();
                if(deleteLocked){
                    String redisKey = String.format(RedisConstants.KEY_PORTFOLIO_WALLET_MESSAGE_LOCK, walletAddress, PortfolioMessageOperateEnum.WALLET_TRANSACTION.getCode());
                    return reactiveRedisTemplate.hasKey(redisKey)
                        .flatMap(exists -> {
                            if(exists){
                                return reactiveRedisTemplate.delete(redisKey);
                            }
                            return Mono.just(0L);
                        })
                        .flatMap(r -> walletTransactionService.batchInsertOrUpdate(List.of(transactionEntity)));
                }else {
                    return walletTransactionService.batchInsertOrUpdate(List.of(transactionEntity));
                }
            }).defaultIfEmpty(true);
    }

    private Mono<Boolean> syncWalletTransactionByOkx(String walletAddress) {


        return Mono.just(true);
    }

    private void recordTime(long cost, String tag) {
        if(cost > costLogThreshold){
            log.info("{} wallet cost: {}", tag, cost);
        }
        Timer timer = cmcMeterRegistry.buildTime(ASSET_WALLET_SYNC_METER, ASSET_WALLET_SYNC_TAG, tag);
        timer.record(cost, TimeUnit.MILLISECONDS);
    }

    private Boolean filterWallet(PortfolioWalletEntity walletEntity, WalletSyncJobParamDTO paramDTO){
        if(StringUtils.isBlank(walletEntity.getWalletAddress())){
            return false;
        }
        if(CollectionUtils.isNotEmpty(paramDTO.getWalletAddressList()) &&
            !paramDTO.getWalletAddressList().contains(walletEntity.getWalletAddress())){
            return false;
        }

        return true;
    }

    private Mono<ChainTransactionPO> getMonoChainTransaction(Integer chainId, String address) {
//        if (Constant.CHAIN_ID_SOLANA.equals(chainId)) {
//            return blockBookBalanceManager.getChainTransaction(chainId, address);
//        }

        if(Constant.CHAIN_ID_BTC.equals(chainId) && address.toLowerCase().startsWith(Constant.WALLET_ADDRESS_XPUB)){
            return blockBookBalanceManager.getXpubResult(chainId, address)
                .flatMap(xpubResponse -> Flux.fromIterable(xpubResponse.getData().getResult())
                    .limitRate(maxWalletSyncConcurrency)
                    .filter(r -> StringUtils.isNotBlank(r.getName()))
                    .flatMap(r -> getChainTransaction(chainId, r.getName(), true))
                    .collectList()
                    .map(transactionPOs -> {
                        List<ChainTransactionTokenPO> tokens = transactionPOs.stream()
                            .flatMap(d -> d.getTokenTransactions().stream())
                            .collect(Collectors.toList());
                        ChainTransactionPO po = ChainTransactionPO.builder()
                            .chainId(chainId)
                            .tokenTransactions(tokens)
                            .build();

                        return po;
                    }))
                .switchIfEmpty(Mono.defer(() -> getChainTransaction(chainId, address, false)));
        }

        return getChainTransaction(chainId, address, false);
    }

    private Mono<ChainTransactionPO> getChainTransaction(Integer chainId, String address, boolean isXpubAddress) {
        if (isXpubAddress) {
            log.info("[WalletSyncServiceImpl.getChainTransaction]Xpub address -- convert address: {} -- chainId: {}", address, chainId);
        }
        String chainShortName = getChainShortName(chainId);
        if(chainShortName == null){
            return Mono.empty();
        }

        if (useOkx(chainId, isXpubAddress)) {
            log.info("[WalletSyncServiceImpl.getChainTransaction]Query transactions by okx api");
            return okxManager.getNearLimitTransActionDetails(chainId, address, 100);
        }
        return okLinkManager.getNearLimitTransActionDetails(chainShortName, address, 100, transactionTimes)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(limitTransActionDetails -> {
                return okLinkManager.fillAmountByTrans(chainShortName, limitTransActionDetails)
                    .flatMap(actionDetails -> buildChainsTransactionToken(chainId, actionDetails, address)
                        .map(tokens -> ChainTransactionPO.builder()
                            .chainId(chainId)
                            .tokenTransactions(tokens)
                            .build()));
            });
    }

    private boolean useOkx(Integer chainId, boolean isXpubAddress) {
        Map<Integer, OkLinkMapConfigItem> okLinkOkxMapConfig = dynamicApolloRefreshConfig.getOkLinkOkxMapConfig();
        return useOkxTransactionApi && okLinkOkxMapConfig.containsKey(chainId);
    }

    private Mono<List<ChainTransactionTokenPO>> buildChainsTransactionToken(Integer chainId, List<AddressTransactionResponse.AddressTransActionDetailDTO> okActionDetailList, String address) {
        return Flux.fromIterable(okActionDetailList)
            .flatMap(actionDetailDTO -> {
                String tokenContractAddress = actionDetailDTO.getTokenContractAddress();
                if(StringUtil.isBlank(tokenContractAddress)){
                    return Mono.just(
                        PortfolioWalletConvert.mongoActionToken(actionDetailDTO, address, actionDetailDTO.getCmcNativeTokenId()));
                } else {
                    Integer cryptoIdFormToken = cryptoCurrencyCache.getIdByContractAddress(chainId, tokenContractAddress);
                    return Mono.just(PortfolioWalletConvert.mongoActionToken(actionDetailDTO, address, cryptoIdFormToken));
                }
            }).collectList();
    }

    private String getChainShortName(Integer chainId) {
        PortfolioOkLinkMapVO chainConfigById = dynamicApolloRefreshConfig.getChainConfigById(chainId);
        if (chainConfigById == null) {
            return null;
        }
        return chainConfigById.getChainShortName();
    }

    private Mono<Boolean> updatePortfolioWallet(String walletAddress){
        PortfolioWalletEntity walletEntity = PortfolioWalletEntity.builder()
            .walletAddress(walletAddress)
            .assetFetchTime(System.currentTimeMillis())
            .build();
        return portfolioWalletService.batchInsertOrUpdate(List.of(walletEntity));
    }
}
