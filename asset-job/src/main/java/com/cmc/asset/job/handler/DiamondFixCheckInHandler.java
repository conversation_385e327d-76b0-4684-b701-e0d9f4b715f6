package com.cmc.asset.job.handler;

import com.cmc.asset.dao.entity.userpoint.UserCheckInLogEntity;
import com.cmc.asset.dao.entity.userpoint.UserLatestCheckInLogEntity;
import com.cmc.asset.job.data.MongoBatchRefreshJobData;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.repository.redis.AssetRedisHelper;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.framework.utils.JacksonUtils;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * fix diamond check in record
 * <AUTHOR> ricky.x
 * @date : 2023/4/19 下午2:16
 */
@Slf4j
@Component
public class DiamondFixCheckInHandler implements JobHandler<String, Boolean> {

    @Resource
    private MongoTemplate cmcAssetMongoTemplate;

    @Resource
    private AssetRedisHelper assetRedisHelper;

    @Value("${com.cmc.asset-job.diamond-checkin-fix.limit-num:200}")
    private Integer limitNum;


    @Override
    public JobHandlingResult<Boolean> handle(String params) {
        Thread.currentThread().setName(UUID.randomUUID().toString());
        MongoBatchRefreshJobData jobData = JacksonUtils.deserialize(params,MongoBatchRefreshJobData.class);
        log.info("DiamondFixCheckInHandler handler start,totalJobData:{}", JacksonUtils.serialize(jobData));
        //参数校验
        if(!checkParams(jobData)){
            return new JobHandlingResult<>(true,false);
        }
        //当前批次是否已经执行完成,执行完成则不用重复执行
        String redisKey=String.format(RedisConstants.MONGO_BATCH_REFRESH_LAST_ID,jobData.getJobName(),jobData.getBatch());
        String existCacheLastId = assetRedisHelper.get(redisKey);
        if(jobData.getEndId().equals(existCacheLastId)){
            log.info("DiamondFixCheckInHandler handler end,batch already execute:{}",jobData.getBatch());
            return new JobHandlingResult<>(true,false);
        }
        //待处理的总数据量totalCount
        String startId=StringUtils.isNotEmpty(existCacheLastId)?existCacheLastId:jobData.getStartId();
        long totalCount= getTotalCountById(startId,jobData.getEndId());
        log.info("DiamondFixCheckInHandler refresh begin,batch:{},startId:{},endId:{},total count:{}",jobData.getBatch(),startId,jobData.getEndId(),totalCount);
        if(totalCount<=0){
            log.error("DiamondFixCheckInHandler error totalCount is null");
            return new JobHandlingResult<>(true,false);
        }
        //开始刷新数据
        refreshTotalDate(totalCount,redisKey,jobData);
        log.info("DiamondFixCheckInHandler handler end,totalJobData:{}", JacksonUtils.serialize(jobData));
        return new JobHandlingResult<>(true,true);
    }


    private void refreshTotalDate(long totalCount,String redisKey,MongoBatchRefreshJobData totalJobData){
        long forTimes=getForTimes(totalCount);
        log.info("DiamondFixCheckInHandler refresh begin batch times:{}",forTimes);
        List<String> userIdList=new ArrayList<>(5000);
        for (int i = 0; i < forTimes; i++) {
            String cacheLastId = assetRedisHelper.get(redisKey);
            String lastId = StringUtils.isEmpty(cacheLastId)?totalJobData.getStartId():cacheLastId;
            //如果是最后一批则limit值为totalCount%limitNum
            int limit=(int)limitNum;
            log.info("DiamondFixCheckInHandler get userId,lastId:{},forTime:{},limit{}",lastId,i+1,limit);
            //根据lastId与limit获取待刷新的数据
            List<UserCheckInLogEntity> waitTradeList = getWaitTradeList(lastId.equals(totalJobData.getStartId()),lastId,totalJobData.getEndId(),limit);
            if(CollectionUtils.isEmpty(waitTradeList)){
                continue;
            }
            for (UserCheckInLogEntity checkInLog: waitTradeList) {
                try{
                    //查询对应的lastLog
                    Query query=Query.query(Criteria.where("userId").is(checkInLog.getUserId()));
                    List<UserLatestCheckInLogEntity> entityList =  cmcAssetMongoTemplate.find(query, UserLatestCheckInLogEntity.class);
                    if(CollectionUtils.isEmpty(entityList)){
                        continue;
                    }
                    UserLatestCheckInLogEntity lastCheckIn = entityList.get(0);
                    if(addUser(checkInLog.getCheckInDate().getTime(),lastCheckIn.getLastCheckDate().getTime(),lastCheckIn.getFlags())){
                        userIdList.add(checkInLog.getUserId());
                    }
                }catch (Exception e){
                    log.error("DiamondFixCheckInHandler get user error,id:{}",checkInLog.getId().toHexString(),e);
                }
            }

            if (userIdList.size() >= 5000) {
                log.info("DiamondFixCheckInHandler result:{}", JacksonUtils.serialize(userIdList));
                userIdList.clear();
            }
            assetRedisHelper.set(redisKey,waitTradeList.get(waitTradeList.size()-1).getId().toHexString());
        }
        if(CollectionUtils.isNotEmpty(userIdList)){
            log.info("DiamondFixCheckInHandler result:{}",userIdList);
            userIdList.clear();
        }
    }

    private static boolean addUser(Long checkInTime,Long lastCheckInTime, LinkedList<Integer> list){
        if(lastCheckInTime <= checkInTime){
            return false;
        }
        if(CollectionUtils.isEmpty(list)){
            return false;
        }
        int index = 6 - (int)((lastCheckInTime - checkInTime)/ (DatetimeUtils.SECONDS_OF_A_DAY * 1000));
        if(index < 0){
            return false;
        }
        return list.get(index) == 0;
    }

    /**
     * 获取分页处理的页数
     */
    private long getForTimes(long totalCount) {
        long forTimes = (totalCount / limitNum);
        if((totalCount % limitNum) != 0){
            forTimes = forTimes + 1;
        }
        return  forTimes;
    }


    /**
     * 检查参数
     */
    private boolean checkParams(MongoBatchRefreshJobData totalJobData) {
        if(StringUtils.isEmpty(totalJobData.getJobName())){
            return false;
        }
        //如果包含tradeIds的情况下则不用关注其他参数
        if(StringUtils.isNotEmpty(totalJobData.getTradeIds())){
            return true;
        }
        //如果不包含tradeIds则关注其他参数
        if(StringUtils.isEmpty(totalJobData.getStartId())){
            return false;
        }
        if(StringUtils.isEmpty(totalJobData.getEndId())){
            return false;
        }
        if(totalJobData.getBatch() == null){
            return false;
        }
        return true;
    }

    /**
     * 获取当前批次待处理的总数
     */
    private long getTotalCountById(String startId, String endId) {
        return cmcAssetMongoTemplate.count(Query.query(Criteria.where("_id").gte(new ObjectId(startId)).lte(new ObjectId(endId))),
            UserCheckInLogEntity.class);
    }

    /**
     * 根据lastId与lastId获取带待处理的数据
     * @param firstQuery 是否第一次请求
     * @param lastId lastId
     * @param limitNum limit
     * @return list
     */
    private List<UserCheckInLogEntity> getWaitTradeList(boolean firstQuery,String lastId,String endId,int limitNum) {
        if(firstQuery){
            Query query=Query.query(Criteria.where("_id").gte(new ObjectId(lastId)).lte(new ObjectId(endId))).limit(limitNum);
            return cmcAssetMongoTemplate.find(query, UserCheckInLogEntity.class);
        }
        Query query=Query.query(Criteria.where("_id").gt(new ObjectId(lastId)).lte(new ObjectId(endId))).limit(limitNum);
        return cmcAssetMongoTemplate.find(query, UserCheckInLogEntity.class);
    }

}
