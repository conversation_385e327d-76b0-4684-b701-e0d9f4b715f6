package com.cmc.asset.job.service.dex;

import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import java.util.List;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2024/7/15 09:37
 */
public interface DexerService {

    Mono<List<PlatformNewDTO>> getPlatformsFromDeQuery();

    /**
     * Batch get token price info from DeQuery by platform and address list
     */
    Mono<List<TokenPriceDTO>> getPairsFromDeQuery(List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens);

}
