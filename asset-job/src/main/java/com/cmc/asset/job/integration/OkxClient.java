package com.cmc.asset.job.integration;

import com.cmc.asset.job.integration.response.OkxAddressBalanceResponse;
import com.cmc.asset.job.integration.response.okx.OkxAddressBalanceDTO;
import com.cmc.asset.job.integration.response.okx.OkxTransactionDTO;
import com.cmc.asset.job.metric.CounterMetricName;
import com.cmc.asset.job.metric.TimerMetricName;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * Okx client
 * Okx api document: https://www.okx.com/zh-hans/web3/build/docs/waas/walletapi-api-all-token-balances-by-address
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Slf4j
@Component
public class OkxClient extends BaseServiceClient {
    private static final int HTTP_SUCCESS_CODE = 200;

    private static final String BIZ_SUCCESS_CODE = "0";

    private static final String BIZ_SUCCESS_MSG = "success";

    private static final String BODY_NULL_CODE = "999";

    private final String THROWABLE_CODE = "998";


    private static final String ALL_TOKEN_BALANCES_BY_ADDRESS_PATH = "/api/v5/wallet/asset/all-token-balances-by-address?address=%s&chains=%s&filter=%s";
    private static final String GET_TRANSACTION_BY_ADDRESS_PATH = "/api/v5/wallet/post-transaction/transactions-by-address?address=%s&chains=%s&limit=20";
    private static final String GET_TRANSACTION_BY_ADDRESS_WITH_CURSOR_PATH = "/api/v5/wallet/post-transaction/transactions-by-address?address=%s&chains=%s&cursor=%s&limit=20";

    @Value("${com.asset.job.portfolio.oauth2.providers.okx.baseUri}")
    private String okxBaseApi;

    @Value("${com.cmc.asset-job.integration-config.okx-access-project}")
    private String okxAccessProject;

    @Value("${com.cmc.asset-job.integration-config.okx-access-key}")
    private String okxAccessKey;

    @Value("${com.cmc.asset-job.integration-config.okx-access-passphrase}")
    private String okxAccessPassphrase;

    @Value("${com.cmc.asset-job.integration-config.okx-secret-key}")
    private String okxSecretKey;

    @Resource
    private CmcMeterRegistry cmcMeterRegistry;

    /**
     * Query all cryptocurrency balances by wallet address
     *
     * @param address wallet address
     * @param chainIds chain ids
     * @param filterRiskyAirdrop Filter risky airdrop tokens, filtered by default
     * @return
     */
    public Mono<OkxAddressBalanceDTO> getAllTokenBalancesByAddress(String address, List<Integer> chainIds, Boolean filterRiskyAirdrop) {
        String chains = Joiner.on(",").join(chainIds);
        String filter = Objects.isNull(filterRiskyAirdrop) || filterRiskyAirdrop ? "0" : "1";
        String requestPath = String.format(ALL_TOKEN_BALANCES_BY_ADDRESS_PATH, address, chains, filter);
        String url = okxBaseApi + requestPath;

        Instant now = Instant.now().truncatedTo(ChronoUnit.MILLIS);
        String timestamp = DateTimeFormatter.ISO_INSTANT.format(now);
        String sign = generateSign(timestamp, "GET", requestPath, null, okxSecretKey);

        return getByOkx(url, sign, timestamp, "get-allToken-balances-by-address").flatMap(data -> {
            if (CollectionUtils.isEmpty(data)) {
                return Mono.empty();
            }
            return Mono.just(data.get(0));
        }).onErrorResume(e -> {
            log.error("[OkxClient.getAllTokenBalancesByAddress]Get error,getAllTokenBalancesByAddress error,request:{}", url, e);
            return Mono.error(e);
        });
    }



    /**
     * Query all cryptocurrency balances by wallet address
     *
     * @param address wallet address
     * @param chainIds chain ids
     * @param cursor cursor
     * @return
     */
    public Mono<OkxTransactionDTO> getTransactionsByAddress(String address, List<Integer> chainIds, String cursor) {
        String chains = Joiner.on(",").join(chainIds);

        String requestPath = StringUtil.isEmpty(cursor) ? String.format(GET_TRANSACTION_BY_ADDRESS_PATH, address, chains)
            : String.format(GET_TRANSACTION_BY_ADDRESS_WITH_CURSOR_PATH, address, chains, cursor);
        String url = okxBaseApi + requestPath;

        Instant now = Instant.now().truncatedTo(ChronoUnit.MILLIS);
        String timestamp = DateTimeFormatter.ISO_INSTANT.format(now);
        String sign = generateSign(timestamp, "GET", requestPath, null, okxSecretKey);

        return getTransactionByOkx(url, sign, timestamp, "get-transactions-by-adddress").flatMap(data -> {
            if (CollectionUtils.isEmpty(data)) {
                return Mono.empty();
            }
            return Mono.just(data.get(0));
        }).onErrorResume(e -> {
            log.error("[OkxClient.getAllTokenBalancesByAddress]Get error,getAllTokenBalancesByAddress error,request:{}", url, e);
            return Mono.error(e);
        });
    }


    private Mono<List<OkxTransactionDTO>> getTransactionByOkx(String url, String sign, String isoTimestamp, String method) {
        Timer.Sample timer = Timer.start();
        String requestTime = DatetimeUtils.LocalDateFormatEnum.YYYY_MM_DD.getDateFormatter()
            .format(LocalDateTime.now());
        return client.get(url, generateHeaders(sign, isoTimestamp))
            .flatMap(e -> {
                if (e.getStatusCodeValue() != HTTP_SUCCESS_CODE || e.getBody() == null) {
                    cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OKX_EXCEPTION_SUM, "date", requestTime, "method", method, "statusCode",
                        StringUtil.isBlank(e.getBody()) ? BODY_NULL_CODE : e.getStatusCodeValue() + "").increment();
                    log.error("[OkxClient.getTransactionByOkx]Okx response error -- HTTP_CODE: {} -- Body: {}", e.getStatusCodeValue(), e.getBody());
                    return Mono.error(new BusinessException("ok client error,statusCode error:".concat(Integer.toString(e
                        .getStatusCodeValue()))));
                }
                OkxAddressBalanceResponse<OkxTransactionDTO> response = JacksonUtils.deserialize(e.getBody(), new TypeReference<>() {
                });
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OKX_SUCCESS_SUM, "date", requestTime, "method", method, "responseCode", response
                    .getCode()).increment();
                if (!response.getCode().equals(BIZ_SUCCESS_CODE) || !response.getMsg().equals(BIZ_SUCCESS_MSG)) {
                    return Mono.error(new BusinessException("Okx client error,response error:".concat(JacksonUtils.serialize(response))));
                }
                return Mono.just(response.getData());
            })
            .retryWhen(Retry.backoff(2, Duration.ofMillis(300L)).filter(throwable -> !(throwable instanceof BusinessException)))
            .onErrorResume(throwable -> {
                if (throwable instanceof BusinessException) {
                    return Mono.error(throwable);
                }
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OKX_EXCEPTION_SUM, "date", requestTime, "method", method, "statusCode", THROWABLE_CODE)
                    .increment();
                return Mono.error(throwable);
            })
            .doOnNext(r -> timer.stop(cmcMeterRegistry.timer(TimerMetricName.INTEGRATION_OKX_EXECUTE_SECONDS, "method", method)));
    }


    private Mono<List<OkxAddressBalanceDTO>> getByOkx(String url, String sign, String isoTimestamp, String method) {
        Timer.Sample timer = Timer.start();
        String requestTime = DatetimeUtils.LocalDateFormatEnum.YYYY_MM_DD.getDateFormatter()
            .format(LocalDateTime.now());
        return client.get(url, generateHeaders(sign, isoTimestamp))
            .flatMap(e -> {
                if (e.getStatusCodeValue() != HTTP_SUCCESS_CODE || e.getBody() == null) {
                    cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OKX_EXCEPTION_SUM, "date", requestTime, "method", method, "statusCode",
                        StringUtil.isBlank(e.getBody()) ? BODY_NULL_CODE : e.getStatusCodeValue() + "").increment();
                    return Mono.error(new BusinessException("ok client error,statusCode error:".concat(Integer.toString(e
                        .getStatusCodeValue()))));
                }
                OkxAddressBalanceResponse<OkxAddressBalanceDTO> response = JacksonUtils.deserialize(e.getBody(), new TypeReference<>() {
                });
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OKX_SUCCESS_SUM, "date", requestTime, "method", method, "responseCode", response
                    .getCode()).increment();
                if (!response.getCode().equals(BIZ_SUCCESS_CODE) || !response.getMsg().equals(BIZ_SUCCESS_MSG)) {
                    return Mono.error(new BusinessException("Okx client error,response error:".concat(JacksonUtils.serialize(response))));
                }
                return Mono.just(response.getData());
            })
            .retryWhen(Retry.backoff(2, Duration.ofMillis(300L)).filter(throwable -> !(throwable instanceof BusinessException)))
            .onErrorResume(throwable -> {
                if (throwable instanceof BusinessException) {
                    return Mono.error(throwable);
                }
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OKX_EXCEPTION_SUM, "date", requestTime, "method", method, "statusCode", THROWABLE_CODE)
                    .increment();
                return Mono.error(throwable);
            })
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(r -> timer.stop(cmcMeterRegistry.timer(TimerMetricName.INTEGRATION_OKX_EXECUTE_SECONDS, "method", method)));
    }

    private Consumer<HttpHeaders> generateHeaders(String sign, String isoTimestamp) {
        return httpHeaders -> {
            httpHeaders.add("Content-Type", "application/json");
            httpHeaders.add("OK-ACCESS-PROJECT", okxAccessProject);
            httpHeaders.add("OK-ACCESS-KEY", okxAccessKey);
            httpHeaders.add("OK-ACCESS-PASSPHRASE", okxAccessPassphrase);
            httpHeaders.add("OK-ACCESS-SIGN", sign);
            httpHeaders.add("OK-ACCESS-TIMESTAMP", isoTimestamp);
        };
    }

    private String generateSign(String timestamp, String method, String requestPath, String body, String secretKey) {
        // 拼接待签名字符串
        String preSign = timestamp + method + requestPath + (body == null ? "" : body);

        // 初始化HMAC-SHA256
        Mac mac = null;
        try {
            mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
        } catch (Exception e) {
            log.error("[OkxClient.generateSign]Error", e);
            throw new BusinessException(MessageCode.SYS_ZUUL_ERROR);
        }

        // 生成HMAC签名
        byte[] hash = mac.doFinal(preSign.getBytes(StandardCharsets.UTF_8));

        // Base64编码
        return Base64.getEncoder().encodeToString(hash);
    }
}
