package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.referral.UserReferralSummaryEntity;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/9/28
 */
@Repository
public interface UserReferralSummaryBlockRepository extends MongoRepository<UserReferralSummaryEntity, ObjectId> {
    UserReferralSummaryEntity findByReferralCode(String referralCode);
}
