package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.job.repository.mongo.asset.entity.WatchListEntity;
import com.mongodb.bulk.BulkWriteResult;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;
import reactor.util.CollectionUtils;

/**
 * @ClassName UserWatchlistRepository.java
 * <AUTHOR>
 * @Date 2022/11/01 10:40
 */
@Repository
public class UserWatchlistRepository  {
    @Resource(name = "cmcAssetPrimaryMongoTemplate")
    private MongoTemplate mongoTemplate;

    /**
     *  query main watchlist by userId
     * @param userIds query ids
     * @return list of watchlist items
     */
    public List<WatchListEntity> queryMainWatchlistByUIds(Collection<String> userIds) {
        Criteria criteria = Criteria.where("userId").in(userIds).and("actived").is(Boolean.TRUE)
            .and("main").is(Boolean.TRUE);
        return mongoTemplate.find(Query.query(criteria), WatchListEntity.class);
    }



    public List<WatchListEntity> findByPlatformByCursor(ObjectId startId, ObjectId endId, ObjectId latestId, int limit) {
        if(latestId != null){
            Query query=Query.query(Criteria.where("_id").gt(latestId).lte(endId)).limit(limit);
            return mongoTemplate.find(query, WatchListEntity.class);
        }
        Query query=Query.query(Criteria.where("_id").gte(startId).lte(endId)).limit(limit);
        return mongoTemplate.find(query, WatchListEntity.class);
    }


    /**
     * 批量更新或插入 WatchListEntity
     * @param list 需要更新的实体列表
     * @return 操作结果
     */
    public Mono<Boolean> batchUpdateDexToken(List<WatchListEntity> list) {
        // 过滤出需要更新的实体
        List<WatchListEntity> entitiesToUpdate = list.stream()
                .filter(e -> !CollectionUtils.isEmpty(e.getDexTokens()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(entitiesToUpdate)) {
            return Mono.just(Boolean.TRUE);
        }
        // 构建批量操作
        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, WatchListEntity.class);
        // 添加批量更新操作
        entitiesToUpdate.forEach(entity -> {
            Update update = new Update();
            update.set("dexTokens",entity.getDexTokens());
            Query query = Query.query(Criteria.where("_id").is(entity.getId()));
            bulkOperations.upsert(query, update);
        });
        // 执行批量操作
        return Mono.fromCallable(() -> {
            BulkWriteResult result = bulkOperations.execute();
            return result.wasAcknowledged();
        }).onErrorReturn(false);
    }


}
