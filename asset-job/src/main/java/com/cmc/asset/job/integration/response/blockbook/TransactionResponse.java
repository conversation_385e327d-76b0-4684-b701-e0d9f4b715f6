package com.cmc.asset.job.integration.response.blockbook;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/24 07:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionResponse extends BlockBookResponse{

    public BlockBookResponseData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BlockBookResponseData{

        private List<BlockBookTransactionDTO> transactions;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BlockBookTransactionDTO {
        private String txHash;
        private String height;
        private Long timestamp;
        private Boolean status;
        private String from;
        private String to;
        private Long fee;
        private String mainAction;
        private List<BalanceChange> balanceChange;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BalanceChange {
        private Long amount;
        private String address;
        private String name;
        private String symbol;
        private int decimals;
        private String tokenAccount;
        private String owner;
        private String programId;
    }

}
