package com.cmc.asset.job.service.portfolio.manager;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.BlockChainTokenPO;
import com.cmc.asset.job.cache.CryptoPriceCache;
import com.cmc.asset.job.cache.DexTokenCache;
import com.cmc.asset.job.cache.DexerPlatformCache;
import com.cmc.asset.job.utils.BigDecimalUtil;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.cmc.asset.model.enums.PortfolioTokenTypeEnum;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * AbstractWalletBalanceService
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractWalletBalanceService {

    @Autowired
    private CryptoPriceCache cryptoPriceCache;

    @Resource
    private DexTokenCache dexTokenCache;
    @Resource
    private DexerPlatformCache dexerPlatformCache;

    @Value("${com.cmc.asset-service.filter-less-one:true}")
    private Boolean filterLessOne;
    @Value("${com.cmc.asset.portfolio.wallet-token-value-limit:100000}")
    private String walletTokenValueLtLimit;

    /**
     * 获取crypto的价格 设置每个crypto的price以及value
     * 同时计算出totalTokenValue,方便historical chart图展示
     * @param result po
     * @return po
     */
    protected Mono<BlockChainPO> calTotalTokenValue(BlockChainPO result) {
        //过滤数据为0的token
        if(CollectionUtils.isEmpty(result.getTokens())){
            return Mono.empty();
        }
        List<BlockChainTokenPO> filterTokens = result.getTokens().stream()
            .filter(e -> {
                boolean b = e.getAmount() != null && e.getAmount().compareTo(BigDecimal.ZERO) > 0;
                if (!b) {
                    e.getAmount();
                }
                return b;
            })
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterTokens)) {
            return Mono.empty();
        }
        Map<Boolean, List<BlockChainTokenPO>> partitioned = filterTokens.stream()
                .collect(Collectors.partitioningBy(b -> BooleanUtils.isTrue(b.getCanMap())));

        Mono<List<BlockChainTokenPO>> fillVerifiedCryptoPrice = this.fillVerifiedCryptoPrice(partitioned.get(true), null);
        Mono<List<BlockChainTokenPO>> fillUnverifiedCryptoPrice = this.fillUnverifiedCryptoPrice(result, partitioned.get(false));
        Mono<PortfolioCryptoInfoDTO> nativeTokenPriceInfoMono = result.getNativeTokenId() != null ? cryptoPriceCache.get(result.getNativeTokenId()) : Mono.empty();

        return Mono.zip(fillVerifiedCryptoPrice, fillUnverifiedCryptoPrice).map(tuple -> {
            List<BlockChainTokenPO> verifiedTokens = tuple.getT1();
            List<BlockChainTokenPO> unverifiedTokens = tuple.getT2();
            List<BlockChainTokenPO> allTokens = new ArrayList<>(verifiedTokens.size() + unverifiedTokens.size());
            allTokens.addAll(verifiedTokens);
            allTokens.addAll(unverifiedTokens);
            result.setTokens(allTokens);
            return result;
        }).flatMap(chain -> {
            List<BlockChainTokenPO> tokens = chain.getTokens();
            if (CollectionUtils.isEmpty(tokens)) {
                chain.setTotalTokenAmount(BigDecimal.ZERO);
                return Mono.just(chain);
            }
            return nativeTokenPriceInfoMono.map(nativeTokenPriceInfo -> {
                //获取value总和
                BigDecimal totalValue =
                        tokens.stream().map(BlockChainTokenPO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalVerifiedValue =
                        tokens.stream()
                                .filter(token -> PortfolioTokenTypeEnum.LISTED_TOKEN.getCode().equals(token.getType()))
                                .map(BlockChainTokenPO::getValue)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal nativeTokenPrice = nativeTokenPriceInfo.transferPrice();
                //除对应的价格获取amount
                BigDecimal totalAmount = nativeTokenPrice.compareTo(BigDecimal.ZERO) > 0 ?
                        decimalFormat128(totalValue.divide(nativeTokenPrice, 10, RoundingMode.DOWN)) : BigDecimal.ZERO;
                BigDecimal totalVerifiedAmount = nativeTokenPrice.compareTo(BigDecimal.ZERO) > 0 ?
                        decimalFormat128(totalVerifiedValue.divide(nativeTokenPrice, 10, RoundingMode.DOWN)) : BigDecimal.ZERO;
                chain.setTotalTokenAmount(totalAmount);
                chain.setTotalVerifiedTokenAmount(totalVerifiedAmount);
                chain.setTotalTokenValue(totalValue);
                chain.setTotalVerifiedTokenValue(totalVerifiedValue);
                return chain;
            }).defaultIfEmpty(chain);
        });
    }

    private Mono<List<BlockChainTokenPO>> fillUnverifiedCryptoPrice(BlockChainPO result, List<BlockChainTokenPO> filterTokens) {
        Map<Integer, PlatformNewDTO> platforms = dexerPlatformCache.getVisibleOnDexScanPlatforms();

        // Filter tokens that need price lookup
        List<BlockChainTokenPO> untrackedTokens = filterTokens.stream()
                .filter(token -> PortfolioTokenTypeEnum.UNTRACKED_TOKEN.getCode().equals(token.getType())
                        && platforms.containsKey(result.getChainId()))
                .collect(Collectors.toList());

        if (untrackedTokens.isEmpty()) {
            return Mono.just(List.of());
        }

        // Get platform info for the chain
        PlatformNewDTO platform = platforms.get(result.getChainId());
        if (platform == null) {
            return Mono.just(List.of());
        }

        // Collect all contract addresses for batch lookup
        Set<String> contractAddresses = untrackedTokens.stream()
                .map(BlockChainTokenPO::getContractAddress)
                .collect(Collectors.toSet());

        // Batch get token prices
        return dexTokenCache.getAll(platform.getDn(), contractAddresses)
                .map(tokenPriceMap -> {
                    List<BlockChainTokenPO> processedTokens = new ArrayList<>();

                    for (BlockChainTokenPO token : untrackedTokens) {
                        String cacheKey = DexTokenCache.buildKey(platform.getDn(), token.getContractAddress());
                        TokenPriceDTO tokenPriceDTO = tokenPriceMap.get(cacheKey);

                        if (tokenPriceDTO != null) {
                            token.setPlatformId(tokenPriceDTO.getPlatformId());
                            token.setCryptoName("");

                            if (tokenPriceDTO.getPrice() == null) {
                                log.info("AbstractWalletBalanceService fillUnverifiedCryptoPrice priceUsd is blank, address: {}, platformId: {}, token:{}",
                                        token.getContractAddress(), result.getChainId(), JacksonUtils.toJson(tokenPriceDTO));
                                token.setType(PortfolioTokenTypeEnum.UNTRACKED_TOKEN.getCode());
                            } else {
                                token.setType(PortfolioTokenTypeEnum.DEX_TOKEN.getCode());
                                BigDecimal price = tokenPriceDTO.getPrice();
                                token.setPrice(price);
                                token.setValue(decimalFormat128(token.getAmount().multiply(price)));
                                token.setChangeUsd24h(tokenPriceDTO.getPriceChange24h());
                            }
                        }
                        // Add token regardless of whether price was found
                        processedTokens.add(token);
                    }

                    // Filter and return only DEX tokens that meet criteria
                    return processedTokens.stream()
                            .filter(token -> PortfolioTokenTypeEnum.DEX_TOKEN.getCode().equals(token.getType()))
                            .filter(token -> !filterLessOne || token.getValue().compareTo(BigDecimal.ONE) > 0)
                            .filter(token -> token.getValue() != null && token.getValue()
                                    .compareTo(new BigDecimal(walletTokenValueLtLimit)) <= 0)
                            .collect(Collectors.toList());
                });
    }

    @NotNull
    private Mono<List<BlockChainTokenPO>> fillVerifiedCryptoPrice(List<BlockChainTokenPO> filterTokens, Mono<Map<Integer, PortfolioCryptoInfoDTO>> idPriceMap) {
        //获取所有cryptoId并批量获取价格
        if (idPriceMap == null) {
            List<Integer> idList = filterTokens.stream()
                    .filter(d -> d.getCryptoId() != null)
                    .map(BlockChainTokenPO::getCryptoId)
                    .collect(Collectors.toList())
                    .stream()
                    .distinct()
                    .collect(Collectors.toList());
            idPriceMap = cryptoPriceCache.getAll(idList);
        }

        return idPriceMap.flatMap(priceMap -> Flux.fromIterable(filterTokens)
                .filter(token1 -> PortfolioTokenTypeEnum.LISTED_TOKEN.getCode().equals(token1.getType()))
                .map(token -> {
                    PortfolioCryptoInfoDTO cryptoInfo = priceMap.get(token.getCryptoId());
                    if (cryptoInfo == null) {
                        log.warn("fillValueTokens get cryptoInfo null, cryptoId:{}", token.getCryptoId());
                        token.setValue(decimalFormat128(token.getAmount().multiply(BigDecimal.ZERO)));
                        return token;
                    }
                    BigDecimal price = cryptoInfo.transferPrice();
                    token.setPrice(price);
                    token.setValue(decimalFormat128(token.getAmount().multiply(price)));
                    return token;
                })
                .filter(token -> !filterLessOne || token.getValue().compareTo(BigDecimal.ONE) > 0)
                .collectList());
    }

    /**
     * 格式化decimal128
     */
    private BigDecimal decimalFormat128(BigDecimal date) {
        return BigDecimalUtil.decimalFormat128(date);
    }

}
