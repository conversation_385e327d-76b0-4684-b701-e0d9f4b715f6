package com.cmc.asset.job.integration;

import com.cmc.asset.domain.portfolio.thirdparty.GetAccessTokenResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.OkxAssetBalancesResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.OkxAssetFinanceResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.OkxAssetFinanceStakingResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.OkxAssetResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.OkxCommonResponse;
import com.cmc.asset.utils.CommonUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2023/5/5 15:02
 */
@Component
@Slf4j
public class OkxOAuth2ApiClient extends DefaultOAuth2ApiClient {

    @Value("${com.cmc.asset.job.integration.oauth.millis-timeout:60000}")
    private Long millisTimeout;
    @Value("${com.cmc.asset.portfolio.snapshot.enable-log:true}")
    private Boolean enableLog;

    public Mono<GetAccessTokenResponseDTO> exchangeCodeForAccessToken(String uri, Map<String, String> params) {
        Consumer<HttpHeaders> headersConsumer = httpHeaders -> {
            httpHeaders.add("Content-Type", "application/json;charset=UTF-8");
        };
        return client.post(uri, params, headersConsumer)
                .filter(stringResponseEntity -> {
                    if (enableLog) {
                        log.info("exchangeCodeForAccessToken request: {} response: {}", params, stringResponseEntity.getBody());
                    }
                    return stringResponseEntity.getBody() != null;
                })
                .map(response -> JacksonUtils.deserialize(response.getBody(), GetAccessTokenResponseDTO.class));
    }

    public Mono<List<OkxAssetResponseDTO>> getSubAsset(String uri, Map<String, String> params,
                                                       Map<String, String> headers) {
        return commonGet(uri, params, headers, new TypeReference<>() {
        });
    }

    public Mono<List<OkxAssetBalancesResponseDTO>> getSubBalancesAsset(String uri, Map<String, String> params,
                                                                       Map<String, String> headers) {
        return commonGet(uri, params, headers, new TypeReference<>() {
        });
    }

    public Mono<List<OkxAssetFinanceResponseDTO>> getSubFinanceAsset(String uri, Map<String, String> params, Map<String, String> headers) {
        return commonGet(uri, params, headers, new TypeReference<>() {
        });
    }

    public Mono<List<OkxAssetFinanceStakingResponseDTO>> getSubFinanceStakingAsset(String uri, Map<String, String> params,
                                                                                   Map<String, String> headers) {
        return commonGet(uri, params, headers, new TypeReference<>() {
        });
    }

    public <T> Mono<List<T>> commonGet(String uri, Map<String, String> params, Map<String, String> headers,
                                       TypeReference<OkxCommonResponse<T>> responseType) {
        Consumer<HttpHeaders> headersConsumer = httpHeaders -> {
            if (headers != null) {
                headers.forEach(httpHeaders::add);
            }
        };
        return client.get(CommonUtils.buildUrl(uri, params), headersConsumer)
                .retryWhen(Retry.backoff(2, Duration.ofMillis(millisTimeout)))
                .filter(stringResponseEntity -> {
                    log.debug("get result, uri: {}, params: {}, response: {}", uri, params, stringResponseEntity.getBody());
                    return stringResponseEntity.getStatusCode()
                            .is2xxSuccessful() && stringResponseEntity.getBody() != null;
                })
                .map(response -> JacksonUtils.deserialize(response.getBody(), responseType))
                .flatMap(res -> {
                    if ("0".equals(res.getCode())) {
                        return Mono.just(res.getData());
                    } else {
                        log.error("get error: uri: {}, params: {}, code = {}, message = {}", uri, params, res.getCode(),
                                res.getMsg());
                        return Mono.empty();
                    }
                })
                .onErrorResume(e -> {
                    log.error("get error: uri: {}, params: {}", uri, params, e);
                    return Mono.empty();
                });
    }

}
