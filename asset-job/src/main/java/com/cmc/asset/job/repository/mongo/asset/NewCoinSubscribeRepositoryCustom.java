package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.NewCoinSubscribeEntity;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * new coin subscribe repository
 *
 * <AUTHOR>
 * @date 2024/4/5 16:17
 */
public interface NewCoinSubscribeRepositoryCustom {

    Flux<NewCoinSubscribeEntity> findByDataId(String dataId, String id, int limit);

    Mono<Boolean> updatePushTimeByDataId(String dataId, Date pushTime);
}
