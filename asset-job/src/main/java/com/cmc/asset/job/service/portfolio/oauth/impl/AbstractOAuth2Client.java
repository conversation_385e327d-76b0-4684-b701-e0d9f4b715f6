package com.cmc.asset.job.service.portfolio.oauth.impl;

import com.cmc.asset.dao.entity.portfolio.AccessToken;
import com.cmc.asset.domain.portfolio.thirdparty.GetAccessTokenResponseDTO;
import com.cmc.asset.job.config.OAuth2Config;
import com.cmc.asset.job.integration.DefaultOAuth2ApiClient;
import com.cmc.asset.job.service.portfolio.oauth.OAuth2Client;
import com.cmc.asset.model.enums.OAuth2ProviderType;
import com.cmc.asset.utils.CommonUtils;
import reactor.core.publisher.Mono;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/5/8 15:58
 */
public abstract class AbstractOAuth2Client implements OAuth2Client {

    protected OAuth2Config.Registration registration;
    protected OAuth2Config.Provider provider;
    protected DefaultOAuth2ApiClient oAuth2ApiClient;

    public AbstractOAuth2Client(OAuth2Config properties, DefaultOAuth2ApiClient oAuth2ApiClient) {
        OAuth2ProviderType providerType = this.getProvider();
        if (properties.getRegistrations() != null) {
            this.registration = properties.getRegistrations().get(providerType.getName());
        }
        if (properties.getProviders() != null) {
            this.provider = properties.getProviders().get(providerType.getName());
        }
        this.oAuth2ApiClient = oAuth2ApiClient;
    }

    @Override
    public String getAuthorizationUrl(String state) {
        Map<String, String> params =
            Map.of("response_type", "code", "client_id", registration.getClientId(), "redirect_uri",
                URLEncoder.encode(registration.getRedirectUri(), StandardCharsets.UTF_8), "state", state, "scope",
                String.join(",", registration.getScope()));
        return CommonUtils.buildUrl(provider.getAuthorizationUri(), params);
    }

    @Override
    public Mono<GetAccessTokenResponseDTO> refreshAccessToken(AccessToken token) {
        Map<String, String> params =
            Map.of("grant_type", "refresh_token", "refresh_token", token.getRefreshToken(), "client_id",
                registration.getClientId(), "client_secret", registration.getClientSecret());
        return oAuth2ApiClient.exchangeCodeForAccessToken(provider.getTokenUri(), params);
    }

}
