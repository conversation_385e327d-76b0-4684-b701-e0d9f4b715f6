package com.cmc.asset.job.service.router.strategy;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.job.service.portfolio.manager.BlockBookBalanceManager;
import com.cmc.asset.job.service.portfolio.manager.OkLinkBalanceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @since 2024/5/14 18:17
 */
@Component
@Slf4j
public class WeightedStrategy implements FetchStrategy {

    @Autowired
    private OkLinkBalanceManager okLinkBalanceManager;
    @Autowired
    private BlockBookBalanceManager blockBookBalanceManager;

    @Value("${com.cmc.asset.portfolio-wallet.switch-percentage}")
    private Integer percentage;

    @Override
    public Mono<BlockChainPO> fetch(Integer chainId, String address, Date currentTaskTime) {
        return Mono.defer(() -> {
            if (switchBlockBook()) {
                return blockBookBalanceManager.addressSingleChainBalanceV2(chainId, address, currentTaskTime)
                        .onErrorResume(e -> {
                            log.error("BlockBookFetchStrategy fetch error, chainId:{}, address:{}, currentTaskTime:{}", chainId, address, currentTaskTime, e);
                            return Mono.empty();
                        });
            } else {
                return okLinkBalanceManager.getChainFromOk(chainId, address, currentTaskTime);
            }
        });
    }

    private boolean switchBlockBook() {
        return percentage >= ThreadLocalRandom.current().nextInt(1, 101);
    }

}
