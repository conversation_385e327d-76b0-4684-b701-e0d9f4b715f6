package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.votecountry.UserPredictCountryCryptoEntity;
import com.mongodb.client.result.UpdateResult;
import java.util.Date;
import javax.annotation.Resource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Yin
 * @Description
 * @date 2022/5/9 下午5:51
 */
@Repository
public class UserPredictCountryCryptoBlockRepository {

    @Resource(name = "cmcAssetPrimaryMongoTemplate")
    private MongoTemplate mongoTemplate;

    public UpdateResult markPredictSuccessUser(String activityId, String countryCode, Date cryptoDeclareDate) {
        // condition
        Criteria criteria = Criteria.where("activityId").is(activityId)
            .and("predictInfo").elemMatch(
                Criteria.where("countryCode").is(countryCode)
                    .and("predictDate").lt(cryptoDeclareDate));
        // mark predict success user
        Update update = new Update();
        update.set("claimInfo.guessReward", true);

        return mongoTemplate.updateMulti(Query.query(criteria),
            update,
            UserPredictCountryCryptoEntity.class);
    }
}
