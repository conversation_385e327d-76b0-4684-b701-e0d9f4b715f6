package com.cmc.asset.job.config;

import com.mongodb.ReadPreference;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.mongo.MongoProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDbFactory;
import org.springframework.data.mongodb.core.convert.DbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * Mongodb config
 *
 * <AUTHOR>
 * @date 2020/11/19
 */
@Configuration
public class MongoDbConfig {

    /**
     * Config for coinmarketcap-asset db
     */
    @Configuration
    @EnableMongoRepositories(basePackages = "com.cmc.asset.job.repository.mongo.asset", mongoTemplateRef = "cmcAssetMongoTemplate"
        ,excludeFilters = {@ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.cmc.asset.dao.dynamo.*")})
    public static class CmcAssetConfig {
        @Primary
        @Bean(name = "cmcAssetMongoProperties")
        @ConfigurationProperties(prefix="com.cmc.asset-job.mongodb.cmc-asset")
        public MongoProperties cmcAssetMongoProperties() {
            return new MongoProperties();
        }

        @Primary
        @Bean(name = "cmcAssetMongoTemplate")
        public MongoTemplate cmcPortalMongoTemplate(
            @Qualifier("cmcAssetMappingMongoConverter") MappingMongoConverter mappingMongoConverter) {
            return new MongoTemplate(cmcAssetFactory(cmcAssetMongoProperties()), mappingMongoConverter);
        }

        @Bean(name = "cmcAssetPrimaryMongoTemplate")
        public MongoTemplate cmcAssetPrimaryMongoTemplate(
            @Qualifier("cmcAssetMappingMongoConverter") MappingMongoConverter mappingMongoConverter) {

            MongoTemplate mongoTemplate = new MongoTemplate(cmcAssetFactory(cmcAssetMongoProperties()), mappingMongoConverter);
            mongoTemplate.setReadPreference(ReadPreference.primary());

            return mongoTemplate;
        }

        @Primary
        @Bean(name = "cmcAssetFactory")
        public MongoDbFactory cmcAssetFactory(@Qualifier("cmcAssetMongoProperties") MongoProperties mongoProperties) {
            return new SimpleMongoClientDbFactory(mongoProperties.getUri());
        }

        @Bean(name = "cmcAssetMappingMongoConverter")
        public MappingMongoConverter mappingMongoConverter(@Qualifier("cmcAssetFactory") MongoDbFactory mongoDbFactory,
            MongoMappingContext mongoMappingContext) {
            DbRefResolver dbRefResolver = new DefaultDbRefResolver(mongoDbFactory);
            MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mongoMappingContext);
            converter.setTypeMapper(new DefaultMongoTypeMapper(null));
            return converter;
        }
    }

    /**
     * Config for coinmarketcap-portal db
     */
    @Configuration
    @EnableMongoRepositories(basePackages = "com.cmc.asset.job.repository.mongo.portal", mongoTemplateRef = "cmcPortalMongoTemplate")
    public static class CmcPortalConfig {
        @Bean(name = "cmcPortalMongoProperties")
        @ConfigurationProperties(prefix="com.cmc.asset-job.mongodb.cmc-portal")
        public MongoProperties cmcPortalMongoProperties() {
            return new MongoProperties();
        }

        @Bean(name = "cmcPortalMongoTemplate")
        public MongoTemplate cmcPortalMongoTemplate(
            @Qualifier("cmcPortalMappingMongoConverter") MappingMongoConverter mappingMongoConverter) {
            return new MongoTemplate(cmcPortalFactory(cmcPortalMongoProperties()), mappingMongoConverter);
        }

        @Bean(name = "cmcPortalFactory")
        public MongoDbFactory cmcPortalFactory(@Qualifier("cmcPortalMongoProperties") MongoProperties mongoProperties) {
            return new SimpleMongoClientDbFactory(mongoProperties.getUri());
        }

        @Bean(name = "cmcPortalMappingMongoConverter")
        public MappingMongoConverter mappingMongoConverter(@Qualifier("cmcPortalFactory") MongoDbFactory mongoDbFactory,
            MongoMappingContext mongoMappingContext) {
            DbRefResolver dbRefResolver = new DefaultDbRefResolver(mongoDbFactory);
            MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mongoMappingContext);
            converter.setTypeMapper(new DefaultMongoTypeMapper(null));
            return converter;
        }
    }
}
