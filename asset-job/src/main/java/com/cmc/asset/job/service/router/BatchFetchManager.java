package com.cmc.asset.job.service.router;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.dto.BatchFetchStrategyConfig;
import com.cmc.asset.job.service.router.strategy.BatchFetchStrategy;
import com.cmc.asset.job.service.router.strategy.StrategyName;
import com.cmc.asset.job.utils.StringUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Slf4j
@Service
public class BatchFetchManager {
    @Resource
    private List<BatchFetchStrategy> strategies;

    @Resource
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    private Map<String, BatchFetchStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = new HashMap<>();
        strategies.forEach(strategy -> {
            StrategyName annotation = strategy.getClass().getAnnotation(StrategyName.class);
            if (Objects.isNull(annotation) || StringUtil.isEmpty(annotation.name())) {
                return;
            }
            String strategyName = annotation.name();
            if (strategyMap.containsKey(strategyName)) {
                log.error("[.BatchFetchManager.init]Strategy already exists");
                throw new IllegalArgumentException("Duplicate strategy name");
            }
            strategyMap.put(strategyName, strategy);
        });
    }

    public Mono<List<BlockChainPO>> batchFetchAsset(List<Integer> chainIds, String address, Date currentTaskTime) {
        BatchFetchStrategyConfig batchFetchStrategyConfig = dynamicApolloRefreshConfig.getBatchFetchStrategyConfig();
        Map<String, List<Integer>> assetBatchFetchStrategies = batchFetchStrategyConfig.getAssetBatchFetchStrategies();

        // 策略分发&执行
        return Flux.fromIterable(assetBatchFetchStrategies.keySet()).flatMap(strategyName -> {
            BatchFetchStrategy strategy = strategyMap.getOrDefault(strategyName, null);
            if (Objects.isNull(strategy)) {
                log.warn("[BatchFetchManager.batchFetchAsset]Strategy executor does not exist -- strategyName: {}", strategyName);
                return Mono.just(new ArrayList<BlockChainPO>());
            }

            if (!assetBatchFetchStrategies.containsKey(strategyName)) {
                log.warn("[BatchFetchManager.batchFetchAsset]Strategy not configured -- strategyName: {}", strategyName);
                return Mono.just(new ArrayList<BlockChainPO>());
            }

            Set<Integer> validChainIds = assetBatchFetchStrategies.get(strategyName)
                .stream()
                .collect(Collectors.toSet());
            List<Integer> chainIdList = chainIds.stream()
                .filter(chainId -> validChainIds.contains(chainId))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(chainIdList)) {
                log.info("[BatchFetchManager.batchFetchAsset]ChainIdList is empty, skip strategy: {}", strategyName);
                return Mono.just(new ArrayList<BlockChainPO>());
            }

            return strategy.batchFetch(chainIdList, address, currentTaskTime)
                .map(list -> list.stream()
                    .filter(blockChain -> Objects.nonNull(blockChain) && !CollectionUtils.isEmpty(blockChain.getTokens()))
                    .collect(Collectors.toList())
                )
                .doOnNext(blockChainPOS -> log.info("[BatchFetchManager.batchFetchAsset]{} execute successfully.", strategyName));
        })
        .collectList()
        .flatMap(list -> Mono.just(list.stream()
            .filter(sublist -> !CollectionUtils.isEmpty(sublist))
            .flatMap(List::stream)
            .collect(Collectors.toList()))
        );
    }
}
