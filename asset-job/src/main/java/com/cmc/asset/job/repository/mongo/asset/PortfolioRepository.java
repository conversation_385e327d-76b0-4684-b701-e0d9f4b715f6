package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.portfolio.PortfolioCalculatingStatusV2Entity;
import com.cmc.asset.dao.entity.portfolio.PortfolioCalculationLogV2Entity;
import com.cmc.asset.dao.entity.portfolio.PortfolioEntity;
import com.mongodb.client.result.DeleteResult;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
public interface PortfolioRepository {

    List<PortfolioEntity> queryPortfolioEntity(String userId, String sourceId, Integer limit, Long skip) ;

    long queryPortfolioEntityCount(String userId, String sourceId);

    List<PortfolioEntity> queryAllPortfolioEntity(String userId, String sourceId);

    <T> T save(T objectToSave);

    PortfolioEntity findFirstPortfolioEntity(String userId, String portfolioSourceId);

    DeleteResult removePortfolioHoldingEntity(String userId, String portfolioSourceId);

    PortfolioCalculationLogV2Entity queryPortfolioCalculationLogV2Entity(String userId, String portfolioSourceId);

    List<PortfolioCalculatingStatusV2Entity> getSchedulingList();
}
