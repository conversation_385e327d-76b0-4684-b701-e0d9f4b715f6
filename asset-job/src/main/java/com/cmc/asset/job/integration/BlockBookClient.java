package com.cmc.asset.job.integration;

import com.cmc.asset.job.integration.request.AddressBalanceRequest;
import com.cmc.asset.job.integration.request.AssociatedAddressesRequest;
import com.cmc.asset.job.integration.request.BlockBookBaseRequest;
import com.cmc.asset.job.integration.request.XpubRequest;
import com.cmc.asset.job.integration.response.blockbook.AddressBalanceResponse;
import com.cmc.asset.job.integration.response.blockbook.AddressBalanceResponse.AccountChainInfo;
import com.cmc.asset.job.integration.response.blockbook.AssociatedAddressesResponse;
import com.cmc.asset.job.integration.response.blockbook.AssociatedAddressesResponse.BlockBookResponseData;
import com.cmc.asset.job.integration.response.blockbook.TransactionResponse;
import com.cmc.asset.job.integration.response.blockbook.XpubResponse;
import com.cmc.asset.job.metric.CounterMetricName;
import com.cmc.asset.job.metric.TimerMetricName;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils.LocalDateFormatEnum;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * block bool client
 *
 * <AUTHOR> ricky.x
 * @date : 2023/2/2 下午6:53
 */
@Component
@Slf4j
public class BlockBookClient extends BaseServiceClient {

    @Value("${com.cmc.asset-job.integration-config.blockBook-url}")
    private String blockBookBaseUrl;
    @Value("${com.cmc.asset-job.integration-config.blockBook-request-mills:30000}")
    private Long blockBookRequestMills;

    private final int success_status_code = 200;

    private final String success_code = "0";

    private final String body_null_code = "999";

    private final String throwable_code = "998";

    @Resource
    private CmcMeterRegistry cmcMeterRegistry;

    /**
     * blockBook查询cardano中stake地址对应的地址列表
     * @param address 地址
     * @return response
     */
    public Mono<BlockBookResponseData> associatedAddresses(String address) {
        String addressBalanceFills = "/account/cardano/associated/addresses";
        String url = blockBookBaseUrl.concat(addressBalanceFills);
        AssociatedAddressesRequest request = AssociatedAddressesRequest.builder().stakeAddress(address).build();
        return postByBlockBook(url, Duration.ofMillis(blockBookRequestMills), request,"associated-addresses")
            .flatMap(e -> {
                AssociatedAddressesResponse response = JacksonUtils.deserialize(e, AssociatedAddressesResponse.class);
                if (response.getData() == null) {
                    return Mono.empty();
                }
                return Mono.just(JacksonUtils.deserialize(e, AssociatedAddressesResponse.class).getData());
            })
            .onErrorResume(throwable -> {
                log.warn("BlockBookClient associatedAddresses error,address:{} e:", address, throwable);
                return Mono.error(throwable);
            }).publishOn(Schedulers.boundedElastic());
    }

    /**
     * blockBook查询address对应的amount
     *
     * @param address 地址
     * @return response
     */
    public Mono<List<AccountChainInfo>> accountBalance(String address) {
        String addressBalanceFills = "/account/balance";
        String url = blockBookBaseUrl.concat(addressBalanceFills);
        AddressBalanceRequest request = AddressBalanceRequest.builder().address(address).build();
        return postByBlockBook(url, Duration.ofMillis(blockBookRequestMills), request,"account-balance")
            .flatMap(e -> {
                AddressBalanceResponse response = JacksonUtils.deserialize(e, AddressBalanceResponse.class);
                if (response.getData() == null) {
                    return Mono.empty();
                }
                return Mono.just(JacksonUtils.deserialize(e, AddressBalanceResponse.class).getData().getResult());
            })
            .onErrorResume(throwable -> {
                log.warn("BlockBookClient accountBalance error,url:{}, address:{}", url, address, throwable);
                return Mono.error(throwable);
            });
    }

    public Mono<List<TransactionResponse.BlockBookTransactionDTO>> getSolanaTransaction(String address, int limit) {
        String url = blockBookBaseUrl + "/transaction/solana?address=" + address + "&limit=" + limit;
        return getByBlockBook(url, Duration.ofMillis(blockBookRequestMills),"account-transaction")
            .flatMap(e -> {
                TransactionResponse response = JacksonUtils.deserialize(e, TransactionResponse.class);
                if (response.getData() == null || CollectionUtils.isEmpty(response.getData().getTransactions())) {
                    log.info("BlockBookClient getTransaction empty,address:{}", address);
                    return Mono.empty();
                }
                return Mono.just(JacksonUtils.deserialize(e, TransactionResponse.class).getData().getTransactions());
            })
            .onErrorResume(throwable -> {
                log.warn("BlockBookClient getTransaction error,url:{}, address:{}", url, address, throwable);
                return Mono.error(throwable);
            });
    }

    public Mono<XpubResponse> getBtcXpub(Integer platformId, String xpubAddress){
        String xpubUrlSuffix = "/account/xpub/address";
        String url = blockBookBaseUrl.concat(xpubUrlSuffix);
        XpubRequest request = XpubRequest.builder().platformId(platformId).address(xpubAddress).build();
        return postByBlockBook(url, Duration.ofMillis(300), request,"xpub-address")
            .flatMap(e -> {
                XpubResponse response = JacksonUtils.deserialize(e, XpubResponse.class);
                if (response.getData() == null || CollectionUtils.isEmpty(response.getData().getResult())) {
                    return Mono.empty();
                }
                return Mono.just(response);
            })
            .doOnError(throwable -> log.error("BlockBookClient xpub error, request:{}", JacksonUtils.toJson(request), throwable));
    }

    private Mono<String> getByBlockBook(String url, Duration time, String method) {
        Timer.Sample timer = Timer.start();
        String requestTime = LocalDateFormatEnum.YYYY_MM_DD.getDateFormatter().format(LocalDateTime.now());
        return client.get(url)
            .flatMap(e -> {
                if (e.getStatusCodeValue() != success_status_code || e.getBody() == null) {
                    cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_BLOCK_BOOK_EXCEPTION_SUM, "date",
                        requestTime, "method", method, "statusCode", StringUtil.isBlank(e.getBody())
                            ? body_null_code : e.getStatusCodeValue() + "").increment();
                    return Mono.error(new BusinessException("BlockBookClient error,statusCode error:"
                        .concat(Integer.toString(e.getStatusCodeValue()))));
                }
                AddressBalanceResponse response = JacksonUtils.deserialize(e.getBody(), AddressBalanceResponse.class);
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_BLOCK_BOOK_SUCCESS_SUM, "date",
                    requestTime, "method", method, "responseCode", response.getStatus().getError_code()).increment();
                if(response.getStatus() == null || !success_code.equals(response.getStatus().getError_code())){
                    return Mono.error(new BusinessException("BlockBookClient error,response error:"
                        .concat(JacksonUtils.serialize(response))));
                }
                return Mono.just(e.getBody());
            })
            .retryWhen(Retry.backoff(2, time))
            .onErrorResume(throwable -> {
                if(throwable instanceof BusinessException){
                    return Mono.error(throwable);
                }
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_BLOCK_BOOK_EXCEPTION_SUM, "date",
                    requestTime, "method", method, "statusCode", throwable_code).increment();
                return Mono.error(throwable);
            })
            .doOnNext(r -> timer.stop(cmcMeterRegistry.timer(TimerMetricName.INTEGRATION_BLOCK_BOOK_EXECUTE_SECONDS, "method", method)));

    }

    private Mono<String> postByBlockBook(String url, Duration time, BlockBookBaseRequest request, String method) {
        Timer.Sample timer = Timer.start();
        String requestTime = LocalDateFormatEnum.YYYY_MM_DD.getDateFormatter().format(LocalDateTime.now());
        return client.post(url, request, httpHeaders -> httpHeaders.add("Content-Type", "application/json"))
            .flatMap(e -> {
                if (e.getStatusCodeValue() != success_status_code || e.getBody() == null) {
                    cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_BLOCK_BOOK_EXCEPTION_SUM, "date",
                        requestTime, "method", method, "statusCode", StringUtil.isBlank(e.getBody())
                            ? body_null_code : e.getStatusCodeValue() + "").increment();
                    return Mono.error(new BusinessException("BlockBookClient error,statusCode error:"
                        .concat(Integer.toString(e.getStatusCodeValue()))));
                }
                AddressBalanceResponse response = JacksonUtils.deserialize(e.getBody(), AddressBalanceResponse.class);
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_BLOCK_BOOK_SUCCESS_SUM, "date",
                    requestTime, "method", method, "responseCode", response.getStatus().getError_code()).increment();
                if(response.getStatus() == null || !success_code.equals(response.getStatus().getError_code())){
                    return Mono.error(new BusinessException("BlockBookClient error,response error:"
                        .concat(JacksonUtils.serialize(response))));
                }
                return Mono.just(e.getBody());
            })
            .retryWhen(Retry.backoff(2, time))
            .onErrorResume(throwable -> {
                if(throwable instanceof BusinessException){
                    return Mono.error(throwable);
                }
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_BLOCK_BOOK_EXCEPTION_SUM, "date",
                    requestTime, "method", method, "statusCode", throwable_code).increment();
                return Mono.error(throwable);
            })
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(r -> timer.stop(cmcMeterRegistry.timer(TimerMetricName.INTEGRATION_BLOCK_BOOK_EXECUTE_SECONDS, "method", method)));

    }

}