package com.cmc.asset.job.handler;

import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.service.watch.impl.GuestWatchDexTokenRefresh;
import com.cmc.asset.job.service.watch.impl.UserWatchDexTokenRefresh;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.asset.model.contract.watchlist.WatchListTokenRefreshDTO;
import javax.annotation.Resource;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * WatchListTokenRefreshHandler
 * <AUTHOR> ricky.x
 * @date: 2025/6/17 19:10
 */
@Slf4j
@Service
public class WatchListTokenRefreshHandler implements JobHandler<WatchListTokenRefreshDTO, Object>{

    @Resource(name = "userWatchDexTokenRefresh")
    private UserWatchDexTokenRefresh userWatchDexTokenRefresh;

    @Resource(name = "guestWatchDexTokenRefresh")
    private GuestWatchDexTokenRefresh guestWatchDexTokenRefresh;

    @Override
    public JobHandlingResult<Object> handle(WatchListTokenRefreshDTO jobData) {
        if (StringUtil.isAnyEmpty(jobData.getStartId(),jobData.getEndId()) || jobData.getIsGuest() == null) {
            log.info("WatchListTokenRefreshHandler end invalid params:{}", jobData);
            return new JobHandlingResult<>(true);
        }
        if(Boolean.TRUE.equals(jobData.getIsGuest())){
            guestWatchDexTokenRefresh.process(new ObjectId(jobData.getStartId()),new ObjectId(jobData.getEndId())).block();
        }else{
            userWatchDexTokenRefresh.process(new ObjectId(jobData.getStartId()),new ObjectId(jobData.getEndId())).block();
        }
        return new JobHandlingResult<>(true);
    }
}
