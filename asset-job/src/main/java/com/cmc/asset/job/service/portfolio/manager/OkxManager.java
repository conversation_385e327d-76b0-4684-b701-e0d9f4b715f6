package com.cmc.asset.job.service.portfolio.manager;

import com.cmc.asset.dao.entity.portfolio.ChainTransactionPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionTokenPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.integration.OkxClient;
import com.cmc.asset.job.integration.response.AddressTransactionResponse;
import com.cmc.asset.job.integration.response.okx.OkxTransactionDTO;
import com.cmc.asset.job.service.portfolio.convert.PortfolioWalletConvert;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.framework.utils.CollectionUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.UnicastProcessor;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Service
@Slf4j
public class OkxManager {
    private static final String NATIVE_TOKEN = "0";
    private static final String INTERACT = "1";
    private static final String ERC_TOKEN = "2";
    private static final String UTXO = "";


    @Resource
    private OkxClient okxClient;

    @Resource
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Resource
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    public Mono<ChainTransactionPO> getNearLimitTransActionDetails(Integer chainId, String address, int limit) {
        Map<Integer, OkLinkMapConfigItem> mapConfig = dynamicApolloRefreshConfig.getOkLinkOkxMapConfig();
        if (!mapConfig.containsKey(chainId)) {
            log.warn("[OkxManager.getNearLimitTransActionDetails]Map config error -- chainId: {}", chainId);
            return Mono.empty();
        }
        OkLinkMapConfigItem configItem = mapConfig.get(chainId);
        return getTokenTransactionFormOkx(configItem, address, limit)
            .flatMap(transactions -> buildChainsTransactionToken(chainId, transactions, address)
                .filter(tokens -> CollectionUtils.isNotEmpty(tokens))
                .map(tokens -> ChainTransactionPO.builder()
                    .chainId(chainId)
                    .tokenTransactions(tokens)
                    .build()));
    }

    private Mono<List<ChainTransactionTokenPO>> buildChainsTransactionToken(Integer chainId, List<AddressTransactionResponse.AddressTransActionDetailDTO> transactions, String address) {
        return Flux.fromIterable(transactions)
            .flatMap(actionDetailDTO -> {
                String tokenContractAddress = actionDetailDTO.getTokenContractAddress();
                if(StringUtil.isBlank(tokenContractAddress)){
                    return Mono.just(
                        PortfolioWalletConvert.mongoActionToken(actionDetailDTO, address, actionDetailDTO.getCmcNativeTokenId()));
                } else {
                    Integer cryptoIdFormToken = cryptoCurrencyCache.getIdByContractAddress(chainId, tokenContractAddress);
                    return Mono.just(PortfolioWalletConvert.mongoActionToken(actionDetailDTO, address, cryptoIdFormToken));
                }
            }).collectList();
    }

    private Mono<List<AddressTransactionResponse.AddressTransActionDetailDTO>> getTokenTransactionFormOkx(
        OkLinkMapConfigItem config, String address, int limit) {
        UnicastProcessor<Tuple3<String, Integer, OkxTransactionDTO>> unicastProcessor =
            UnicastProcessor.create();

        // 初始化cursor参数
        unicastProcessor.sink().next(Tuples.of(
            "", 1,
            OkxTransactionDTO.builder()
                .transactionList(new ArrayList<>())
                .build()
        ));

        List<Integer> chainIds = new ArrayList<>();
        chainIds.add(config.getOkxChainId());
        return unicastProcessor
            .flatMap(tuple -> okxClient.getTransactionsByAddress(address, chainIds, tuple.getT1())
                .switchIfEmpty(Mono.defer(() -> {
                    unicastProcessor.sink().complete();
                    return Mono.empty();
                }))
                .doOnNext(okxTransaction -> {
                    // 已查询所有数据
                    // 累加结果到 T3
                    OkxTransactionDTO tempOkxTransactionDTO = tuple.getT3();
                    tempOkxTransactionDTO.getTransactionList().addAll(okxTransaction.getTransactionList());
                    if (tempOkxTransactionDTO.getTransactionList().size() > 100 || StringUtil.isEmpty(okxTransaction.getCursor()) || StringUtil.equals(okxTransaction.getCursor(), tuple.getT1())) {
                        unicastProcessor.sink().complete();
                    } else {
                        Tuple3<String, Integer, OkxTransactionDTO> next = Tuples.of(okxTransaction.getCursor(),
                            tuple.getT2() + 1, tempOkxTransactionDTO);
                        unicastProcessor.sink().next(next);
                    }
                })
                .map(okxTransactionDto -> {
                    if (tuple.getT2() == 1) {
                        return okxTransactionDto.getTransactionList().stream().collect(Collectors.toList());
                    }

                    // 第二次之后每次剔除第一条重复数据
                    if (CollectionUtils.isEmpty(okxTransactionDto.getTransactionList()) || okxTransactionDto.getTransactionList().size() == 1) {
                        return new ArrayList<OkxTransactionDTO.Transaction>();
                    }
                    return okxTransactionDto.getTransactionList().subList(1, okxTransactionDto.getTransactionList().size());
                }))
            .collect(Collectors.reducing(new ArrayList<OkxTransactionDTO.Transaction>(), (d1, d2) -> mergeTransactions(d1, d2)))
            .flatMap(transactions -> {
                if (CollectionUtils.isEmpty(transactions)) {
                    return Mono.empty();
                }

                // 合并
                Map<String, OkxTransactionDTO.Transaction> nativeTokenTxMap = new HashMap<>();
                transactions.forEach(tx -> {
                    if (StringUtil.equals(tx.getIType(), NATIVE_TOKEN)) {
                        nativeTokenTxMap.put(tx.getTxHash(), tx);
                    }
                });

                List<OkxTransactionDTO.Transaction> mergedList = new ArrayList<>();
                transactions.forEach(tx -> {
                    OkxTransactionDTO.Transaction nativeTokenTx = nativeTokenTxMap.get(tx.getTxHash());
                    if (StringUtil.equals(tx.getIType(), UTXO)) {
                        mergedList.add(tx);
                        return;
                    }

                    if (StringUtil.equals(tx.getIType(), NATIVE_TOKEN)) {
                        BigDecimal nativeTokenAmount = new BigDecimal(tx.getAmount());
                        if (nativeTokenAmount.compareTo(BigDecimal.ZERO) == 1) {
                            mergedList.add(tx);
                        }
                        return;
                    }

                    if (StringUtil.equals(tx.getIType(), INTERACT)) {
                        BigDecimal nativeTokenAmount = new BigDecimal(tx.getAmount());
                        if (nativeTokenAmount.compareTo(BigDecimal.ZERO) == 1) {
                            if (Objects.nonNull(nativeTokenTx)) {
                                tx.setTxFee(nativeTokenTx.getTxFee());
                                tx.setTxStatus(nativeTokenTx.getTxStatus());
                            }
                            mergedList.add(tx);
                        }
                        return;
                    }

                    if (StringUtil.equals(tx.getIType(), ERC_TOKEN)) {
                        if (Objects.nonNull(nativeTokenTx)) {
                            tx.setTxFee(nativeTokenTx.getTxFee());
                            tx.setTxStatus(nativeTokenTx.getTxStatus());
                        }
                        mergedList.add(tx);
                    }
                });


                // 截断
                int truncateCount = Math.min(mergedList.size(), limit);
                List<AddressTransactionResponse.AddressTransActionDetailDTO> transActionDetailDTOS = mergedList.subList(0, truncateCount)
                    .stream()
                    .map(trans -> convert(address, trans, config))
                    .collect(Collectors.toList());
                return Mono.just(transActionDetailDTOS);
            });
    }

    private int calculateQueryTimes(int limit) {
        // 计算查询次数（cursor方式查询，每次最多返回20条数据，除第一此外，之后每次查询会有一条重复数据）
        // 查询的非主链币会有2条记录，所以总记录数最大为limit * 2
        // 20 + (n-1)*19 >= limit * 2
        // 计算最少查询次数 n，确保获取到至少 limit 条数据
        return (int) Math.ceil((double) (limit * 2 - 1) / 19);
    }

    private List<OkxTransactionDTO.Transaction> mergeTransactions(List<OkxTransactionDTO.Transaction> d1, List<OkxTransactionDTO.Transaction> d2) {
        d1.addAll(d2);
        return d1;
    }

    private AddressTransactionResponse.AddressTransActionDetailDTO convert(String walletAddress, OkxTransactionDTO.Transaction transaction, OkLinkMapConfigItem config) {
        AddressTransactionResponse.AddressTransActionDetailDTO addressTransDTO = new AddressTransactionResponse.AddressTransActionDetailDTO();
        addressTransDTO.setCmcNativeTokenId(Integer.valueOf(config.getNativeTokenId()));
        addressTransDTO.setQueryType(StringUtil.isEmpty(transaction.getTokenAddress()) ? 0 : 1);
        addressTransDTO.setTxId(transaction.getTxHash());
        addressTransDTO.setMethodId(transaction.getMethodId());
        addressTransDTO.setTransactionTime(transaction.getTxTime());

        Tuple3<String, String, String> tuple = StringUtil.isEmpty(transaction.getIType()) ?
            parseUtxoModel(walletAddress, transaction) : parseAccountModel(transaction);
        addressTransDTO.setFrom(tuple.getT1());
        addressTransDTO.setTo(tuple.getT2());
        addressTransDTO.setAmount(tuple.getT3());
        addressTransDTO.setTransactionSymbol(transaction.getSymbol());

        if (addressTransDTO.getFrom().toLowerCase().contains(walletAddress.toLowerCase())) {
            addressTransDTO.setTxFee(transaction.getTxFee());
        }
        addressTransDTO.setState(transaction.getTxStatus());
        addressTransDTO.setTokenContractAddress(transaction.getTokenAddress());
        return addressTransDTO;
    }

    private Tuple3<String, String, String> parseAccountModel(OkxTransactionDTO.Transaction transaction) {
        String fromAddress = transaction.getFrom()
            .stream()
            .map(OkxTransactionDTO.TransactionDetail::getAddress)
            .collect(Collectors.joining(","));
        String toAddress = transaction.getTo()
            .stream()
            .map(OkxTransactionDTO.TransactionDetail::getAddress)
            .collect(Collectors.joining(","));
        return Tuples.of(fromAddress, toAddress, transaction.getAmount());
    }

    private Tuple3<String, String, String> parseUtxoModel(String walletAddress, OkxTransactionDTO.Transaction transaction) {
        String fromAddress = transaction.getFrom()
            .stream()
            .map(OkxTransactionDTO.TransactionDetail::getAddress)
            .collect(Collectors.joining(","));

        boolean inFrom = StringUtil.isEmpty(fromAddress) ? false : fromAddress.toLowerCase().contains(walletAddress.toLowerCase());

        BigDecimal fromAmount = transaction.getFrom()
            .stream()
            .filter(detail -> StringUtil.isEmpty(detail.getAddress()) ? false : detail.getAddress().toLowerCase().contains(walletAddress.toLowerCase()))
            .map(OkxTransactionDTO.TransactionDetail::getAmount)
            .map(amount -> StringUtil.isNotEmpty(amount) ? new BigDecimal(amount) : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        String toAddress = transaction.getTo().stream()
            .map(OkxTransactionDTO.TransactionDetail::getAddress)
            .filter(Objects::nonNull)
            .flatMap(address -> Arrays.stream(address.split(",")))
            .filter(address -> inFrom ? !address.toLowerCase().equals(walletAddress.toLowerCase()) : true)
            .collect(Collectors.joining(","));

        BigDecimal toAmount = transaction.getTo()
            .stream()
            .filter(detail -> StringUtil.isEmpty(detail.getAddress()) ? false : detail.getAddress().toLowerCase().contains(walletAddress.toLowerCase()))
            .map(OkxTransactionDTO.TransactionDetail::getAmount)
            .map(amount -> StringUtil.isNotEmpty(amount) ? new BigDecimal(amount) : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal absAmount = fromAmount.subtract(toAmount).abs();
        return Tuples.of(fromAddress, toAddress, absAmount.toPlainString());
    }
}
