package com.cmc.asset.job.integration;

import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.data.common.ApiResponse;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * dexer api client
 */
@Component
@Slf4j
public class DexerApiClient extends BaseServiceClient {

    @Value("${com.cmc.asset.job.integration.dex-query.url:}")
    private String dqueryServiceBaseUrl;

    public static final String DQUERY_PLATFORM_API = "%s/v1/platform/list";
    public static final String DQUERY_BATCH_QUERY_TOKEN_API = "%s/v1/token/price/batch";

    public Mono<List<PlatformNewDTO>> getPlatformsFromDeQuery() {
        final String url = String.format(DQUERY_PLATFORM_API, dqueryServiceBaseUrl);
        return client.get(url)
                .filter(responseEntity -> responseEntity.getStatusCode().is2xxSuccessful() && StringUtils.isNotBlank(responseEntity.getBody()))
                .flatMap(responseEntity -> {
                    ApiResponse<List<PlatformNewDTO>> response =
                            JacksonUtils.deserialize(responseEntity.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {});
                    return Mono.just(response.getData());
                })
                .onErrorResume(throwable -> {
                    log.error("getPairs failed, url:{}", url, throwable);
                    return Mono.just(new ArrayList<>());
                }).publishOn(Schedulers.boundedElastic());
    }

    public Mono<List<TokenPriceDTO>> getPairListByDeQuery(List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens) {
        if(CollectionUtils.isEmpty(tokens)){
            return Mono.just(List.of());
        }
        BatchPlatformTokenRequestDTO requestDTO = BatchPlatformTokenRequestDTO.builder().tokens(tokens).build();
        return client.post(String.format(DQUERY_BATCH_QUERY_TOKEN_API, dqueryServiceBaseUrl), requestDTO)
            .filter(res -> res.getStatusCode().is2xxSuccessful() && res.getBody() != null)
            .map(response -> {
                ApiResponse<List<TokenPriceDTO>> tokenPriceDTO =
                    JacksonUtils.deserialize(response.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {
                    });
                if (tokenPriceDTO != null && tokenPriceDTO.getData() != null) {
                    return tokenPriceDTO.getData();
                }
                return List.<TokenPriceDTO>of();
            })
            .retryWhen(Retry.backoff(2, Duration.of(3000, ChronoUnit.MILLIS)))
            .publishOn(Schedulers.boundedElastic())
            .doOnError(throwable -> log.error("dquery getPairList failed", throwable));
    }

}
