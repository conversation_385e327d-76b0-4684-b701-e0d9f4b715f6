package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2024/6/27
 */
@Repository
public interface PortfolioMultiRepository extends ReactiveMongoRepository<PortfolioMultiEntity, ObjectId>,
    ReactiveCrudRepository<PortfolioMultiEntity, ObjectId>, PortfolioMultiRepositoryCustom {

    Flux<PortfolioMultiEntity> findAllByUserId(ObjectId userId);

    Mono<PortfolioMultiEntity> findFirstByUserIdAndPortfolioSourceId(ObjectId userId, String sourceId);

    Mono<PortfolioMultiEntity> findFirstByIdAndUserId(ObjectId sourceId, ObjectId userId);

}
