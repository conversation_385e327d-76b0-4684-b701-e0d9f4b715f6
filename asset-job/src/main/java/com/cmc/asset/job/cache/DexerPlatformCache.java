package com.cmc.asset.job.cache;

import com.cmc.asset.job.service.dex.DexerService;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/3/12 12:44
 */
@Slf4j
@Component
public class DexerPlatformCache extends BaseLocalCache<String, Map<Integer, PlatformNewDTO>> {

    @Autowired
    private DexerService dexerService;

    protected DexerPlatformCache(@Value("${com.cmc.asset.cache.dex-platform.min-refresh-seconds:3600}") Integer minRefreshSeconds) {
        super(RandomUtils.nextInt(minRefreshSeconds, minRefreshSeconds * 2), TimeUnit.SECONDS);
    }

    @Override
    protected void preLoad() {
        log.info("Preload the crypto cache data.");
        getPlatforms();
    }

    @SneakyThrows
    public Map<Integer, PlatformNewDTO> getPlatforms() {
        return loadingCache.get(StringUtils.EMPTY);
    }

    @SneakyThrows
    public Map<Integer, PlatformNewDTO> getVisibleOnDexScanPlatforms() {
        return loadingCache.get(StringUtils.EMPTY)
                .values()
                .stream()
                .filter(PlatformNewDTO::getV)
                .collect(Collectors.toMap(PlatformNewDTO::getId, PlatformNewDTO -> PlatformNewDTO));
    }

    @Override
    protected Map<Integer, PlatformNewDTO> load(String key) {
        return dexerService.getPlatformsFromDeQuery()
                .map(list -> list.stream()
                        .peek(p -> p.setDn(p.getDn().toLowerCase()))
                        .collect(Collectors.toMap(PlatformNewDTO::getId, PlatformNewDTO -> PlatformNewDTO)))
                .block(Duration.ofSeconds(30));
    }

}
