package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.userpoint.UserPointEntity;
import org.springframework.data.mongodb.core.query.Criteria;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/8/5 下午2:20
 */
public interface UserPointRepositoryCustom {

    Flux<UserPointEntity> pageQueryByCondition(Criteria criteria, int pageNo, int pageSize);

    Mono<Long> countByCondition(Criteria criteria);
}
