package com.cmc.asset.job.service.portfolio.manager;

import static com.cmc.asset.model.common.Constant.CHAIN_SOLANA_DECIMALS;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.BlockChainTokenPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionPO;
import com.cmc.asset.dao.entity.portfolio.ChainTransactionTokenPO;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfigV2;
import com.cmc.asset.job.integration.BlockBookClient;
import com.cmc.asset.job.integration.response.blockbook.AddressBalanceResponse.AccountChainInfo;
import com.cmc.asset.job.integration.response.blockbook.AddressBalanceResponse.AccountChainTokenInfo;
import com.cmc.asset.job.integration.response.blockbook.TransactionResponse;
import com.cmc.asset.job.integration.response.blockbook.XpubResponse;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.asset.model.enums.PortfolioTokenTypeEnum;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.MathUtils;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * 基于okClient的处理类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BlockBookBalanceManager extends AbstractWalletBalanceService{

    @Autowired
    private BlockBookClient blockBookClient;

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Autowired
    private DynamicApolloRefreshConfigV2 dynamicApolloRefreshConfigV2;

    @Value("${com.cmc.asset.job.portfolio.transaction-limit:500}")
    private Integer transactionLimit;


    public Mono<BlockChainPO> stakeAddressBalanceAsync(Integer blockBookId, String address, Date currentTaskTime) {
        return stakeAddressBalance(blockBookId, address, currentTaskTime)
            .filter(CollectionUtils::isNotEmpty)
            .map(list -> list.get(0));
    }

    public Mono<List<BlockChainPO>> stakeAddressBalance(Integer blockBookId, String address, Date currentTaskTime) {
        return blockBookClient.associatedAddresses(address)
            .filter(associatedResponse -> associatedResponse != null && CollectionUtils.isNotEmpty(associatedResponse.getAddresses()))
            .flatMap(associatedResponse -> {
                List<String> addressList = associatedResponse.getAddresses();
                BigDecimal rewards = getOrZero(associatedResponse.getRewards());
                Integer cryptoId = getNativeTokenId(blockBookId);
                CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(cryptoId);
                if (rewards.compareTo(BigDecimal.ZERO) > 0 && cryptoId != null && currencyInfoDTO != null ) {
                    BlockChainTokenPO nativeToken = BlockChainTokenPO.builder().cryptoId(cryptoId).canMap(true)
                        .type(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode())
                        .amount(decimalFormat128(rewards)).cryptoName(currencyInfoDTO.getName()).build();

                    BlockChainPO blockChain = BlockChainPO.builder()
                        .chainId(blockBookId)
                        .syncTime(currentTaskTime)
                        .nativeTokenId(cryptoId)
                        .tokens(List.of(nativeToken))
                        .build();

                    Mono<BlockChainPO> blockChainPOMono = calTotalTokenValue(blockChain)
                        .defaultIfEmpty(BlockChainPO.builder().build());

                    Mono<List<BlockChainPO>> blockChainPOsMono = Flux.fromIterable(addressList)
                        .flatMap(realAddress -> monoAddressBalanceV2(blockBookId, realAddress, currentTaskTime))
                        .collectList()
                        .defaultIfEmpty(Lists.newArrayList());

                    return Mono.zip(blockChainPOMono, blockChainPOsMono)
                        .map(tuple2 -> {
                            BlockChainPO po = tuple2.getT1();
                            List<BlockChainPO> pos = tuple2.getT2();
                            if(po.getChainId() != null){
                                pos.add(po);
                            }
                            return mergeBlockChain(pos);
                        });
                }else {
                    return Flux.fromIterable(addressList)
                        .flatMap(realAddress -> monoAddressBalanceV2(blockBookId, realAddress, currentTaskTime))
                        .collectList()
                        .map(this::mergeBlockChain);
                }
            });
    }

    private Mono<BlockChainPO> monoAddressBalanceV2(Integer blockBookId, String address, Date currentTaskTime) {
        return addressSingleChainBalanceV2(blockBookId, address, currentTaskTime)
            .subscribeOn(Schedulers.boundedElastic());
    }

    private List<BlockChainPO> mergeBlockChain(List<BlockChainPO> allList) {
       HashMap<Integer,BlockChainPO> map = new HashMap<>();
        for (BlockChainPO blockChain : allList) {
            BlockChainPO mapCache = map.get(blockChain.getChainId());
            if(mapCache == null ){
                map.put(blockChain.getChainId(),blockChain);
            }else{
                map.put(blockChain.getChainId(),mergeBlockChainTokens(mapCache,blockChain));
            }
        }

        List<BlockChainPO> chainList = new ArrayList<>(map.values())
           .stream()
           .peek(e -> e.setTokens(e.getTokens().stream().filter(e1 -> e1.getValue().compareTo(BigDecimal.ONE) >= 0)
               .collect(Collectors.toList())))
           .collect(Collectors.toList());
        return chainList;
    }

    private BlockChainPO mergeBlockChainTokens(BlockChainPO mapChain,BlockChainPO chain) {
        //汇总totalTokenAmount
        mapChain.setTotalTokenAmount(
            decimalFormat128(mapChain.getTotalTokenAmount()).add(decimalFormat128(chain.getTotalTokenAmount())));
        Map<Integer, BlockChainTokenPO> mapToken = mapChain.getTokens().stream().filter(e -> e.getCryptoId() != null)
            .collect(Collectors.toMap(BlockChainTokenPO::getCryptoId, x -> x));
        Map<Integer, BlockChainTokenPO> chainToken = chain.getTokens().stream().filter(e -> e.getCryptoId() != null)
            .collect(Collectors.toMap(BlockChainTokenPO::getCryptoId, x -> x));
        //汇总list
        mapToken.forEach((k, v) -> chainToken.merge(k, v,
            (entity1, entity2) -> BlockChainTokenPO.builder().cryptoId(entity1.getCryptoId())
                .amount(entity1.getAmount()).cryptoName(entity1.getCryptoName())
                .value(decimalFormat128(entity1.getValue()).add(decimalFormat128(entity2.getValue())))
                .type(entity1.getType())
                .canMap(entity1.getCanMap()).changeUsd24h(entity1.getChangeUsd24h())
                .contractAddress(entity1.getContractAddress()).price(entity1.getPrice())
                .amount(decimalFormat128(entity1.getAmount()).add(decimalFormat128(entity2.getAmount()))).build()));
        mapChain.setTokens(new ArrayList<>(chainToken.values()));
        return mapChain;
    }

    public Mono<BlockChainPO> addressSingleChainBalanceV2(Integer chainId, String address, Date currentTaskTime) {
        return addressBalanceV2(Set.of(chainId), address, currentTaskTime)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0));
    }

    public Mono<List<BlockChainPO>> addressBalanceV2(Set<Integer> blockBookIds, String address, Date currentTaskTime) {
        return blockBookClient.accountBalance(address)
            .filter(CollectionUtils::isNotEmpty)
            .flatMapMany(Flux::fromIterable)
            .collectMap(k -> getChainIdByPlatformId(k.getPlatformId()), x -> x)
            .flatMapMany(map -> Flux.fromIterable(blockBookIds)
                .flatMap(chain -> {
                    BlockChainPO blockChain = buildBlockChain(chain, map.get(chain), currentTaskTime);
                    if (blockChain == null) {
                        return Mono.empty();
                    }
                    return calTotalTokenValue(blockChain);
                })
            )
            .filter(Objects::nonNull)
            .collectList();
    }

    public Mono<ChainTransactionPO> getChainTransaction(Integer chainId, String address) {
        return blockBookClient.getSolanaTransaction(address, transactionLimit)
                .flatMap(dtos -> this.convertChainTransaction(chainId, address, dtos))
                .defaultIfEmpty(List.of())
                .onErrorResume(e -> {
                    log.error("getChainTransaction error,chainId:{},address:{}", chainId, address, e);
                    return Mono.just(List.of());
                })
                .map(transactions -> ChainTransactionPO.builder()
                        .chainId(chainId)
                        .tokenTransactions(transactions)
                        .build());
    }

    public Mono<XpubResponse> getXpubResult(Integer chainId, String address){
        return blockBookClient.getBtcXpub(chainId, address);
    }

    private Mono<List<ChainTransactionTokenPO>> convertChainTransaction(Integer chainId, String address, List<TransactionResponse.BlockBookTransactionDTO> transactions) {
        return Flux.fromIterable(transactions)
                .filter(TransactionResponse.BlockBookTransactionDTO::getStatus)
                .flatMap(transaction -> {
                    String txId = transaction.getTxHash();
                    String height = transaction.getHeight();
                    String transactionTime = String.valueOf(transaction.getTimestamp() / 1000L);
                    String state = transaction.getStatus() ? "success" : "fail";
                    String from = transaction.getFrom();
                    String to = transaction.getTo();

                    return Flux.fromIterable(transaction.getBalanceChange())
                            .flatMap(balanceChange -> {
                                BigDecimal amount = BigDecimal.valueOf(balanceChange.getAmount(), balanceChange.getDecimals());
                                String transactionSymbol = balanceChange.getSymbol();
                                String tokenAddress = balanceChange.getAddress();

                                String transactionType;
                                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                                    transactionType = "out";
                                } else if (amount.compareTo(BigDecimal.ZERO) > 0) {
                                    transactionType = "in";
                                } else {
                                    transactionType = null;
                                }
                                if (transactionType == null) {
                                    return Mono.empty();
                                }
                                Integer cryptoId = cryptoCurrencyCache.getIdByContractAddress(chainId, tokenAddress);

                                return Mono.just(ChainTransactionTokenPO.builder()
                                    .txId(txId)
                                    .transactionType(transactionType)
                                    .cryptoId(cryptoId)
                                    .transactionSymbol(transactionSymbol)
                                    .height(height)
                                    .transactionTime(transactionTime)
                                    .transactionAddress(address.equals(from) ? to : from)
                                    .amount(amount.abs())
                                    .txFee(BigDecimal.valueOf(transaction.getFee(), CHAIN_SOLANA_DECIMALS))
                                    .state(state)
                                    .build());
                            })
                            .filter(Objects::nonNull);
                })
                .collectList();
    }

    private BlockChainPO buildBlockChain(Integer chain,AccountChainInfo accountChainInfo,Date currentTaskTime) {
        int nativeTokenId = getNativeTokenId(chain);
        CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(nativeTokenId);
        if(currencyInfoDTO == null){
            return null;
        }
        BlockChainPO blockChain= BlockChainPO.builder().chainId(chain).nativeTokenId(nativeTokenId).syncTime(currentTaskTime).build();
        blockChain.setTokens(getBlockChainTokens(chain, currencyInfoDTO, accountChainInfo));
        return blockChain;
    }

    private List<BlockChainTokenPO> getBlockChainTokens(Integer platformId, CryptoCurrencyInfoDTO nativeTokenInfo,AccountChainInfo accountChainInfo){
        List<BlockChainTokenPO> blockChainTokens = new ArrayList<>();

        //添加nativeToken
        if(decimalFormat128(new BigDecimal(accountChainInfo.getBalance())).compareTo(BigDecimal.ZERO) > 0){
            BlockChainTokenPO nativeToken = BlockChainTokenPO.builder()
                    .cryptoId(nativeTokenInfo.getId())
                    .canMap(true)
                    .type(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode())
                    .amount(decimalFormat128(new BigDecimal(accountChainInfo.getBalance())))
                    .cryptoName(nativeTokenInfo.getName())
                    .build();
            blockChainTokens.add(nativeToken);
        }
        //添加erc20
        if (CollectionUtils.isNotEmpty(accountChainInfo.getTokens())) {
            List<BlockChainTokenPO> tokens = accountChainInfo.getTokens()
                    .stream()
                    .map(info -> this.convertChainToken(platformId, info))
                    .filter(
                            Objects::nonNull)
                    .collect(Collectors.toList());
            blockChainTokens.addAll(tokens);
        }
        return blockChainTokens;
    }

    private BlockChainTokenPO convertChainToken(Integer platformId, AccountChainTokenInfo outerToken) {
        String contractAddress = outerToken.getContract().toLowerCase();
        Integer cryptoId = cryptoCurrencyCache.getIdByContractAddress(platformId, contractAddress);
        CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(cryptoId);
        if (currencyInfoDTO == null) {
            return BlockChainTokenPO.builder().canMap(false)
                    .type(PortfolioTokenTypeEnum.UNTRACKED_TOKEN.getCode())
                    .contractAddress(contractAddress)
                    .amount(decimalFormat128(getOrZero(outerToken.getBalance()))).build();
        }
        //当crypto可以映射cmcId,则price value与change24h不保存,展示时获取最新的数据
        return BlockChainTokenPO.builder()
                .canMap(true)
                .type(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode())
                .contractAddress(contractAddress)
                .cryptoId(cryptoId)
                .cryptoName(currencyInfoDTO.getName())
                .amount(decimalFormat128(getOrZero(outerToken.getBalance())))
                .build();
    }


    private Integer getChainIdByPlatformId(String platformId) {
        return dynamicApolloRefreshConfigV2.getBlockBookChainMap().get(platformId);
    }

    private Integer getNativeTokenId(Integer chainId){
        return dynamicApolloRefreshConfigV2.getCmcChainInfoIdMap().get(chainId).getNativeTokenId();
    }


    /**
     * 格式化decimal128
     */
    private BigDecimal decimalFormat128(BigDecimal date){
        String formatDecimal = MathUtils.getOrZero(date).stripTrailingZeros().toPlainString();
        return new BigDecimal(formatDecimal).round(MathContext.DECIMAL128);
    }

    private BigDecimal getOrZero(String value){
        return StringUtil.isBlank(value)?BigDecimal.ZERO:new BigDecimal(value);
    }


}
