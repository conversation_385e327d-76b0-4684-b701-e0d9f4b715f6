package com.cmc.asset.job.service.watch.impl;

import com.cmc.asset.job.repository.mongo.asset.GuestWatchlistRepository;
import com.cmc.asset.job.repository.mongo.asset.entity.GuestWatchListEntity;
import com.cmc.asset.job.service.watch.AbstractWatchRefreshService;
import com.cmc.asset.model.contract.dexer.DexTokenDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.framework.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * GuestWatchDexTokenRefresh
 * <AUTHOR> ricky.x
 * @date: 2025/6/9 23:47
 */
@Service
@Slf4j
public class GuestWatchDexTokenRefresh extends AbstractWatchRefreshService<GuestWatchListEntity> {

    @Autowired
    private GuestWatchlistRepository guestWatchlistRepository;


    @Override
    protected Mono<List<GuestWatchListEntity>> queryPendingData(ObjectId startId, ObjectId endId, ObjectId latestId, int limit) {
        return Mono.fromCallable(() -> guestWatchlistRepository.findByPlatformByCursor(startId,endId,latestId,limit))
                .publishOn(Schedulers.boundedElastic());
    }

    @Override
    protected Mono<Tuple2<Integer, Optional<ObjectId>>> processAndSave(List<GuestWatchListEntity> entities) {
        return getAndFillDexToken(entities)
                .flatMap(list -> guestWatchlistRepository.batchUpdateDexToken(list))
                .map(e -> Tuples.of(entities.size(), Optional.of(entities.get(entities.size() - 1).getId())));
    }


    /**
     * 填充 DEX token 信息
     * @param entities WatchListEntity 列表
     * @return 填充后的 WatchListEntity 列表
     */
    private Mono<List<GuestWatchListEntity>> getAndFillDexToken(List<GuestWatchListEntity> entities) {
        Set<Long> distinctDexPairIds = extractDistinctDexPairIds(entities);
        if (CollectionUtils.isEmpty(distinctDexPairIds)) {
            return Mono.just(entities);
        }
        return super.transPoolIdToDexTokens(new ArrayList<>(distinctDexPairIds))
                .map(pairInfoMap -> fillEntitiesWithDexTokens(entities, pairInfoMap));
    }


    private Set<Long> extractDistinctDexPairIds(List<GuestWatchListEntity> entities) {
        return entities.stream()
                .filter(Objects::nonNull)
                .flatMap(entity -> Optional.ofNullable(entity.getDexPairs())
                        .orElse(Collections.emptyList())
                        .stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


    private List<GuestWatchListEntity> fillEntitiesWithDexTokens(List<GuestWatchListEntity> entities, Map<Long, PairInfoDTO> pairInfoMap) {
        if(CollectionUtils.isEmpty(pairInfoMap)){
            return List.of();
        }
        return entities.stream()
                .filter(Objects::nonNull)
                .map(entity -> fillEntityWithDexTokens(entity, pairInfoMap))
                .collect(Collectors.toList());
    }


    private GuestWatchListEntity fillEntityWithDexTokens(GuestWatchListEntity entity, Map<Long, PairInfoDTO> pairInfoMap) {
        if (CollectionUtils.isEmpty(entity.getDexPairs())) {
            return entity;
        }
        List<String> pairTransToken = entity.getDexPairs().stream()
                .map(pairId -> convertPairIdToDexToken(pairId, pairInfoMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> existToken = entity.getDexTokens();
        List<String> finalTokenList = Stream.concat(
                        pairTransToken.stream(),
                        Optional.ofNullable(existToken).orElse(Collections.emptyList()).stream()
                )
                .distinct()
                .collect(Collectors.toList());
        entity.setDexTokens(finalTokenList);
        return entity;
    }

    private String convertPairIdToDexToken(Long pairId, Map<Long, PairInfoDTO> pairInfoMap) {
        return Optional.ofNullable(pairInfoMap.get(pairId))
                .map(pairInfo -> {
                    DexTokenDTO baseToken = pairInfo.getBaseToken();
                    if (baseToken == null || StringUtils.isEmpty(baseToken.getAddress())) {
                        return null;
                    }
                    return pairInfo.getPlatformId() + "_" + baseToken.getAddress();
                })
                .orElse(null);
    }


}
