package com.cmc.asset.job.constants;

/**
 * <AUTHOR>
 * @since 2021/9/16
 **/
public interface Constants {

    String ENV_STAGING = "staging";
    String ENV_LOCAL = "local";
    String ENV_PROPERTY_NAME = "local_env";

    /**
     * leaderboard 100 rank
     */
    String USER_POINT_100_RANK_LIST = "user_point_100_rank_list";

    /**
     * leaderboard 10K rank map
     */
    String USER_POINT_10K_RANK_MAP = "user_point_10K_rank_map";


    /**
     * leaderboard rank time
     */
    String LEADERBOARD_RANK_TIME = "leader_board_rank_time";

    /**
     * affiliate leaderboard 100 rank
     */
    String AFFILIATE_POINT_100_RANK_LIST = "affiliate_point_100_rank_list";

    /**
     * affiliate leaderboard 100 rank map
     */
    String AFFILIATE_POINT_100_RANK_MAP = "affiliate_point_100_rank_map";

    /**
     * affiliate leaderboard rank time
     */
    String AFFILIATE_LEADERBOARD_RANK_TIME = "affiliate_leader_board_rank_time";

    /**
     * percent_asc_crypto_currency
     */
    String PERCENT_ASC_CRYPTO_CURRENCY = "v3:cr:percent_usd_24_asc:set";

    String LANGUAGE_EN = "en";

    Integer NEW_COIN_ALERT_TOPIC_ID = 26;

    String WATCHLIST_KAFKA_TOPIC = "watchlist_notification";

    String WALLET_SYNC_ASSET_LOCK = "v3:wallet:asset:sync:lock:%s";
    String WALLET_SYNC_TRANSACTION_LOCK = "v3:wallet:transaction:sync:lock:%s";
}
