package com.cmc.asset.job.repository.mongo.asset.entity;

import com.cmc.asset.dao.BaseMongoEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/10/17 16:17
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "user_watchlist")
public class WatchListEntity extends BaseMongoEntity<ObjectId> {
    @Id
    private ObjectId id;

    private Long uid;

    private String userId;

    private Set<Integer> cryptos;

    private Boolean main;

    /**
     * dex pairs
     */
    private List<Long> dexPairs;



    /**
     * 交易对
     */
    private List<String> dexTokens;
}
