package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.job.entity.priceprediction.PricePredictionUserStatisticEntity;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/8/5 14:45:13
 */
@Repository
public interface PricePredictionUserStatisticRepository {

    List<PricePredictionUserStatisticEntity> getTopOrderByScore(Integer statisticTime, Integer currentPage, Integer size);

    /**
     * 一个userId每个statisticTime 只有一条记录,存在就更新,不存在就插入
     *
     * @param entities
     */
    void saveAll(List<PricePredictionUserStatisticEntity> entities);
}
