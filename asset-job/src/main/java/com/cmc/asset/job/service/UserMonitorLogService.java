package com.cmc.asset.job.service;

import com.cmc.asset.dao.entity.log.UserMonitorLogEntity;
import com.cmc.asset.job.repository.mongo.asset.UserMonitorLogRepository;
import com.cmc.asset.model.user.UserMonitorDTO;
import com.cmc.data.common.utils.JacksonUtils;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2023/4/18
 **/
@Service
@Slf4j
public class UserMonitorLogService {

    @Autowired
    private UserMonitorLogRepository userMonitorLogRepository;

    public Mono<UserMonitorDTO> saveUserMonitorLog(UserMonitorDTO monitor) {

        if (StringUtils.isEmpty(monitor.getSourceId())) {
            log.info("skip saving log -> no resource id found for monitor log {}", JacksonUtils.toJsonString(monitor));
            return Mono.just(UserMonitorDTO.empty());
        }

        Date now = new Date();
        UserMonitorLogEntity entity = UserMonitorLogEntity
                .builder()
                .ip(monitor.getIp())
                .sourceType(monitor.getSourceType())
                .flowId(monitor.getFlowId())
                .sourceId(monitor.getSourceId())
                .fvideoid(monitor.getFvideoid())
                .userId(monitor.getUserId())
                .countryCode(monitor.getCountryCode())
                .createTime(now)
                .updateTime(now)
                .build();

        return userMonitorLogRepository.insert(entity)
                .map(result -> UserMonitorDTO
                        .builder()
                        .ip(result.getIp())
                        .sourceType(result.getSourceType())
                        .sourceId(result.getSourceId())
                        .fvideoid(result.getFvideoid())
                        .userId(result.getUserId())
                        .countryCode(result.getCountryCode())
                        .build()
                );
    }
}
