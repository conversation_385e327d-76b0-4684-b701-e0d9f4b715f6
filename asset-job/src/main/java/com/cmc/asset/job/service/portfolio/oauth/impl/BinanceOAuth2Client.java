package com.cmc.asset.job.service.portfolio.oauth.impl;

import static com.cmc.asset.model.common.Constant.BEARER_TOKEN_PREFIX;

import com.cmc.asset.dao.entity.portfolio.AccessToken;
import com.cmc.asset.domain.portfolio.thirdparty.GetAccessTokenResponseDTO;
import com.cmc.asset.domain.portfolio.thirdparty.ThirdPartyAssetDTO;
import com.cmc.asset.domain.portfolio.thirdparty.ThirdPartyUserInfoDTO;
import com.cmc.asset.job.config.OAuth2Config;
import com.cmc.asset.job.integration.BinanceOAuth2ApiClient;
import com.cmc.asset.model.enums.OAuth2ProviderType;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
public class BinanceOAuth2Client extends AbstractOAuth2Client {

    @Value("${com.cmc.asset.job.service.portfolio.oauth.refresh-error-code:invalid_grant}")
    private List<String> refreshErrorCodeList;

    private BinanceOAuth2ApiClient binanceOAuth2ApiClient;

    public BinanceOAuth2Client(OAuth2Config properties, BinanceOAuth2ApiClient binanceOAuth2ApiClient) {
        super(properties, binanceOAuth2ApiClient);
        this.binanceOAuth2ApiClient = binanceOAuth2ApiClient;
    }

    @Override
    public OAuth2ProviderType getProvider() {
        return OAuth2ProviderType.BINANCE;
    }

    @Override
    public Mono<ThirdPartyUserInfoDTO> getUserInfo(String accessToken) {
        return binanceOAuth2ApiClient.getUserInfo(this.provider.getUserInfoUri(), Map.of(), Map.of("Authorization", BEARER_TOKEN_PREFIX + accessToken))
            .map(binanceUserInfoDTO -> ThirdPartyUserInfoDTO.builder()
                .openId(binanceUserInfoDTO.getUserId())
                .email(binanceUserInfoDTO.getEmail())
                .build());
    }

    @Override
    public Mono<GetAccessTokenResponseDTO> refreshAccessToken(AccessToken token) {
        return super.refreshAccessToken(token)
                .map(getAccessTokenResponseDTO -> {
                    if (StringUtils.isNotBlank(getAccessTokenResponseDTO.getError()) && CollectionUtils.isNotEmpty(refreshErrorCodeList) && refreshErrorCodeList.stream()
                            .anyMatch(errorMsg -> StringUtils.equalsIgnoreCase(errorMsg, getAccessTokenResponseDTO.getError()))) {
                        getAccessTokenResponseDTO.setValid(false);
                    }
                    return getAccessTokenResponseDTO;
                });
    }

    @Override
    public Mono<List<ThirdPartyAssetDTO>> getAsset(String accessToken, String openId) {
        return binanceOAuth2ApiClient.getAsset(this.provider.getAssetUri(), Map.of("access_token", accessToken),
                        Map.of("Authorization", BEARER_TOKEN_PREFIX + accessToken, "x-user-id", openId))
                .map(binanceAssetResponseDTO -> {
                    if (CollectionUtils.isEmpty(binanceAssetResponseDTO)) {
                        return List.<ThirdPartyAssetDTO>of();
                    }
                    return binanceAssetResponseDTO.stream()
                            .map(asset -> ThirdPartyAssetDTO.builder()
                                    .cryptoName(asset.getAssetName())
                                    .cryptoSymbol(asset.getAsset())
                                    .balance(new BigDecimal(asset.getAmount()))
                                    .value(new BigDecimal(asset.getValuationAmount()))
                                    .build())
                            .collect(Collectors.toList());
                })
                .defaultIfEmpty(List.of());
    }

}