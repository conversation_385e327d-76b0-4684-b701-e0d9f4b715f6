package com.cmc.asset.job.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortfolioThirdPartyJobData {
    private Date startTime;
    private Date endTime;
    private String thirdPartyId;
    private Boolean cleanHistoryData;
}
