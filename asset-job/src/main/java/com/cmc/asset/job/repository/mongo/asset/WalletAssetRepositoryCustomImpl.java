package com.cmc.asset.job.repository.mongo.asset;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.or;

import com.cmc.asset.job.repository.mongo.UpdateUtils;
import com.cmc.asset.dao.entity.WalletAssetEntity;
import com.cmc.framework.utils.CollectionUtils;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuples;

/**
 * @Description
 * <AUTHOR> Z
 * @CreateTime 2024-07-23
 */
@Repository
public class WalletAssetRepositoryCustomImpl implements WalletAssetRepositoryCustom {

    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Override
    public Mono<Boolean> batchInsertOrUpdate(List<WalletAssetEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Mono.just(true);
        }

        UpdateOptions updateOptions = new UpdateOptions();
        updateOptions.upsert(true);
        return reactiveMongoTemplate.execute(WalletAssetEntity.class,
            collection -> Mono.justOrEmpty(entityList)
                .map(entities -> entities.stream()
                    .map(m -> Tuples.of(m,
                        UpdateUtils.applyUpdate(reactiveMongoTemplate.getConverter(), new Update(), m, true)))
                    .map(m -> new UpdateOneModel<Document>(
                        or(
                            eq("_id", m.getT1().getId()),
                            and(
                                eq("walletAddress", m.getT1().getWalletAddress())
                            )
                        ),
                        m.getT2().getUpdateObject(), updateOptions)
                    )
                ).flatMapMany(u -> collection.bulkWrite(u.collect(Collectors.toList()), new BulkWriteOptions().ordered(false)))
        ).collectList()
            .thenReturn(Boolean.TRUE);
    }
}
