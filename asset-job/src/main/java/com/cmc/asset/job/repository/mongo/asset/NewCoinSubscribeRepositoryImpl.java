package com.cmc.asset.job.repository.mongo.asset;

import com.cmc.asset.dao.entity.NewCoinSubscribeEntity;
import com.cmc.framework.utils.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * new coin subscribe repository
 *
 * <AUTHOR>
 * @date 2024/4/5 16:17
 */
@Repository
public class NewCoinSubscribeRepositoryImpl implements NewCoinSubscribeRepositoryCustom {

    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Override
    public Flux<NewCoinSubscribeEntity> findByDataId(String dataId, String id, int limit) {
        Criteria criteria = Criteria.where("newCoinDataId").is(dataId);
        if (StringUtils.isNotEmpty(id)) {
            criteria.and("id").lt(new ObjectId(id));
        }
        return reactiveMongoTemplate.find(Query.query(criteria).with(Sort.by(Sort.Order.desc("_id"))).limit(limit),
            NewCoinSubscribeEntity.class);
    }

    @Override
    public Mono<Boolean> updatePushTimeByDataId(String dataId, Date pushTime) {
        Criteria criteria = Criteria.where("newCoinDataId").is(dataId);

        Update update = new Update();
        update.set("pushTime", pushTime);
        return reactiveMongoTemplate.updateMulti(Query.query(criteria), update, NewCoinSubscribeEntity.class)
            .map(result -> result.getModifiedCount() > 0);
    }
}
