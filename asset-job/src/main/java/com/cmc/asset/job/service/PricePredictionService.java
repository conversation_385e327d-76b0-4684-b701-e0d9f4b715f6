package com.cmc.asset.job.service;

import com.cmc.asset.job.repository.mysql.po.CryptoCurrencyDatapointPO;
import com.cmc.asset.job.service.adapter.CurrencyDataPointV3QueryAdapter;
import com.cmc.asset.job.utils.RedisUtils;
import com.cmc.asset.model.common.RedisConstants;
import com.cmc.data.common.utils.MathUtils;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/1/13 下午7:51
 * @description
 */
@Service
@Slf4j
public class PricePredictionService {
    @Autowired
    @Qualifier("baseRedisUtils")
    private RedisUtils baseRedisUtils;

    @Autowired
    private CurrencyDataPointV3QueryAdapter currencyDataPointV3QueryAdapter;

    private static final BigDecimal DEF_ACCURACY = BigDecimal.ZERO;

    public BigDecimal getAccuracy(BigDecimal priceUsd, BigDecimal estimatePrice) {
        if (null == priceUsd || null == estimatePrice) {
            return DEF_ACCURACY;
        }
        if (priceUsd.compareTo(BigDecimal.ZERO) == 0 && estimatePrice.compareTo(BigDecimal.ZERO) != 0) {
            return DEF_ACCURACY;   //如果价格为0,无论估计多少,accuracy都为1,此处设定默认值
        }
        BigDecimal deviation = priceUsd.subtract(estimatePrice).abs();  //计算绝对值
        // 1- deviation/priceUsd = 准确率
        BigDecimal accuracy = BigDecimal.ONE.subtract(MathUtils.divideWithScale(deviation, priceUsd, 4));
        if (accuracy.compareTo(BigDecimal.ZERO) < 0) {  //估价差距较大,会有负数,设置默认值 0
            accuracy = DEF_ACCURACY;
        }
        return accuracy;
    }

    public BigDecimal getPriceUsdByMonth(Integer cryptoId, String date) {
        String key = String
            .format(RedisConstants.KEY_LAST_MONTH_CLOSING_PRICE, date);  // 每个月一个key: v3:last:month:closing:price:202106
        String hkey = cryptoId.toString();
        String price = baseRedisUtils.hget(key, hkey);
        if (StringUtils.isBlank(price)) {
            BigDecimal priceUsd = getPriceUsdByDB(cryptoId, date);    //缓存没有从db取
            if (priceUsd != null) {
                baseRedisUtils.hset(key, hkey, priceUsd.toString());    //更新到缓存,其他用户的计算可能用到
                return priceUsd;
            } else {
                log.error("PricePredictionServiceImpl,getPriceUsd is null,cryptoId={},date={}", cryptoId, date);
                return null;
            }
        }
        return new BigDecimal(price);
    }

    private BigDecimal getPriceUsdByDB(Integer cryptoId, String date) {
        DateTimeFormatter fmt =
            new DateTimeFormatterBuilder().appendPattern("yyyyMM").parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                .toFormatter();    // 添加默认日期
        LocalDate statisticTime = LocalDate.parse(date, fmt);    //支持手动传递参数

        LocalDateTime startTime =
            statisticTime.atStartOfDay().with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(55)
                .withSecond(01);
        Date startTimeDate = Date.from(startTime.atZone(ZoneId.from(ZoneOffset.UTC)).toInstant());
        LocalDateTime endTime =
            statisticTime.atStartOfDay().with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59)
                .withSecond(59);
        Date endTimeDate = Date.from(endTime.atZone(ZoneId.from(ZoneOffset.UTC)).toInstant());
        List<CryptoCurrencyDatapointPO> datapoints = currencyDataPointV3QueryAdapter
            .selectVolumeAndPriceByTimeRange(cryptoId, startTimeDate, endTimeDate);    //取每个币指定月末月最后5分钟的价格
        List<CryptoCurrencyDatapointPO> list =
            datapoints.stream().sorted(Comparator.comparing(CryptoCurrencyDatapointPO::getCreateTime).reversed())
                .collect(Collectors.toList());   //1个币最后5分钟的数据可能有多条记录,按时间倒序取最后一条.
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0).getPriceUsd();
    }
}
