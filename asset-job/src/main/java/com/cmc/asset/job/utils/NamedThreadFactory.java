package com.cmc.asset.job.utils;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.lang3.StringUtils;

/**
 * 迁移cmc-util快照包中的util类
 * <AUTHOR> ricky.x
 * @date : 2023/8/20 13:46
 */
public class NamedThreadFactory implements ThreadFactory {
    private final AtomicInteger threadCount = new AtomicInteger(0);
    private String prefix;

    public NamedThreadFactory(String prefix) {
        if (StringUtils.isEmpty(prefix)) {
            throw new IllegalArgumentException("prefix should not be null or empty.");
        } else {
            this.prefix = prefix;
        }
    }

    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r);
        String var10001 = this.prefix;
        thread.setName(var10001 + this.threadCount.getAndAdd(1));
        return thread;
    }
}
