package com.cmc.asset.job.service.router.strategy;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.job.service.portfolio.manager.OkLinkBalanceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/5/14 18:17
 */
@Component
@Slf4j
public class OkLinkFetchStrategy implements FetchStrategy {

    @Autowired
    private OkLinkBalanceManager okLinkBalanceManager;

    @Override
    public Mono<BlockChainPO> fetch(Integer chainId, String address, Date currentTaskTime) {
        return okLinkBalanceManager.getChainFromOk(chainId, address, currentTaskTime)
            .onErrorResume(e -> {
                log.error("OkLinkFetchStrategy fetch error chainId:{},address:{},e:", chainId, address, e);
                return Mono.empty();
            });
    }

}
