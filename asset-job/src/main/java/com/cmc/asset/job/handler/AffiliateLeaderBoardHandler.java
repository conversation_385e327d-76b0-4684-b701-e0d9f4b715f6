package com.cmc.asset.job.handler;

import com.cmc.asset.dao.entity.referral.UserReferralSummaryEntity;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.repository.mongo.asset.UserReferralSummaryRepository;
import com.cmc.asset.job.repository.mongo.portal.entity.CmcUserEntity;
import com.cmc.asset.job.repository.mongo.portal.repository.CmcUserRepository;
import com.cmc.asset.job.repository.redis.AssetCacheRedisRepository;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.asset.model.contract.referral.AffiliateLeaderBoardDTO;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cmc.asset.job.constants.Constants.AFFILIATE_LEADERBOARD_RANK_TIME;
import static com.cmc.asset.job.constants.Constants.AFFILIATE_POINT_100_RANK_LIST;
import static com.cmc.asset.job.constants.Constants.AFFILIATE_POINT_100_RANK_MAP;

/**
 * @ClassName AffiliateLeaderBoardHandler.java
 * <AUTHOR>
 * @Date 2021/9/27 15:28
 */
@Slf4j
@Component
public class AffiliateLeaderBoardHandler implements JobHandler<Object, Object> {

    @Autowired(required = false)
    private AssetCacheRedisRepository assetCacheRedisRepository;

    @Autowired
    private UserReferralSummaryRepository userReferralSummaryRepository;

    @Autowired(required = false)
    private CmcUserRepository cmcUserRepository;

    @Override
    public JobHandlingResult<Object> handle(Object jobData) {
        log.info("AffiliateLeaderBoardHandler.handle start");
        Set<ZSetOperations.TypedTuple<String>> top100Set = Sets.newHashSet();
        Map<String, String> top100RankMap = Maps.newHashMap();
        List<UserReferralSummaryEntity> userReferralSummaryEntities = userReferralSummaryRepository
            .findByReferralAwardGreaterThanOrderByReferralAwardDesc(0, PageRequest.of(0, 100));
        if (CollectionUtils.isNotEmpty(userReferralSummaryEntities)) {
            List<CmcUserEntity> cmcUserEntities = cmcUserRepository.findByIdIn(userReferralSummaryEntities.stream()
                .map(UserReferralSummaryEntity::getUserId).collect(Collectors.toList()));
            Map<String, CmcUserEntity> userMap = cmcUserEntities.stream()
                .collect(Collectors.toMap(cmcUserEntity -> cmcUserEntity.getId().toHexString(), entity -> {
                    entity.setUsername(StringUtil.overlay(entity.getUsername()));
                    return entity;
                }, (old, newDa) -> newDa));
            rankUserPoint(userReferralSummaryEntities, top100Set, top100RankMap, userMap);
            cacheTop100RankMap(top100RankMap);
            cacheTop100RankList(top100Set);
            cacheRankTime();
        } else {
            log.info("AffiliateLeaderBoardHandler.handle userReferralSummaryRepository get data null");
        }
        return new JobHandlingResult<>(true);
    }

    private void cacheTop100RankMap(Map<String, String> top100RankMap) {
        assetCacheRedisRepository.deleteAndPutAll(AFFILIATE_POINT_100_RANK_MAP, top100RankMap);
    }

    private void cacheTop100RankList(Set<ZSetOperations.TypedTuple<String>> top100Set) {
        assetCacheRedisRepository.zadd(AFFILIATE_POINT_100_RANK_LIST, top100Set);
    }

    private void cacheRankTime() {
        assetCacheRedisRepository.set(AFFILIATE_LEADERBOARD_RANK_TIME, String.valueOf(new Date().getTime()));
    }

    public void rankUserPoint(List<UserReferralSummaryEntity> userReferralSummaryEntities,
        Set<ZSetOperations.TypedTuple<String>> top100Set, Map<String, String> top100RankMap,
        Map<String, CmcUserEntity> userMap) {
        int size = userReferralSummaryEntities.size();
        int rank = 0;
        int pre = 0;
        for (int i = 0; i < size; i++) {
            UserReferralSummaryEntity root = userReferralSummaryEntities.get(i);
            if (pre != root.getReferralAward()) {
                rank = i+1;
            }
            CmcUserEntity rootUser = userMap.get(root.getUserId());
            AffiliateLeaderBoardDTO leaderBoardDTO = AffiliateLeaderBoardDTO.builder()
                .accumulatedDiamonds(root.getReferralAward()).rank(rank)
                .userName(Objects.isNull(rootUser) ? "" : rootUser.getUsername())
                .avatarId(Objects.isNull(rootUser) ? "" : rootUser.getAvatarId())
                .index(i+1).build();
            top100Set.add(new DefaultTypedTuple<>(JacksonUtils.serialize(leaderBoardDTO), (i+1) * 1.0));
            top100RankMap.put(root.getUserId(), JacksonUtils.serialize(leaderBoardDTO));
            pre = root.getReferralAward();
        }


    }

}
