package com.cmc.asset.job.dto;

import com.cmc.asset.job.enums.ChannelEnum;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Kafka Message log event
 *
 * <AUTHOR>
 * @date 2021/9/6 18:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MessageRecordDTO {

    /**
     * send channel source.(jpush, sms,email)
     */
    private ChannelEnum channel;

    /**
     * the message logs
     */
    private List<DexLpTradeNotificationMessageDTO> bodies;
}
