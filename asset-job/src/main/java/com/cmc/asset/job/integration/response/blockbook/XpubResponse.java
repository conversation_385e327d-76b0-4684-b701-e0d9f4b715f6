package com.cmc.asset.job.integration.response.blockbook;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024-06-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XpubResponse {

    public BlockBookResponseData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BlockBookResponseData{

        private List<Result> result;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result{
        private String type;
        private String name;
        private String path;
        private String transfers;
        private Integer decimals;
        private String balance;
        private String totalReceived;
        private String totalSent;
    }
}
