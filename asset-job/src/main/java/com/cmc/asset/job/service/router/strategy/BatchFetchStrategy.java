package com.cmc.asset.job.service.router.strategy;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import java.util.Date;
import java.util.List;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
public interface BatchFetchStrategy {
    /**
     * batch fetch
     *
     * @param chainIds
     * @param address
     * @param currentTaskTime
     * @return
     */
    Mono<List<BlockChainPO>> batchFetch(List<Integer> chainIds, String address, Date currentTaskTime);
}
