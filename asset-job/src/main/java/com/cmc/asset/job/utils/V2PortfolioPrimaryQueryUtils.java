package com.cmc.asset.job.utils;

import com.cmc.asset.dao.entity.portfolio.PortfolioCalculatingStatusV2Entity;
import com.mongodb.ReadPreference;
import java.util.List;
import java.util.Optional;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.convert.QueryMapper;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/4/21
 */
@Component
public class V2PortfolioPrimaryQueryUtils {
    private static String entityTableName;

    @Value("${com.cmc.asset-job.portfolio.calculating.status.entityTableName:portfolio_calculating_status_v2}")
    public void setEntityTableName(String entityTableName) {
        V2PortfolioPrimaryQueryUtils.entityTableName = entityTableName;
    }

    public static List<PortfolioCalculatingStatusV2Entity> findStatusByPrimary(MongoTemplate cmcAssetMongoTemplate,
        Query query) {
        MongoConverter converter = cmcAssetMongoTemplate.getConverter();
        QueryMapper queryMapper = new QueryMapper(converter);
        Document mappedObject = queryMapper.getMappedObject(query.getQueryObject(), Optional.empty());
        Document find = new Document("find", entityTableName);
        find.put("filter", mappedObject);
        find.put("batchSize", 9999);
        Document document = cmcAssetMongoTemplate.executeCommand(find, ReadPreference.primary());
        Document cursor = (Document)document.get("cursor");
        PortfolioCalculatingStatusBatch read =
            converter.read(PortfolioCalculatingStatusBatch.class, cursor);
        return read.getFirstBatch();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PortfolioCalculatingStatusBatch {
        private List<PortfolioCalculatingStatusV2Entity> firstBatch;
    }
}
