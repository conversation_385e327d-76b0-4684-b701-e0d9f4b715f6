package com.cmc.asset.job.service.portfolio;

import com.cmc.asset.dao.entity.portfolio.PortfolioMultiEntity;
import com.cmc.asset.job.dto.PortfolioMultiDTO;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024-08-02
 */
public interface PortfolioMultiService {

    Flux<PortfolioMultiDTO> pageScrollById();

    Mono<Boolean> moveToWalletTable();

    Mono<PortfolioMultiEntity> getAndRefreshCache(ObjectId userId, String sourceId);

}
