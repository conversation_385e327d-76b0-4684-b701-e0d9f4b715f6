package com.cmc.asset.job.core;

import com.cmc.asset.job.data.BaseJobData;
import com.cmc.asset.job.data.DiamondPushJobData;
import com.cmc.asset.job.data.MongoBatchRefreshJobData;
import com.cmc.asset.job.data.PortfolioJobData;
import com.cmc.asset.job.data.PortfolioSelectableCoinsPriceLoadJobData;
import com.cmc.asset.job.data.PortfolioThirdPartyJobData;
import com.cmc.asset.job.data.UserAuditJobData;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.handler.AffiliateLeaderBoardHandler;
import com.cmc.asset.job.handler.CryptoWatchCountHandler;
import com.cmc.asset.job.handler.DiamondFixCheckInHandler;
import com.cmc.asset.job.handler.LeaderBoardHandler;
import com.cmc.asset.job.handler.NewCoinListingPushHandler;
import com.cmc.asset.job.handler.PopularFollowWatchListHandler;
import com.cmc.asset.job.handler.PortfolioBatchUpdateHandler;
import com.cmc.asset.job.handler.PortfolioFetchThirdPartyAssetHandler;
import com.cmc.asset.job.handler.PortfolioManualUpdateHandler;
import com.cmc.asset.job.handler.PortfolioMigrateHistoryHandler;
import com.cmc.asset.job.handler.PortfolioMultiWalletMoveHandler;
import com.cmc.asset.job.handler.PortfolioRefreshThirdPartyTokenHandler;
import com.cmc.asset.job.handler.PortfolioSelectableCoinsPriceLoadHandler;
import com.cmc.asset.job.handler.PortfolioUpdateHandler;
import com.cmc.asset.job.handler.PricePredictionUserLeaderboardHandler;
import com.cmc.asset.job.handler.PricePredictionUserLeaderboardNameHandler;
import com.cmc.asset.job.handler.UserAuditHandler;
import com.cmc.asset.job.handler.WalletSyncHandler;
import com.cmc.asset.job.handler.WatchListTokenRefreshHandler;
import com.cmc.asset.job.handler.notification.DiamondPushHandler;
import com.cmc.asset.job.handler.notification.PreWatchlistDailyDigestHandler;
import com.cmc.asset.job.handler.notification.WatchlistDailyDigestHandler;
import com.cmc.asset.job.utils.XxlJobParamUtils;
import com.cmc.asset.model.contract.watchlist.WatchListTokenRefreshDTO;
import com.cmc.framework.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/8/5 10:40:41
 */
@Component
@Slf4j
public class AssetXxlJob {

    private static final ReturnT<String> PARAM_PARSE_FAIL = new ReturnT<>(ReturnT.FAIL_CODE, "Params can't be parsed.");

    private ReturnT<String> handleResult(JobHandlingResult<?> result) {
        if (result.isSucceeded()) {
            return ReturnT.SUCCESS;
        } else {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getErrorMsg());
        }
    }

    @XxlJob("pricePredictionUserLeaderboardHandler")
    public ReturnT<String> pricePredictionUserLeaderboardHandler(String params, PricePredictionUserLeaderboardHandler pricePredictionUserLeaderboardHandler) {
        JobHandlingResult<Object> result = pricePredictionUserLeaderboardHandler.handle(params);

        return handleResult(result);
    }

    @XxlJob("pricePredictionUserLeaderboardNameHandler")
    public ReturnT<String> pricePredictionUserLeaderboardNameHandler(String params, PricePredictionUserLeaderboardNameHandler pricePredictionUserLeaderboardNameHandler) {
        JobHandlingResult<Object> result = pricePredictionUserLeaderboardNameHandler.handle(params);

        return handleResult(result);
    }

    @XxlJob("leaderBoardHandler")
    public ReturnT<String> leaderBoardHandler(String param, LeaderBoardHandler leaderBoardHandler) {
        return handleResult(leaderBoardHandler.handle(param));
    }

    @XxlJob("affiliateLeaderBoardHandler")
    public ReturnT<String> affiliateLeaderBoardHandler(String param, AffiliateLeaderBoardHandler affiliateLeaderBoardHandler) {
        return handleResult(affiliateLeaderBoardHandler.handle(param));
    }

    @XxlJob("cryptoWatchCountHandler")
    public ReturnT<String> cryptoWatchCountHandler(String param, CryptoWatchCountHandler cryptoWatchCountHandler) {
        return handleResult(cryptoWatchCountHandler.handle(param));
    }

    @XxlJob("diamondFixCheckInHandler")
    public ReturnT<String> diamondFixCheckInHandler(String params, DiamondFixCheckInHandler diamondFixCheckInHandler){
        JobHandlingResult<Boolean> result = diamondFixCheckInHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("portfolioSelectableCoinsPriceLoadHandler")
    public ReturnT<String> portfolioSelectableCoinsPriceLoadHandler(String params, PortfolioSelectableCoinsPriceLoadHandler portfolioSelectableCoinsPriceLoadHandler){
        PortfolioSelectableCoinsPriceLoadJobData jobData = new PortfolioSelectableCoinsPriceLoadJobData();
        jobData.setIsReload("true".equalsIgnoreCase(params));
        JobHandlingResult<Object> result = portfolioSelectableCoinsPriceLoadHandler.handle(jobData);
        return handleResult(result);
    }

    @XxlJob("portfolioRefreshThirdPartyTokenHandler")
    public ReturnT<String> portfolioRefreshThirdPartyTokenHandler(String params, PortfolioRefreshThirdPartyTokenHandler portfolioRefreshThirdPartyTokenHandler) {
        BaseJobData baseJobData = new BaseJobData();
        if (StringUtils.isNotBlank(params)) {
            baseJobData = XxlJobParamUtils.deserialize(params, BaseJobData.class);
            if (baseJobData == null) {
                return PARAM_PARSE_FAIL;
            }
        }
        JobHandlingResult<Boolean> result = portfolioRefreshThirdPartyTokenHandler.handle(baseJobData);
        return handleResult(result);
    }

    @XxlJob("portfolioFetchThirdPartyAssetHandler")
    public ReturnT<String> portfolioFetchThirdPartyAssetHandler(String params, PortfolioFetchThirdPartyAssetHandler portfolioFetchThirdPartyAssetHandler) {
        PortfolioThirdPartyJobData baseJobData = new PortfolioThirdPartyJobData();
        if (StringUtils.isNotBlank(params)) {
            baseJobData = XxlJobParamUtils.deserialize(params, PortfolioThirdPartyJobData.class);
            if (baseJobData == null) {
                return PARAM_PARSE_FAIL;
            }
        }
        JobHandlingResult<Boolean> result = portfolioFetchThirdPartyAssetHandler.handle(baseJobData);
        return handleResult(result);
    }

    @XxlJob("portfolioMigrateHistoryHandler")
    public ReturnT<String> portfolioFetchThirdPartyAssetHandler(String params, PortfolioMigrateHistoryHandler portfolioMigrateHistoryHandler) {
        PortfolioThirdPartyJobData baseJobData = new PortfolioThirdPartyJobData();
        if (StringUtils.isNotBlank(params)) {
            baseJobData = XxlJobParamUtils.deserialize(params, PortfolioThirdPartyJobData.class);
            if (baseJobData == null) {
                return PARAM_PARSE_FAIL;
            }
        }
        JobHandlingResult<Boolean> result = portfolioMigrateHistoryHandler.handle(baseJobData);
        return handleResult(result);
    }

    @XxlJob("userAuditHandler")
    public ReturnT<String> userAuditHandler(String params, UserAuditHandler userAuditHandler) {
        UserAuditJobData userAuditJobData = new UserAuditJobData();
        if (StringUtils.isNotBlank(params)) {
            userAuditJobData = XxlJobParamUtils.deserialize(params, UserAuditJobData.class);
            if (userAuditJobData == null) {
                return PARAM_PARSE_FAIL;
            }
        }
        JobHandlingResult<Boolean> result = userAuditHandler.handle(userAuditJobData);
        return handleResult(result);
    }

    @XxlJob("popularFollowWatchListHandler")
    public ReturnT<String> popularFollowWatchListHandler(String params,
        PopularFollowWatchListHandler popularFollowWatchListHandler) {
        return handleResult(popularFollowWatchListHandler.handle(params));
    }

    @XxlJob("diamondPushHandler")
    public ReturnT<String> diamondPushHandler(String params,
        DiamondPushHandler diamondPushHandler) {
        DiamondPushJobData diamondPushJobData = null;
        if (StringUtils.isNotBlank(params)) {
            diamondPushJobData = XxlJobParamUtils.deserialize(params, DiamondPushJobData.class);
        }
        return handleResult(diamondPushHandler.handle(diamondPushJobData));
    }

    @XxlJob("preWatchlistDailyDigestHandler")
    public ReturnT<String> preWatchlistDailyDigestHandler(String params,
        PreWatchlistDailyDigestHandler preWatchlistDailyDigestHandler) {
        DiamondPushJobData diamondPushJobData = null;
        if (StringUtils.isNotBlank(params)) {
            diamondPushJobData = XxlJobParamUtils.deserialize(params, DiamondPushJobData.class);
        }
        return handleResult(preWatchlistDailyDigestHandler.handle(diamondPushJobData));
    }

    @XxlJob("watchlistDailyDigestHandler")
    public ReturnT<String> watchlistDailyDigestHandler(String params,
        WatchlistDailyDigestHandler watchlistDailyDigestHandler) {
        WatchlistDailyDigestHandler.WatchlistDailyDigestParam watchlistDailyDigestParam = null;
        if (StringUtils.isNotBlank(params)) {
            watchlistDailyDigestParam = XxlJobParamUtils.deserialize(params, WatchlistDailyDigestHandler.WatchlistDailyDigestParam.class);
        }
        return handleResult(watchlistDailyDigestHandler.handle(watchlistDailyDigestParam));
    }

    @XxlJob("newCoinListingPushHandler")
    public ReturnT<String> newCoinListingPushHandler(String params, NewCoinListingPushHandler newCoinListingPushHandler) {
        return handleResult(newCoinListingPushHandler.handle(params));
    }

    @XxlJob("walletSyncHandler")
    public ReturnT<String> walletSyncHandler(String params, WalletSyncHandler walletSyncHandler) {
        return handleResult(walletSyncHandler.handle(params));
    }

    @XxlJob("portfolioMultiWalletMoveHandler")
    public ReturnT<String> portfolioMultiWalletMoveHandler(String params, PortfolioMultiWalletMoveHandler portfolioMultiWalletMoveHandler) {
        return handleResult(portfolioMultiWalletMoveHandler.handle(params));
    }

    @XxlJob("portfolioUpdateHandler")
    public ReturnT<String> portfolioUpdateHandler(String params, PortfolioUpdateHandler portfolioUpdateHandler) {
        PortfolioJobData jobData = null;
        if (StringUtils.isNotBlank(params)) {
            jobData = XxlJobParamUtils.deserialize(params, PortfolioJobData.class);
        }
        return handleResult(portfolioUpdateHandler.handle(jobData));
    }

    @XxlJob("portfolioManualUpdateHandler")
    public ReturnT<String> portfolioManualUpdateHandler(String params, PortfolioManualUpdateHandler portfolioManualUpdateHandler) {
        MongoBatchRefreshJobData jobData = null;
        if (StringUtils.isNotBlank(params)) {
            jobData = XxlJobParamUtils.deserialize(params, MongoBatchRefreshJobData.class);
        }
        return handleResult(portfolioManualUpdateHandler.handle(jobData));
    }

    @XxlJob("portfolioBatchUpdateHandler")
    public ReturnT<String> portfolioMultiWalletMoveHandler(String params, PortfolioBatchUpdateHandler portfolioBatchUpdateHandler) {
        return handleResult(portfolioBatchUpdateHandler.handle(null));
    }

    @XxlJob("watchListTokenRefreshHandler")
    public ReturnT<String> watchListTokenRefreshHandler(String params, WatchListTokenRefreshHandler watchListTokenRefreshHandler) {
        WatchListTokenRefreshDTO jobData = null;
        if (StringUtils.isNotBlank(params)) {
            jobData = XxlJobParamUtils.deserialize(params, WatchListTokenRefreshDTO.class);
        }
        return handleResult(watchListTokenRefreshHandler.handle(jobData));
    }
}
