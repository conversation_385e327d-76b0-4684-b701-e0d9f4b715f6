package com.cmc.asset.job.service.portfolio.impl;

import com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint1dRepository;
import com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint1hRepository;
import com.cmc.asset.dao.dynamo.repository.impl.CryptoDatapoint5mRepository;
import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint1dEntity;
import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint1hEntity;
import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint5mEntity;
import com.cmc.asset.dao.entity.vo.CryptoHistoryDataCacheVo;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.config.CryptoBaseCurrencyConfig;
import com.cmc.asset.job.repository.redis.CryptoCacheRepository;
import com.cmc.asset.model.common.MetricConstant;
import com.cmc.asset.model.enums.ListingStatus;
import com.cmc.data.common.utils.JacksonUtils;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CryptoHistoryDataService {

    @Autowired
    private CryptoBaseCurrencyConfig cryptoBaseCurrencyConfig;

    @Autowired
    private CryptoCacheRepository cryptoCacheRepository;

    @Autowired
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Autowired
    private CryptoDatapoint5mRepository cryptoDatapoint5mRepository;

    @Autowired
    private CryptoDatapoint1hRepository cryptoDatapoint1hRepository;

    @Autowired
    private CryptoDatapoint1dRepository cryptoDatapoint1dRepository;

    @Autowired
    private CmcMeterRegistry cmcMeterRegistry;

    @Value("${com.cmc.asset.crypto.5m.allow-cache-common-crypto}")
    private Boolean allowCacheCommonCrypto;

    @Value("${com.cmc.asset.crypto.5m.cache-expire-time}")
    private Long expireTime;

    @Value("${com.cmc.asset.crypto.history.price.block-invalid-crypto:true}")
    private Boolean blockInvalidCrypto;

    @Value("${com.cmc.asset.portfolio.enable-metric:false}")
    private Boolean enableMetric;
    /**
     * due to use ohlcv 1hr,1d's data, total supply will lose
     *
     * @param cryptoId  crypto id
     * @param timeCodes time code
     * @return
     */
    public Mono<List<CryptoHistoryDataCacheVo>> getCryptoHistoryDatas(Integer cryptoId, List<Long> timeCodes) {
        CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(cryptoId);
        boolean isFiat = currencyInfoDTO != null && "FIAT".equalsIgnoreCase(currencyInfoDTO.getCategory());
        boolean validCrypto = currencyInfoDTO != null && (ListingStatus.ACTIVE.getCode()
                .equals(currencyInfoDTO.getStatus()) || isFiat);
        if (blockInvalidCrypto && !validCrypto) {
            return Mono.just(List.of());
        }
        //whether to allow the cache the common currency
        if (allowCacheCommonCrypto) {
            return queryByRedis(cryptoId, timeCodes, isFiat);
        } else if (cryptoBaseCurrencyConfig.isValid(cryptoId)) {
            //only cache base currency
            return queryByRedis(cryptoId, timeCodes, isFiat);
        } else {
            //query DDB directly without caching
            return queryDdb(cryptoId, Sets.newHashSet(timeCodes), isFiat);
        }
    }

    private Mono<List<CryptoHistoryDataCacheVo>> queryByRedis(Integer cryptoId, List<Long> timeCodes, boolean isFiat) {
        //data in a query redis
        return cryptoCacheRepository.getCryptoHistoryData(cryptoId, timeCodes)
                .defaultIfEmpty(List.of())
                .onErrorReturn(List.of())
                .flatMap(pairs -> {
                    List<CryptoHistoryDataCacheVo> cryptoHistoryDataCacheVos = Lists.newArrayListWithCapacity(pairs.size());
                    List<Long> existTimeCodes = Lists.newArrayListWithCapacity(pairs.size());
                    for (Pair<Long, String> pair : pairs) {
                        Long timeCode = pair.getLeft();
                        String cacheStr = pair.getRight();
                        //to prevent cache penetration, empty object exist
                        CryptoHistoryDataCacheVo cryptoHistoryDataCacheVo = JacksonUtils.getInstance()
                                .deserialize(cacheStr, CryptoHistoryDataCacheVo.class);
                        cryptoHistoryDataCacheVos.add(cryptoHistoryDataCacheVo);
                        existTimeCodes.add(timeCode);
                    }
                    if (enableMetric) {
                        cmcMeterRegistry.counter(MetricConstant.CRYPTO_PRICE_CACHE_STATS_METRIC, "hit", "redis")
                                .increment(existTimeCodes.size());
                    }
                    Set<Long> tmpTimeCodes = Sets.newHashSet(timeCodes);
                    //timeCode does not exist in redis, need to go to ddb query
                    tmpTimeCodes.removeAll(existTimeCodes);
                    if (CollectionUtils.isNotEmpty(tmpTimeCodes)) {
                        List<CryptoHistoryDataCacheVo> finalCryptoHistoryDataCacheVos = cryptoHistoryDataCacheVos;
                        return queryDdb(cryptoId, tmpTimeCodes, isFiat).flatMap(cacheVos -> {
                            //穿透到ddb，ddb中存在到code
                            Set<Long> timeSet = cacheVos.stream()
                                    .map(CryptoHistoryDataCacheVo::getScore)
                                    .collect(Collectors.toSet());
                            //timecode, which does not exist in either ddb or redis, prints logs and caches empty objects to prevent cache penetration
                            tmpTimeCodes.removeAll(timeSet);
                            if (CollectionUtils.isNotEmpty(tmpTimeCodes)) {
                                log.info("CryptoHistoryDataService.queryByRedis and ddb both not exist cryptoId {},{}",
                                        cryptoId, JacksonUtils.toJsonString(tmpTimeCodes));
                            }
                            return cryptoCacheRepository.addCryptoHistoryData(cacheVos, expireTime)
                                    .map(res -> {
                                        if (CollectionUtils.isNotEmpty(cacheVos)) {
                                            finalCryptoHistoryDataCacheVos.addAll(cacheVos);
                                        }
                                        //because there are cache empty objects, we need to filter
                                        return finalCryptoHistoryDataCacheVos.stream()
                                                .filter(cryptoHistoryDataCacheVo -> cryptoHistoryDataCacheVo != null && cryptoHistoryDataCacheVo.getScore() != null
                                                        && cryptoHistoryDataCacheVo.getId() != null)
                                                .collect(Collectors.toList());
                                    });
                        });
                    } else {
                        cryptoHistoryDataCacheVos = cryptoHistoryDataCacheVos.stream()
                                .filter(cryptoHistoryDataCacheVo -> cryptoHistoryDataCacheVo != null && cryptoHistoryDataCacheVo.getScore() != null
                                        && cryptoHistoryDataCacheVo.getId() != null)
                                .collect(Collectors.toList());
                        return Mono.just(cryptoHistoryDataCacheVos);
                    }
                });
    }

    private Mono<List<CryptoHistoryDataCacheVo>> queryDdb(Integer cryptoId, Set<Long> timeCodes, boolean isFiat) {
        if (CollectionUtils.isEmpty(timeCodes)) {
            return Mono.just(List.of());
        }
        //if ohlcv data is allowed to be used, determine whether the data of the whole hour and the whole day are used, and then query the ddb of 1hr and 1d
        List<Long> crypto5ms = Lists.newArrayListWithCapacity(timeCodes.size());
        List<Long> crypto1hs = Lists.newArrayListWithCapacity(timeCodes.size());
        List<Long> crypto1ds = Lists.newArrayListWithCapacity(timeCodes.size());
        if (isFiat) {
            crypto5ms.addAll(timeCodes);
        }else{
            long todayUTCStart = LocalDate.now(ZoneOffset.UTC).atStartOfDay(ZoneOffset.UTC).toEpochSecond();
            for (Long timeCode : timeCodes) {
                if (timeCode >= todayUTCStart) {
                    if (timeCode % 3600 == 0) {
                        crypto1hs.add(timeCode);
                    } else {
                        crypto5ms.add(timeCode);
                    }
                }else{
                    if (timeCode % 86400 == 0) {
                        crypto1ds.add(timeCode);
                    } else if (timeCode % 3600 == 0) {
                        crypto1hs.add(timeCode);
                    } else {
                        crypto5ms.add(timeCode);
                    }
                }
            }
        }
        //to prevent stream termination, both mono and arraylist are returned
        return Mono.zip(cryptoDatapoint5mRepository.getCryptoDataPoint5m(cryptoId, crypto5ms)
                                .defaultIfEmpty(List.of()),
                        cryptoDatapoint1hRepository.getOhlcvHistoricalhData(cryptoId, crypto1hs)
                                .defaultIfEmpty(List.of()),
                        cryptoDatapoint1dRepository.getOhlcvHistorical1dData(cryptoId, crypto1ds)
                                .defaultIfEmpty(List.of()))
                .map(tuple3 -> {
                    List<CryptoDatapoint5mEntity> cryptoDatapoint5mEntities = tuple3.getT1();
                    List<CryptoDatapoint1hEntity> cryptoDatapoint1hEntities = tuple3.getT2();
                    List<CryptoDatapoint1dEntity> cryptoDatapoint1dEntities = tuple3.getT3();
                    List<CryptoHistoryDataCacheVo> convert5mToCacheVo = convert5mToCacheVo(cryptoDatapoint5mEntities);
                    List<CryptoHistoryDataCacheVo> convert1hToCacheVo =
                            convert1hToCacheVo(cryptoDatapoint1hEntities);
                    List<CryptoHistoryDataCacheVo> convert1dToCacheVo =
                            convert1dToCacheVo(cryptoDatapoint1dEntities);
                    convert5mToCacheVo.addAll(convert1hToCacheVo);
                    convert5mToCacheVo.addAll(convert1dToCacheVo);
                    convert5mToCacheVo.sort(Comparator.comparing(CryptoHistoryDataCacheVo::getScore));
                    if (enableMetric) {
                        cmcMeterRegistry.counter(MetricConstant.CRYPTO_PRICE_CACHE_STATS_METRIC, "hit", "ddb").increment(convert5mToCacheVo.size());
                    }
                    return convert5mToCacheVo;
                })
                .defaultIfEmpty(List.of())
                .onErrorReturn(List.of());
    }

    /**
     * crypto 5m ddb data convert to cache vo
     */
    private List<CryptoHistoryDataCacheVo> convert5mToCacheVo(List<CryptoDatapoint5mEntity> cryptoDatapoint5mEntities) {
        if (CollectionUtils.isEmpty(cryptoDatapoint5mEntities)) {
            return Lists.newArrayList();
        }
        return cryptoDatapoint5mEntities.stream()
                .filter(cryptoDatapoint5mEntity -> cryptoDatapoint5mEntity != null
                        && cryptoDatapoint5mEntity.getScore() != null)
                .map(cryptoDatapoint5mEntity -> {
                    CryptoHistoryDataCacheVo cryptoHistoryDataCacheVo = new CryptoHistoryDataCacheVo();
                    cryptoHistoryDataCacheVo.setId(cryptoDatapoint5mEntity.getCryptoId());
                    cryptoHistoryDataCacheVo.setScore(cryptoDatapoint5mEntity.getScore());
                    cryptoHistoryDataCacheVo.setPriceUsd(cryptoDatapoint5mEntity.getPriceUsd());
                    return cryptoHistoryDataCacheVo;
                })
                .collect(Collectors.toList());
    }

    /**
     * ohlcv 1hr ddb data convert to cache vo，lose total_supply
     */
    private List<CryptoHistoryDataCacheVo> convert1hToCacheVo(
            List<CryptoDatapoint1hEntity> cryptoDatapoint1hEntities) {
        if (CollectionUtils.isEmpty(cryptoDatapoint1hEntities)) {
            return Lists.newArrayList();
        }
        return cryptoDatapoint1hEntities.stream()
                .filter(cryptoCurrencyOhlcv1hDynamondbPo -> cryptoCurrencyOhlcv1hDynamondbPo != null
                        && cryptoCurrencyOhlcv1hDynamondbPo.getScore() != null)
                .map(cryptoCurrencyOhlcv1hDynamondbPo -> {
                    CryptoHistoryDataCacheVo cryptoHistoryDataCacheVo = new CryptoHistoryDataCacheVo();
                    cryptoHistoryDataCacheVo.setId(cryptoCurrencyOhlcv1hDynamondbPo.getCryptoId());
                    cryptoHistoryDataCacheVo.setScore(cryptoCurrencyOhlcv1hDynamondbPo.getScore());
                    cryptoHistoryDataCacheVo.setPriceUsd(cryptoCurrencyOhlcv1hDynamondbPo.getOpenUsd());
                    cryptoHistoryDataCacheVo.setOpenUsd(cryptoCurrencyOhlcv1hDynamondbPo.getOpenUsd());
                    cryptoHistoryDataCacheVo.setCloseUsd(cryptoCurrencyOhlcv1hDynamondbPo.getCloseUsd());
                    cryptoHistoryDataCacheVo.setHighUsd(cryptoCurrencyOhlcv1hDynamondbPo.getHighUsd());
                    cryptoHistoryDataCacheVo.setLowUsd(cryptoCurrencyOhlcv1hDynamondbPo.getLowUsd());
                    return cryptoHistoryDataCacheVo;
                })
                .collect(Collectors.toList());
    }

    /**
     * ohlcv 1hr ddb data convert to cache vo，lose total_supply
     */
    private List<CryptoHistoryDataCacheVo> convert1dToCacheVo(
            List<CryptoDatapoint1dEntity> cryptoDatapoint1dEntities) {
        if (CollectionUtils.isEmpty(cryptoDatapoint1dEntities)) {
            return Lists.newArrayList();
        }
        return cryptoDatapoint1dEntities.stream()
                .filter(cryptoCurrencyOhlcv1dDynamondbPo -> cryptoCurrencyOhlcv1dDynamondbPo != null
                        && cryptoCurrencyOhlcv1dDynamondbPo.getScore() != null)
                .map(cryptoCurrencyOhlcv1dDynamondbPo -> {
                    CryptoHistoryDataCacheVo cryptoHistoryDataCacheVo = new CryptoHistoryDataCacheVo();
                    cryptoHistoryDataCacheVo.setId(cryptoCurrencyOhlcv1dDynamondbPo.getCryptoId());
                    cryptoHistoryDataCacheVo.setScore(cryptoCurrencyOhlcv1dDynamondbPo.getScore());
                    cryptoHistoryDataCacheVo.setPriceUsd(cryptoCurrencyOhlcv1dDynamondbPo.getOpenUsd());
                    cryptoHistoryDataCacheVo.setOpenUsd(cryptoCurrencyOhlcv1dDynamondbPo.getOpenUsd());
                    cryptoHistoryDataCacheVo.setCloseUsd(cryptoCurrencyOhlcv1dDynamondbPo.getCloseUsd());
                    cryptoHistoryDataCacheVo.setHighUsd(cryptoCurrencyOhlcv1dDynamondbPo.getHighUsd());
                    cryptoHistoryDataCacheVo.setLowUsd(cryptoCurrencyOhlcv1dDynamondbPo.getLowUsd());
                    return cryptoHistoryDataCacheVo;
                })
                .collect(Collectors.toList());
    }

}
