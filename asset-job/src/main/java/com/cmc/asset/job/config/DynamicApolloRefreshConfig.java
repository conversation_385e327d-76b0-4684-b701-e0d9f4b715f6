package com.cmc.asset.job.config;

import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.job.dto.BatchFetchStrategyConfig;
import com.cmc.asset.job.dto.PortfolioBatchUpdateData;
import com.cmc.asset.job.dto.PortfolioOkLinkMapVO;
import com.cmc.framework.utils.JacksonUtils;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * DynamicApolloRefreshConfig
 *
 * <AUTHOR> ricky.x
 * @date : 2023/2/5 下午9:40
 */
@Data
@Component
@Slf4j
public class DynamicApolloRefreshConfig {

    private static final String SHARDING_TABLE_SWITCH_PREFIX = "com.cmc.asset-job.refresh-apollo";

    public static final String OK_CHAIN_MAP = "com.cmc.asset-job.refresh-apollo.ok-chain-map";

    private static final String OKLINK_OKX_APOLLO_MAP_CONFIG_KEY = "com.cmc.asset-job.refresh-apollo.oklink-okx-id-map";

    private static final String TOKEN_MIGRATION_MAP = "com.cmc.asset-job.refresh-apollo.token-migration-map";

    private static final String BATCH_FETCH_STRATEGY_CONFIG = "com.cmc.asset-job.refresh-apollo.batch-fetch-strategy-config";

    private static final String BATCH_UPDATE_USER_PORTFOLIO_LIST = "com.cmc.asset-job.refresh-apollo.batch-update-user-portfolio-list";

    private List<PortfolioOkLinkMapVO> okLinkMapList;

    private Map<Integer, PortfolioOkLinkMapVO> chainIdMap = new HashMap<>(16);

    private Map<String, PortfolioOkLinkMapVO> shortNameMap = new HashMap<>(16);

    private List<Integer> chainIds = new ArrayList<>(16);

    @Value("${com.cmc.asset-job.refresh-apollo.ok-chain-map}")
    private String okChainMapJson;

    @Value("${com.cmc.asset-job.refresh-apollo.oklink-okx-id-map}")
    private String okLinkToOkxMapConfigStr;

    private Map<Integer, OkLinkMapConfigItem> okLinkOkxMapConfig;

    @Value("${com.cmc.asset-job.refresh-apollo.token-migration-map}")
    private String tokenMigrationMapStr;
    private Map<Integer, Integer> tokenMigrationMap;

    @Value("${com.cmc.asset-job.refresh-apollo.batch-fetch-strategy-config}")
    private String batchFetchStrategyConfigStr;
    private BatchFetchStrategyConfig batchFetchStrategyConfig;

    @Value("${com.cmc.asset-job.refresh-apollo.batch-update-user-portfolio-list}")
    private String batchUpdateUserPortfolioListStr;
    private List<PortfolioBatchUpdateData> batchUpdateUserPortfolioList;

    @PostConstruct
    public void initShardingTableSwitch() {
        if (StringUtils.isNotBlank(okChainMapJson)) {
            initOkLinkMap(okChainMapJson);
        }

        okLinkOkxMapConfig = StringUtils.isNotEmpty(okLinkToOkxMapConfigStr) ?
            initOkLinkOkxMap(okLinkToOkxMapConfigStr) : new HashMap<>();

        initTokenMigrationMap(tokenMigrationMapStr);

        initBatchFetchStrategyConfig(batchFetchStrategyConfigStr);

        initBatchUpdateUserPortfolioList(batchUpdateUserPortfolioListStr);
    }

    @ApolloConfigChangeListener(interestedKeyPrefixes = {SHARDING_TABLE_SWITCH_PREFIX})
    public void changeDynamicConfig(ConfigChangeEvent changeEvent) {
        log.info("changeDynamicConfig changedKeys {}", changeEvent.changedKeys());
        if (changeEvent.isChanged(OK_CHAIN_MAP)) {
            String newValue = changeEvent.getChange(OK_CHAIN_MAP).getNewValue();
            initOkLinkMap(newValue);
        }
        if (changeEvent.isChanged(OKLINK_OKX_APOLLO_MAP_CONFIG_KEY)) {
            String newValue = changeEvent.getChange(OKLINK_OKX_APOLLO_MAP_CONFIG_KEY).getNewValue();
            okLinkOkxMapConfig = initOkLinkOkxMap(newValue);
        }

        if (changeEvent.isChanged(TOKEN_MIGRATION_MAP)) {
            String newValue = changeEvent.getChange(TOKEN_MIGRATION_MAP).getNewValue();
            initTokenMigrationMap(newValue);
        }

        if (changeEvent.isChanged(BATCH_FETCH_STRATEGY_CONFIG)) {
            String newValue = changeEvent.getChange(BATCH_FETCH_STRATEGY_CONFIG).getNewValue();
            initBatchFetchStrategyConfig(newValue);
        }

        if (changeEvent.isChanged(BATCH_UPDATE_USER_PORTFOLIO_LIST)) {
            String newValue = changeEvent.getChange(BATCH_UPDATE_USER_PORTFOLIO_LIST).getNewValue();
            initBatchUpdateUserPortfolioList(newValue);
        }
    }

    private void initOkLinkMap(String value) {
        List<PortfolioOkLinkMapVO> deserializeSwitch = JacksonUtils.deserialize(value, new TypeReference<>() {
        });
        if (null != deserializeSwitch) {
            okLinkMapList = deserializeSwitch;
            for (PortfolioOkLinkMapVO portfolioOkLinkMap : okLinkMapList) {
                chainIdMap.put(portfolioOkLinkMap.getChainId(), portfolioOkLinkMap);
                shortNameMap.put(portfolioOkLinkMap.getChainShortName(),portfolioOkLinkMap);
                chainIds.add(portfolioOkLinkMap.getChainId());
            }
            log.info("initOkLinkMap okLinkMapList:{},chainIdMap:{}", okLinkMapList, chainIdMap);
        }
    }

    private Map<Integer, OkLinkMapConfigItem> initOkLinkOkxMap(String value) {
        return JacksonUtils.deserialize(value, new TypeReference<>() {});
    }

    private void initTokenMigrationMap(String tokenMigrationMapStr) {
        if (StringUtils.isEmpty(tokenMigrationMapStr)) {
            tokenMigrationMap = new HashMap<>();
            return;
        }
        try {
            tokenMigrationMap = JacksonUtils.deserialize(tokenMigrationMapStr, new TypeReference<>() {});
        } catch (Exception e) {
            log.error("[DynamicApolloRefreshConfig.initTokenMigrationMap]Parse error -- tokenMigrationMapStr: {}", tokenMigrationMapStr, e);
        }
    }

    private void initBatchFetchStrategyConfig(String batchFetchStrategyConfigStr) {
        if (StringUtils.isEmpty(batchFetchStrategyConfigStr)) {
            batchFetchStrategyConfig = BatchFetchStrategyConfig.builder()
                .batchFetchChainIds(Collections.EMPTY_LIST)
                .assetBatchFetchStrategies(Collections.EMPTY_MAP)
                .transactionBatchFetchStrategies(Collections.EMPTY_MAP)
                .build();
            return;
        }
        try {
            batchFetchStrategyConfig = JacksonUtils.deserialize(batchFetchStrategyConfigStr, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("[DynamicApolloRefreshConfig.initBatchFetchStrategyConfig]Parse error -- batchFetchStrategyConfigStr: {}", batchFetchStrategyConfigStr, e);
        }
    }

    private void initBatchUpdateUserPortfolioList(String batchUpdateUserPortfolioListStr) {
        if (StringUtils.isEmpty(batchUpdateUserPortfolioListStr)) {
            batchUpdateUserPortfolioList = new ArrayList<>();
            return;
        }
        try {
            batchUpdateUserPortfolioList = JacksonUtils.deserialize(batchUpdateUserPortfolioListStr, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("[DynamicApolloRefreshConfig.initBatchUpdateUserPortfolioList]Parse error -- batchUpdateUserPortfolioListStr: {}", batchUpdateUserPortfolioListStr, e);
        }
    }

    public PortfolioOkLinkMapVO getChainConfigById(Integer chainId) {
        return chainIdMap.get(chainId);
    }

    public PortfolioOkLinkMapVO getChainConfigByShortName(String shortName) {
        return shortNameMap.get(shortName);
    }

    public List<Integer> getChainConfigIds() {
        return chainIds;
    }

}
