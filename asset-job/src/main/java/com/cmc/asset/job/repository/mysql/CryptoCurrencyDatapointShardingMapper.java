package com.cmc.asset.job.repository.mysql;

import com.cmc.asset.job.repository.mysql.po.CryptoCurrencyDatapointPO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * CryptoCurrencyDatapointShardingMapper
 * 1.sharding数据源已经通过DataSourceConfiguration类注册进动态数据源,通过@DS("sharding")即可切换
 * 2.对应表结构如下: crypto_currency_datapoint_v3_$->{2021..2052}_$->{0..7}
 * 3.此表使用复合分片算法,对应分片算法的实现如下:
 *
 * <AUTHOR> ricky.x
 * @date : 2022/10/19 上午1:46
 */
@Mapper
@DS("sharding")
public interface CryptoCurrencyDatapointShardingMapper extends BaseMapper<CryptoCurrencyDatapointPO> {

    /**
     * Query the volumes and prices of a crypto within a time range.
     *
     * @param cryptoId the id of the crypto
     * @param startTime the start time
     * @param endTime the end time
     * @return the volumes and prices
     */
    @Select("select volume_usd, price_usd, create_time from crypto_currency_datapoint_v3 where crypto_id = #{cryptoId} and create_time >= #{startTime} and create_time <= #{endTime}")
    List<CryptoCurrencyDatapointPO> selectVolumeAndPriceByTimeRange(@Param("cryptoId") Integer cryptoId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
