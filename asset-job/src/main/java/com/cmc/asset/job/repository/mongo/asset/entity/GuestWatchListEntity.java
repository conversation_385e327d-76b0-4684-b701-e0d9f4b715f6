package com.cmc.asset.job.repository.mongo.asset.entity;

import com.cmc.asset.dao.BaseMongoEntity;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * GuestWatchListEntity
 * <AUTHOR> ricky.x
 * @date: 2025/6/17 19:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "guest_watchlist")
public class GuestWatchListEntity extends BaseMongoEntity<ObjectId> {

    @Id
    private ObjectId id;

    /**
     * uid for user.Long type userId.
     */
    private Long guestId;

    /**
     * watchlist name
     */
    private String name;

    /**
     * 关注币列表
     */
    private Set<Integer> cryptos;

    /**
     * 关注交易所
     */
    private Set<Integer> exchanges;

    /**
     * 交易对
     */
    private Set<Integer> marketPairs;

    /**
     * dex pairs
     */
    private List<Long> dexPairs;

    /**
     * dex tokens
     */
    private List<String> dexTokens;

    private String type;
}
