package com.cmc.asset.job.integration.response.blockbook;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AddressBalanceFillsResponse
 * <AUTHOR> ricky.x
 * @date : 2023/2/2 下午5:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddressBalanceResponse extends BlockBookResponse{

    public BlockBookResponseData data;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BlockBookResponseData{
        /**
         * 当前地址
         */
        private List<AccountChainInfo> result;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountChainInfo{
        /**
         * 当前地址
         */
        private String address;

        /**
         * chain flag
         */
        private String platformId;

        /**
         * chain native token
         */
        private String symbol;

        /**
         * native token balance
         */
        private String balance;


        private List<AccountChainTokenInfo> tokens;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountChainTokenInfo{

        /**
         * token contract address
         */
        private String contract;

        /**
         * token contract address
         */
        private String symbol;

        /**
         * token balance
         */
        private String balance;
    }

}
