package com.cmc.asset.job.vo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021年06月30日11:58:35
 **/
@Data
public class PricePredictionUserStatisticVO {
    private String id;

    private String userId;

    private String avatarId;

    private String profileId;

    private String userName;    //进入榜单的top用户才设置userName,如果是邮箱,做隐私处理

    private BigDecimal averageAccuracy;

    private BigDecimal coverage;

    private Integer estimateScore;   //预测综合得分,排名依据,4位小数放大一万倍存储为整数,读取时转换为BigDecimal 8848->88.48%

    private Integer rankings;   //前200排名

    private Integer index;

    private BigDecimal distribution;    //前50%-95% 分布

    private PricePredictionDataVO bestAccuracy;    //6个月内最好预测结果

    private Integer statisticTime; //统计月份,6位int(202106) 和 estimateScore 做索引

    private Integer estimateCount;   //有效预测数量,最近6个月内,Accuracy不为null的记录

    private Date updateTime;
}
