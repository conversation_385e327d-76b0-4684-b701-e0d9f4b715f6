package com.cmc.asset.job.service.adapter;

import com.cmc.asset.job.enums.TableRequestEnum;
import com.cmc.asset.job.repository.mysql.po.CryptoCurrencyDatapointPO;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import org.springframework.beans.factory.annotation.Value;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import reactor.tuple.Tuple2;

/**
 * AbstractCurrencyDataPointV3Adapter 抽象类
 * <AUTHOR> ricky.x
 * @date : 2022/10/20 下午9:26
 */
@Slf4j
@Data
public abstract class AbstractCurrencyDataPointV3Adapter {

    /**
     * UTC时间,导入历史数据的开始时间,超过一年的数据需要请求oldTable
     */
    @Value("${com.cmc.asset-job.crypto-currency-datapoint-v3.query-check-utc-time}")
    private long checkUtcTime;

    /**
     * 灰度流量切换万分比(仅用于查询)
     */
    @Value("${com.cmc.asset-job.crypto-currency-datapoint-v3.gray-percentage}")
    private int grayscalePercentage;

    /**
     * 并行查询开关 true打开并行 false关闭并行
     */
    @Value("${com.cmc.asset-job.crypto-currency-datapoint-v3.concurrency-query-switch}")
    private boolean concurrentQuerySwitch;

    /**
     * 判断请求走旧表还是新表
     */
    protected TableRequestEnum judgeTableRequest(String paramStr, String method, Date... dates) {
        //如果切换之后，出现请求超过导入历史数据的开始时间,则请求oldTable
        if (moreThanOneYear(dates)) {
            log.error("CurrencyDataPointV3Adapter more than one year method:{},params:{}", method, paramStr);
            return TableRequestEnum.OLD;
        }
        //不在灰度流量切换百分比之内,则请求oldTable
        if (notGray()) {
            return TableRequestEnum.OLD;
        }
        //在灰度流量范围内,如果并行关闭,则只请求newTable
        if (!isConcurrentQuerySwitch()) {
            return TableRequestEnum.NEW;
        }
        //在灰度流量范围内,并行打开,则请求oldTable与newTable并进行对比
        return TableRequestEnum.BOTH;

    }

    /**
     * 并发结果处理 当两个并发结果不一致时取原逻辑
     *
     * @param paramStr 请求参数
     * @param method   方法名称
     * @param tuple2   T1 oldResult T2 newResult
     * @return result
     */
    protected List<CryptoCurrencyDatapointPO> currentListResult(String paramStr, String method,
        Tuple2<List<CryptoCurrencyDatapointPO>, List<CryptoCurrencyDatapointPO>> tuple2) {

        if (tuple2.getT1() == null) {
            if (tuple2.getT2() != null) {
                log.error("CurrencyDataPointV3Adapter error method:{},oldTable: null,newTable:{},params:{}", method,
                    tuple2.getT2(), paramStr);
                return tuple2.getT1();
            }
            return tuple2.getT1();
        }
        if (!tuple2.getT1().equals(tuple2.getT2())) {
            log.error("CurrencyDataPointV3Adapter error method:{},oldTable:{},newTable:{},params:{}", method,
                tuple2.getT1(), tuple2.getT2(), paramStr);
            return tuple2.getT1();
        }
        return tuple2.getT2();

    }

    /**
     * 判断请求参数日期是否超过指定时间
     * @param dates request date
     * @return true or false
     */
    private boolean moreThanOneYear(Date... dates) {
        for (Date date : dates) {
            if (date != null && date.getTime() < checkUtcTime) {
                return true;
            }
        }
        return false;
    }

    /**
     * is not gray
     * @return true or false
     */
    private boolean notGray() {
        return grayscalePercentage < ThreadLocalRandom.current().nextInt(1, 10001);
    }

}
