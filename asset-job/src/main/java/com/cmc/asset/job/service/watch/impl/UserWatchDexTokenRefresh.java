package com.cmc.asset.job.service.watch.impl;

import com.cmc.asset.job.repository.mongo.asset.UserWatchlistRepository;
import com.cmc.asset.job.repository.mongo.asset.entity.WatchListEntity;
import com.cmc.asset.job.service.watch.AbstractWatchRefreshService;
import com.cmc.asset.model.contract.dexer.DexTokenDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.CollectionUtils;
import reactor.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * UserWatchDexTokenRefresh
 * <AUTHOR> ricky.x
 * @date: 2025/6/9 23:47
 */
@Service
@Slf4j
public class UserWatchDexTokenRefresh extends AbstractWatchRefreshService<WatchListEntity> {


    @Autowired
    private UserWatchlistRepository userWatchlistRepository;




    @Override
    protected Mono<List<WatchListEntity>> queryPendingData(ObjectId startId, ObjectId endId, ObjectId latestId, int limit) {
        return Mono.fromCallable(() -> userWatchlistRepository.findByPlatformByCursor(startId,endId,latestId,limit))
                .publishOn(Schedulers.boundedElastic());
    }

    @Override
    protected Mono<Tuple2<Integer, Optional<ObjectId>>> processAndSave(List<WatchListEntity> entities) {
        return getAndFillDexToken(entities)
                .flatMap(list -> userWatchlistRepository.batchUpdateDexToken(list))
                .map(e -> Tuples.of(entities.size(), Optional.of(entities.get(entities.size() - 1).getId())));
    }


    /**
     * 填充 DEX token 信息
     * @param entities WatchListEntity 列表
     * @return 填充后的 WatchListEntity 列表
     */
    private Mono<List<WatchListEntity>> getAndFillDexToken(List<WatchListEntity> entities) {
        Set<Long> distinctDexPairIds = extractDistinctDexPairIds(entities);
        if (CollectionUtils.isEmpty(distinctDexPairIds)) {
            return Mono.just(entities);
        }
        return super.transPoolIdToDexTokens(new ArrayList<>(distinctDexPairIds))
                .map(pairInfoMap -> fillEntitiesWithDexTokens(entities, pairInfoMap));
    }


    private Set<Long> extractDistinctDexPairIds(List<WatchListEntity> entities) {
        return entities.stream()
                .filter(Objects::nonNull)
                .flatMap(entity -> Optional.ofNullable(entity.getDexPairs())
                        .orElse(Collections.emptyList())
                        .stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


    private List<WatchListEntity> fillEntitiesWithDexTokens(List<WatchListEntity> entities, Map<Long, PairInfoDTO> pairInfoMap) {
        if(CollectionUtils.isEmpty(pairInfoMap)){
            return List.of();
        }
        return entities.stream()
                .filter(Objects::nonNull)
                .map(entity -> fillEntityWithDexTokens(entity, pairInfoMap))
                .collect(Collectors.toList());
    }


    private WatchListEntity fillEntityWithDexTokens(WatchListEntity entity, Map<Long, PairInfoDTO> pairInfoMap) {
        if (CollectionUtils.isEmpty(entity.getDexPairs())) {
            return entity;
        }
        List<String> pairTransToken = entity.getDexPairs().stream()
                .map(pairId -> convertPairIdToDexToken(pairId, pairInfoMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> existToken = Optional.ofNullable(entity.getDexTokens()).orElse(Collections.emptyList());
        pairTransToken.addAll(existToken);
        if(CollectionUtils.isEmpty(pairTransToken)){
            return entity;
        }
        List<String> finalTokenList = pairTransToken.stream()
                .distinct()
                .collect(Collectors.toList());
        entity.setDexTokens(finalTokenList);
        return entity;
    }

    private String convertPairIdToDexToken(Long pairId, Map<Long, PairInfoDTO> pairInfoMap) {
        return Optional.ofNullable(pairInfoMap.get(pairId))
                .map(pairInfo -> {
                    DexTokenDTO baseToken = pairInfo.getBaseToken();
                    if (baseToken == null || StringUtils.isEmpty(baseToken.getAddress())) {
                        return null;
                    }
                    return pairInfo.getPlatformId() + "_" + baseToken.getAddress();
                })
                .orElse(null);
    }


}
