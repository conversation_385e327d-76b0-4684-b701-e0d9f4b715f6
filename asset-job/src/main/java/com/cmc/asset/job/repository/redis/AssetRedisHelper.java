package com.cmc.asset.job.repository.redis;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/1/4
 */
public interface AssetRedisHelper {
    String get(String key);
    void set(String key,String value);
    void set(String key, String value, Duration duration);
    Boolean delete(String key);
    Long delete(Collection<String> keys);
    void setByExpire(String key, String value, int time, TimeUnit timeUnit);
    Set<String> zrangebyscore(String key, double minScore, double maxScore);
    Map<String, Double> zrange(String key, int startIdx, int endIdx);
    Long zCard(String key);
    void zadd(String key, Map<String, Double> data);
    Boolean setIfAbsent(String key, String value, long seconds);
    Set<String> getMembers(String key);
}
