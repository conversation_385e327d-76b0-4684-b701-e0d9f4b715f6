package com.cmc.asset.job.repository.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName AssetCacheRedisRepository.java
 * <AUTHOR>
 * @Date 2021/10/14 19:26
 */
@Repository
public class AssetCacheRedisRepository {
    @Autowired(required = false)
    @Qualifier("assetRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;



    public void deleteAndPutAll(String key, Map<String,String> dataMap) {
        redisTemplate.delete(key);
        redisTemplate.opsForHash().putAll(key, dataMap);
    }

    public void zadd(String key, Set<ZSetOperations.TypedTuple<String>> value) {
        redisTemplate.delete(key);
        redisTemplate.opsForZSet().add(key,value);
    }

    public void set(String key, String value) {
        redisTemplate.opsForValue().set(key,value);
    }

    public void set(String key, String value, Duration timeout) {
        redisTemplate.opsForValue().set(key,value, timeout);
    }

    public String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public Set<String> rangeByScore(String key, Double min, Double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    public Long addAll(String key, Set<ZSetOperations.TypedTuple<String>> tuples) {
        return redisTemplate.opsForZSet().add(key, tuples);
    }

    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    public Boolean delKey(String key) {
        return redisTemplate.delete(key);
    }

}
