package com.cmc.asset.job.integration;

import com.cmc.asset.job.integration.response.MarketPairSearchDTO;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.auth.common.enums.ErrorCode;
import com.cmc.data.common.ApiResponse;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * <AUTHOR>
 * @since 2023/11/14 09:20
 */
@Component
@Slf4j
public class DexServiceClient extends BaseServiceClient {

    @Value("${com.cmc.asset.job.integration.dex.url:}")
    private String dexServiceBaseUrl;

    public static final String DEXER_QUERY_POOL_API = "%s/system/v3/dexer/pools";

    public Mono<List<MarketPairSearchDTO>> getPairList(String tokenAddress, Integer platformId) {
        String url = dexServiceBaseUrl + "/v3/dexer/pair-list?base-address=%s&start=1&limit=10&platform-id=%s&sort=liquidity";
        return client.get(String.format(url, tokenAddress, platformId))
                .map(response -> {
                    ApiResponse<List<MarketPairSearchDTO>> pairs =
                            JSON.parseObject(response.getBody(), new TypeReference<>() {
                            });
                    if (!(ErrorCode.SUCCESS.getCode() + "").equals(
                            pairs.getStatus().getError_code())) {
                        pairs.setData(List.of());
                    }
                    return pairs.getData();
                })
                .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
                .doOnError(throwable -> log.error("getPairList failed", throwable));
    }

    public Mono<List<PairInfoDTO>> getPairs(List<Long> poolIdList) {
        final String url = String.format(DEXER_QUERY_POOL_API, dexServiceBaseUrl);

        // 分批处理每100个元素
        return Flux.fromIterable(partitionList(poolIdList, 100))
                .flatMap(subList ->
                        client.post(url, subList)
                                .flatMap(responseEntity -> {
                                    if(responseEntity == null || StringUtils.isBlank(responseEntity.getBody())){
                                        List<PairInfoDTO> defaultList = new ArrayList<>();
                                        return Mono.just(defaultList);
                                    }
                                    ApiResponse<List<PairInfoDTO>> response =
                                            JacksonUtils.deserialize(responseEntity.getBody(), new com.fasterxml.jackson.core.type.TypeReference<>() {});
                                    return Mono.just(response.getData());
                                })
                                .onErrorResume(throwable -> {
                                    log.error("getPairs failed, url:{}, param:{}", url, subList, throwable);
                                    return Mono.just(new ArrayList<>());
                                })
                )
                .collectList()
                .map(lists -> lists.stream().flatMap(List::stream).collect(Collectors.toList()))
                .publishOn(Schedulers.boundedElastic());
    }

    private <T> List<List<T>> partitionList(List<T> list, int size) {
        List<List<T>> partitioned = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitioned.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitioned;
    }

}
