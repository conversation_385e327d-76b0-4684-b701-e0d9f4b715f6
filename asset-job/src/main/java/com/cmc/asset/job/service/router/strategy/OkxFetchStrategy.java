package com.cmc.asset.job.service.router.strategy;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.BlockChainTokenPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.integration.OkxClient;
import com.cmc.asset.job.integration.response.okx.OkxAddressBalanceDTO;
import com.cmc.asset.job.service.portfolio.manager.AbstractWalletBalanceService;
import com.cmc.asset.job.utils.BigDecimalUtil;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.asset.model.enums.ListingStatus;
import com.cmc.asset.model.enums.PortfolioTokenTypeEnum;
import com.cmc.framework.utils.CollectionUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * Use Okx Api for retrieving balance
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Slf4j
@Component
public class OkxFetchStrategy extends AbstractWalletBalanceService implements FetchStrategy {
    private static final String APOLLO_MAP_CONFIG_KEY = "com.cmc.asset-job.integration-config.oklink-okx-id-map";

    @Resource
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Resource
    private OkxClient okxClient;

    @Resource
    private CryptoCurrencyCache cryptoCurrencyCache;


    @Override
    public Mono<BlockChainPO> fetch(Integer chainId, String address, Date currentTaskTime) {
        Map<Integer, OkLinkMapConfigItem> mapConfig = dynamicApolloRefreshConfig.getOkLinkOkxMapConfig();
        if (!mapConfig.containsKey(chainId)) {
            log.warn("[OkxFetchStrategy.fetch]Map config error -- chainId: {}", chainId);
            return Mono.empty();
        }
        OkLinkMapConfigItem configItem = mapConfig.get(chainId);
        List<Integer> chainIds = new ArrayList<>();
        chainIds.add(configItem.getOkxChainId());
        BlockChainPO blockChainPO = BlockChainPO.builder()
            .chainId(chainId)
            .syncTime(currentTaskTime)
            .tokens(new ArrayList<>())
            .build();
        return okxClient.getAllTokenBalancesByAddress(address, chainIds, false)
            .map(balance -> {
                Map<String, OkxAddressBalanceDTO.TokenAsset> balanceMap = new HashMap<>();
                List<OkxAddressBalanceDTO.TokenAsset> mergedList = new ArrayList<>();
                balance.getTokenAssets().forEach(asset -> {
                    String tokenAddress = asset.getTokenAddress();
                    if (!balanceMap.containsKey(tokenAddress)) {
                        mergedList.add(asset);
                        balanceMap.put(tokenAddress, asset);
                        return;
                    }

                    // map中已存在记录，累加balance
                    OkxAddressBalanceDTO.TokenAsset mergedTokenAsset = balanceMap.get(tokenAddress);
                    BigDecimal totalBalance = new BigDecimal(mergedTokenAsset.getBalance());
                    BigDecimal newBalance = new BigDecimal(asset.getBalance());
                    totalBalance = totalBalance.add(newBalance);
                    mergedTokenAsset.setBalance(totalBalance.toPlainString());
                });


                List<BlockChainTokenPO> blockChainTokens = mergedList
                    .stream()
                    .map(tokenAsset -> convertChainToken(chainId, tokenAsset, Integer.valueOf(configItem.getNativeTokenId())))
                    .collect(Collectors.toList());
                blockChainPO.setNativeTokenId(Integer.valueOf(configItem.getNativeTokenId()));
                blockChainPO.setSyncTime(currentTaskTime);
                blockChainPO.setTokens(blockChainTokens);
                return blockChainPO;
            })
            .flatMap(blockChain -> {
                if (CollectionUtils.isEmpty(blockChain.getTokens())) {
                    return Mono.just(blockChainPO);
                }
                return calTotalTokenValue(blockChain);
            })
            .onErrorResume(e -> {
                log.error("[OkxFetchStrategy.fetch]Fetch error. -- address: {}, chainId: {}, okxChainId: {}", address, chainId, configItem.getOkxChainId(), e);
                return Mono.error(e);
            });
    }

    private BlockChainTokenPO convertChainToken(Integer chainId, OkxAddressBalanceDTO.TokenAsset tokenAsset, Integer nativeTokenId) {
        // 如果为原生代币
        if (StringUtils.isEmpty(tokenAsset.getTokenAddress())) {
            CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(nativeTokenId);
            return BlockChainTokenPO.builder()
                .cryptoId(nativeTokenId)
                .type(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode())
                .canMap(true)
                .amount(decimalFormat128(getOrZero(tokenAsset.getBalance())))
                .cryptoName(currencyInfoDTO.getName())
                .build();
        }

        // 非原生代币
        Integer cryptoId = cryptoCurrencyCache.getIdByContractAddress(chainId, tokenAsset.getTokenAddress());
        CryptoCurrencyInfoDTO currencyInfoDTO = cryptoCurrencyCache.getCryptoCurrencyById(cryptoId);
        //当crypto映射不到cmcId,则保存三方price value与change24h用来展示
        if (cryptoId == null || currencyInfoDTO == null || !ListingStatus.ACTIVE.getCode().equals(currencyInfoDTO.getStatus())) {
            return BlockChainTokenPO.builder().canMap(false)
                .type(PortfolioTokenTypeEnum.UNTRACKED_TOKEN.getCode())
                .cryptoName(tokenAsset.getSymbol())
                .amount(decimalFormat128(getOrZero(tokenAsset.getBalance())))
                .price(decimalFormat128(getOrZero(tokenAsset.getTokenPrice())))
                .contractAddress(tokenAsset.getTokenAddress())
                .build();
        }
        //当crypto可以映射cmcId,则price value与change24h不保存,展示时获取最新的数据
        return BlockChainTokenPO.builder()
            .canMap(true)
            .type(PortfolioTokenTypeEnum.LISTED_TOKEN.getCode())
            .cryptoId(cryptoId)
            .cryptoName(currencyInfoDTO.getName())
            .amount(decimalFormat128(getOrZero(tokenAsset.getBalance())))
            .build();

    }

    private BigDecimal getOrZero(String value){
        return StringUtil.isBlank(value)?BigDecimal.ZERO:new BigDecimal(value);
    }

    /**
     * 格式化decimal128
     */
    private BigDecimal decimalFormat128(BigDecimal date) {
        return BigDecimalUtil.decimalFormat128(date);
    }
}
