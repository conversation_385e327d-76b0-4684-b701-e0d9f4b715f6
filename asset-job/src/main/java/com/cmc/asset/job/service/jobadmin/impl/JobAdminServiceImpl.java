package com.cmc.asset.job.service.jobadmin.impl;

import com.cmc.asset.job.dto.JobAdminDTO;
import com.cmc.asset.job.service.jobadmin.JobAdminService;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import java.util.concurrent.CompletableFuture;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/8/4 14:31:20
 */
@Service
@Slf4j
public class JobAdminServiceImpl implements JobAdminService {

    @Override
    public Boolean executeJobHandler(JobAdminDTO jobAdminDTO) {
        CompletableFuture.runAsync(() -> {
            IJobHandler jobHandler = XxlJobExecutor.loadJobHandler(jobAdminDTO.getJobName());
            try {
                jobHandler.execute(jobAdminDTO.getParam());
            } catch (Throwable e) {
                log.error("executeJobHandler error jobName: {}", jobAdminDTO.getJobName(), e);
            }
        });
        return true;
    }
}
