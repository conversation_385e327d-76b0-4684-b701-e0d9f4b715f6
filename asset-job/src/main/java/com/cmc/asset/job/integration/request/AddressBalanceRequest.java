package com.cmc.asset.job.integration.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AddressBalanceRequest
 *
 * <AUTHOR> ricky.x
 * @date : 2023/2/2 下午5:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressBalanceRequest extends BlockBookBaseRequest{

    /**
     * 当前地址
     */
    private String address;

}
