package com.cmc.asset.job.integration;

import com.cmc.asset.job.enums.OkProtocolTypeEnum;
import com.cmc.asset.job.integration.response.AddressBalanceFillsResponse;
import com.cmc.asset.job.integration.response.AddressBalanceFillsResponse.AddressBalanceFillsDTO;
import com.cmc.asset.job.integration.response.AddressSummaryResponse;
import com.cmc.asset.job.integration.response.AddressSummaryResponse.AddressSummaryDTO;
import com.cmc.asset.job.integration.response.AddressTransactionResponse;
import com.cmc.asset.job.integration.response.AddressTransactionResponse.AddressTransactionDTO;
import com.cmc.asset.job.integration.response.OkLinkBaseResponse;
import com.cmc.asset.job.integration.response.TransactionFillsResponse;
import com.cmc.asset.job.integration.response.TransactionFillsResponse.TransactionFillsDTO;
import com.cmc.asset.job.metric.CounterMetricName;
import com.cmc.asset.job.metric.TimerMetricName;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.data.common.exception.BusinessException;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.DatetimeUtils.LocalDateFormatEnum;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * okLink api client
 * <AUTHOR> ricky.x
 * @date : 2023/2/2 下午6:53
 */
@Component
@Slf4j
public class OkLinkClient extends BaseServiceClient {

    @Value("${com.cmc.asset-job.integration-config.oklink-url}")
    private String okLinkBaseUrl;

    @Value("${com.cmc.asset-job.oklink-config.api-key:d4a3945d-acee-4763-a6d4-a4ee538c69dd,6b50b212-dac6-4faa-bd07-45a0c8b4036d}")
    private List<String> okLinkApiKeyList;

    private final int success_status_code=200;

    private final String body_null_code="999";

    private final String throwable_code = "998";

    private final String success_code="0";

    @Value("${com.cmc.asset-job.integration-config.oklink-request-mills:30000}")
    private Long okLinkRequestMills;

    @Value("${com.cmc.asset-job.integration-config.oklink-transaction-list-time:500}")
    private Long transactionListTime;

    @Value("${com.cmc.asset-job.integration-config.oklink-transaction-fills-time:500}")
    private Long transactionFillsTime;

    @Value("${com.cmc.asset-job.integration-config.oklink-detail-page-limit:50}")
    private Integer pageLimit;

    @Resource
    private CmcMeterRegistry cmcMeterRegistry;

    /**
     * 查询地址在指定链上所有代币及其余额(此接口不包括原生代币)
     * 此接口ok提供了分页,但是实际分页失败,与ok沟通去除掉limit和page
     * @param chainCode chainCode
     * @param address 地址
     * @return response
     */
    public Mono<AddressBalanceFillsDTO> addressBalanceFills(String chainCode, String address, int page) {
        String addressBalanceFills = "/api/v5/explorer/address/address-balance-fills";
        String url = String.format(okLinkBaseUrl.concat(addressBalanceFills)
            .concat("?chainShortName=%s&protocolType=token_20&address=%s&page=%s&limit=%s"), chainCode, address, page, pageLimit);
        return getByOkLink(url,Duration.ofMillis(okLinkRequestMills),"address-balance-fills").flatMap(e -> {
            AddressBalanceFillsResponse response = JacksonUtils.deserialize(e, AddressBalanceFillsResponse.class);
            if(CollectionUtils.isEmpty(response.getData())){
                return Mono.empty();
            }
            AddressBalanceFillsDTO addressBalanceFillsDTO = JacksonUtils.deserialize(e, AddressBalanceFillsResponse.class)
                    .getData()
                    .get(0);
            log.debug("OkLinkClient addressBalanceFills response:{}", addressBalanceFillsDTO);
            return Mono.just(addressBalanceFillsDTO);
        }).onErrorResume(throwable -> {
            log.error("OkLinkClient get error,addressBalanceFills error,request:{}", url, throwable);
            return Mono.empty();
        });
    }

    /**
     * 查询地址基本信息(主要查询原生代币)
     * @param chainCode chainCode
     * @param address 地址
     * @return response
     */
    public Mono<AddressSummaryDTO> addressSummary(String chainCode, String address) {
        String addressBaseInfo = "/api/v5/explorer/address/address-summary";
        String url = String.format(okLinkBaseUrl.concat(addressBaseInfo)
            .concat("?chainShortName=%s&address=%s"), chainCode, address);

        return getByOkLink(url,Duration.ofMillis(okLinkRequestMills),"address-summary").flatMap(e -> {
            AddressSummaryResponse response = JacksonUtils.deserialize(e, AddressSummaryResponse.class);
            if(CollectionUtils.isEmpty(response.getData())){
                return Mono.empty();
            }
            AddressSummaryDTO summaryDTO = JacksonUtils.deserialize(e, AddressSummaryResponse.class).getData().get(0);
            log.debug("OkLinkClient addressSummary response:{}", summaryDTO);
            return Mono.just(summaryDTO);
        }).onErrorResume(throwable -> {
            log.error("OkLinkClient get error,addressSummary error,request:{},throwable:", url, throwable);
            return Mono.empty();
        });
    }


    /**
     * 查询地址在指定链上所有代币及其余额(此接口不包括原生代币)
     * 此接口ok提供了分页,但是实际分页失败,与ok沟通去除掉limit和page
     * @param chainCode chainCode
     * @param address 地址
     * @return response
     */
    public Mono<AddressTransactionDTO> transactionList(String chainCode, String address, OkProtocolTypeEnum type, int page) {
        String addressBalanceFills = "/api/v5/explorer/address/transaction-list";
        String url = String.format(okLinkBaseUrl.concat(addressBalanceFills)
            .concat("?chainShortName=%s&address=%s&protocolType=%s&page=%s&limit=100"),chainCode, address,type.getCode(),page);
        return getByOkLink(url,Duration.ofMillis(transactionListTime),"transaction-list").flatMap(e -> {
            AddressTransactionResponse response = JacksonUtils.deserialize(e, AddressTransactionResponse.class);
            if(CollectionUtils.isEmpty(response.getData())){
                return Mono.empty();
            }
            return Mono.just(JacksonUtils.deserialize(e, AddressTransactionResponse.class).getData().get(0));
        }).onErrorResume(throwable -> {
            log.error("OkLinkClient get error,addressTransActionList error,request:{}", url, throwable);
            return Mono.error(throwable);
        });
    }

    /**
     * 获取tansaction详情
     * @param chainCode chain
     * @param txId txId
     * @return response
     */
    public Mono<List<TransactionFillsDTO>> transactionFills(String chainCode, String txId) {
        String addressBalanceFills = "/api/v5/explorer/transaction/transaction-fills";
        String url = String.format(okLinkBaseUrl.concat(addressBalanceFills)
            .concat("?chainShortName=%s&txid=%s"),chainCode,txId);
        return getByOkLink(url,Duration.ofMillis(transactionFillsTime),"transaction-fills").flatMap(e -> {
            TransactionFillsResponse response = JacksonUtils.deserialize(e, TransactionFillsResponse.class);
            if(CollectionUtils.isEmpty(response.getData())){
                return Mono.empty();
            }
            return Mono.just(JacksonUtils.deserialize(e, TransactionFillsResponse.class).getData());
        }).onErrorResume(throwable -> {
            log.error("OkLinkClient get error,transactionFills error,request:{}", url, throwable);
            return Mono.error(throwable);
        });
    }

    private Mono<String> getByOkLink(String url,Duration time,String method) {
        Timer.Sample timer = Timer.start();
        String requestTime = LocalDateFormatEnum.YYYY_MM_DD.getDateFormatter().format(LocalDateTime.now());
        Random random = new Random();
        int n = random.nextInt(okLinkApiKeyList.size());
        return client.get(url, httpHeaders -> {
            httpHeaders.add("Content-Type", "application/json");
            httpHeaders.add("Ok-Access-Key", okLinkApiKeyList.get(n));
        }).flatMap(e -> {
            if (e.getStatusCodeValue() != success_status_code || e.getBody() == null) {
                cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OK_EXCEPTION_SUM,"date",requestTime,"method",method,"statusCode",
                    StringUtil.isBlank(e.getBody()) ? body_null_code : e.getStatusCodeValue() + "").increment();
                return Mono.error(new BusinessException("ok client error,statusCode error:".concat(Integer.toString(e.getStatusCodeValue()))));
            }
            OkLinkBaseResponse response = JacksonUtils.deserialize(e.getBody(), OkLinkBaseResponse.class);
            cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OK_SUCCESS_SUM,"date",requestTime,"method",method,"responseCode",response.getCode()).increment();
            if (!response.getCode().equals(success_code)) {
                return Mono.error(new BusinessException("ok client error,response error:".concat(JacksonUtils.serialize(response))));
            }
            return Mono.just(e.getBody());
        }).onErrorResume(throwable -> {
            if (throwable instanceof BusinessException) {
                return Mono.error(throwable);
            }
            cmcMeterRegistry.counter(CounterMetricName.INTEGRATION_OK_EXCEPTION_SUM, "date", requestTime, "method",
                method, "statusCode", throwable_code).increment();
            return Mono.error(throwable);
        })
        .publishOn(Schedulers.boundedElastic())
        .doOnNext(r -> timer.stop(cmcMeterRegistry.timer(TimerMetricName.INTEGRATION_OK_EXECUTE_SECONDS, "method", method)));
    }

}