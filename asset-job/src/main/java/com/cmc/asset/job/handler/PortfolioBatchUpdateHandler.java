package com.cmc.asset.job.handler;

import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.data.PortfolioJobData;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.dto.PortfolioBatchUpdateData;
import com.cmc.asset.job.service.portfolio.IPortfolioCalculateService;
import com.cmc.asset.job.utils.BeanFactoryUtil;
import com.cmc.asset.job.utils.StringUtil;
import com.cmc.asset.model.enums.PortfolioMessageOperateEnum;
import com.cmc.asset.model.enums.PortfolioOperateEnum;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Slf4j
@Component
public class PortfolioBatchUpdateHandler implements JobHandler<List<PortfolioBatchUpdateData>, Boolean> {
    @Resource
    private BeanFactoryUtil beanFactoryUtil;

    @Resource
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Override
    public JobHandlingResult<Boolean> handle(List<PortfolioBatchUpdateData> jobData) {
        PortfolioMessageOperateEnum type = PortfolioMessageOperateEnum.MANUAL;
        Date pushTime = new Date();

        List<PortfolioBatchUpdateData> dataList = dynamicApolloRefreshConfig.getBatchUpdateUserPortfolioList();

        List<PortfolioJobData> jobDataList = dataList.stream()
            .filter(data -> StringUtil.isNotEmpty(data.getUserId())
                && StringUtil.isNotEmpty(data.getPortfolioSourceId()))
            .map(data -> {
                PortfolioJobData portfolioJobData = PortfolioJobData.builder()
                    .userId(data.getUserId())
                    .sourceId(data.getPortfolioSourceId())
                    .pushTime(pushTime)
                    .portfolioType(type.getCode())
                    .manualOperate(PortfolioOperateEnum.ADD.getCode())
                    .build();
                return portfolioJobData;
            })
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(jobDataList)) {
            return new JobHandlingResult<>(true);
        }

        IPortfolioCalculateService service = beanFactoryUtil.getPortfolioCalculateService(type);
        jobDataList.forEach(data -> service.operateHandle(data));
        return new JobHandlingResult<>(true);
    }
}
