package com.cmc.asset.job.utils;

import com.cmc.framework.utils.MathUtils;
import com.cmc.framework.utils.StringUtils;
import java.math.BigDecimal;
import java.math.MathContext;
import org.springframework.stereotype.Component;

/**
 * BigDecimalUtil
 * <AUTHOR> ricky.x
 * @date : 2023/2/2 下午2:32
 */
@Component
public class BigDecimalUtil {

    /**
     * 格式化decimal128
     */
    public static BigDecimal decimalFormat128(BigDecimal date){
        String formatDecimal = MathUtils.getOrZero(date).stripTrailingZeros().toPlainString();
        return new BigDecimal(formatDecimal).round(MathContext.DECIMAL128);
    }

    /**
     * 格式化decimal128
     */
    public static BigDecimal stringFormat128(String dateStr){
        BigDecimal date = StringUtils.isEmpty(dateStr)?BigDecimal.ZERO:new BigDecimal(dateStr);
        return decimalFormat128(date);
    }

}
