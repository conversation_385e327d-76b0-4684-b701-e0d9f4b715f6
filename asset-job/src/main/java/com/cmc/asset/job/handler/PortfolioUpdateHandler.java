package com.cmc.asset.job.handler;

import com.cmc.asset.job.data.PortfolioJobData;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.utils.BeanFactoryUtil;
import com.cmc.asset.model.enums.PortfolioMessageOperateEnum;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR> Z
 * @CreateTime 2024-07-23
 */
@Slf4j
@Component
public class PortfolioUpdateHandler implements JobHandler<PortfolioJobData, Boolean> {

    @Autowired
    private BeanFactoryUtil beanFactoryUtil;

    @Override
    public JobHandlingResult<Boolean> handle(PortfolioJobData portfolioJobData) {
        PortfolioMessageOperateEnum type = PortfolioMessageOperateEnum.MANUAL;
        if (StringUtils.isNotBlank(portfolioJobData.getPortfolioType())) {
            type = PortfolioMessageOperateEnum.findByCode(portfolioJobData.getPortfolioType());
            if (type == null) {
                log.warn("PortfolioHoldingsHandler type not match portfolioType {}", portfolioJobData.getPortfolioType());
                return new JobHandlingResult<>(true);
            }
        }

        if (StringUtils.isBlank(portfolioJobData.getUserId()) || StringUtils.isBlank(portfolioJobData.getSourceId())) {
            log.warn("PortfolioHoldingsHandler userId or sourceId is empty: {}", JacksonUtils.toJson(portfolioJobData));
            return new JobHandlingResult<>(true);
        }

        beanFactoryUtil.getPortfolioCalculateService(type).operateHandle(portfolioJobData);
        return new JobHandlingResult<>(true);
    }

}
