package com.cmc.asset.job.service.dex.impl;

import static com.cmc.asset.model.common.RedisConstants.KEY_DEXER_TOKEN_CACHE_KEY;

import com.cmc.asset.job.integration.DexerApiClient;
import com.cmc.asset.job.service.dex.DexerService;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @since 2024/7/15 09:38
 */
@Service
public class DexerServiceImpl implements DexerService {

    @Autowired
    private DexerApiClient dexerApiClient;
    @Autowired
    private ReactiveRedisTemplate<String, String> assetAsyncRedisTemplate;

    @Value("${com.cmc.asset.dexer.cache.expiration.seconds:300}")
    private int cacheExpirationSeconds;
    @Value("${com.cmc.asset.dexer.query.batch-size:50}")
    private int batchSize;
    @Value("${com.cmc.asset.dexer.query.max-batch-concurrency:3}")
    private int maxBatchConcurrency;

    @Override
    public Mono<List<PlatformNewDTO>> getPlatformsFromDeQuery() {
        return dexerApiClient.getPlatformsFromDeQuery();
    }

    /**
     * Batch get token price info from DeQuery by platform and address list
     */
    @Override
    public Mono<List<TokenPriceDTO>> getPairsFromDeQuery(List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens) {
        if (tokens == null || tokens.isEmpty()) {
            return Mono.just(List.of());
        }
        List<String> cacheKeys = tokens.stream()
            .map(token -> String.format(KEY_DEXER_TOKEN_CACHE_KEY, token.getPlatform(), token.getAddress()))
            .collect(Collectors.toList());
        return assetAsyncRedisTemplate.opsForValue()
            .multiGet(cacheKeys)
            .publishOn(Schedulers.boundedElastic())
            .defaultIfEmpty(List.of())
            .flatMap(cachedValues -> {
                Map<String, TokenPriceDTO> cachedMap = IntStream.range(0, tokens.size())
                    .filter(i -> StringUtils.isNotEmpty(cachedValues.get(i)))
                    .boxed()
                    .collect(Collectors.toMap(cacheKeys::get,
                        i -> JacksonUtils.deserialize(cachedValues.get(i), TokenPriceDTO.class)));
                List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> missingTokens = IntStream.range(0, tokens.size())
                    .filter(i -> !cachedMap.containsKey(cacheKeys.get(i)))
                    .mapToObj(tokens::get)
                    .collect(Collectors.toList());
                List<TokenPriceDTO> result = new ArrayList<>(cachedMap.values());
                if (missingTokens.isEmpty()) {
                    return Mono.just(result);
                }
                return queryTokenPrice(missingTokens).doOnNext(dto -> {
                    if (dto != null && dto.getAddress() != null && dto.getPlatformDexerName() != null) {
                        String key =
                            String.format(KEY_DEXER_TOKEN_CACHE_KEY, dto.getPlatformDexerName(), dto.getAddress());
                        assetAsyncRedisTemplate.opsForValue()
                            .set(key, JacksonUtils.serialize(dto), Duration.ofSeconds(cacheExpirationSeconds))
                            .subscribe();
                    }
                }).collectList().map(apiList -> {
                    result.addAll(apiList);
                    return result;
                });
            });
    }

    @NotNull
    private Flux<TokenPriceDTO> queryTokenPrice(List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> missingTokens) {
        return Flux.fromIterable(missingTokens)
            .buffer(batchSize)
            .flatMap(batch -> dexerApiClient.getPairListByDeQuery(batch), maxBatchConcurrency)
            .flatMap(Flux::fromIterable);
    }

}
