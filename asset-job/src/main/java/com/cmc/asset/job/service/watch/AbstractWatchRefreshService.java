package com.cmc.asset.job.service.watch;

import com.cmc.asset.job.integration.DexServiceClient;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import com.cmc.framework.utils.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;


/**
 * AbstractTrendingService
 * <AUTHOR> ricky.x
 * @date: 2025/5/6 16:24
 */
@Slf4j
public abstract class AbstractWatchRefreshService<T> {


    @Resource
    private DexServiceClient dexServiceClient;

    @Value("${com.cmc.asset-job.dex-token.watchlist.query-db-size:200}")
    protected Integer queryDbSize;

    @Value("${com.cmc.asset.watchlist.refresh.skip-pair:}")
    private Set<Long> skipPairInfo;


    public Mono<Boolean> process(ObjectId startId, ObjectId endId) {
      return processPlatformRecursively(startId,endId, null);
    }


    /**
     * 递归所有符合条件的init数据
     * 过滤
     * save到db
     */
    private Mono<Boolean> processPlatformRecursively(ObjectId startId, ObjectId endId,ObjectId latestId) {
        return queryPendingData(startId, endId, latestId, queryDbSize)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(this::processAndSave)
                .flatMap(result -> {
                    log.info("WatchListTokenRefreshHandler batch trade size:{} nextCursor:{}", result.getT1(), result.getT2());
                    int batchSize = result.getT1();
                    Optional<ObjectId> nextCursor = result.getT2();
                    if (nextCursor.isPresent() && batchSize >= queryDbSize) {
                        return processPlatformRecursively(startId,endId, nextCursor.get());
                    }
                    return Mono.just(Boolean.TRUE);
                })
                .defaultIfEmpty(Boolean.TRUE);
    }

    protected abstract Mono<List<T>> queryPendingData(ObjectId startId, ObjectId endId, ObjectId latestId, int limit);



    protected abstract Mono<Tuple2<Integer, Optional<ObjectId>>> processAndSave(List<T> entities);





    protected Mono<Map<Long, PairInfoDTO>> transPoolIdToDexTokens(List<Long> pairs) {
        pairs = pairs.stream().filter(e -> CollectionUtils.isEmpty(skipPairInfo) || !skipPairInfo.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pairs)) {
            return Mono.just(Map.of());
        }
        return dexServiceClient.getPairs(pairs)
                .map(e -> e.stream()
                        .filter(dto -> dto != null && dto.getPoolId() != null)
                        .collect(Collectors.toMap(PairInfoDTO::getPoolId, dto -> dto)))
                .defaultIfEmpty(Map.of());
    }


}
