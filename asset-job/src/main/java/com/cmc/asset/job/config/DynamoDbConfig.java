package com.cmc.asset.job.config;

import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint1dEntity;
import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint1hEntity;
import com.cmc.asset.dao.entity.dynamondb.CryptoDatapoint5mEntity;
import com.cmc.framework.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClientBuilder;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClientBuilder;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2022/3/20 15:00:00
 */
@Configuration
public class DynamoDbConfig {

    @Value("${com.cmc.asset.ddbUrl:}")
    private String ddbUrl;

    @Value("${com.cmc.asset.dynamodb.crypto-5m.name:crypto_dp_5m_v2_beta}")
    private String cryptoDp5mDynamodbTableName;

    @Value("${com.cmc.asset.dao-dynamodb-config.crypto-1h-name:crypto_ohlcv_1h_v2_beta}")
    private String cryptoDp1hDynamodbTableName;

    @Value("${com.cmc.asset.dao-dynamodb-config.crypto-1d-name:crypto_ohlcv_1d_v2_beta}")
    private String cryptoDp1dDynamodbTableName;

    @Bean
    public DynamoDbClient getDynamoDbClient() {
        DynamoDbClientBuilder builder = DynamoDbClient.builder();
        if (StringUtils.isNotEmpty(ddbUrl)) {
            builder.endpointOverride(URI.create(ddbUrl));
        }
        return builder.credentialsProvider(DefaultCredentialsProvider.create()).build();
    }

    @Bean
    public DynamoDbEnhancedClient getDynamoDbEnhancedClient() {
        return DynamoDbEnhancedClient.builder()
            .dynamoDbClient(getDynamoDbClient())
            .build();
    }

    @Bean
    public DynamoDbAsyncClient getDynamoDbAsyncClient() {
        DynamoDbAsyncClientBuilder builder = DynamoDbAsyncClient.builder();
        if (StringUtils.isNotEmpty(ddbUrl)) {
            builder.endpointOverride(URI.create(ddbUrl));
        }
        return builder.credentialsProvider(DefaultCredentialsProvider.create()).build();
    }


    @Bean
    public DynamoDbEnhancedAsyncClient getDynamoDbEnhancedAsyncClient() {
        return DynamoDbEnhancedAsyncClient.builder().dynamoDbClient(getDynamoDbAsyncClient()).build();
    }

    @Bean
    public DynamoDbAsyncTable<CryptoDatapoint5mEntity> cryptoDatapoint5mTable(DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient) {
        return dynamoDbEnhancedAsyncClient.table(cryptoDp5mDynamodbTableName, TableSchema.fromBean(
            CryptoDatapoint5mEntity.class));
    }

    @Bean
    public DynamoDbAsyncTable<CryptoDatapoint1hEntity> cryptoDatapoint1hTable(DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient) {
        return dynamoDbEnhancedAsyncClient.table(cryptoDp1hDynamodbTableName, TableSchema.fromBean(
                CryptoDatapoint1hEntity.class));
    }

    @Bean
    public DynamoDbAsyncTable<CryptoDatapoint1dEntity> cryptoDatapoint1dTable(DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient) {
        return dynamoDbEnhancedAsyncClient.table(cryptoDp1dDynamodbTableName, TableSchema.fromBean(
                CryptoDatapoint1dEntity.class));
    }
}

