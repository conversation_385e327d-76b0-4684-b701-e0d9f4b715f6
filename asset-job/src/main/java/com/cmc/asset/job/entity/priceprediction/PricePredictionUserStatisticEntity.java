package com.cmc.asset.job.entity.priceprediction;

import java.math.BigDecimal;
import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021-06-29 14:46:58
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "price_prediction_user_statistic")
public class PricePredictionUserStatisticEntity {

    private ObjectId id;

    private String userId;

    private BigDecimal averageAccuracy;

    private BigDecimal coverage;

    private Integer estimateScore;   //预测综合得分,排名依据,4位小数放大一万倍存储为整数,读取时转换为BigDecimal 8848->88.48%

    private Integer rankings;   //前200排名

    private BigDecimal distribution;    //前50%-95% 分布

    private PricePredictionEntity bestAccuracy;    //6个月内最好预测结果

    private Integer statisticTime; //统计月份,6位int(202106) 和 estimateScore 做索引

    private Integer estimateCount;   //有效预测数量,最近6个月内,Accuracy不为null的记录

    private Date updateTime;

}