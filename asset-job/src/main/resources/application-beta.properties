## producer dexer_trade_notification topic
cmc.boot.kafka.settings.notification.producer.dexer_trade_notification.ack = all
cmc.boot.kafka.settings.notification.producer.dexer_trade_notification.keySerializer = org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.notification.producer.dexer_trade_notification.valueSerializer = org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.notification.producer.dexer_trade_notification.requestTimeout = 60000

#consumer cmc_dexer_lp_subscribed_trade_event topic from dexer trade event
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.keyDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.valueDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.autoOffsetReset = latest
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.sessionTimeout = 300000
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.heartbeatInterval = 3000
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.maxPollRecords = 100
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.requestTimeout = 60000
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.enableAutoCommit = false
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.maxPollInterval = 120000
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.clientId = asset_dexer_trade_event_client
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.groupId = asset_dexer_trade_event_group
cmc.boot.kafka.settings.dexer.consumer.cmc_dexer_lp_subscribed_trade_event.subscribe = com.cmc.asset.job.consumer.DexLpTradeEventConsumer
cmc.boot.kafka.settings.dexer.bootstrapServers = msk.beta.coinmarketcap.supply:9092

#consumer user monitor collector
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.keyDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.valueDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.autoOffsetReset = latest
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.sessionTimeout = 300000
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.heartbeatInterval = 3000
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.maxPollRecords = 100
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.requestTimeout = 60000
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.enableAutoCommit = false
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.maxPollInterval = 120000
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.clientId = user_monitor_collector_dexer_client
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.groupId = user_monitor_collector_dexer_group
cmc.boot.kafka.settings.dexer.consumer.user_monitor_collector.subscribe = com.cmc.asset.job.consumer.UserMonitorCollectorConsumer

#consumer user monitor collector
cmc.boot.kafka.settings.user.bootstrapServers = 10.83.42.95:9092,10.83.43.188:9092,10.83.41.151:9092
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.keyDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.valueDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.autoOffsetReset = latest
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.sessionTimeout = 300000
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.heartbeatInterval = 3000
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.maxPollRecords = 100
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.requestTimeout = 60000
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.enableAutoCommit = false
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.maxPollInterval = 120000
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.clientId = user_domain_delete_beta_asset_job_client
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.groupId = user_domain_delete_beta_asset_job_group
cmc.boot.kafka.settings.user.consumer.user_domain_delete_beta.subscribe = com.cmc.asset.job.consumer.UserDomainDeleteConsumer

#risk-service
com.cmc.asset.client.risk=https://api.beta.coinmarketcap.supply/risk-control
