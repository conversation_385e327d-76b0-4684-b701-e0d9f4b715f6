package com.cmc.asset.job.service;

import com.cmc.asset.job.integration.BaseDataServiceClient;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoAggregationVo;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyInfoDetailCacheV3VO;
import com.cmc.asset.model.contract.crypto.CryptoCurrencyQuotesLatestCacheV3VO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class PortfolioCryptoRedisServiceTest {

    @Mock
    private BaseDataServiceClient mockBaseDataServiceClient;

    @InjectMocks
    private PortfolioCryptoRedisService portfolioCryptoRedisServiceUnderTest;

    private AutoCloseable mockitoCloseable;

    public PortfolioCryptoRedisServiceTest() {
    }

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testQueryCryptoInfo() {
        // Setup
        // Configure BaseDataServiceClient.getCryptoCurrencyQuotesDetailExtra(...).
        final CryptoCurrencyInfoAggregationVo cryptoCurrencyInfoAggregationVo = new CryptoCurrencyInfoAggregationVo();
        final CryptoCurrencyInfoDetailCacheV3VO cryptoCurrencyInfoDetailCacheV3VO =
            new CryptoCurrencyInfoDetailCacheV3VO();
        cryptoCurrencyInfoDetailCacheV3VO.setId(0);
        cryptoCurrencyInfoDetailCacheV3VO.setName("name");
        cryptoCurrencyInfoDetailCacheV3VO.setSymbol("symbol");
        cryptoCurrencyInfoDetailCacheV3VO.setCategory("category");
        cryptoCurrencyInfoDetailCacheV3VO.setSlug("slug");
        cryptoCurrencyInfoDetailCacheV3VO.setIsActive(0);
        cryptoCurrencyInfoDetailCacheV3VO.setIsHidden(0);
        cryptoCurrencyInfoDetailCacheV3VO.setIsListed(0);
        cryptoCurrencyInfoDetailCacheV3VO.setRank(0);
        cryptoCurrencyInfoDetailCacheV3VO.setContractAddresses(List.of("value"));
        cryptoCurrencyInfoAggregationVo.setDetailCacheV3VOs(List.of(cryptoCurrencyInfoDetailCacheV3VO));
        final CryptoCurrencyQuotesLatestCacheV3VO cryptoCurrencyQuotesLatestCacheV3VO =
            new CryptoCurrencyQuotesLatestCacheV3VO();
        cryptoCurrencyQuotesLatestCacheV3VO.setId(0);
        cryptoCurrencyQuotesLatestCacheV3VO.setCirculatingSupply(new BigDecimal("0.00"));
        cryptoCurrencyQuotesLatestCacheV3VO.setMaxSupply(new BigDecimal("0.00"));
        cryptoCurrencyQuotesLatestCacheV3VO.setNumMarkets(0);
        cryptoCurrencyQuotesLatestCacheV3VO.setRank(0);
        cryptoCurrencyQuotesLatestCacheV3VO.setTotalSupply(new BigDecimal("0.00"));
        cryptoCurrencyQuotesLatestCacheV3VO.setPriceUsd(new BigDecimal("0.00"));
        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsd(new BigDecimal("0.00"));
        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdChangePercentage(new BigDecimal("0.00"));
        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdReportedChangePercentage(new BigDecimal("0.00"));
        cryptoCurrencyInfoAggregationVo.setQuotesLatestCacheV3VOS(List.of(cryptoCurrencyQuotesLatestCacheV3VO));
        final Mono<CryptoCurrencyInfoAggregationVo> cryptoCurrencyInfoAggregationVoMono =
            Mono.just(cryptoCurrencyInfoAggregationVo);
        when(mockBaseDataServiceClient.getCryptoCurrencyQuotesDetailExtra(List.of(0)))
            .thenReturn(cryptoCurrencyInfoAggregationVoMono);


        // Run the test
        final Mono<PortfolioCryptoInfoDTO> result = portfolioCryptoRedisServiceUnderTest
            .queryCryptoInfo(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results

        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

}
