package com.cmc.asset.job.service.portfolio.manager;

import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.portfolio.ChainTransactionPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.cache.DexTokenCache;
import com.cmc.asset.job.cache.DexerPlatformCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.integration.OkxClient;
import com.cmc.asset.job.integration.response.okx.OkxTransactionDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class OkxManagerTest {
    @InjectMocks
    private OkxManager okxManager;

    @Mock
    private OkxClient okxClient;

    @Mock
    private CryptoCurrencyCache cryptoCurrencyCache;

    @Mock
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;
    @Mock
    private DexerPlatformCache dexerPlatformCache;
    @Mock
    private DexTokenCache dexTokenCache;
    private AutoCloseable mockitoCloseable;

    private Map<Integer, OkLinkMapConfigItem> configItemMap;

    private TokenPriceDTO tokenPriceDTO;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);

        configItemMap = new HashMap<>();
        OkLinkMapConfigItem configItem = new OkLinkMapConfigItem();
        configItem.setChainId(1);
        configItem.setNativeTokenId("1027");
        configItemMap.put(1, configItem);

        tokenPriceDTO = new TokenPriceDTO();
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void getNearLimitTransActionDetails() {
        List<Integer> chainIds = new ArrayList<>();
        chainIds.add(1);
        List<OkxTransactionDTO.Transaction> transactionList1 = new ArrayList<>();
        List<OkxTransactionDTO.Transaction> transactionList2 = new ArrayList<>();
        List<OkxTransactionDTO.Transaction> transactionList3 = new ArrayList<>();
        List<OkxTransactionDTO.Transaction> transactionList4 = new ArrayList<>();
        OkxTransactionDTO.TransactionDetail detail = OkxTransactionDTO.TransactionDetail.builder()
            .address("test")
            .amount("0")
            .build();
        List<OkxTransactionDTO.TransactionDetail> from = new ArrayList<>();
        from.add(detail);
        List<OkxTransactionDTO.TransactionDetail> to = new ArrayList<>();
        to.add(detail);
        OkxTransactionDTO.Transaction transaction1 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("1").tokenAddress("abc").iType("").amount("1").build();
        OkxTransactionDTO.Transaction transaction2 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("1").iType("0").txFee("100").amount("1").build();
        OkxTransactionDTO.Transaction transaction3 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("3").tokenAddress("abc").iType("2").amount("1").build();
        OkxTransactionDTO.Transaction transaction4 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("3").iType("0").txFee("100").amount("1").build();
        transactionList1.add(transaction1);
        transactionList2.add(transaction2);
        transactionList3.add(transaction3);
        transactionList4.add(transaction4);
        OkxTransactionDTO firstResponseData = OkxTransactionDTO.builder().cursor("1").transactionList(transactionList1).build();
        OkxTransactionDTO secondResponseData = OkxTransactionDTO.builder().cursor("2").transactionList(transactionList2).build();
        OkxTransactionDTO thirdResponseData = OkxTransactionDTO.builder().cursor("3").transactionList(transactionList3).build();
        OkxTransactionDTO fourthResponseData = OkxTransactionDTO.builder().cursor("").transactionList(transactionList4).build();
        Mockito.when(dynamicApolloRefreshConfig.getOkLinkOkxMapConfig()).thenReturn(configItemMap);
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq(""))).thenReturn(Mono.just(firstResponseData));
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq("1"))).thenReturn(Mono.just(secondResponseData));
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq("2"))).thenReturn(Mono.just(thirdResponseData));
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq("3"))).thenReturn(Mono.just(fourthResponseData));
        Mockito.when(cryptoCurrencyCache.getIdByContractAddress(Mockito.anyInt(), Mockito.anyString())).thenReturn(1);
        Mono<ChainTransactionPO> result = okxManager.getNearLimitTransActionDetails(1, "test", 20);
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void getNearLimitTransActionDetails_lessData() {
        List<Integer> chainIds = new ArrayList<>();
        chainIds.add(1);
        List<OkxTransactionDTO.Transaction> transactionList1 = new ArrayList<>();
        List<OkxTransactionDTO.Transaction> transactionList2 = new ArrayList<>();
        List<OkxTransactionDTO.Transaction> transactionList3 = new ArrayList<>();
        List<OkxTransactionDTO.Transaction> transactionList4 = new ArrayList<>();
        OkxTransactionDTO.TransactionDetail detail = OkxTransactionDTO.TransactionDetail.builder()
            .address("test")
            .amount("0")
            .build();
        List<OkxTransactionDTO.TransactionDetail> from = new ArrayList<>();
        from.add(detail);
        List<OkxTransactionDTO.TransactionDetail> to = new ArrayList<>();
        to.add(detail);
        OkxTransactionDTO.Transaction transaction1 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("1").tokenAddress("abc").iType("2").amount("1").build();
        OkxTransactionDTO.Transaction transaction2 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("1").iType("0").txFee("100").amount("1").build();
        OkxTransactionDTO.Transaction transaction3 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("3").tokenAddress("abc").iType("2").amount("1").build();
        OkxTransactionDTO.Transaction transaction4 = OkxTransactionDTO.Transaction.builder().from(from).to(to).txHash("3").iType("0").txFee("100").amount("1").build();
        transactionList1.add(transaction1);
        transactionList2.add(transaction2);
        transactionList3.add(transaction3);
        transactionList4.add(transaction4);
        OkxTransactionDTO firstResponseData = OkxTransactionDTO.builder().cursor("1").transactionList(transactionList1).build();
        OkxTransactionDTO secondResponseData = OkxTransactionDTO.builder().cursor("2").transactionList(transactionList2).build();
        OkxTransactionDTO thirdResponseData = OkxTransactionDTO.builder().cursor("").transactionList(transactionList3).build();
        Mockito.when(dynamicApolloRefreshConfig.getOkLinkOkxMapConfig()).thenReturn(configItemMap);
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq(""))).thenReturn(Mono.just(firstResponseData));
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq("1"))).thenReturn(Mono.just(secondResponseData));
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq("2"))).thenReturn(Mono.just(thirdResponseData));
        Mockito.when(okxClient.getTransactionsByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.eq("3"))).thenReturn(Mono.just(thirdResponseData));
        Mockito.when(cryptoCurrencyCache.getIdByContractAddress(Mockito.anyInt(), Mockito.anyString())).thenReturn(null);
        PlatformNewDTO v1 = new PlatformNewDTO();
        v1.setDn("eth");
        TokenPriceDTO tokenPriceDTO = new TokenPriceDTO();
        Mockito.when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, v1, 14, v1));
        Mockito.when(dexTokenCache.get(Mockito.anyString(), Mockito.anyString())).thenReturn(Mono.just(tokenPriceDTO));
        Mono<ChainTransactionPO> result = okxManager.getNearLimitTransActionDetails(1, "test", 100);
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }
}