package com.cmc.asset.job.data;
import java.time.LocalDate;
import java.util.ArrayList;

import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class PricePredictionUserIdStatisticJobDataTest {

    private PricePredictionUserIdStatisticJobData pricePredictionUserIdStatisticJobDataUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        pricePredictionUserIdStatisticJobDataUnderTest = new PricePredictionUserIdStatisticJobData();
        pricePredictionUserIdStatisticJobDataUnderTest.setUsers(new ArrayList<>());
        pricePredictionUserIdStatisticJobDataUnderTest.setTargetDate(LocalDate.now());
        pricePredictionUserIdStatisticJobDataUnderTest.setEnd(0);



    }

    @Test
    public void testEquals() {
        assertFalse(pricePredictionUserIdStatisticJobDataUnderTest.equals(new PricePredictionUserIdStatisticJobData()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(pricePredictionUserIdStatisticJobDataUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, pricePredictionUserIdStatisticJobDataUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", pricePredictionUserIdStatisticJobDataUnderTest.toString());
    }
}
