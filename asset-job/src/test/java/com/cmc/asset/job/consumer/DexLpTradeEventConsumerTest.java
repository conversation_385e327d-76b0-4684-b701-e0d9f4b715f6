package com.cmc.asset.job.consumer;

import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.alert.UserTradeAlertEntity;
import com.cmc.asset.job.dto.DexLpSubscribeTradeEventDTO;
import com.cmc.asset.job.repository.mongo.asset.UserTradeAlertRepository;
import com.cmc.asset.job.repository.redis.AssetCacheRedisRepository;
import com.cmc.asset.job.service.kafka.KafkaProducerSenderService;
import java.util.Set;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2023/1/12
 */
public class DexLpTradeEventConsumerTest {
    @Mock
    private AssetCacheRedisRepository assetCacheRedisRepository;

    @Mock
    private UserTradeAlertRepository userTradeAlertRepository;

    @Mock
    private KafkaProducerSenderService kafkaProducerSenderService;

    @InjectMocks
    private DexLpTradeEventConsumer dexLpTradeEventConsumer;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(dexLpTradeEventConsumer, "enableTradeEventConsume", true);
        ReflectionTestUtils.setField(dexLpTradeEventConsumer, "dexTradeAlertTopic", "dexer_trade_notification");
        ReflectionTestUtils.setField(dexLpTradeEventConsumer, "cacheLimit", 1);
        ReflectionTestUtils.setField(dexLpTradeEventConsumer, "title", "");
        ReflectionTestUtils.setField(dexLpTradeEventConsumer, "content", "");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testConsumerWhenEventNotValid() {
        DexLpSubscribeTradeEventDTO event = DexLpSubscribeTradeEventDTO.builder().totalUsd("100").build();
        dexLpTradeEventConsumer.consumer(event);
        Mockito.verify(kafkaProducerSenderService, Mockito.times(0))
            .notificationSend(Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void testConsumerWhenCacheExist() {
        Mockito.when(assetCacheRedisRepository.hasKey(Mockito.anyString())).thenReturn(true);
        Mockito.when(assetCacheRedisRepository.rangeByScore(Mockito.anyString(), Mockito.any(), Mockito.any()))
            .thenReturn(Set.of("user1", "user2"));
        DexLpSubscribeTradeEventDTO event = DexLpSubscribeTradeEventDTO.builder()
            .totalUsd("100")
            .pair("DAI/WBNB")
            .poolId(1L)
            .transactionType("BUY")
            .dexerPlatformName("")
            .pairContractAddress("")
            .build();
        dexLpTradeEventConsumer.consumer(event);
        Mockito.verify(kafkaProducerSenderService, Mockito.times(1))
            .notificationSend(Mockito.anyString(), Mockito.anyString());
    }

    @Test
    public void testConsumerWhenCacheNotExist() {
        Mockito.when(assetCacheRedisRepository.hasKey(Mockito.anyString())).thenReturn(false);
        Mockito.when(userTradeAlertRepository.countSubscribers(Mockito.anyString(), Mockito.any()))
            .thenReturn(Mono.just(1L));
        Mockito.when(userTradeAlertRepository.findSubscribedUsers(Mockito.anyString(), Mockito.any()))
            .thenReturn(Flux.just(UserTradeAlertEntity.builder().userId("user1").threshold(1).build()));
        DexLpSubscribeTradeEventDTO event = DexLpSubscribeTradeEventDTO.builder()
            .totalUsd("100")
            .pair("DAI/WBNB")
            .poolId(1L)
            .transactionType("BUY")
            .dexerPlatformName("")
            .pairContractAddress("")
            .build();
        dexLpTradeEventConsumer.consumer(event);
        Mockito.verify(kafkaProducerSenderService, Mockito.times(1))
            .notificationSend(Mockito.anyString(), Mockito.anyString());
    }
}
