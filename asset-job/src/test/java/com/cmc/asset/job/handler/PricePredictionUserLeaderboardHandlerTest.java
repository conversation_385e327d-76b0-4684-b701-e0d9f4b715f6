package com.cmc.asset.job.handler;

import com.cmc.asset.job.entity.priceprediction.PricePredictionUserStatisticEntity;
import com.cmc.asset.job.repository.mongo.asset.PricePredictionUserStatisticRepository;
import com.cmc.asset.job.repository.mongo.portal.entity.CmcUserEntity;
import com.cmc.asset.job.repository.mongo.portal.repository.CmcUserRepository;
import com.cmc.asset.job.utils.RedisUtils;
import com.cmc.asset.model.common.DesensitizationUtils;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @date 2021/8/6 11:04:32
 */
public class PricePredictionUserLeaderboardHandlerTest {

    private PricePredictionUserLeaderboardHandler handler = new PricePredictionUserLeaderboardHandler();
    private PricePredictionUserStatisticRepository pricePredictionUserStatisticRepository =
        Mockito.mock(PricePredictionUserStatisticRepository.class);
    private RedisUtils baseRedisUtils = Mockito.mock(RedisUtils.class);
    private CmcUserRepository cmcUserRepository = Mockito.mock(CmcUserRepository.class);

    @BeforeTest
    public void setUp() {
        ReflectionTestUtils
            .setField(handler, "pricePredictionUserStatisticRepository", pricePredictionUserStatisticRepository);
        ReflectionTestUtils.setField(handler, "baseRedisUtils", baseRedisUtils);
        ReflectionTestUtils.setField(handler, "cmcUserRepository", cmcUserRepository);
        ReflectionTestUtils.setField(handler, "topCount", 200);
    }

    @Test
    public void testSetRankings() {
        PricePredictionUserStatisticEntity entity = new PricePredictionUserStatisticEntity();
        entity.setUserId("5ee051b06bc4634fa642f1e6");
        entity.setCoverage(new BigDecimal("0.2"));
        entity.setAverageAccuracy(new BigDecimal("0.6"));
        entity.setStatisticTime(202106);
        entity.setEstimateScore(8376);
        PricePredictionUserStatisticEntity entity2 = new PricePredictionUserStatisticEntity();
        entity2.setUserId("606473b89d72f400124e7c9d");
        entity2.setCoverage(new BigDecimal("0.2"));
        entity2.setAverageAccuracy(new BigDecimal("0.6"));
        entity2.setStatisticTime(202106);
        entity2.setEstimateScore(7376);
        List<PricePredictionUserStatisticEntity> list = List.of(entity, entity2);

        CmcUserEntity user = new CmcUserEntity();
        user.setUsername("<EMAIL>");
        user.setId(new ObjectId("6089456eda1ea9468b3c0249"));
        Mockito.when(cmcUserRepository.findById(ArgumentMatchers.anyString())).thenReturn(user);
        Map<String, Double> topMap = new HashMap<>();
        ReflectionTestUtils.invokeMethod(handler, "setRankings", list, topMap, new HashMap<>(), new HashMap<>());

        System.out.println(topMap);
        Assert.assertNotNull(topMap);
    }

    @Test
    public void testHandle() {
        PricePredictionUserStatisticEntity entity = new PricePredictionUserStatisticEntity();
        entity.setId(new ObjectId("5ee051b06bc4634fa642f1e6"));
        entity.setUserId("5ee051b06bc4634fa642f1e6");
        entity.setCoverage(new BigDecimal("0.2"));
        entity.setAverageAccuracy(new BigDecimal("0.6"));
        entity.setStatisticTime(202106);
        entity.setEstimateScore(8376);
        entity.setId(new ObjectId("6089456eda1ea9468b3c0249"));
        PricePredictionUserStatisticEntity entity2 = new PricePredictionUserStatisticEntity();
        entity2.setId(new ObjectId("606473b89d72f400124e7c9d"));
        entity2.setUserId("606473b89d72f400124e7c9d");
        entity2.setCoverage(new BigDecimal("0.2"));
        entity2.setAverageAccuracy(new BigDecimal("0.6"));
        entity2.setStatisticTime(202106);
        entity2.setEstimateScore(7376);
        entity2.setId(new ObjectId("6089456eda1ea9468b3c0248"));
        List<PricePredictionUserStatisticEntity> list = List.of(entity, entity2);

        CmcUserEntity cmcUserEntity = new CmcUserEntity();
        cmcUserEntity.setId(new ObjectId("5ee051b06bc4634fa642f1e6"));
        Mockito.when(
            pricePredictionUserStatisticRepository.getTopOrderByScore(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt()))
            .thenReturn(list);

        Mockito.doNothing().when(baseRedisUtils).deleteAndHmset(ArgumentMatchers.any(), ArgumentMatchers.any());

        Mockito.doNothing().when(baseRedisUtils).set(ArgumentMatchers.any(), ArgumentMatchers.any());

        CmcUserEntity user = new CmcUserEntity();
        user.setUsername("<EMAIL>");
        user.setId(new ObjectId("6089456eda1ea9468b3c0249"));
        Mockito.when(cmcUserRepository.findById(ArgumentMatchers.anyString())).thenReturn(user);
        Mockito.when(cmcUserRepository.findById(ArgumentMatchers.anyString())).thenReturn(cmcUserEntity);

        handler.handle(null);

        Mockito.verify(pricePredictionUserStatisticRepository)
            .getTopOrderByScore(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt());
    }

    @Test
    public void testPrivacy() {
        String userName = DesensitizationUtils.privacyName("<EMAIL>");
        String userName1 = DesensitizationUtils.privacyName("<EMAIL>");
        String userName2 = DesensitizationUtils.privacyName("@Zi.panbinance.com");
        String userName3 = DesensitizationUtils.privacyName("Zi.panbinance.com@");
        String userName4 = DesensitizationUtils.privacyName("abc");
        String userName5 = DesensitizationUtils.privacyName("ab");
        String userName6 = DesensitizationUtils.privacyName("a");
        Assert.assertEquals(userName.length(), 5);
        Assert.assertEquals(userName1.length(), 5);
        Assert.assertEquals(userName2.length(), 5);
        Assert.assertEquals(userName3.length(), 5);
        Assert.assertEquals(userName4.length(), 5);
        Assert.assertEquals(userName5.length(), 5);
        Assert.assertEquals(userName6.length(), 5);
    }
}