package com.cmc.asset.job.entity.priceprediction;
import java.util.Date;

import com.cmc.asset.job.entity.priceprediction.PricePredictionEntity.PricePredictionEntityBuilder;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.bson.types.ObjectId;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class PricePredictionEntityTest {

    private PricePredictionEntity pricePredictionEntityUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        pricePredictionEntityUnderTest =
            new PricePredictionEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", 0, new BigDecimal("0.00"), new BigDecimal("0.00"), 2020, 1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new BigDecimal("0.00"),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pricePredictionEntityUnderTest.setId(new ObjectId());
        pricePredictionEntityUnderTest.setUserId("");
        pricePredictionEntityUnderTest.setCryptoId(0);
        pricePredictionEntityUnderTest.setPredictedPrice(new BigDecimal(0));
        pricePredictionEntityUnderTest.setAccuracy(new BigDecimal(0));
        pricePredictionEntityUnderTest.setTargetYear(0);
        pricePredictionEntityUnderTest.setTargetMonth(0);
        pricePredictionEntityUnderTest.setTargetDate(new Date());
        pricePredictionEntityUnderTest.setCreateDate(new Date());
        pricePredictionEntityUnderTest.setLastModifyDate(new Date());
        pricePredictionEntityUnderTest.setMonthEndClosingPriceUsd(new BigDecimal(0));
        pricePredictionEntityUnderTest.setClosingPriceTime(new Date());
        pricePredictionEntityUnderTest = new PricePredictionEntity();
    }

    @Test
    public void testEquals() {
        assertTrue(pricePredictionEntityUnderTest.equals(new PricePredictionEntity()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(pricePredictionEntityUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, pricePredictionEntityUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", pricePredictionEntityUnderTest.toString());
    }

    @Test
    public void testBuilder() {
        // Setup
        // Run the test
        final PricePredictionEntityBuilder result = PricePredictionEntity.builder();
        result.toString();
        PricePredictionEntity pricePredictionEntity =
            PricePredictionEntity.builder().id(new ObjectId()).userId("").cryptoId(0).predictedPrice(new BigDecimal(0))
                .accuracy(new BigDecimal(0)).targetYear(0).targetMonth(0).targetDate(new Date()).createDate(new Date())
                .lastModifyDate(new Date()).monthEndClosingPriceUsd(new BigDecimal(0)).closingPriceTime(new Date())
                .build();
        // Verify the results
    }
}
