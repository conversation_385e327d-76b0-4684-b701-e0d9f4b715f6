package com.cmc.asset.job.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.service.watch.impl.GuestWatchDexTokenRefresh;
import com.cmc.asset.job.service.watch.impl.UserWatchDexTokenRefresh;
import com.cmc.asset.model.contract.watchlist.WatchListTokenRefreshDTO;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

/**
 * WatchListTokenRefreshHandler Test
 * <AUTHOR> ricky.x
 * @date: 2025/6/17 19:10
 */
public class WatchListTokenRefreshHandlerTest {

    @Mock
    private UserWatchDexTokenRefresh mockUserWatchDexTokenRefresh;

    @Mock
    private GuestWatchDexTokenRefresh mockGuestWatchDexTokenRefresh;

    @InjectMocks
    private WatchListTokenRefreshHandler watchListTokenRefreshHandler;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testHandle_InvalidParams_StartIdNull() {
        // Setup - startId 为 null
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId(null)
            .endId("507f1f77bcf86cd799439011")
            .isGuest(false)
            .build();

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_InvalidParams_EndIdNull() {
        // Setup - endId 为 null
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId("507f1f77bcf86cd799439011")
            .endId(null)
            .isGuest(false)
            .build();

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_InvalidParams_IsGuestNull() {
        // Setup - isGuest 为 null
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId("507f1f77bcf86cd799439011")
            .endId("507f1f77bcf86cd799439012")
            .isGuest(null)
            .build();

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_InvalidParams_StartIdEmpty() {
        // Setup - startId 为空字符串
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId("")
            .endId("507f1f77bcf86cd799439011")
            .isGuest(false)
            .build();

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_InvalidParams_EndIdEmpty() {
        // Setup - endId 为空字符串
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId("507f1f77bcf86cd799439011")
            .endId("")
            .isGuest(false)
            .build();

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_UserWatch_Success() {
        // Setup - 用户 watchlist 刷新
        String startId = "507f1f77bcf86cd799439011";
        String endId = "507f1f77bcf86cd799439012";
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId(startId)
            .endId(endId)
            .isGuest(false)
            .build();

        // Mock UserWatchDexTokenRefresh.process(...)
        when(mockUserWatchDexTokenRefresh.process(any(ObjectId.class), any(ObjectId.class)))
            .thenReturn(Mono.empty());

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_GuestWatch_Success() {
        // Setup - 访客 watchlist 刷新
        String startId = "507f1f77bcf86cd799439011";
        String endId = "507f1f77bcf86cd799439012";
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId(startId)
            .endId(endId)
            .isGuest(true)
            .build();

        // Mock GuestWatchDexTokenRefresh.process(...)
        when(mockGuestWatchDexTokenRefresh.process(any(ObjectId.class), any(ObjectId.class)))
            .thenReturn(Mono.empty());

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }


    @Test
    public void testHandle_ValidObjectIds() {
        // Setup - 使用有效的 ObjectId
        String startId = "507f1f77bcf86cd799439011";
        String endId = "507f1f77bcf86cd799439012";
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId(startId)
            .endId(endId)
            .isGuest(false)
            .build();

        // Mock UserWatchDexTokenRefresh.process(...)
        when(mockUserWatchDexTokenRefresh.process(any(ObjectId.class), any(ObjectId.class)))
            .thenReturn(Mono.empty());

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }

    @Test
    public void testHandle_AllParamsValid() {
        // Setup - 所有参数都有效
        String startId = "507f1f77bcf86cd799439011";
        String endId = "507f1f77bcf86cd799439012";
        WatchListTokenRefreshDTO jobData = WatchListTokenRefreshDTO.builder()
            .startId(startId)
            .endId(endId)
            .isGuest(true)
            .build();

        // Mock GuestWatchDexTokenRefresh.process(...)
        when(mockGuestWatchDexTokenRefresh.process(any(ObjectId.class), any(ObjectId.class)))
            .thenReturn(Mono.empty());

        // Run the test
        JobHandlingResult<Object> result = watchListTokenRefreshHandler.handle(jobData);

        // Verify the results
        Assert.assertNotNull(result);
    }
} 