package com.cmc.asset.job.controller;

import org.mockito.Mock;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;

public class SystemControllerTest {

    @Mock
    private ApplicationContext mockContext;

    private SystemController systemControllerUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        systemControllerUnderTest = new SystemController(mockContext);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testShutdownApp() {
        // Setup
        final ResponseEntity<String> expectedResult = new ResponseEntity<>("Shutting Down in 15 seconds!", HttpStatus.OK);

        // Run the test
        final ResponseEntity<String> result = systemControllerUnderTest.shutdownApp();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testHealth() {
        // Setup
        final ResponseEntity<String> expectedResult = new ResponseEntity<>("I am OK!", HttpStatus.OK);

        // Run the test
        final ResponseEntity<String> result = systemControllerUnderTest.health();

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
