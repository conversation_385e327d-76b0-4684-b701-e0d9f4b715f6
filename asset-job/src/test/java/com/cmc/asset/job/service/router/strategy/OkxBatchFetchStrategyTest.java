package com.cmc.asset.job.service.router.strategy;

import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.cache.CryptoPriceCache;
import com.cmc.asset.job.cache.DexTokenCache;
import com.cmc.asset.job.cache.DexerPlatformCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.integration.OkxClient;
import com.cmc.asset.job.integration.response.okx.OkxAddressBalanceDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class OkxBatchFetchStrategyTest {
    @InjectMocks
    private OkxBatchFetchStrategy okxBatchFetchStrategy;

    @Mock
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Mock
    private OkxClient okxClient;

    @Mock
    private CryptoCurrencyCache cryptoCurrencyCache;

    private AutoCloseable mockitoCloseable;

    @Mock
    private CryptoPriceCache cryptoPriceCache;
    @Mock
    private DexerPlatformCache dexerPlatformCache;
    @Mock
    private DexTokenCache dexTokenCache;

    private OkxAddressBalanceDTO balanceDTO;

    private Map<Integer, OkLinkMapConfigItem> configItemMap;

    private CryptoCurrencyInfoDTO currencyInfoDTO;

    private CryptoCurrencyInfoDTO nativeCurrencyInfoDTO;

    private Map<Integer, PortfolioCryptoInfoDTO> idMap;

    private PortfolioCryptoInfoDTO portfolioCryptoInfoDTO;

    private TokenPriceDTO tokenPriceDTO;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        balanceDTO = new OkxAddressBalanceDTO();
        OkxAddressBalanceDTO.TokenAsset nativeAsset = new OkxAddressBalanceDTO.TokenAsset();
        nativeAsset.setChainIndex("1");
        nativeAsset.setTokenAddress("");
        nativeAsset.setAddress("test");
        nativeAsset.setSymbol("test");
        nativeAsset.setBalance("100");
        nativeAsset.setTokenPrice("100");

        OkxAddressBalanceDTO.TokenAsset nativeAsset2 = new OkxAddressBalanceDTO.TokenAsset();
        nativeAsset2.setChainIndex("56");
        nativeAsset2.setTokenAddress("");
        nativeAsset2.setAddress("test");
        nativeAsset2.setSymbol("test");
        nativeAsset2.setBalance("100");
        nativeAsset2.setTokenPrice("100");
        OkxAddressBalanceDTO.TokenAsset nonNativeAsset = new OkxAddressBalanceDTO.TokenAsset();
        nonNativeAsset.setChainIndex("1");
        nonNativeAsset.setTokenAddress("test");
        nonNativeAsset.setAddress("test");
        nonNativeAsset.setSymbol("test");
        nonNativeAsset.setBalance("100");
        nonNativeAsset.setTokenPrice("100");

        OkxAddressBalanceDTO.TokenAsset nonNativeAsset2 = new OkxAddressBalanceDTO.TokenAsset();
        nonNativeAsset2.setChainIndex("56");
        nonNativeAsset2.setTokenAddress("test");
        nonNativeAsset2.setAddress("test");
        nonNativeAsset2.setSymbol("test");
        nonNativeAsset2.setBalance("100");
        nonNativeAsset2.setTokenPrice("100");

        OkxAddressBalanceDTO.TokenAsset nonNativeAsset3 = new OkxAddressBalanceDTO.TokenAsset();
        nonNativeAsset3.setChainIndex("56");
        nonNativeAsset3.setTokenAddress("test");
        nonNativeAsset3.setAddress("test");
        nonNativeAsset3.setSymbol("test");
        nonNativeAsset3.setBalance("100");
        nonNativeAsset3.setTokenPrice("100");
        List<OkxAddressBalanceDTO.TokenAsset> tokenAssets = new ArrayList<>();
        tokenAssets.add(nativeAsset);
        tokenAssets.add(nativeAsset2);
        tokenAssets.add(nonNativeAsset);
        tokenAssets.add(nonNativeAsset2);
        tokenAssets.add(nonNativeAsset3);
        balanceDTO.setTokenAssets(tokenAssets);

        configItemMap = new HashMap<>();
        OkLinkMapConfigItem configItem1 = new OkLinkMapConfigItem();
        configItem1.setChainId(1);
        configItem1.setNativeTokenId("1027");
        configItem1.setOkxChainId(1);
        OkLinkMapConfigItem configItem2 = new OkLinkMapConfigItem();
        configItem2.setChainId(14);
        configItem2.setNativeTokenId("1839");
        configItem2.setOkxChainId(56);
        configItemMap.put(1, configItem1);
        configItemMap.put(14, configItem2);

        currencyInfoDTO = new CryptoCurrencyInfoDTO();
        currencyInfoDTO.setName("test");

        nativeCurrencyInfoDTO = new CryptoCurrencyInfoDTO();
        nativeCurrencyInfoDTO.setName("test");
        nativeCurrencyInfoDTO.setStatus("active");

        idMap = new HashMap<>();
        portfolioCryptoInfoDTO = new PortfolioCryptoInfoDTO();
        portfolioCryptoInfoDTO.setCryptoId(1);
        portfolioCryptoInfoDTO.setPrice("100");
        idMap.put(1, portfolioCryptoInfoDTO);

        tokenPriceDTO = new TokenPriceDTO();


        ReflectionTestUtils.setField(okxBatchFetchStrategy, "filterLessOne", true);
        ReflectionTestUtils.setField(okxBatchFetchStrategy, "walletTokenValueLtLimit", "100000");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void test_batchFetch() {
        List<Integer> chainIds = Lists.newArrayList(1, 14);
        Mockito.when(dynamicApolloRefreshConfig.getOkLinkOkxMapConfig()).thenReturn(configItemMap);
        Mockito.when(okxClient.getAllTokenBalancesByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Mono.just(balanceDTO));
        Mockito.when(cryptoCurrencyCache.getCryptoCurrencyById(Mockito.anyInt())).thenReturn(currencyInfoDTO);
        Mockito.when(cryptoPriceCache.getAll(Mockito.anyCollection())).thenReturn(Mono.just(idMap));
        Mockito.when(cryptoPriceCache.get(Mockito.any())).thenReturn(Mono.just(portfolioCryptoInfoDTO));
        PlatformNewDTO v1 = new PlatformNewDTO();
        v1.setDn("eth");
        Mockito.when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, v1, 14, v1));
        Mockito.when(dexTokenCache.get(Mockito.anyString(), Mockito.anyString())).thenReturn(Mono.just(tokenPriceDTO));
        Mono<List<BlockChainPO>> result = okxBatchFetchStrategy.batchFetch(chainIds, "test", null);
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();

    }

}