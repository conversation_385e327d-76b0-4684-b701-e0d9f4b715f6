package com.cmc.asset.job.service.router;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.BlockChainTokenPO;
import com.cmc.asset.job.service.router.strategy.OkLinkFetchStrategy;
import com.cmc.asset.job.service.router.strategy.StrategyFactory;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * PortfolioRefreshPlTotalHandlerTest
 *
 * <AUTHOR> ricky.x
 * @date : 2022/12/9 下午3:26
 */
public class WalletPortfolioRouterTest {

    @Mock
    private OkLinkFetchStrategy mockOkLinkFetchStrategy;

    @Mock
    private StrategyFactory strategyFactory;

    @InjectMocks
    private WalletPortfolioRouter walletPortfolioRouterUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(walletPortfolioRouterUnderTest, "concurrency", 3);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testExecuteHandle() {
        BlockChainPO blockChainPO = BlockChainPO.builder().chainId(1)
                .tokens(List.of(BlockChainTokenPO.builder().cryptoId(1).amount(new BigDecimal("100")).build()))
                .nativeTokenId(100).build();
        Mockito.when(strategyFactory.createStrategy(anyInt())).thenReturn(mockOkLinkFetchStrategy);
        Mockito.when(mockOkLinkFetchStrategy.fetch(anyInt(), anyString(), any(Date.class)))
            .thenReturn(Mono.justOrEmpty(blockChainPO));
        List<BlockChainPO> result =
            walletPortfolioRouterUnderTest.getAddressBalanceV2(List.of(1), "1", List.of(BlockChainPO.builder().build()),
                new Date(1639393893000L));
        Assert.assertNotNull(result);
    }

}
