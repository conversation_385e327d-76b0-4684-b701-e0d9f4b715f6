package com.cmc.asset.job.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.audit.UserAuditEntity;
import com.cmc.asset.job.data.UserAuditJobData;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.integration.GravityApiClient;
import com.cmc.asset.job.integration.ImplioAuditClient;
import com.cmc.asset.job.integration.UserApiClient;
import com.cmc.asset.job.repository.mongo.asset.UserAuditRepository;
import com.cmc.asset.model.contract.user.UserAvatarSyncRequestDTO;
import com.cmc.asset.model.contract.useraudit.ImplioAuditResultRespDTO;
import com.cmc.asset.model.contract.useraudit.ImplioAuditResultRespDTO.Ad;
import com.cmc.asset.model.contract.useraudit.ImplioAuditResultRespDTO.AdDetail;
import com.cmc.asset.model.contract.useraudit.ImplioAuditResultRespDTO.PollingInfo;
import com.cmc.asset.model.contract.useraudit.ImplioAuditResultRespDTO.Result;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * UserAuditHandlerTest
 *
 * <AUTHOR> ricky.x
 * @date : 2022/12/9 下午3:26
 */
public class UserAuditHandlerTest {

    @Mock
    private ImplioAuditClient mockImplioAuditClient;

    @Mock
    private UserApiClient mockUserApiClient;

    @Mock
    private GravityApiClient mockGravityApiClient;

    @Mock
    private UserAuditRepository mockUserAuditRepository;

    @InjectMocks
    private UserAuditHandler userAuditHandlerTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(userAuditHandlerTest, "begTimeLimit", 10L);
        ReflectionTestUtils.setField(userAuditHandlerTest, "pageSize", 1000);
        ReflectionTestUtils.setField(userAuditHandlerTest, "duration", 500);
        ReflectionTestUtils.setField(userAuditHandlerTest, "timeRange", 6);
        ReflectionTestUtils.setField(userAuditHandlerTest, "prompt", "");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testExecuteHandle() {
        UserAuditJobData paramDTO = new UserAuditJobData();
        paramDTO.setStartDate("2023-08-22 00:00:00");
        paramDTO.setEndDate("2023-08-23 00:00:00");
        UserAuditEntity userAuditEntity = new UserAuditEntity();
        userAuditEntity.setId(new ObjectId("64ec1397c36e9c481dfa5135"));
        userAuditEntity.setUserId("64ec1397c36e9c481dfa5135");
        userAuditEntity.setUserUid(0L);
        userAuditEntity.setType(1);
        userAuditEntity.setStatus(0);
        userAuditEntity.setSubject("");
        userAuditEntity.setTaskId("mockTaskId");
        userAuditEntity.setPreSubject("");
        userAuditEntity.setCreatedTime(LocalDateTime.now());
        userAuditEntity.setUpdatedTime(LocalDateTime.now());
        userAuditEntity.setVersion(0L);
        userAuditEntity.setActived(false);
        Mockito.when(mockUserAuditRepository.queryByStatus(any(), any(),any(),any(),any()))
            .thenReturn(Flux.just(userAuditEntity));
        ImplioAuditResultRespDTO implioAuditResultRespDTO = new ImplioAuditResultRespDTO();
        implioAuditResultRespDTO.setPollingInfo(PollingInfo.builder().newerAdsExist(true).newTimestamp(new Date()).build());
        implioAuditResultRespDTO.setAds(List.of(AdDetail.builder().ad(Ad.builder().taskId("mockTaskId").build()).result(
                Result.builder().outcome("approved").build())
            .domain("domain").build()));
        Mockito.when(mockImplioAuditClient.getWordFilteringResult(any(Date.class), anyInt(),anyList()))
            .thenReturn(Mono.just(implioAuditResultRespDTO));
        Mockito.when(mockUserAuditRepository.proceedAudit(any(ObjectId.class), anyInt()))
            .thenReturn(Mono.just(userAuditEntity));
        Mockito.when(mockUserApiClient.syncUserAvatarRef(any(UserAvatarSyncRequestDTO.class)))
            .thenReturn(Mono.just(Boolean.TRUE));
        JobHandlingResult<Boolean> result = userAuditHandlerTest.handle(paramDTO);
        Assert.assertNotNull(result);
    }

}
