package com.cmc.asset.job.utils;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.cmc.asset.model.enums.PortfolioMessageOperateEnum;
import com.cmc.asset.model.enums.PortfolioTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.ApplicationContext;

public class BeanFactoryUtilTest {

    private BeanFactoryUtil beanFactoryUtilUnderTest;

    @BeforeEach
    void setUp() {
        beanFactoryUtilUnderTest = new BeanFactoryUtil();
    }

    @Test
    void testSetApplicationContext() {
        // Setup
        final ApplicationContext applicationContext = null;

        // Run the test
        beanFactoryUtilUnderTest.setApplicationContext(applicationContext);

        // Verify the results
    }


    @Test
    void testGetPortfolioCalculateService_ThrowsException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> beanFactoryUtilUnderTest.getPortfolioCalculateService(PortfolioMessageOperateEnum.WALLET))
            .isInstanceOf(Exception.class);
    }
}
