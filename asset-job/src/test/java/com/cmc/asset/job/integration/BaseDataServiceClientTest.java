//package com.cmc.asset.job.integration;
//import java.math.BigDecimal;
//
//import reactor.core.publisher.Mono;
//import reactor.test.StepVerifier;
//
//import com.cmc.asset.model.contract.crypto.CryptoCurrencyQuotesLatestCacheV3VO;
//import com.cmc.data.common.utils.JacksonUtils;
//import com.cmc.framework.common.http.HttpWebClient;
//import com.cmc.framework.common.http.HttpWebClientBuilder;
//import java.util.List;
//import org.mockito.Mockito;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.test.util.ReflectionTestUtils;
//import org.springframework.web.reactive.function.client.WebClient;
//import org.testng.annotations.BeforeMethod;
//import org.testng.annotations.Test;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.when;
//
//public class BaseDataServiceClientTest {
//
//    private BaseDataServiceClient baseDataServiceClientUnderTest;
//
//    private HttpWebClient client;
//
//    @BeforeMethod
//    public void setUp() {
//        baseDataServiceClientUnderTest = new BaseDataServiceClient();
//        client = Mockito.mock(HttpWebClient.class);
//        ReflectionTestUtils.setField(baseDataServiceClientUnderTest, "client", client);
//        ReflectionTestUtils.setField(baseDataServiceClientUnderTest, "url", "url");
//    }
//
//    @Test
//    public void testQueryLatestQuotesV3() {
//        // Setup
//        CryptoCurrencyQuotesLatestCacheV3VO cryptoCurrencyQuotesLatestCacheV3VO = new CryptoCurrencyQuotesLatestCacheV3VO();
//        cryptoCurrencyQuotesLatestCacheV3VO.setId(0);
//        cryptoCurrencyQuotesLatestCacheV3VO.setCirculatingSupply(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setMaxSupply(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setNumMarkets(0);
//        cryptoCurrencyQuotesLatestCacheV3VO.setRank(0);
//        cryptoCurrencyQuotesLatestCacheV3VO.setTotalSupply(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPriceUsd(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsd(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdChangePercentage(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdReportedChangePercentage(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceUsd1h(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceUsd24h(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceUsd7d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceUsd30d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceUsd60d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceUsd90d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceBtc1h(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceBtc24h(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceEth1h(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setPercentageChangePriceEth24h(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setLastUpdated("");
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsd7d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsd30d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdReported(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdReported7d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setVolumeUsdReported30d(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setMarketCapChangePercentage(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setMarketCapDominance(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setFullyDilutedMarketCapChangePercentage(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setMarketCap(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setFullyDilutedMarketCap(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setTvlUsd(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setTvlRatio(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setTurnover(new BigDecimal(0));
//        cryptoCurrencyQuotesLatestCacheV3VO.setYtdPriceChangePercentage(new BigDecimal(0));
//
//        when(client.get(anyString()))
//            .thenReturn(Mono.just(
//                    new ResponseEntity(
//                        JacksonUtils.toJsonString(List.of(cryptoCurrencyQuotesLatestCacheV3VO)),
//                        HttpStatus.OK
//                    )));
//        // Run the test
//        final Mono<List<CryptoCurrencyQuotesLatestCacheV3VO>> result =
//            baseDataServiceClientUnderTest.queryLatestQuotesV3(List.of(0));
//
//        // Verify the results
//        StepVerifier.create(result).expectNextCount(1).verifyComplete();
//    }
//}
