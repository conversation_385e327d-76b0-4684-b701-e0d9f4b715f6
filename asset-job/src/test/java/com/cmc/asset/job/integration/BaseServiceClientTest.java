package com.cmc.asset.job.integration;

import com.cmc.framework.common.http.HttpWebClient;
import org.testng.annotations.BeforeMethod;
import static org.mockito.Mockito.mock;

public class BaseServiceClientTest {

    private BaseServiceClient baseServiceClientUnderTest;

    @BeforeMethod
    public void setUp() {
        baseServiceClientUnderTest = new BaseServiceClient() {
        };
        baseServiceClientUnderTest.client = mock(HttpWebClient.class);
    }
}
