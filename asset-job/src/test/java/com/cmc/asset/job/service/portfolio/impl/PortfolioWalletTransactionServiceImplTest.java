package com.cmc.asset.job.service.portfolio.impl;

import com.cmc.asset.job.data.PortfolioJobData;
import com.cmc.asset.job.service.portfolio.WalletSyncService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class PortfolioWalletTransactionServiceImplTest {

    @Mock
    private WalletSyncService walletSyncService;

    @InjectMocks
    private PortfolioWalletTransactionServiceImpl portfolioWalletTransactionServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(portfolioWalletTransactionServiceImplUnderTest, "blockOn", true);
        ReflectionTestUtils.setField(portfolioWalletTransactionServiceImplUnderTest, "blockTime", 60L);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testOperateHandle() {
        PortfolioJobData portfolioJobData =
            PortfolioJobData.builder().walletAddress("60b850e8dae0a17606cd1cf2").build();
        when(walletSyncService.syncWalletTransaction(anyString())).thenReturn(Mono.just(true));

        portfolioWalletTransactionServiceImplUnderTest.operateHandle(portfolioJobData);
    }

}
