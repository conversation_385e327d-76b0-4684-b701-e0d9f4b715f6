package com.cmc.asset.job.service.watch.impl;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.job.integration.DexServiceClient;
import com.cmc.asset.job.repository.mongo.asset.GuestWatchlistRepository;
import com.cmc.asset.job.repository.mongo.asset.entity.GuestWatchListEntity;
import com.cmc.asset.model.contract.dexer.PairInfoDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * GuestWatchDexTokenRefresh Test
 * <AUTHOR> ricky.x
 * @date: 2025/6/17 19:10
 */
public class GuestWatchDexTokenRefreshTest {

    @Mock
    private GuestWatchlistRepository mockGuestWatchlistRepository;

    @Mock
    private DexServiceClient mockDexServiceClient;

    @InjectMocks
    private GuestWatchDexTokenRefresh guestWatchDexTokenRefresh;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(guestWatchDexTokenRefresh, "queryDbSize", 200);
        ReflectionTestUtils.setField(guestWatchDexTokenRefresh, "skipPairInfo", Set.of());

    }

    @AfterMethod
    public void tearDown() throws Exception {


        mockitoCloseable.close();
    }

    @Test
    public void testProcess() {
        // Setup
        ObjectId startId = new ObjectId("6822addfc40276e2d2cfc690");
        ObjectId endId = new ObjectId("6822addfc40276e2d2cfc690");
        int limit = 100;


        List<GuestWatchListEntity> guestWatchListEntities = new ArrayList<>();
        guestWatchListEntities.add(GuestWatchListEntity.builder().id(new ObjectId("6822addfc40276e2d2cfc690")).dexPairs(List.of(1L,2L)).build());
        guestWatchListEntities.add(GuestWatchListEntity.builder().id(new ObjectId("6822addfc40276e2d2cfc690")).dexPairs(List.of(1L,2L)).build());
        when(mockGuestWatchlistRepository.findByPlatformByCursor(Mockito.any(ObjectId.class), Mockito.any(ObjectId.class), Mockito.any(), Mockito.anyInt()))
                .thenReturn(guestWatchListEntities);

        when(mockGuestWatchlistRepository.batchUpdateDexToken(Mockito.anyList()))
                .thenReturn(Mono.just(Boolean.TRUE));

        when(mockDexServiceClient.getPairs(Mockito.anyList()))
                .thenReturn(Mono.just(List.of(PairInfoDTO.builder().poolId(1L).platformId(14).address("test").build())));

        Mono<Boolean> result = guestWatchDexTokenRefresh.process(startId, endId);
        StepVerifier.create(result).expectSubscription()
                .assertNext(res -> {
                    Assert.assertNotNull(result);
                }).verifyComplete();
    }

} 