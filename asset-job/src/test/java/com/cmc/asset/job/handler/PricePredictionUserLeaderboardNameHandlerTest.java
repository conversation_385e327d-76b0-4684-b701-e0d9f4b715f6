package com.cmc.asset.job.handler;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.repository.mongo.portal.entity.CmcUserEntity;
import com.cmc.asset.job.repository.mongo.portal.repository.CmcUserRepository;
import com.cmc.asset.job.utils.RedisUtils;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class PricePredictionUserLeaderboardNameHandlerTest {

    @Mock
    private RedisUtils mockBaseRedisUtils;
    @Mock
    private CmcUserRepository mockCmcUserRepository;

    @InjectMocks
    private PricePredictionUserLeaderboardNameHandler handler;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        handler = new PricePredictionUserLeaderboardNameHandler();
        ReflectionTestUtils.setField(handler, "topCount", 200);

        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testHandle() {
        // Setup
        when(mockBaseRedisUtils.zrangebyscore("key", 0.0, 0.0)).thenReturn(Set.of("value"));

        // Configure CmcUserRepository.findByIdIn(...).
        final List<CmcUserEntity> cmcUserEntities = List.of(
            new CmcUserEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "email",
                "username","avatarId","referralCode", 1L));
        when(mockCmcUserRepository.findByIdIn(List.of("value"))).thenReturn(cmcUserEntities);

        // Run the test
        final JobHandlingResult<Object> result = handler.handle("params");

        // Verify the results
        Assert.assertTrue(result.isSucceeded());
    }

    @Test
    public void testHandle_RedisUtilsZrangebyscoreReturnsNoItems() {
        // Setup
        when(mockBaseRedisUtils.zrangebyscore("key", 0.0, 0.0)).thenReturn(Collections.emptySet());

        // Configure CmcUserRepository.findByIdIn(...).
        final List<CmcUserEntity> cmcUserEntities = List.of(
            new CmcUserEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "email",
                "username","avatarId","referralCode",1L));
        when(mockCmcUserRepository.findByIdIn(List.of("value"))).thenReturn(cmcUserEntities);

        // Run the test
        final JobHandlingResult<Object> result = handler.handle("params");

        // Verify the results
        Assert.assertTrue(result.isSucceeded());
    }

    @Test
    public void testHandle_CmcUserRepositoryReturnsNoItems() {
        // Setup
        when(mockBaseRedisUtils.zrangebyscore("key", 0.0, 0.0)).thenReturn(Set.of("value"));
        when(mockCmcUserRepository.findByIdIn(List.of("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final JobHandlingResult<Object> result = handler.handle("params");

        // Verify the results
        Assert.assertTrue(result.isSucceeded());
    }
}
