package com.cmc.asset.job.handler;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.referral.UserReferralSummaryEntity;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.repository.mongo.asset.UserReferralSummaryRepository;
import com.cmc.asset.job.repository.mongo.portal.entity.CmcUserEntity;
import com.cmc.asset.job.repository.mongo.portal.repository.CmcUserRepository;
import com.cmc.asset.job.repository.redis.AssetCacheRedisRepository;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.bson.types.ObjectId;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.data.domain.PageRequest;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class AffiliateLeaderBoardHandlerTest {

    @Mock
    private UserReferralSummaryRepository mockUserReferralSummaryRepository;
    @Mock
    private CmcUserRepository mockCmcUserRepository;

    @Mock
    private AssetCacheRedisRepository mockAssetCacheRedisRepository;

    @InjectMocks
    private AffiliateLeaderBoardHandler affiliateLeaderBoardHandlerUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testHandle() {
        // Setup

        // Configure UserReferralSummaryRepository.findByReferralAwardGreaterThanOrderByReferralAwardDesc(...).
        final List<UserReferralSummaryEntity> userReferralSummaryEntities = List.of(
            new UserReferralSummaryEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", "referralCode", 10, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()),
            new UserReferralSummaryEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId2", "referralCode", 20, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockUserReferralSummaryRepository
            .findByReferralAwardGreaterThanOrderByReferralAwardDesc(0, PageRequest.of(0, 100)))
            .thenReturn(userReferralSummaryEntities);

        // Configure CmcUserRepository.findByIdIn(...).
        final List<CmcUserEntity> cmcUserEntities = List.of(
            new CmcUserEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "email",
                "username", "avatarId", "referralCode",1L));
        when(mockCmcUserRepository.findByIdIn(List.of("userId","userId2"))).thenReturn(cmcUserEntities);
        Mockito.doNothing().when(mockAssetCacheRedisRepository).deleteAndPutAll(ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing().when(mockAssetCacheRedisRepository).zadd(ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing().when(mockAssetCacheRedisRepository).set(ArgumentMatchers.any(), ArgumentMatchers.any());
        // Run the test
        final JobHandlingResult<Object> result = affiliateLeaderBoardHandlerUnderTest.handle("jobData");

        // Verify the results
        Assert.assertTrue(result.isSucceeded());


    }

    @Test
    public void testHandle_UserReferralSummaryRepositoryReturnsNoItems() {
        // Setup
        when(mockUserReferralSummaryRepository
            .findByReferralAwardGreaterThanOrderByReferralAwardDesc(0, PageRequest.of(0, 100)))
            .thenReturn(Collections.emptyList());

        // Configure CmcUserRepository.findByIdIn(...).
        when(mockCmcUserRepository.findByIdIn(Collections.emptyList())).thenReturn(Collections.emptyList());

        // Run the test
        final JobHandlingResult<Object> result = affiliateLeaderBoardHandlerUnderTest.handle("jobData");
        Mockito.doNothing().when(mockAssetCacheRedisRepository).deleteAndPutAll(ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing().when(mockAssetCacheRedisRepository).zadd(ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing().when(mockAssetCacheRedisRepository).set(ArgumentMatchers.any(), ArgumentMatchers.any());

        // Verify the results
        Assert.assertTrue(result.isSucceeded());

    }

    @Test
    public void testHandle_CmcUserRepositoryReturnsNoItems() {
        // Setup

        // Configure UserReferralSummaryRepository.findByReferralAwardGreaterThanOrderByReferralAwardDesc(...).
        final List<UserReferralSummaryEntity> userReferralSummaryEntities = List.of(
            new UserReferralSummaryEntity(new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0),
                "userId", "referralCode", 0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
        when(mockUserReferralSummaryRepository
            .findByReferralAwardGreaterThanOrderByReferralAwardDesc(0, PageRequest.of(0, 100)))
            .thenReturn(userReferralSummaryEntities);
        Mockito.doNothing().when(mockAssetCacheRedisRepository).deleteAndPutAll(ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing().when(mockAssetCacheRedisRepository).zadd(ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing().when(mockAssetCacheRedisRepository).set(ArgumentMatchers.any(), ArgumentMatchers.any());

        when(mockCmcUserRepository.findByIdIn(List.of("userId"))).thenReturn(Collections.emptyList());

        // Run the test
        final JobHandlingResult<Object> result = affiliateLeaderBoardHandlerUnderTest.handle("jobData");

        // Verify the results
        Assert.assertTrue(result.isSucceeded());
    }

}
