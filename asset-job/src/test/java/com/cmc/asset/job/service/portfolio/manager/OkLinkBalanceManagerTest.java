package com.cmc.asset.job.service.portfolio.manager;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.cache.CryptoPriceCache;
import com.cmc.asset.job.cache.DexTokenCache;
import com.cmc.asset.job.cache.DexerPlatformCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.dto.PortfolioOkLinkMapVO;
import com.cmc.asset.job.integration.OkLinkClient;
import com.cmc.asset.job.integration.response.AddressBalanceFillsResponse.AddressBalanceFillsDTO;
import com.cmc.asset.job.integration.response.AddressBalanceFillsResponse.AddressBalanceFillsTokenDTO;
import com.cmc.asset.job.integration.response.AddressSummaryResponse.AddressSummaryDTO;
import com.cmc.asset.job.service.PortfolioCryptoRedisService;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

/**
 * PortfolioRefreshPlTotalHandlerTest
 *
 * <AUTHOR> ricky.x
 * @date : 2022/12/9 下午3:26
 */
public class OkLinkBalanceManagerTest {

    @Mock
    private OkLinkClient mockOkLinkClient;
    @Mock
    private CryptoCurrencyCache mockCryptoCurrencyCache;
    @Mock
    private CryptoPriceCache cryptoPriceCache;
    @Mock
    private DynamicApolloRefreshConfig mockDynamicApolloRefreshConfig;
    @Mock
    private PortfolioCryptoRedisService mockPortfolioCryptoRedisService;
    @Mock
    private DexerPlatformCache dexerPlatformCache;
    @Mock
    private DexTokenCache dexTokenCache;
    @InjectMocks
    private OkLinkBalanceManager okLinkBalanceManager;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(okLinkBalanceManager, "maxPage", 10);
        ReflectionTestUtils.setField(okLinkBalanceManager, "filterLessOne", true);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testAddressBalance() {
        String mockAddress = "mockAddress";
        Integer mockId = 1;
        Integer mockNativeToken = 1027;
        String mockChainName = "ETH";
        String mockCryptoName = "ETH";
        String mockTokenAddress = "mockTokenAddress";
        String mockTwoTokenAddress = "mockTwoTokenAddress";

        final Map<Integer, BlockChainPO> tempMap = Map.ofEntries(Map.entry(0, BlockChainPO.builder().build()));
        Mockito.when(mockCryptoCurrencyCache.getIdByContractAddress(anyInt(), anyString())).thenReturn(1);
        Mockito.when(mockCryptoCurrencyCache.getIdByContractAddress(anyInt(), anyString())).thenReturn(1);

        List<AddressBalanceFillsTokenDTO> firstTokenList = new ArrayList<>();
        firstTokenList.add(
            AddressBalanceFillsTokenDTO.builder().tokenContractAddress(mockTokenAddress).holdingAmount("55").build());
        List<AddressBalanceFillsTokenDTO> secondTokenList = new ArrayList<>();
        secondTokenList.add(AddressBalanceFillsTokenDTO.builder().tokenContractAddress(mockTwoTokenAddress)
            .holdingAmount("105").build());

        Mockito.when(mockOkLinkClient.addressBalanceFills(eq(mockChainName), eq(mockAddress), eq(1)))
            .thenReturn(Mono.just(AddressBalanceFillsDTO.builder().totalPage("2").tokenList(firstTokenList).build()));
        Mockito.when(mockOkLinkClient.addressBalanceFills(eq(mockChainName), eq(mockAddress), eq(2)))
            .thenReturn(Mono.just(AddressBalanceFillsDTO.builder().totalPage("2").tokenList(secondTokenList).build()));
        Mockito.when(mockCryptoCurrencyCache.getCryptoCurrencyById(eq(mockNativeToken)))
            .thenReturn(CryptoCurrencyInfoDTO.builder().id(mockNativeToken).name(mockCryptoName).build());
        Mockito.when(mockDynamicApolloRefreshConfig.getChainConfigByShortName(eq(mockChainName)))
            .thenReturn(PortfolioOkLinkMapVO.builder().chainId(mockId).nativeTokenId(mockNativeToken).chainShortName(mockChainName)
                .build());
        Mockito.when(mockDynamicApolloRefreshConfig.getChainConfigById(eq(mockId))).thenReturn(
            PortfolioOkLinkMapVO.builder().chainId(mockId).nativeTokenId(mockNativeToken).chainShortName(mockChainName)
                .build());
        Mockito.when(mockOkLinkClient.addressSummary(eq(mockChainName), eq(mockAddress)))
            .thenReturn(Mono.just(AddressSummaryDTO.builder().balance("100").build()));
        Mockito.when(mockCryptoCurrencyCache.getCryptoCurrencyById(eq(mockId)))
            .thenReturn(CryptoCurrencyInfoDTO.builder().id(mockId).name(mockCryptoName).build());
        Mockito.when(cryptoPriceCache.getAll(anyList()))
            .thenReturn(Mono.just(Map.of(1, PortfolioCryptoInfoDTO.builder().cryptoId(mockId).name(mockCryptoName).build())));
        Map<Integer, PortfolioCryptoInfoDTO> priceMap = new HashMap<>();
        PortfolioCryptoInfoDTO portfolioCryptoInfoDTO = PortfolioCryptoInfoDTO.builder().price("1001").build();
        priceMap.put(mockNativeToken, portfolioCryptoInfoDTO);
        Mockito.when(mockPortfolioCryptoRedisService.getPortfolioCryptoInfosByBaseDataService(Mockito.anyList()))
            .thenReturn(Mono.just(priceMap));
        Mockito.when(mockPortfolioCryptoRedisService.getPortfolioCryptoInfoByBaseDataService(anyInt()))
            .thenReturn(Mono.just(portfolioCryptoInfoDTO));
        PlatformNewDTO v1 = new PlatformNewDTO();
        v1.setDn("eth");
        TokenPriceDTO tokenPriceDTO = new TokenPriceDTO();
        Mockito.when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, v1, 14, v1));
        Mockito.when(dexTokenCache.get(Mockito.anyString(), Mockito.anyString())).thenReturn(Mono.just(tokenPriceDTO));
        Mono<BlockChainPO> result =
            okLinkBalanceManager.getChainFromOk(mockId, mockAddress, new Date(1639393893000L));
        Assert.assertNotNull(result.block());
    }


}
