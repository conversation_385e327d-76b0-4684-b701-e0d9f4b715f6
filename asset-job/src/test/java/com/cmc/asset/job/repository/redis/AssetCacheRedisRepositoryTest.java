package com.cmc.asset.job.repository.redis;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class AssetCacheRedisRepositoryTest {

    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;
    @Mock
    private HashOperations<String, Object, Object> hashOperations;
    @Mock
    private ZSetOperations<String, String> zSetOperations;

    @InjectMocks
    private AssetCacheRedisRepository assetCacheRedisRepositoryUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testDeleteAndPutAll() {
        // Setup
        final Map<String, String> dataMap = Map.ofEntries(Map.entry("value", "value"));
        when(mockRedisTemplate.delete(anyString())).thenReturn(true);
        when(mockRedisTemplate.opsForHash()).thenReturn(hashOperations);
        doNothing().when(hashOperations).putAll(anyString(), anyMap());

        // Run the test
        assetCacheRedisRepositoryUnderTest.deleteAndPutAll("key", dataMap);

        // Verify the results
    }

    @Test
    public void testZadd() {
        // Setup
        final Set<TypedTuple<String>> value = Set.of();
        when(mockRedisTemplate.delete(anyString())).thenReturn(true);
        when(mockRedisTemplate.opsForZSet()).thenReturn(zSetOperations);
        when(zSetOperations.add(anyString(), anySet())).thenReturn(0L);

        // Run the test
        assetCacheRedisRepositoryUnderTest.zadd("key", value);

        // Verify the results
    }

    @Test
    public void testSet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        doNothing().when(valueOperations).set(any(), any());

        // Run the test
        assetCacheRedisRepositoryUnderTest.set("key", "value");

        // Verify the results
    }
}
