package com.cmc.asset.job.data;

import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;

public class PortfolioSelectableCoinsPriceLoadJobDataTest {

    private PortfolioSelectableCoinsPriceLoadJobData portfolioSelectableCoinsPriceLoadJobDataUnderTest;

    @BeforeMethod
    public void setUp() throws Exception {
        portfolioSelectableCoinsPriceLoadJobDataUnderTest = new PortfolioSelectableCoinsPriceLoadJobData();
        portfolioSelectableCoinsPriceLoadJobDataUnderTest.setIsReload(false);
    }

    @Test
    public void testEquals() {
        assertFalse(portfolioSelectableCoinsPriceLoadJobDataUnderTest.equals(new PortfolioSelectableCoinsPriceLoadJobData()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(portfolioSelectableCoinsPriceLoadJobDataUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, portfolioSelectableCoinsPriceLoadJobDataUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", portfolioSelectableCoinsPriceLoadJobDataUnderTest.toString());
    }
}
