package com.cmc.asset.job.entity.priceprediction;
import java.util.Date;
import com.cmc.asset.job.entity.priceprediction.PricePredictionEntity;

import com.cmc.asset.job.entity.priceprediction.PricePredictionUserStatisticEntity.PricePredictionUserStatisticEntityBuilder;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.bson.types.ObjectId;
import org.mockito.Mock;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotEquals;
import static org.testng.Assert.assertTrue;

public class PricePredictionUserStatisticEntityTest {

    @Mock
    private PricePredictionEntity mockBestAccuracy;

    private PricePredictionUserStatisticEntity pricePredictionUserStatisticEntityUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
        pricePredictionUserStatisticEntityUnderTest = new PricePredictionUserStatisticEntity(
            new ObjectId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0), "userId",
            new BigDecimal("0.00"), new BigDecimal("0.00"), 0, 0, new BigDecimal("0.00"), mockBestAccuracy, 0, 0,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pricePredictionUserStatisticEntityUnderTest.setId(new ObjectId());
        pricePredictionUserStatisticEntityUnderTest.setUserId("");
        pricePredictionUserStatisticEntityUnderTest.setAverageAccuracy(new BigDecimal(0));
        pricePredictionUserStatisticEntityUnderTest.setCoverage(new BigDecimal(0));
        pricePredictionUserStatisticEntityUnderTest.setEstimateScore(0);
        pricePredictionUserStatisticEntityUnderTest.setRankings(0);
        pricePredictionUserStatisticEntityUnderTest.setDistribution(new BigDecimal(0));
        pricePredictionUserStatisticEntityUnderTest.setBestAccuracy(new PricePredictionEntity());
        pricePredictionUserStatisticEntityUnderTest.setStatisticTime(0);
        pricePredictionUserStatisticEntityUnderTest.setEstimateCount(0);
        pricePredictionUserStatisticEntityUnderTest.setUpdateTime(new Date());
        pricePredictionUserStatisticEntityUnderTest = new PricePredictionUserStatisticEntity();
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testEquals() {
        assertTrue(pricePredictionUserStatisticEntityUnderTest.equals(new PricePredictionUserStatisticEntity()));
    }

    @Test
    public void testCanEqual() {
        assertFalse(pricePredictionUserStatisticEntityUnderTest.canEqual("other"));
    }

    @Test
    public void testHashCode() {
        assertNotEquals(0, pricePredictionUserStatisticEntityUnderTest.hashCode());
    }

    @Test
    public void testToString() {
        assertNotEquals("result", pricePredictionUserStatisticEntityUnderTest.toString());
    }

    @Test
    public void testBuilder() {
        // Setup
        // Run the test
        final PricePredictionUserStatisticEntityBuilder result = PricePredictionUserStatisticEntity.builder();
        result.toString();
        PricePredictionUserStatisticEntity pricePredictionUserStatisticEntity =
            PricePredictionUserStatisticEntity.builder().id(new ObjectId()).userId("")
                .averageAccuracy(new BigDecimal(0)).coverage(new BigDecimal(0)).estimateScore(0).rankings(0)
                .distribution(new BigDecimal(0)).bestAccuracy(new PricePredictionEntity()).statisticTime(0)
                .estimateCount(0).updateTime(new Date()).build();
        // Verify the results
    }
}
