package com.cmc.asset.job.service.dex.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.cmc.asset.job.integration.DexerApiClient;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.BatchPlatformTokenRequestDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.util.List;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;

public class DexerServiceImplTest {
    private DexerApiClient dexerApiClient;
    private ReactiveRedisTemplate<String, String> assetAsyncRedisTemplate;
    private ReactiveValueOperations<String, String> valueOperations;
    private DexerServiceImpl dexerServiceImpl;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        dexerApiClient = mock(DexerApiClient.class);
        assetAsyncRedisTemplate = mock(ReactiveRedisTemplate.class);
        valueOperations = mock(ReactiveValueOperations.class);
        dexerServiceImpl = new DexerServiceImpl();
        ReflectionTestUtils.setField(dexerServiceImpl, "dexerApiClient", dexerApiClient);
        ReflectionTestUtils.setField(dexerServiceImpl, "assetAsyncRedisTemplate", assetAsyncRedisTemplate);
        ReflectionTestUtils.setField(dexerServiceImpl, "cacheExpirationSeconds", 300);
        ReflectionTestUtils.setField(dexerServiceImpl, "batchSize", 50);
        ReflectionTestUtils.setField(dexerServiceImpl, "maxBatchConcurrency", 3);
        when(assetAsyncRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    public void testGetPlatformsFromDeQuery() {
        PlatformNewDTO dto = new PlatformNewDTO();
        when(dexerApiClient.getPlatformsFromDeQuery()).thenReturn(Mono.just(List.of(dto)));
        Mono<List<PlatformNewDTO>> result = dexerServiceImpl.getPlatformsFromDeQuery();
        List<PlatformNewDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
    }

    @Test
    public void testGetPairsFromDeQuery_allCache() {
        // Create test tokens
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token1 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token1);
        
        // Create cached TokenPriceDTO
        TokenPriceDTO cachedDto = new TokenPriceDTO();
        cachedDto.setAddress("0xabc");
        cachedDto.setPlatformDexerName("eth");
        String cachedJson = JacksonUtils.serialize(cachedDto);
        
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of(cachedJson)));
        
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
        Assert.assertEquals(list.get(0).getAddress(), "0xabc");
    }

    @Test
    public void testGetPairsFromDeQuery_partialCache() {
        // Create test tokens
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token1 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token2 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xdef")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token1, token2);
        
        // Create cached TokenPriceDTO for first token
        TokenPriceDTO cachedDto = new TokenPriceDTO();
        cachedDto.setAddress("0xabc");
        cachedDto.setPlatformDexerName("eth");
        String cachedJson = JacksonUtils.serialize(cachedDto);
        
        // Mock cache response - first token cached, second not cached (empty string)
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of(cachedJson, "")));
        
        // Mock API response for missing token
        TokenPriceDTO apiDto = new TokenPriceDTO();
        apiDto.setAddress("0xdef");
        apiDto.setPlatformDexerName("eth");
        when(dexerApiClient.getPairListByDeQuery(anyList())).thenReturn(Mono.just(List.of(apiDto)));
        when(valueOperations.set(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));
        
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 2);
    }

    @Test
    public void testGetPairsFromDeQuery_noCacheHit() {
        // Create test tokens
        BatchPlatformTokenRequestDTO.PlatformTokenDTO token1 = BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder()
            .platform("eth")
            .address("0xabc")
            .build();
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(token1);
        
        // Mock empty cache response
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of("")));
        
        // Mock API response
        TokenPriceDTO apiDto = new TokenPriceDTO();
        apiDto.setAddress("0xabc");
        apiDto.setPlatformDexerName("eth");
        when(dexerApiClient.getPairListByDeQuery(anyList())).thenReturn(Mono.just(List.of(apiDto)));
        when(valueOperations.set(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));
        
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 1);
        Assert.assertEquals(list.get(0).getAddress(), "0xabc");
    }

    @Test
    public void testGetPairsFromDeQuery_emptyInput() {
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(null);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertTrue(list.isEmpty());
        
        result = dexerServiceImpl.getPairsFromDeQuery(List.of());
        list = result.block();
        Assert.assertNotNull(list);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testGetPairsFromDeQuery_batchProcessing() {
        // Create multiple test tokens to test batch processing
        List<BatchPlatformTokenRequestDTO.PlatformTokenDTO> tokens = List.of(
            BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder().platform("eth").address("0xabc").build(),
            BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder().platform("eth").address("0xdef").build(),
            BatchPlatformTokenRequestDTO.PlatformTokenDTO.builder().platform("bsc").address("0x123").build()
        );
        
        // Mock empty cache for all tokens
        when(valueOperations.multiGet(anyList())).thenReturn(Mono.just(List.of("", "", "")));
        
        // Mock API responses
        List<TokenPriceDTO> apiResponse = List.of(
            createTokenPriceDTO("0xabc", "eth"),
            createTokenPriceDTO("0xdef", "eth"),
            createTokenPriceDTO("0x123", "bsc")
        );
        when(dexerApiClient.getPairListByDeQuery(anyList())).thenReturn(Mono.just(apiResponse));
        when(valueOperations.set(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));
        
        Mono<List<TokenPriceDTO>> result = dexerServiceImpl.getPairsFromDeQuery(tokens);
        List<TokenPriceDTO> list = result.block();
        Assert.assertNotNull(list);
        Assert.assertEquals(list.size(), 3);
    }

    private TokenPriceDTO createTokenPriceDTO(String address, String platform) {
        TokenPriceDTO dto = new TokenPriceDTO();
        dto.setAddress(address);
        dto.setPlatformDexerName(platform);
        return dto;
    }
}
