package com.cmc.asset.job.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.WatchListEntity;
import com.cmc.asset.job.dto.JobHandlingResult;
import com.cmc.asset.job.repository.mongo.asset.CryptoWatchListRepository;
import com.cmc.asset.job.repository.redis.AssetCacheRedisRepository;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class PopularFollowWatchListHandlerTest {

    @Mock
    private CryptoWatchListRepository mockCryptoWatchListRepository;
    @Mock
    private AssetCacheRedisRepository mockAssetCacheRedisRepository;

    @InjectMocks
    private PopularFollowWatchListHandler popularFollowWatchListHandlerUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() throws Exception {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testHandle() throws Exception {
        // Setup
        // Configure CryptoWatchListRepository.findAllBySharedTrueAndActivedTrueOrderByFollowsDesc(...).
        final List<WatchListEntity> watchListEntities = List.of(
            new WatchListEntity(new ObjectId(), "userId",
                1L, "name", "description", 0L, Set.of(0), Set.of(0), Set.of(0), List.of(), false, false, 0, null, null));
        when(mockCryptoWatchListRepository.findAllBySharedTrueAndActivedTrueOrderByFollowsDesc(1000))
            .thenReturn(watchListEntities);
        doNothing().when(mockAssetCacheRedisRepository).zadd(any(), any());

        // Run the test
        final JobHandlingResult<String> result = popularFollowWatchListHandlerUnderTest.handle("jobData");

        // Verify the results
        Assert.assertTrue(result.isSucceeded());

    }

}
