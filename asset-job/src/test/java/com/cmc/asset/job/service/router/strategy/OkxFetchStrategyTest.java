package com.cmc.asset.job.service.router.strategy;

import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.asset.dao.entity.portfolio.BlockChainPO;
import com.cmc.asset.dao.entity.portfolio.OkLinkMapConfigItem;
import com.cmc.asset.domain.crypto.CryptoCurrencyInfoDTO;
import com.cmc.asset.job.cache.CryptoCurrencyCache;
import com.cmc.asset.job.cache.CryptoPriceCache;
import com.cmc.asset.job.cache.DexTokenCache;
import com.cmc.asset.job.cache.DexerPlatformCache;
import com.cmc.asset.job.config.DynamicApolloRefreshConfig;
import com.cmc.asset.job.integration.OkxClient;
import com.cmc.asset.job.integration.response.okx.OkxAddressBalanceDTO;
import com.cmc.asset.model.contract.dexer.PlatformNewDTO;
import com.cmc.asset.model.contract.dquery.TokenPriceDTO;
import com.cmc.asset.model.contract.portfolio.PortfolioCryptoInfoDTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class OkxFetchStrategyTest {
    @InjectMocks
    private OkxFetchStrategy okxFetchStrategy;

    @Mock
    private DynamicApolloRefreshConfig dynamicApolloRefreshConfig;

    @Mock
    private OkxClient okxClient;

    @Mock
    private CryptoCurrencyCache cryptoCurrencyCache;

    private AutoCloseable mockitoCloseable;

    @Mock
    private CryptoPriceCache cryptoPriceCache;
    @Mock
    private DexerPlatformCache dexerPlatformCache;
    @Mock
    private DexTokenCache dexTokenCache;
    private OkxAddressBalanceDTO balanceDTO;

    private Map<Integer, OkLinkMapConfigItem> configItemMap;

    private CryptoCurrencyInfoDTO currencyInfoDTO;

    private CryptoCurrencyInfoDTO nativeCurrencyInfoDTO;

    private Map<Integer, PortfolioCryptoInfoDTO> idMap;

    private PortfolioCryptoInfoDTO portfolioCryptoInfoDTO;

    private TokenPriceDTO tokenPriceDTO;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        balanceDTO = new OkxAddressBalanceDTO();
        OkxAddressBalanceDTO.TokenAsset nativeAsset = new OkxAddressBalanceDTO.TokenAsset();
        nativeAsset.setChainIndex("1");
        nativeAsset.setTokenAddress("");
        nativeAsset.setAddress("test");
        nativeAsset.setSymbol("test");
        nativeAsset.setBalance("100");
        nativeAsset.setTokenPrice("100");

        OkxAddressBalanceDTO.TokenAsset nativeAsset2 = new OkxAddressBalanceDTO.TokenAsset();
        nativeAsset2.setChainIndex("1");
        nativeAsset2.setTokenAddress("");
        nativeAsset2.setAddress("test");
        nativeAsset2.setSymbol("test");
        nativeAsset2.setBalance("100");
        nativeAsset2.setTokenPrice("100");
        OkxAddressBalanceDTO.TokenAsset nonNativeAsset = new OkxAddressBalanceDTO.TokenAsset();
        nonNativeAsset.setChainIndex("2");
        nonNativeAsset.setTokenAddress("test");
        nonNativeAsset.setAddress("test");
        nonNativeAsset.setSymbol("test");
        nonNativeAsset.setBalance("100");
        nonNativeAsset.setTokenPrice("100");

        OkxAddressBalanceDTO.TokenAsset nonNativeAsset2 = new OkxAddressBalanceDTO.TokenAsset();
        nonNativeAsset2.setChainIndex("2");
        nonNativeAsset2.setTokenAddress("test");
        nonNativeAsset2.setAddress("test");
        nonNativeAsset2.setSymbol("test");
        nonNativeAsset2.setBalance("100");
        nonNativeAsset2.setTokenPrice("100");
        List<OkxAddressBalanceDTO.TokenAsset> tokenAssets = new ArrayList<>();
        tokenAssets.add(nativeAsset);
        tokenAssets.add(nativeAsset2);
        tokenAssets.add(nonNativeAsset);
        tokenAssets.add(nonNativeAsset2);
        balanceDTO.setTokenAssets(tokenAssets);

        configItemMap = new HashMap<>();
        OkLinkMapConfigItem configItem = new OkLinkMapConfigItem();
        configItem.setChainId(1);
        configItem.setNativeTokenId("1027");
        configItemMap.put(1, configItem);

        currencyInfoDTO = new CryptoCurrencyInfoDTO();
        currencyInfoDTO.setName("test");

        nativeCurrencyInfoDTO = new CryptoCurrencyInfoDTO();
        nativeCurrencyInfoDTO.setName("test");
        nativeCurrencyInfoDTO.setStatus("active");

        idMap = new HashMap<>();
        portfolioCryptoInfoDTO = new PortfolioCryptoInfoDTO();
        portfolioCryptoInfoDTO.setCryptoId(1);
        portfolioCryptoInfoDTO.setPrice("100");
        idMap.put(1, portfolioCryptoInfoDTO);

        tokenPriceDTO = new TokenPriceDTO();


        ReflectionTestUtils.setField(okxFetchStrategy, "filterLessOne", true);
        ReflectionTestUtils.setField(okxFetchStrategy, "walletTokenValueLtLimit", "100000");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void fetch() {
        Mockito.when(dynamicApolloRefreshConfig.getOkLinkOkxMapConfig()).thenReturn(configItemMap);
        Mockito.when(okxClient.getAllTokenBalancesByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Mono.just(balanceDTO));
        Mockito.when(cryptoCurrencyCache.getCryptoCurrencyById(Mockito.anyInt())).thenReturn(currencyInfoDTO);
        Mockito.when(cryptoPriceCache.getAll(Mockito.anyCollection())).thenReturn(Mono.just(idMap));
        Mockito.when(cryptoPriceCache.get(Mockito.any())).thenReturn(Mono.just(portfolioCryptoInfoDTO));
        PlatformNewDTO v1 = new PlatformNewDTO();
        v1.setDn("eth");
        TokenPriceDTO tokenPriceDTO = new TokenPriceDTO();
        Mockito.when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, v1, 14, v1));
        Mockito.when(dexTokenCache.get(Mockito.anyString(), Mockito.anyString())).thenReturn(Mono.just(tokenPriceDTO));
        Mono<BlockChainPO> result = okxFetchStrategy.fetch(1, "test", new Date());
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void fetch_nonNativeTokenMapedCmc() {
        Mockito.when(dynamicApolloRefreshConfig.getOkLinkOkxMapConfig()).thenReturn(configItemMap);
        Mockito.when(okxClient.getAllTokenBalancesByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Mono.just(balanceDTO));
        Mockito.when(cryptoCurrencyCache.getCryptoCurrencyById(Mockito.anyInt())).thenReturn(nativeCurrencyInfoDTO);
        Mockito.when(cryptoPriceCache.getAll(Mockito.anyCollection())).thenReturn(Mono.just(idMap));
        Mockito.when(cryptoPriceCache.get(Mockito.any())).thenReturn(Mono.just(portfolioCryptoInfoDTO));
        PlatformNewDTO v1 = new PlatformNewDTO();
        v1.setDn("eth");
        TokenPriceDTO tokenPriceDTO = new TokenPriceDTO();
        Mockito.when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, v1, 14, v1));
        Mockito.when(dexTokenCache.get(Mockito.anyString(), Mockito.anyString())).thenReturn(Mono.just(tokenPriceDTO));
        Mono<BlockChainPO> result = okxFetchStrategy.fetch(1, "test", new Date());
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }

    @Test
    public void fetch_nonNativeToken() {
        Mockito.when(dynamicApolloRefreshConfig.getOkLinkOkxMapConfig()).thenReturn(configItemMap);
        Mockito.when(okxClient.getAllTokenBalancesByAddress(Mockito.anyString(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Mono.just(balanceDTO));
        Mockito.when(cryptoCurrencyCache.getCryptoCurrencyById(Mockito.anyInt())).thenReturn(currencyInfoDTO);
        Mockito.when(cryptoPriceCache.getAll(Mockito.anyCollection())).thenReturn(Mono.just(idMap));
        Mockito.when(cryptoPriceCache.get(Mockito.any())).thenReturn(Mono.just(portfolioCryptoInfoDTO));
        PlatformNewDTO v1 = new PlatformNewDTO();
        v1.setDn("eth");
        TokenPriceDTO tokenPriceDTO = new TokenPriceDTO();
        Mockito.when(dexerPlatformCache.getVisibleOnDexScanPlatforms()).thenReturn(Map.of(1, v1, 14, v1));
        Mockito.when(dexTokenCache.get(Mockito.anyString(), Mockito.anyString())).thenReturn(Mono.just(tokenPriceDTO));
        Mono<BlockChainPO> result = okxFetchStrategy.fetch(1, "test", new Date());
        StepVerifier.create(result).expectSubscription()
            .assertNext(res -> {
                Assert.assertNotNull(result);
            }).verifyComplete();
    }
}