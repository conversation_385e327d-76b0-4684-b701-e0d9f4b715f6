package com.cmc.asset.service.controller;

import com.cmc.asset.ServerContext;
import com.cmc.asset.business.portfolio.IPortfolioOldService;
import com.cmc.asset.model.contract.portfolio.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/12
 * @desc 针对老app的portfolio接口，不需要走鉴权，用秘钥来对接，单独设置的接口监控，新版的用portfolioController
 */
@RestController
@RequestMapping("/v1/portfolio")
public class PortfolioV1Controller {

    @Resource
    private IPortfolioOldService portfolioOldService;

    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @PostMapping("/add")
    public Mono<PortfolioResultDTO> add(@Valid @RequestBody PortfolioAddDTO portfolioAddDTO){
        return monitorForOldApi(portfolioOldService.add(portfolioAddDTO));
    }


    @PostMapping("/update")
    public Mono<PortfolioResultDTO> update(@Valid @RequestBody PortfolioUpdateDTO portfolioUpdateDTO){
        return monitorForOldApi(portfolioOldService.update(portfolioUpdateDTO));
    }

    @PostMapping("/delete")
    public Mono<Boolean> delete(@Valid @RequestBody PortfolioDeleteDTO portfolioIdDTO){
       return monitorForOldApi(portfolioOldService.delete(portfolioIdDTO));
    }

    @PostMapping("/get")
    public Mono<PortfolioResultDTO> detail(@Valid @RequestBody PortfolioQueryDTO portfolioQueryDTO){
        return monitorForOldApi(portfolioOldService.detail(portfolioQueryDTO));
    }

    @PostMapping("/getAll")
    public Mono<List<PortfolioQueryResultDTO>> queryAll(@Valid @RequestBody PortfolioQueryDTO portfolioQueryDTO){
        return monitorForOldApi(portfolioOldService.queryAll(portfolioQueryDTO).collectList());
    }

    private Mono monitorForOldApi(Mono apiService){
        Long start = System.currentTimeMillis();
        return Mono.subscriberContext().flatMap(context-> appendContext(context).flatMap(serverContext->apiService).doFinally(item -> {
//            ServerContext serverContext = context.get(ServerContext.KEY_NAME);
//            String name = serverContext != null ? serverContext.getUrl() : "Unknown";
//            mr.recordExecutionTime(removeUrlSuffix(name), System.currentTimeMillis() - start);
        }));
    }


    /**
     * remove the url's suffix
     *
     * @param url
     * @return
     */
    private String removeUrlSuffix(String url) {

        if (StringUtils.isBlank(url)) {
            return "Unknown";
        }
        if (StringUtils.startsWith(url, "/system")) {
            return StringUtils.substring(url, 0, url.lastIndexOf('/'));
        }
        return url;
    }


    /**
     * append context
     *
     * @param context the context is webflux context.
     * @return
     */
    private Mono<ServerContext> appendContext(final Context context) {

        final ServerContext serverContext = context.get(ServerContext.KEY_NAME);
        String url = getPathURL(serverContext.getServerWebExchange());
        return requestMappingHandlerMapping.getHandler(serverContext.getServerWebExchange())
            .map(h -> {
                HandlerMethod handlerMethod = (HandlerMethod) h;
                serverContext.setFullActionName(handlerMethod.toString());
                serverContext.setUrl(url);
                return serverContext;
            });
    }


    /**
     * get the request url.
     *
     * @param exchange
     * @return
     */
    private String getPathURL(ServerWebExchange exchange) {

        if (exchange == null
            || exchange.getRequest() == null
            || exchange.getRequest().getPath() == null) {
            return Strings.EMPTY;
        }
        return exchange.getRequest().getPath().value();
    }

}
