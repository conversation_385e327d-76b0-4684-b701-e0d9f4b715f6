package com.cmc.asset.service.controller;

import com.cmc.asset.business.coins.impl.UserCoinService;
import com.cmc.asset.model.contract.news.QueryTopCoinsParamDTO;
import com.cmc.asset.model.contract.news.QueryTopCoinsResultDTO;
import com.cmc.asset.model.contract.news.QueryUserTopCoinsParamDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsRequestDTO;
import com.cmc.asset.model.contract.user.UserRelevantCoinsResponseDTO;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.ApiOperation;
import reactor.core.publisher.Mono;

/**
 * controller for customized news
 *
 * <AUTHOR>
 * @date 2022/3/30 下午11:21
 */
@RestController
@RequestMapping("/system/v3/user")
public class UserCoinController extends BaseController {

    @Autowired
    private UserCoinService userCoinService;

    @ApiOperation(value = """
        Get the top 5 coins in the user's portfolio and watchlist list
        """ , httpMethod = "POST")
    @PostMapping("/get-top-coins")
    @Auth
    public Mono<ApiResponse<QueryTopCoinsResultDTO>> getTopCoins(@Valid @RequestBody QueryTopCoinsParamDTO param) {
        return invoke(param, userCoinService::getTopCoins);
    }

    @ApiOperation(value = """
    Get the top N coins in the user's portfolio and watchlist list
    """ , httpMethod = "POST")
    @PostMapping("/get-user-top-coins")
    @Auth
    public Mono<ApiResponse<QueryTopCoinsResultDTO>> getUserTopCoins(@Valid @RequestBody QueryUserTopCoinsParamDTO param) {
        return invoke(param, userCoinService::getUserTopCoins);
    }

    @ApiOperation(value = "get user relevant coins", httpMethod = "POST")
    @PostMapping("/get-user-relevant-coins")
    public Mono<UserRelevantCoinsResponseDTO> getUserRelevantCoins(
        @RequestBody UserRelevantCoinsRequestDTO userRelevantCoinsRequestDTO) {
        return userCoinService.getUserRelevantCoins(userRelevantCoinsRequestDTO);
    }
}
