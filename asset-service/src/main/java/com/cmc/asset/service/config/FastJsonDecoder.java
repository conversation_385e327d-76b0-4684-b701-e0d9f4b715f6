package com.cmc.asset.service.config;

import com.fasterxml.jackson.core.JsonEncoding;
import lombok.RequiredArgsConstructor;
import lombok.extern.java.Log;
import org.reactivestreams.Publisher;
import org.springframework.core.ResolvableType;
import org.springframework.core.codec.Hints;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.codec.HttpMessageDecoder;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.MimeType;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/22 17:58
 * @description
 */
@Log
@RequiredArgsConstructor
@Component
public class FastJsonDecoder implements HttpMessageDecoder<Object> {

    /**
     * Determine the JSON encoding to use for the given mime type.
     *
     * @param mimeType the mime type as requested by the caller
     * @return the JSON encoding to use (never {@code null})
     * @since 5.0.5
     */
    protected JsonEncoding getJsonEncoding(@Nullable MimeType mimeType) {
        if (mimeType != null && mimeType.getCharset() != null) {
            Charset charset = mimeType.getCharset();
            for (JsonEncoding encoding : JsonEncoding.values()) {
                if (charset.name().equals(encoding.getJavaName())) {
                    return encoding;
                }
            }
        }
        return JsonEncoding.UTF8;
    }

    @Override
    public Map<String, Object> getDecodeHints(ResolvableType actualType, ResolvableType elementType, ServerHttpRequest request, ServerHttpResponse response) {

        return Hints.none();
    }

    @Override
    public boolean canDecode(ResolvableType elementType, MimeType mimeType) {


        return false;
    }

    @Override
    public Flux<Object> decode(Publisher<DataBuffer> inputStream, ResolvableType elementType, MimeType mimeType, Map<String, Object> hints) {
        return null;
    }

    @Override
    public Mono<Object> decodeToMono(Publisher<DataBuffer> inputStream, ResolvableType elementType, MimeType mimeType, Map<String, Object> hints) {
        return null;
    }

    @Override
    public List<MimeType> getDecodableMimeTypes() {
        return null;
    }
}
