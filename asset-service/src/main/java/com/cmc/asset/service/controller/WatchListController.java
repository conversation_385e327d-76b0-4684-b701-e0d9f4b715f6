package com.cmc.asset.service.controller;

import com.cmc.asset.business.watchlist.WatchListService;
import com.cmc.asset.model.contract.watchlist.AutoFillFollowParamDTO;
import com.cmc.asset.model.contract.watchlist.DeleteWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.FollowWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QuerySharedWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicRequest;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicResponse;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicV2Request;
import com.cmc.asset.model.contract.watchlist.QueryWatchListBasicV2Response;
import com.cmc.asset.model.contract.watchlist.QueryWatchListMultiParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.QueryWatchListRespDTO;
import com.cmc.asset.model.contract.watchlist.WatchAssetInfoRespDTO;
import com.cmc.asset.model.contract.watchlist.WatchListAssetParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListGuestToRegularDTO;
import com.cmc.asset.model.contract.watchlist.WatchListParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResourceResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListResultMultiDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSharedResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeParamDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSubscribeResultDTO;
import com.cmc.asset.model.contract.watchlist.WatchListSummaryDTO;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import com.cmc.data.common.BaseRequest;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.ApiOperation;
import reactor.core.publisher.Mono;

/**
 * /watchlist/subscribe
 * /watchlist/unsubscribe
 * /watchlist/userid
 *
 * <AUTHOR>
 * @date 2020/10/17 14:54
 * @description
 */
@RestController
@RequestMapping("/")
public class WatchListController extends BaseController {

    @Autowired
    private WatchListService watchListService;

    @Auth(required = false)
    @PostMapping("v3/watchlist/query")
    public Mono<ApiResponse<WatchListResultDTO>> query(@RequestBody QueryWatchListParamDTO param) {

        return invoke(param, watchListService::query);
    }

    @Auth(required = false)
    @PostMapping("v3/watchlist/query-basic")
    public Mono<ApiResponse<QueryWatchListBasicResponse>> queryBasic(@RequestBody QueryWatchListBasicRequest param) {
        return invoke(param, watchListService::queryBasic);
    }

    @Auth(required = false)
    @ApiOperation(value = "watchlist query basic")
    @PostMapping("v3.1/watchlist/query-basic")
    public Mono<ApiResponse<QueryWatchListBasicV2Response>> queryBasicV2(@RequestBody QueryWatchListBasicV2Request param) {
        return invoke(param, watchListService::queryBasicV2);
    }

    @Auth
    @PostMapping("v3/watchlist/query/resource")
    public Mono<ApiResponse<WatchListResourceResultDTO>> queryByResourceId(@RequestBody QueryWatchListParamDTO param) {
        return invoke(param, watchListService::queryByResourceId);
    }

    @Auth
    @PostMapping("v3/watchlist/subscribe")
    public Mono<ApiResponse<WatchListSubscribeResultDTO>> subscribe(@RequestBody WatchListSubscribeParamDTO param) {
        return invoke(param, watchListService::subscribe);
    }

    @Auth
    @PostMapping("system/v3/watchlist/fillFollow")
    public Mono<ApiResponse<WatchListResultDTO>> fillFollow(@RequestBody AutoFillFollowParamDTO param) {
        return invoke(param, watchListService::fillFollow);
    }

    @Auth
    @PostMapping("v3/watchlist/remove")
    public Mono<ApiResponse<String>> delete(@RequestBody DeleteWatchListParamDTO param) {

        return invoke(param, watchListService::delete);
    }

    @Auth
    @PostMapping("v3/watchlist/save")
    public Mono<ApiResponse<String>> save(@RequestBody WatchListParamDTO param) {

        return invoke(param, watchListService::save);
    }

    @Auth(required = false)
    @PostMapping("v3/watchlist/shared/query")
    public Mono<ApiResponse<WatchListSharedResultDTO>> querySharedWatchList(@RequestBody QuerySharedWatchListParamDTO param) {

        return invoke(param, watchListService::querySharedWatchLists);
    }

    @Auth
    @PostMapping("v3/watchlist/follow")
    public Mono<ApiResponse<String>> follow(@RequestBody FollowWatchListParamDTO param) {

        return invoke(param, watchListService::followWatchList);
    }

    @GetMapping("system/v3/watchlist/query/{userId}")
    public Mono<ApiResponse<WatchListResultDTO>> queryForInteranl(@ModelAttribute QueryWatchListParamDTO param, @PathVariable("userId") String userId) {

        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::query);
    }

    @GetMapping("system/v3/watchlist/query-crypto-ids/{userId}")
    public Mono<ApiResponse<Set<Integer>>> queryWatchListCryptoIdsForInternal(@ModelAttribute QueryWatchListParamDTO param, @PathVariable("userId") String userId){
        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::queryWatchListCryptoIds);
    }

    @PostMapping("system/v3/watchlist/subscribe/{userId}")
    public Mono<ApiResponse<WatchListSubscribeResultDTO>> subscribeForInteranl(@RequestBody WatchListSubscribeParamDTO param, @PathVariable("userId") String userId) {

        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::subscribe);
    }

    @PostMapping("system/v3/watchlist/remove/{userId}")
    public Mono<ApiResponse<String>> deleteForInteranl(@RequestBody DeleteWatchListParamDTO param, @PathVariable("userId") String userId) {

        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::delete);
    }

    @PostMapping("system/v3/watchlist/main/create/{userId}")
    public Mono<ApiResponse<String>> createMainWatchlistForInteranl(@PathVariable("userId") String userId) {

        BaseRequest baseRequest = BaseRequest.getInstance();
        baseRequest.getHeader().setUserId(userId);
        return invoke(baseRequest, watchListService::createMainWatchList);
    }

    @PostMapping("system/v3/watchlist/guest-to-regular")
    public Mono<ApiResponse<String>> createMainWatchlistForGuestToRegular(@RequestBody WatchListGuestToRegularDTO param) {
        param.getHeader().setUserId(param.getUserId());
        param.getHeader().setUid(param.getUid());
        return invoke(param, watchListService::guestToRegularMainWatchList);
    }

    @PostMapping("system/v3/watchlist/save/{userId}")
    public Mono<ApiResponse<String>> saveForInteranl(@RequestBody WatchListParamDTO param, @PathVariable("userId") String userId) {

        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::save);
    }

    @GetMapping("system/v3/watchlist/shared/query/{userId}")
    public Mono<ApiResponse<WatchListSharedResultDTO>> querySharedWatchListForInteranl(@ModelAttribute QuerySharedWatchListParamDTO param, @PathVariable("userId") String userId) {

        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::querySharedWatchLists);
    }

    @PostMapping("system/v3/watchlist/follow/{userId}")
    public Mono<ApiResponse<String>> followForInteranl(@RequestBody FollowWatchListParamDTO param, @PathVariable("userId") String userId) {
        param.getHeader().setUserId(userId);
        return invoke(param, watchListService::followWatchList);
    }

    @Auth(required = false)
    @PostMapping("v3/watchlist/query/multi")
    public Mono<ApiResponse<WatchListResultMultiDTO>> queryMulti(@RequestBody QueryWatchListMultiParamDTO param) {

        return invoke(param, watchListService::queryMulti);
    }

    @PostMapping("v3/watchlist/item/query")
    public Mono<ApiResponse<WatchAssetInfoRespDTO>> queryWatchAsset(@RequestBody WatchListAssetParamDTO param) {
        return invoke(param, watchListService::queryWatchAsset);
    }


    @PostMapping("system/v3/watchlist/get")
    public Mono<ApiResponse<QueryWatchListRespDTO>> getById(@RequestBody WatchListParamDTO param) {
        return invoke(param, watchListService::getById);
    }

    @Auth(required = false)
    @PostMapping("v3/watchlist/summary")
    public Mono<ApiResponse<List<WatchListSummaryDTO>>> queryWatchlistSummary(@RequestBody BaseRequest param) {
        return invoke(param, watchListService::queryWatchlistSummary);
    }

}
