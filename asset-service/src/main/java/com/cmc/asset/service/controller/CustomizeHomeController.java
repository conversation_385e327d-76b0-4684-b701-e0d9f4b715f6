package com.cmc.asset.service.controller;

import com.cmc.asset.business.UserCustomizeSectionService;
import com.cmc.asset.model.contract.user.QueryUserCustomizeSectionParamDTO;
import com.cmc.asset.model.contract.user.UserCustomizeSectionParamDTO;
import com.cmc.asset.model.contract.user.UserCustomizeSectionResultDTO;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/4/8 19:00
 * @description
 */
@RestController
@RequestMapping("/")
public class CustomizeHomeController extends BaseController {

    @Autowired
    private UserCustomizeSectionService userCustomizeSectionService;


    @Auth(required = false)
    @GetMapping("v3/homepage/customize/section")
    public Mono<ApiResponse<UserCustomizeSectionResultDTO>> queryForGet(@ModelAttribute QueryUserCustomizeSectionParamDTO param) {

        return invoke(param, userCustomizeSectionService::queryAppSections);
    }

    @Auth(required = false)
    @PostMapping("v3/homepage/customize/section")
    public Mono<ApiResponse<UserCustomizeSectionResultDTO>> queryForPost(@RequestBody QueryUserCustomizeSectionParamDTO param) {

        return invoke(param, userCustomizeSectionService::queryAppSections);
    }

    @Auth(required = true)
    @PostMapping("v3/homepage/customize/section/add")
    public Mono<ApiResponse<UserCustomizeSectionResultDTO>> save(@RequestBody UserCustomizeSectionParamDTO param) {

        return invoke(param, userCustomizeSectionService::saveAppSections);
    }
}