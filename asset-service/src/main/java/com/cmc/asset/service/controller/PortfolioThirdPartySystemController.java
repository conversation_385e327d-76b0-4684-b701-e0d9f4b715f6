package com.cmc.asset.service.controller;

import com.cmc.asset.business.portfolio.PortfolioThirdPartyService;
import com.cmc.asset.model.contract.BaseResultResponse;
import com.cmc.asset.model.contract.portfolio.thirdparty.RefreshDataRequestDTO;
import com.cmc.data.common.ApiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2023/4/24 12:35
 */
@RestController
@RequestMapping("/system/v3/portfolio/third-party")
public class PortfolioThirdPartySystemController extends BaseController {

    private final PortfolioThirdPartyService portfolioThirdPartyService;

    public PortfolioThirdPartySystemController(PortfolioThirdPartyService portfolioThirdPartyService) {
        this.portfolioThirdPartyService = portfolioThirdPartyService;
    }

    @PostMapping("/add-data")
    public Mono<ApiResponse<BaseResultResponse>> refreshData(@Valid @RequestBody RefreshDataRequestDTO requestDTO) {
        return invoke(requestDTO, portfolioThirdPartyService::refreshData);
    }

}
