package com.cmc.asset.service.controller;

import com.cmc.asset.business.referral.ReferralService;
import com.cmc.asset.model.common.PageDTO;
import com.cmc.asset.model.contract.referral.ClaimAllRequest;
import com.cmc.asset.model.contract.referral.ClaimResultDTO;
import com.cmc.asset.model.contract.referral.ClaimSingleRequest;
import com.cmc.asset.model.contract.referral.ReferralCollectDTO;
import com.cmc.asset.model.contract.referral.ReferralDetailPageDTO;
import com.cmc.asset.model.contract.referral.ReferralResultDTO;
import com.cmc.asset.model.contract.referral.ReferralSummaryDTO;
import com.cmc.asset.model.contract.referral.ReferredTaskRequest;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import com.cmc.data.common.BaseRequest;

import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> Jiang
 * @since 2021/9/27
 **/
@Api("Referral Program API")
@RestController
@RequestMapping("/v3/referral")
public class ReferralController extends BaseController{
    @Autowired
    private ReferralService referralService;

    @Auth
    @PostMapping("/join")
    public Mono<ApiResponse<ReferralResultDTO>> join(@RequestBody BaseRequest baseRequest) {
        return invoke(baseRequest, referralService::join);
    }

    /**
     * Claim all the diamonds for the referral whose referred users who have finished the accumulated diamonds task.
     *
     * @param claimAllRequest the request object
     * @return
     */
    @Auth
    @PostMapping("/claim-all")
    public Mono<ApiResponse<ClaimResultDTO>> claimAll(@RequestBody ClaimAllRequest claimAllRequest) {
        return invoke(claimAllRequest, referralService::claimAll);
    }

    /**
     * Claim single the diamonds for the referral whose referred user who has finished the accumulated diamonds task.
     * @param claimSingleRequest the request object
     * @return
     */
    @Auth
    @PostMapping("/claim-single")
    public Mono<ApiResponse<ClaimResultDTO>> claimSingle(@RequestBody ClaimSingleRequest claimSingleRequest) {
        return invoke(claimSingleRequest, referralService::claimSingle);
    }

    @Auth
    @PostMapping("/collect")
    public Mono<ApiResponse<ReferralCollectDTO>> collect(@RequestBody ReferredTaskRequest baseRequest) {
        return invoke(baseRequest, referralService::collect);
    }

    @Auth
    @PostMapping("/list")
    public Mono<ApiResponse<ReferralDetailPageDTO>> referralList(@Valid @RequestBody PageDTO pageDTO) {
        return invoke(pageDTO, referralService::referralList);
    }

    @Auth
    @PostMapping("/summary")
    public Mono<ApiResponse<ReferralSummaryDTO>> referralSummary(@Valid @RequestBody BaseRequest request) {
        return invoke(request, referralService::referralSummary);
    }

    @Auth
    @PostMapping("/is-a-referral")
    public Mono<ApiResponse<Boolean>> checkIsReferral(@Valid @RequestBody BaseRequest request) {
        return invoke(request, referralService::isReferral);
    }
}
