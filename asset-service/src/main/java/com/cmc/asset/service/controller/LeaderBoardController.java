package com.cmc.asset.service.controller;

import com.cmc.asset.business.leaderboard.LeaderBoardService;
import com.cmc.asset.domain.leaderboard.LeaderBoardVo;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import com.cmc.data.common.BaseRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/7/15
 */
@RestController
@RequestMapping("v3/leaderboard")
public class LeaderBoardController extends BaseController{
    @Autowired(required = false)
    private LeaderBoardService leaderBoardService;

    @Auth
    @PostMapping("/rankInfo")
    public Mono<ApiResponse<LeaderBoardVo>> queryUserRankInfo(@Valid @RequestBody BaseRequest baseRequest){
        return invoke(baseRequest,leaderBoardService::getUserRankInfo);
    }

}
