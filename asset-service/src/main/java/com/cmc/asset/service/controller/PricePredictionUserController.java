package com.cmc.asset.service.controller;

import com.cmc.asset.business.priceprediction.PricePredictionUserService;
import com.cmc.asset.model.contract.priceprediction.PricePredictionRankingResultDTO;
import com.cmc.asset.model.contract.priceprediction.PricePredictionResultDTO;
import com.cmc.asset.model.contract.priceprediction.PricePredictionUserResultDTO;
import com.cmc.asset.model.contract.priceprediction.QueryPricePredictionUserParamDTO;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import reactor.core.publisher.Mono;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @date 2021/7/22 16:31:29
 */
@Api("The price prediction user api")
@RestController
@RequestMapping("/v3/price-prediction/user")
public class PricePredictionUserController extends BaseController {

    @Resource
    private PricePredictionUserService pricePredictionUserService;

    @Auth(required = false)
    @PostMapping("/query/single")
    public Mono<ApiResponse<PricePredictionUserResultDTO>> queryOthersByUserIdWithoutLogin(@RequestBody QueryPricePredictionUserParamDTO request) {
        return invoke(request, pricePredictionUserService::queryByUserId);
    }

    @Auth
    @ApiIgnore
    @PostMapping("/query/single-with-login")
    public Mono<ApiResponse<PricePredictionUserResultDTO>> querySingleByUserIdWithLogin(@RequestBody QueryPricePredictionUserParamDTO request) {
        return invoke(request, pricePredictionUserService::queryByUserId);
    }

    @Auth
    @PostMapping("/query/monthly-predictions")
    public Mono<ApiResponse<PricePredictionResultDTO>> queryMonthlyPredictions(@RequestBody QueryPricePredictionUserParamDTO request) {
        return invoke(request, pricePredictionUserService::queryPersonalPredictions);
    }

    @Auth
    @PostMapping("/query/recent-predictions")
    public Mono<ApiResponse<PricePredictionResultDTO>> queryRecentPredictions(@RequestBody QueryPricePredictionUserParamDTO request) {
        return invoke(request, pricePredictionUserService::queryRecentPredictions);
    }

    @Auth
    @PostMapping("/query/ranking")
    public Mono<ApiResponse<PricePredictionRankingResultDTO>> queryRanking(@RequestBody QueryPricePredictionUserParamDTO request) {
        return invoke(request, pricePredictionUserService::queryRanking);
    }
}
