package com.cmc.asset.service.exception;

import com.cmc.data.common.ApiResponse;
import com.cmc.data.common.enums.MessageCode;
import com.cmc.data.common.exception.BusinessException;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import lombok.extern.slf4j.Slf4j;

/**
 * @author:hzj
 * @date:2019-07-08
 */
@RestControllerAdvice
@Slf4j
public class GlobalWebExceptionHandler {

    @ExceptionHandler({
            BusinessException.class,
            IllegalArgumentException.class,
            NullPointerException.class,
            MethodArgumentNotValidException.class,
            ValidationException.class,
            ConstraintViolationException.class,
            ConversionNotSupportedException.class,
            TypeMismatchException.class,
            HttpMessageNotReadableException.class,
            HttpMessageNotWritableException.class,
            MethodArgumentNotValidException.class,
            BindException.class,
            AsyncRequestTimeoutException.class,
            Exception.class,
    })
    public ResponseEntity<Object> handleException(Exception ex, ServerHttpRequest serverHttpRequest) {
        log.error("GlobalWebExceptionHandler error, path:{},platform:{},ex:", getPath(serverHttpRequest),
            getPlatform(serverHttpRequest), ex);
        return getResponseEntity(ex);
    }

    /**
     * throw error response
     * TODO
     * @param ex
     * @return
     */
    private ResponseEntity<Object> getResponseEntity(Throwable ex) {

        BusinessException businessException;
        ResponseEntity response = null;
        if (ex instanceof BusinessException &&
                (businessException = (BusinessException) ex) != null) {
            response = ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON_UTF8)
                    .body(ApiResponse.getErrorResult(businessException.getCode(),businessException.getDesc()));
        } else if (ex instanceof IllegalArgumentException
                || ex instanceof ValidationException
                || ex instanceof ConstraintViolationException) {
            response = ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON_UTF8)
                    .body(ApiResponse.getErrorResult(MessageCode.SYS_PARAMETER_ERROR));
        } else {
            response = ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON_UTF8)
                    .body(ApiResponse.getErrorResult(MessageCode.SYS_ZUUL_ERROR));
        }
        return response;
    }

    private String getPlatform(ServerHttpRequest serverHttpRequest){
        if(serverHttpRequest == null){
            return null;
        }
        try {
            return serverHttpRequest.getHeaders().getFirst("platform");
        }catch (Exception e){
            return null;
        }
    }


    private String getPath(ServerHttpRequest serverHttpRequest){
        if(serverHttpRequest == null){
            return null;
        }
        try {
            return serverHttpRequest.getPath().toString();
        }catch (Exception e){
            return null;
        }
    }
}
