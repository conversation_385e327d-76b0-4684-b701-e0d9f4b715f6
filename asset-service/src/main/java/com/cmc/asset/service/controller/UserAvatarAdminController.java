package com.cmc.asset.service.controller;

import com.cmc.asset.business.avatar.IUserAvatarService;
import com.cmc.asset.domain.avatar.AvatarAuditVO;
import com.cmc.asset.domain.avatar.AvatarVO;
import com.cmc.asset.model.common.CommonPageVO;
import com.cmc.asset.model.contract.user.BatchUpdateUserAvatarSortDTO;
import com.cmc.asset.model.contract.user.UserAvatarAdminListDTO;
import com.cmc.asset.model.contract.user.UserAvatarAdminSaveDTO;
import com.cmc.asset.model.contract.user.UserAvatarAuditQueryDTO;
import com.cmc.asset.model.contract.user.UserAvatarDetailQueryDTO;
import com.cmc.data.common.ApiResponse;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2021/8/26
 */
@RestController
@RequestMapping("system/v3/avatar")
public class UserAvatarAdminController extends BaseController {

    @Autowired
    private IUserAvatarService userAvatarService;

    @PostMapping("/list")
    public Mono<ApiResponse<CommonPageVO<AvatarVO>>> list(@Valid @RequestBody UserAvatarAdminListDTO request) {
        return invoke(request, userAvatarService::list);
    }

    @PostMapping("/save")
    public Mono<ApiResponse<AvatarVO>> save(@Valid @RequestBody UserAvatarAdminSaveDTO request) {
         return invoke(request, userAvatarService::saveSystemAvatar);
    }

    @PostMapping("/detail")
    public Mono<ApiResponse<AvatarVO>> detail(@Valid @RequestBody UserAvatarDetailQueryDTO request) {
        return invoke(request, userAvatarService::queryAvatarById);
    }

    @PostMapping("/batch-update-sort")
    public Mono<ApiResponse<List<AvatarVO>>> batchUpdateSort(@Valid @RequestBody BatchUpdateUserAvatarSortDTO request) {
        return invoke(request, userAvatarService::batchUpdateSorts);
    }

    @PostMapping("/audit/detail")
    public Mono<ApiResponse<AvatarAuditVO>> auditDetail(@Valid @RequestBody UserAvatarAuditQueryDTO request) {
        return invoke(request, userAvatarService::queryAvatarAuditById);

    }
}
