package com.cmc.asset.service.controller;

import com.cmc.asset.business.useraudit.UserAuditService;
import com.cmc.asset.model.contract.useraudit.ImplioWebHookRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2022/8/10 18:32
 */
@RestController
@RequestMapping("/v3/audit")
@Slf4j
public class UserAuditController extends BaseController {

    @Value("${com.cmc.asset.integration.implio-audit.api-key-asset-audit:}")
    private String apiKey;

    private final UserAuditService userAuditService;

    public UserAuditController(UserAuditService userAuditService) {
        this.userAuditService = userAuditService;
    }

    @PostMapping("/third-party/web-hook")
    public Mono<ResponseEntity<Boolean>> webHookCallback(@Valid @RequestBody ImplioWebHookRequestDTO request, @RequestHeader("X-Api-Key") String apiKeyInHeader) {
        log.debug("receive webhook request: apikey - {}, request body - {}", apiKeyInHeader, request);
        if (!apiKey.equals(apiKeyInHeader)) {
            ResponseEntity<Boolean> unAuthError = ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            return Mono.just(unAuthError);
        }
        ResponseEntity<Boolean> error = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        return userAuditService.handleCallback(request).filter(p -> p).map(ResponseEntity::ok).switchIfEmpty(Mono.just(error));
    }
}
