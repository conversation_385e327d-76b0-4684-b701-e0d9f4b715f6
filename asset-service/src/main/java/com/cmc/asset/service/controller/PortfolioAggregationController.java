package com.cmc.asset.service.controller;

import com.cmc.asset.business.portfolio.IPortfolioAggregationService;
import com.cmc.asset.model.contract.portfolio.PortfolioAggrAllocationRequest;
import com.cmc.asset.model.contract.portfolio.PortfolioAggrAllocationResponse;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioLineChartQueryDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioLineHistoricalChartResponseDTO;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsQueryRequest;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsQueryResponse;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsRequest;
import com.cmc.asset.model.contract.portfolio.aggregation.PortfolioSummaryStatisticsResponse;
import com.cmc.asset.service.Auth;
import com.cmc.data.common.ApiResponse;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.validation.Valid;

/**
 * portfolio aggr api for app
 *
 * <AUTHOR> ricky.x
 * @date : 2023/4/12 下午3:13
 */
@RestController
@RequestMapping("/v3/portfolio/aggr")
@RequiredArgsConstructor
public class PortfolioAggregationController extends BaseController {

    private final IPortfolioAggregationService portfolioAggregationService;

    @ApiOperation(value = "query portfolio allocation")
    @PostMapping("/asset-allocation")
    public Mono<ApiResponse<PortfolioAggrAllocationResponse>> assetAllocation(
            @Valid @RequestBody PortfolioAggrAllocationRequest request, @RequestHeader String userId) {
        request.getHeader().setUserId(userId);
        return invoke(request, portfolioAggregationService::aggrAssetAllocation);
    }

    @ApiOperation(value = "query portfolio summary")
    @PostMapping("/query-summary-statistics")
    public Mono<ApiResponse<PortfolioSummaryStatisticsResponse>> querySummaryStatistics(
            @Valid @RequestBody PortfolioSummaryStatisticsRequest portfolioSummaryStatisticsRequest,
            @RequestHeader String userId) {
        portfolioSummaryStatisticsRequest.getHeader().setUserId(userId);
        return invoke(portfolioSummaryStatisticsRequest, portfolioAggregationService::querySummaryStatistics);
    }

    /**
     * simple query-summary-statistics, use for app widget
     *
     * @param portfolioSummaryStatisticsRequest
     * @return
     */
    @ApiOperation(value = "query portfolio summary")
    @GetMapping("/query-summary-statistics/simple")
    public Mono<ApiResponse<PortfolioSummaryStatisticsQueryResponse>> querySummaryStatisticsSimple(
            @Valid @ModelAttribute PortfolioSummaryStatisticsQueryRequest portfolioSummaryStatisticsRequest,
            @RequestHeader String userId) {
        portfolioSummaryStatisticsRequest.getHeader().setUserId(userId);
        return invoke(portfolioSummaryStatisticsRequest, portfolioAggregationService::querySummaryStatisticsSimple);
    }

    @ApiOperation(value = "query portfolio summary")
    @PostMapping("/query-line-historical-chart")
    public Mono<ApiResponse<PortfolioLineHistoricalChartResponseDTO>> queryForLineHistoricalChart(
            @Valid @RequestBody PortfolioLineChartQueryDTO portfolioLineChartQueryDTO, @RequestHeader String userId) {
        portfolioLineChartQueryDTO.getHeader().setUserId(userId);
        return invoke(portfolioLineChartQueryDTO, portfolioAggregationService::queryForLineHistoricalChart);
    }

}
