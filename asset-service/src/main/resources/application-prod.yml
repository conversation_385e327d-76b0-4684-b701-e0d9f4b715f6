server:
  port: 8090
  env: prod
  servlet:
    context-path: /
# Monitor config
management:
  endpoint:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: info,env,prometheus
  health:
    redis:
      enabled: false
# Redis config
com:
  cmc:
    asset:
      s3: https://s3.coinmarketcap.com
      redis:
        asset:
          host: tf-cmc-prod-cache1.qkgkxc.ng.0001.use1.cache.amazonaws.com,tf-cmc-prod-cache1-ro.qkgkxc.ng.0001.use1.cache.amazonaws.com
          port: 6379
          password:
          database: 6
          use-ssl: false
          cluster: false
          command-timeout: 20000
        ## base-cache data
        base-cache:
          host: tf-cmc-prod-cache1.qkgkxc.ng.0001.use1.cache.amazonaws.com,tf-cmc-prod-cache1-ro.qkgkxc.ng.0001.use1.cache.amazonaws.com
          port: 6379
          password:
          database: 4
          use-ssl: false
          cluster: false
          static-topology: true
          command-timeout: 20000
      client:
        data-api: http://cmc-data-api:8050
        content: http://cmc-content-service:8120
        portal-api: http://coinmarketcap-portal-api-proxy.cmc-api
        market-service: http://cmc-worker-market-service:8000
        base-data: http://cmc-base-data-service:8001
      cmc-auth-key:
        portal-api: 5480b3b6-c683-44a1-9439-8ed46bc456b3
        shop-api: 5296b3b6-c683-44a1-9439-8ed27bc486b0
      mongodb:
        min-conn: 40
        max-conn: 300

#mongo db connection string
spring:
  data:
    mongodb:
      uri: mongodb+srv://${mongo_cmc_asset_username}:${mongo_cmc_asset_password}@cmc-asset-prod.joohc.mongodb.net/cmc-asset?retryWrites=true&w=majority&maxidletimems=60000&waitqueuetimeoutms=10000&maxpoolsize=300
      database: cmc-asset
#Open API 3 disable
springfox:
  documentation:
    swagger-ui:
      enabled: false

auth:
  api:
    base:
      url: http://cmc-auth-service-service:8100/


cmc:
  asset:
    kafka:
      taskTopic: user_point_daily_task_data_job_prod
      uri: msk.prod.coinmarketcap.supply:9092
      acks:
        config: all
      request:
        timeout: 60000
      producer:
        portfolio:
          clientId: portfolio_update_job_producer
          topic: portfolio_update_job
        userpoint:
          topic: user_point_data_job_prod
        userMonitor:
          bootstrap-servers: b-1.tfcmcprodworkernewkafk.b5tame.c4.kafka.us-east-1.amazonaws.com:9092,b-2.tfcmcprodworkernewkafk.b5tame.c4.kafka.us-east-1.amazonaws.com:9092,b-3.tfcmcprodworkernewkafk.b5tame.c4.kafka.us-east-1.amazonaws.com:9092
          clientId: user_monitor_asset_service_client
          topic: user_monitor_collector
      # notification kafka setting
      notification:
        bootstrap-servers: msk-notification.prod.coinmarketcap.supply:9092
        producer:
          watchlist:
            topic: user_watchlist_change

    secretKey: dd7ea1f0-3997-4a33-b625-3e6e1d1afaea


bn:
  captcha:
    secret: 2ed2ce737d6842qazx5a8b425d91defr
    gateway: https://api.commonservice.io/gateway-api
