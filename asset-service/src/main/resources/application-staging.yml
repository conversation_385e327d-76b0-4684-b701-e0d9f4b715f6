server:
  port: 8090
  env: staging
  servlet:
    context-path: /
# Monitor config
management:
  endpoint:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: info,env,prometheus
  health:
    redis:
      enabled: false
# Redis config
com:
  cmc:
    asset:
      s3: https://s3.staging.coinmarketcap.com
      redis:
        asset:
          host: tf-cmc-staging-cache1.qkgkxc.ng.0001.use1.cache.amazonaws.com,tf-cmc-staging-cache1-ro.qkgkxc.ng.0001.use1.cache.amazonaws.com
          port: 6379
          password:
          database: 6
          use-ssl: false
          cluster: false
          static-topology: true
          command-timeout: 20000
        base-cache:
          host:  tf-cmc-staging-cache1.qkgkxc.ng.0001.use1.cache.amazonaws.com,tf-cmc-staging-cache1-ro.qkgkxc.ng.0001.use1.cache.amazonaws.com
          port: 6379
          password:
          database: 4
          use-ssl: false
          cluster: false
          static-topology: true
          command-timeout: 20000
      client:
        data-api: http://cmc-data-api:8050
        content: http://cmc-content-service:8120
        portal-api: http://coinmarketcap-portal-api-proxy.cmc-api
        market-service: http://cmc-worker-market-service:8000
        base-data: http://cmc-base-data-service:8001
      cmc-auth-key:
        portal-api: 5480b3b6-c683-44a1-9439-8ed46bc456b3
        shop-api: 5296b3b6-c683-44a1-9439-8ed27bc486b0
      mongodb:
        min-conn: 2
        max-conn: 2

#mongo db connection string
spring:
  data:
    mongodb:
      uri: mongodb+srv://${mongo_cmc_staging_username}:${mongo_cmc_staging_password}@cmc-staging-portal-db.joohc.mongodb.net/cmc-asset?retryWrites=true&w=majority&minPoolSize=2&maxPoolSize=3&readPreference=secondary&maxStalenessSeconds=120&readConcernLevel=majority
      database: cmc-asset
#Open API 3 disable
springdoc:
  swagger-ui:
    enabled: true

auth:
  api:
    base:
      url: http://cmc-auth-service-service:8100/

cmc:
  asset:
    kafka:
      taskTopic: user_point_daily_task_data_job_staging
      uri: msk.staging.coinmarketcap.supply:9092
      acks:
        config: all
      request:
        timeout: 60000
      producer:
        portfolio:
          clientId: portfolio_update_job_producer
          topic: portfolio_update_job
        userpoint:
          topic: user_point_data_job_staging
        userMonitor:
          bootstrap-servers: msk.staging.coinmarketcap.supply:9092
          clientId: user_monitor_asset_service_client
          topic: user_monitor_collector
    secretKey: dd7ea1f0-3997-4a33-b625-3e6e1d1afaea

bn:
  captcha:
    secret: 2ed2ce737d6842qazx5a8b425d91defr
    gateway: https://api.commonservice.io/gateway-api
