server:
  port: 8090
  env: beta
  servlet:
    context-path: /

# Monitor config
management:
  endpoint:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: info,env,prometheus
  health:
    redis:
      enabled: false

# 连接池最大连接数（使用负值表示没有限制） spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接 spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接 spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）spring.redis.timeout=1000
# Redis config
com:
  cmc:
    asset:
      s3: https://s3.beta.coinmarketcap.com
      redis:
        asset:
          host: tf-cmc-beta-bigdata-gravity.qkgkxc.clustercfg.use1.cache.amazonaws.com
          port: 6379
          password:
          use-ssl: false
          cluster: true
          static-topology: true
          command-timeout: 20000
        base-cache:
          host: tf-cmc-beta-cache1.qkgkxc.ng.0001.use1.cache.amazonaws.com
          port: 6379
          password:
          database: 4
          use-ssl: false
          cluster: false
          command-timeout: 20000
      client:
        data-api: https://api.beta.coinmarketcap.supply/data-api
        content: https://api.beta.coinmarketcap.supply/cmc-content
        portal-api: http://coinmarketcap-portal-api-proxy.cmc-api
        market-service: https://api.beta.coinmarketcap.supply/market-service
        base-data: http://cmc-base-data-service:8001
      cmc-auth-key:
        portal-api: 5480b3b6-c683-44a1-9439-8ed46bc456b3
        shop-api: 5296b3b6-c683-44a1-9439-8ed27bc486b0
      mongodb:
        min-conn: 2
        max-conn: 2
      referral:
        condition:
          count: 10
      referred:
        complete:
          count: 1
#mongo db connection string
spring:
  data:
    mongodb:
      uri: mongodb+srv://data-api:<EMAIL>/cmc-asset?retryWrites=true&w=majority
      database: cmc-asset
#springdoc:
#  version: '@springdoc.version@'
#  swagger-ui:
#    path: /asset/swagger-ui.html
#    enabled: true

auth:
  api:
    base:
      url: https://api.beta.coinmarketcap.supply/auth/


cmc:
  asset:
    kafka:
      taskTopic: user_point_daily_task_data_job_beta_1
      uri: msk.beta.coinmarketcap.supply:9092
      acks:
        config: all
      request:
        timeout: 60000
      producer:
        portfolio:
          clientId: portfolio_update_job_producer
          topic: portfolio_update_job
        userpoint:
          topic: user_point_data_job_beta
        userMonitor:
          bootstrap-servers: msk.beta.coinmarketcap.supply:9092
          clientId: user_monitor_asset_service_client
          topic: user_monitor_collector
    secretKey: 97b8bd4b-bf32-4357-94fc-774af1657f7e

bn:
  captcha:
    secret: 2ed2ce737d6842qazx5a8b425d91defr
    gateway: https://api.commonservice.io/gateway-api
