<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-bom-parent</artifactId>
        <version>0.0.6.3</version>
        <relativePath />
    </parent>

    <groupId>com.cmc</groupId>
    <artifactId>cmc-asset-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.165-SNAPSHOT</version>
    <modules>
        <module>asset-model</module>
        <module>asset-dao</module>
        <module>asset-business</module>
        <module>asset-service</module>
        <module>asset-job</module>
        <module>asset-test-report</module>
        <module>asset-common</module>
    </modules>

    <properties>
        <java.version>13</java.version>
        <lombok.version>1.18.12</lombok.version>
        <commons.beanutils.version>1.9.4</commons.beanutils.version>
        <commons.collections4.version>4.4</commons.collections4.version>
        <reactor-core.version>3.3.5.RELEASE</reactor-core.version>
        <commons.collection.version>3.2.1</commons.collection.version>
        <commons.io.version>2.4</commons.io.version>
        <commons.lang.version>3.10</commons.lang.version>
        <snippetsDirectory>${project.build.directory}/generated-snippets</snippetsDirectory>
        <springdoc.version>1.3.9</springdoc.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <dockerfile-maven.version>1.4.13</dockerfile-maven.version>
        <docker.root.repo>346764516239.dkr.ecr.us-east-1.amazonaws.com</docker.root.repo>
        <docker.tag>latest</docker.tag>
        <docker.skip>true</docker.skip>
        <reactor.kafka.version>1.2.1.RELEASE</reactor.kafka.version>
        <jacoco.version>0.8.6</jacoco.version>
        <testng.version>6.8</testng.version>
        <mockito.version>3.7.0</mockito.version>
        <aggregate.report.dir>asset-test-report/target/site/jacoco-aggregate/jacoco.xml</aggregate.report.dir>
        <sonar.coverage.jacoco.xmlReportPaths>./${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
        <apollo.version>1.9.0</apollo.version>
        <mysql.driver.version>8.0.33</mysql.driver.version>
        <mybatis.version>3.5.4</mybatis.version>
        <mybatis.plus.version>3.3.2</mybatis.plus.version>
        <mybatis.starter.version>2.1.1</mybatis.starter.version>
        <mybatis.plus.dynamic.ds.version>3.2.0</mybatis.plus.dynamic.ds.version>
        <aws.java.sdk.dynamodb.version>1.12.182</aws.java.sdk.dynamodb.version>
        <aws.java.sdk.sts.version>1.12.182</aws.java.sdk.sts.version>
        <dynamodb.enhanced.version>2.14.7</dynamodb.enhanced.version>
        <software.amazon.awssdk.sts.version>2.13.60</software.amazon.awssdk.sts.version>
        <hibernate.validator.version>6.0.19.Final</hibernate.validator.version>
        <shardingsphere.version>4.0.1</shardingsphere.version>
        <jaxb.api.version>2.3.0</jaxb.api.version>
        <jaxb.core.version>2.3.0</jaxb.core.version>
        <jaxb.impl.version>2.3.0</jaxb.impl.version>
        <shardingsphere.sql.version>4.0.1</shardingsphere.sql.version>
        <block.chain.version>v1.5.0</block.chain.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-inline</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-junit-jupiter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>data-api-common</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>auth-api-common</artifactId>
                <version>1.6</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>asset-model</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>asset-dao</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>asset-business</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>asset-common</artifactId>
                <version>1.0.165-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-core</artifactId>
                <version>${reactor-core.version}</version>
            </dependency>

            <dependency>
                <groupId>io.projectreactor.kafka</groupId>
                <artifactId>reactor-kafka</artifactId>
                <version>${reactor.kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <!-- springdoc ui -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>


            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatis.plus.dynamic.ds.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-dynamodb</artifactId>
                <version>${aws.java.sdk.dynamodb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-sts</artifactId>
                <version>${aws.java.sdk.sts.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sts</artifactId>
                <version>${software.amazon.awssdk.sts.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>software.amazon.awssdk</groupId>
                        <artifactId>apache-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>software.amazon.awssdk</groupId>
                        <artifactId>netty-nio-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dynamodb</artifactId>
                <version>${dynamodb.enhanced.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dynamodb-enhanced</artifactId>
                <version>${dynamodb.enhanced.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-jexl3</artifactId>
                <version>3.2.1</version>
            </dependency>

            <!-- sharding jdbc -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.impl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-sql-parser-engine</artifactId>
                <version>${shardingsphere.sql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>cmc-framework-blockchain-utils</artifactId>
                <version>${block.chain.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <scm>
        <connection>scm:git:****************************:cmc-backend/cmc-asset-service.git</connection>
        <url>https://git.coinmarketcap.supply/cmc-backend/cmc-asset-service</url>
        <tag>1.0.0-SNAPSHOT</tag>
    </scm>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <release>${java.version}</release>
<!--                    <fork>true</fork>-->
                    <compilerArgs>
                        <arg>--enable-preview</arg>
                    </compilerArgs>
                    <!--<forceJavacCompilerUse>true</forceJavacCompilerUse>-->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <argLine>@{surefireArgLine} --enable-preview</argLine>
                    <excludedGroups>integration</excludedGroups>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <inherited>false</inherited>
                        <goals>
                            <goal>aggregate-pmd-check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <printFailingErrors>true</printFailingErrors>
                    <rulesets>
                        <!--                        <ruleset>rulesets/java/ali-comment.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-concurrent.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-constant.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-exception.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-flowcontrol.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-naming.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-oop.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-orm.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-other.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-set.xml</ruleset>-->
                        <ruleset>rulesets/java/cmc-swagger.xml</ruleset>
                    </rulesets>
                    <failurePriority>2</failurePriority>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.alibaba.p3c</groupId>
                        <artifactId>p3c-pmd</artifactId>
                        <version>2.1.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.cmc</groupId>
                        <artifactId>coding-convention-pmd</artifactId>
                        <version>[1.0.0,)</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>


        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven.version}</version>
                    <configuration>
                        <skip>${docker.skip}</skip>
                        <skipDockerInfo>true</skipDockerInfo>
                        <tag>${docker.tag}</tag>
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals><goal>prepare-agent</goal></goals>
                        </execution>
                    </executions>
                    <configuration>
                        <propertyName>surefireArgLine</propertyName>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <reportSets>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate-pmd-no-fork</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.coinmarketcap.supply/repository/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.coinmarketcap.supply/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
